# http://editorconfig.org
root = true

[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.php]
indent_size = 4

#Intellij
#IntelliJ - Types
ij_any_space_after_type_cast = true
ij_php_space_after_type_cast = true
#IntelliJ - Alignment
ij_php_align_assignments = true
ij_php_align_class_constants = true
ij_php_align_phpdoc_comments = true
ij_php_align_phpdoc_param_names = true
ij_php_align_key_value_pairs = true
#IntelliJ - Style
ij_php_lower_case_boolean_const = true
ij_php_lower_case_null_const = true
ij_php_force_short_declaration_array_style = true

[**/{DigitalerMaklerbetreuer,MaklerbetreuerStatistics,Demodaten,Beratungsvorsprung/modules,modules/Tags,modules/Oauth,modules/ApiGateway,modules/Marketplace}/**.php]
#IntelliJ - Alignment
ij_php_align_assignments = false
ij_php_align_class_constants = false
ij_php_align_phpdoc_comments = false
ij_php_align_phpdoc_param_names = false
ij_php_keep_rparen_and_lbrace_on_one_line = true

[*.{js, jsx, ts, tsx, vue}]
max_line_length = 100

[composer.json]
indent_size = 4

[*.md]
trim_trailing_whitespace = false
