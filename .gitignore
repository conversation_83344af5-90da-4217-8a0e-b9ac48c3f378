# OS-spezifische Dateien
[Tt]humbs.db
.DS_Store

# Editor directories and files
nbproject/private/
tags
tags.tmp
*.njsproj
*.ntvs*
*.sln
*.suo
*.sw?
.buildpath
.idea
.phpcd
.phpintel
.project
.settings
.vscode
.run

# Environments
.env
.env.auth
/docker/*.env

# PHP
vendor
coverage
phpunit.xml
.phpunit.result.cache
phpstan.neon

# Yii backend
/protected/config/*.local.php
/protected/log/*
/protected/modules/backend/modules/verwaltung/views/mail/mailAllgemeineOnlineSchulung.php
/protected/modules/bipro/backend/debug/
/protected/modules/bipro/backend/local.php
/protected/modules/bipro/responses/*
/protected/modules/bipro/xml/nepatec/*

# Generated content
*.zip
!/tests/**/*.zip
/public/assets/
/public_upload
/uploads
js/tinymce/plugins/moxiemanager/data/storage
_misc/_database/sqlite/

# Frontend
node_modules
/protected/modules/kundenakte/assets/mix-manifest.json
/protected/views/frontend_v2.php
/public/hot
/public/js/chunk/local
/public/v2
/webpack.local.json

/modules
/modules/*

# Quality tools
*.phar
cghooks.lock

# Tooling logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# dockerised pnpm-store
.pnpm-store

# yalc https://github.com/wclr/yalc
**/.yalc/*
yalc.lock

# csv
*.csv

# AI-Coding-Agents
.aider*

# Docker
docker-compose.override.yml
