# Coding Conventions and Guidelines

This document provides a clear and cohesive set of coding conventions and guidelines for contributing to this repository. By adhering to these standards, we ensure a consistent, clean, and secure codebase, promoting better collaborations and long-term maintainability.

## Codebase Overview

The project utilizes specific frameworks and technologies to deliver a robust CRM system tailored for insurance brokers. Here's a breakdown of the core elements of the codebase:

1. **Backend Framework: Yii 1.1.30 Framework**
  - The codebase is built on **Yii 1.1.30**.
  - It follows the **MVC architecture** (Model-View-Controller) and relies on Yii-specific components.
  - **Legacy considerations** are essential, as Yii 1.1.30 remains outdated. We prioritize backward compatibility while gradually implementing improvements.

2. **Backend Programming Language: PHP 8.1**
  - The project now runs on **PHP 8.1**, allowing the use of features such as named arguments, readonly properties, and `match` expressions.
  - Although PHP 8.1 introduces many modern features, compatibility with Yii 1.1 means avoiding features that may conflict with older Yii components. Testing remains essential to ensure stability across PHP 8.1 environments.

3. **Insurance Broker CRM Domain**
  - The system primarily serves as a **Customer Relationship Management (CRM)** tool for **insurance brokers**.
  - Domain-specific logic involves managing policies, handling client data, and processing insurance products.
  - We need to manage workflows specific to the insurance industry.

---

## Development Focus Areas

When contributing to this repository, there are key principles and best practices that we follow to ensure high-quality, maintainable, and efficient code:

1. **Clean Code and Architecture**
  - Write clean, modular, and maintainable code.
  - Ensure that classes are small and focused and that each method does one thing.
  - Avoid complexity by following **consistent naming conventions** and keeping functions and methods concise.

2. **SOLID Principles**
  - Adhere to the **SOLID** design principles to ensure object-oriented best practices:
    - **Single Responsibility Principle** (SRP): Each class should have one well-defined responsibility.
    - **Open-Closed Principle** (OCP): Classes should be open for extension but closed for modification.
    - **Liskov Substitution Principle** (LSP): Objects should be replaceable by instances of their subtypes without affecting the correctness of the program.
    - **Interface Segregation Principle** (ISP): Clients should not be forced to depend on interfaces they do not use.
    - **Dependency Inversion Principle** (DIP): Depend on abstractions rather than concrete implementations.

3. **Type Declarations**
  - Wherever applicable, use **typed properties**, return types, and parameter types. This enhances readability and reduces potential type-related errors.
  - Leverage PHP 8.1 features for type safety:
    - Example:
      ```php
      private ?string $name = null;

      public function setName(string $name): void 
      {
          $this->name = $name;
      }

      public function getName(): ?string
      {
          return $this->name;
      }
      ```
  - We use **PHPStan** for static code analysis in several parts of the repository to ensure type safety, code consistency, and adherence to best practices.
  - In places where PHP's native typing is insufficient (e.g., generics, arrays of specific object types), **DocBlock comments** can be utilized in combination with PHPStan to enhance type safety beyond what PHP natively supports.
    - **PHPStan docblock types** enable us to define more complex types such as arrays of certain objects, intersection types, union types, etc.
    - Proper usage of **DocBlock comments** should follow PHPStan's guidance to unlock features like generics or advanced type annotations:
      ```php
      /** 
       * @var MyModel[] $models Array of MyModel instances 
       */
      protected array $models = [];

      /** 
       * @param array<int, MyModel> $models
       */
      public function loadModels(array $models): void
      {
          $this->models = $models;
      }
      ```
    - For a complete list of supported types and best practices related to PHPDoc and PHPStan, please refer to the [PHPStan Documentation on PHPDoc Types](https://phpstan.org/writing-php-code/phpdoc-types).
    - Always ensure the documentation is up-to-date and meaningful, especially when describing complex return types or multi-type arguments.

4. **DocBlock Comments**
  - Use **docblock** comments only where necessary (e.g., for arrays or complex object types). Avoid over-commenting in places where types are self-explanatory.
  - Include useful descriptions for functions, methods, and classes, especially if there is complex business logic involved.

5. **Performance Optimization**
  - Continuously focus on **optimizing** both backend logic and database queries to improve overall system performance.
  - Implement **caching strategies**, such as data, query, or page caching, where appropriate.
  - Try to **minimize unnecessary database hits** by grouping operations or handling bulk loading of data.

6. **Security Best Practices**
  - Ensure **input validation** and **sanitization** on all incoming data to avoid vulnerabilities like SQL Injection or Cross-Site Scripting (XSS).
  - Utilize **Yii's built-in security features** such as CSRF protection and avoid writing raw SQL queries whenever possible, using parameterized queries instead.

---

By adhering to these development focus areas, we maintain high standards for code quality, security, and performance while addressing the specific needs of PW.
