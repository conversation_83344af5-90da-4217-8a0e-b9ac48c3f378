# syntax=docker.io/docker/dockerfile:1.10.0

# Build assets v1 in clean environment.
# Later we will copy only the built assets to the final image.
FROM node:10.24.1-alpine@sha256:02767d92553e465bf51e0bd661074f2e70bd575c4a69a0d610aa6e78fd20a9bf AS assets-v1

WORKDIR /build

# Minimal configs required to build assets v1.
COPY --link \
  package.json \
  yarn.lock \
  webpack.config.js \
  webpack.json \
  webpack.mix.js \
  /build/

# For whatever reason the build step of assets v1 requires files in
# `protected/modules/kundenakte/assets/css` to be present.
COPY --link protected/ /build/protected/

# Copy the source files of assets v1.
COPY --link assets/ /build/assets/

# TODO: We may want to use an external cache for node_modules in the future.
RUN yarn install --frozen-lockfile

# Separate build step for better caching.
RUN yarn production


# Build assets v2 in clean environment.
# Later we will copy only the built assets to the final image.
FROM node:18.20.4-alpine@sha256:02376a266c84acbf45bd19440e08e48b1c8b98037417334046029ab585de03e2 AS assets-v2

WORKDIR /build

# Copy the enitre assets v2 directory as it is pretty small and self-contained.
COPY --link assets/v2/ /build/assets/v2/

# TODO: We may want to use an external cache for node_modules in the future.
RUN \
  --mount=type=secret,id=NPM_FRONTEND_UTILS_AUTH_TOKEN,env=NPM_FRONTEND_UTILS_AUTH_TOKEN,required=true \
  --mount=type=secret,id=NPM_FONTAWESOME_AUTH_TOKEN,env=NPM_FONTAWESOME_AUTH_TOKEN,required=true \
  --mount=type=secret,id=NPM_TIPTAP_PRO_AUTH_TOKEN,env=NPM_TIPTAP_PRO_AUTH_TOKEN,required=true \
  yarn --cwd /build/assets/v2 install --frozen-lockfile

# Separate build step for better caching.
RUN \
  --mount=type=secret,id=NPM_FRONTEND_UTILS_AUTH_TOKEN,env=NPM_FRONTEND_UTILS_AUTH_TOKEN,required=true \
  --mount=type=secret,id=NPM_FONTAWESOME_AUTH_TOKEN,env=NPM_FONTAWESOME_AUTH_TOKEN,required=true \
  --mount=type=secret,id=NPM_TIPTAP_PRO_AUTH_TOKEN,env=NPM_TIPTAP_PRO_AUTH_TOKEN,required=true \
  yarn --cwd /build/assets/v2 build


# Execute `composer install` in clean environment.
# Later we will copy only the `vendor` directory to the final image.
# We use our custom `nginx-php-dev` image as it contains all required build
# tools and dependencies.
# It should be the same PHP version as the final image.
FROM ghcr.io/demvsystems/nginx-php-dev:8.1.29@sha256:8a60b24f27319fe2199b16d1d0e869050ebc53daa9420881cd24dfa20e6bd676 AS build

WORKDIR /build

COPY --link composer.json composer.lock /build/

# Install PHP extensions from `composer.json`.
RUN set -eux; \
    composer check-platform-reqs --no-dev --lock --format=json 2> /dev/null | \
      jq -r 'map(.name | select(startswith("ext-")) | sub("^ext-(zend-)?"; "")) | join(" ")' > /build/extensions.txt; \
    install-php-extensions $(cat /build/extensions.txt)

# These patches will be applied by the `composer install` command.
COPY --link iban-generation-patch.diff smalot-pdfparser.diff /build/

# Required for scanning classes via `ClassMapGenerator.php`.
COPY --link protected/ /build/protected/

# Execute `composer install`.
# The following secrets are required to install the private composer packages.
# TODO: We may want to use an external cache for vendor in the future.
RUN \
  --mount=type=secret,id=GITHUB_TOKEN,env=GITHUB_TOKEN,required=true \
  --mount=type=secret,id=SETASIGN_SIGNER_USER,env=SETASIGN_SIGNER_USER,required=true \
  --mount=type=secret,id=SETASIGN_SIGNER_PASS,env=SETASIGN_SIGNER_PASS,required=true \
  COMPOSER_AUTH="$(echo '{"github-oauth":{"github.com":"${GITHUB_TOKEN}"},"http-basic":{"www.setasign.com":{"username": "${SETASIGN_SIGNER_USER}","password":"${SETASIGN_SIGNER_PASS}"}}}' | envsubst)" \
  composer install --prefer-dist --no-dev --optimize-autoloader --no-progress


# This is our final image.
# It contains all required assets and dependencies for Professional works.
# From this point onwards, every step will be included in the final image.
FROM ghcr.io/demvsystems/nginx-php:8.1.29@sha256:576bf7f7dacbd7367bbf9fc5629f65f2a9cae6d1c1114bc0f94ade84c8b537b9

ENV APP_BASE_DIR=/var/www/professionalworks

WORKDIR "$APP_BASE_DIR"

# Install system dependencies.
RUN apk add --no-cache \
    # p7zip was renamed to 7zip in Alpine 3.18
    p7zip \
    # The 7z version in the 7.4.33 PHP base image has issues with permissions,
    # so install unzip, too.
    unzip \
    texlive-full \
    imagemagick \
    # Required for pdftk. Don't forget to update in new alpine versions.
    openjdk17-jre-headless

COPY --link --from=build /build/extensions.txt "${APP_BASE_DIR}/"

# Install PHP extensions from `composer.json`.
RUN install-php-extensions $(cat "${APP_BASE_DIR}/extensions.txt")

COPY --link \
  --from=pdftk/pdftk:3.3.3@sha256:81104a5850b977888df77c2d6ee7e421e017dcf49dc0eef6350fc11b71c5a8b4 \
  /usr/share/java/ /usr/share/java/

COPY --link \
  --from=pdftk/pdftk:3.3.3@sha256:81104a5850b977888df77c2d6ee7e421e017dcf49dc0eef6350fc11b71c5a8b4 \
  /usr/bin/pdftk  /usr/bin/

# The following COPY satements are using `--chown` attribute to set the correct
# ownership of the files. This prevents the need to run `chown` in the final
# RUN statement, which increases the image size significantly (all changed files
# are duplicated in the new layer with the new permissions).
# Furthermore, we have to use UIDs instead of usernames, as there are known
# bugs in conjunction with `--link`:
#   - https://github.com/docker/buildx/issues/1408
#   - https://github.com/docker/docs/issues/20660
#   - https://github.com/moby/buildkit/issues/2987

# Copy base structure from repository.
COPY --link --chown=65534:65534 console.php "${APP_BASE_DIR}/"
COPY --link --chown=65534:65534 protected/ "${APP_BASE_DIR}/protected/"
COPY --link --chown=65534:65534 public/ "${APP_BASE_DIR}/public/"

# Copy `vendor` directory.
COPY --link --chown=65534:65534 --from=build /build/vendor/ "${APP_BASE_DIR}/vendor/"

# Copy assets v1.
COPY --link --chown=65534:65534 --from=assets-v1 /build/public/ "${APP_BASE_DIR}/public/"

# Copy assets v2.
COPY --link --chown=65534:65534 --from=assets-v2 /build/protected/ "${APP_BASE_DIR}/protected/"
COPY --link --chown=65534:65534 --from=assets-v2 /build/public/ "${APP_BASE_DIR}/public/"

# Copy configs.
COPY --link docker/etc/ /etc/

# Theses directories are required to exist and be writable by php-fpm.
RUN set -eux; \
    mkdir "${APP_BASE_DIR}/uploads" "${APP_BASE_DIR}/public/assets"; \
    chown nobody:www-data "${APP_BASE_DIR}/protected/runtime" "${APP_BASE_DIR}/public/assets" "${APP_BASE_DIR}/uploads"; \
    chmod g+w "${APP_BASE_DIR}/protected/runtime" "${APP_BASE_DIR}/public/assets" "${APP_BASE_DIR}/uploads"
