##
## BUILD COMMANDS
## --------------
##

.PHONY: init-config new build-all build-pw db db-create-users pw

init-config: ## Creates the initial configuration files
	cp -n protected/config/params.local.php.dist protected/config/params.local.php

new: kill build-all db db-create-users minio_setup pw ## Completely rebuild and restart everything

build-all: ## Build and start all containers
	mkdir -p vendor # vendor/ is mounted by docker compose, and if it doesn't exist, it gets created with root as owner, which breaks stuff
	docker compose up -d --build --remove-orphans

build-pw: ## Rebuild the php-fpm container
	docker compose build php-fpm

minio_setup:
	docker compose run --rm --entrypoint bash $(DOCKER_MC_SERVICE) -c "                                                     \
	  mc alias set minio http://s3:9000 $(PW_STORAGE_S3_KEY) $(PW_STORAGE_S3_SECRET);     \
	  mc mb --ignore-existing --region=eu-central-1 \
	    minio/demv-dev-pw-beratungsvorsprung \
	    minio/demv-dev-pw-client-files \
	    minio/demv-dev-pw-company-logos \
	  "

db: wait-for-db ## Import initial database fixtures
	docker compose exec -T $(DOCKER_DB_SERVICE) mysql -u$(DB_DOCKER_USER) -p$(DB_DOCKER_PASSWORD) < $(COMMON_DATA_DUMP)
	docker compose exec -T $(DOCKER_DB_SERVICE) mysql -u$(DB_DOCKER_USER) -p$(DB_DOCKER_PASSWORD) < $(LOG_DB_DUMP)
	docker compose exec -T $(DOCKER_DB_SERVICE) mysql -u$(DB_DOCKER_USER) -p$(DB_DOCKER_PASSWORD) < $(PW_DUMP)
	docker compose exec -T $(DOCKER_DB_SERVICE) mysql -u$(DB_DOCKER_USER) -p$(DB_DOCKER_PASSWORD) < $(BIPRO_DUMP)
	docker compose exec -T $(DOCKER_DB_SERVICE) mysql -u$(DB_DOCKER_USER) -p$(DB_DOCKER_PASSWORD) < $(MODULES_DUMP)

db-create-users: wait-for-db ## Create database users
	docker compose exec -T $(DOCKER_DB_SERVICE) mysql -u$(DB_DOCKER_USER) -p$(DB_DOCKER_PASSWORD) < $(DEBEZIUM_USER)

install-company-logos: minio_setup
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php installcompanylogos --path=_misc/_uploads/company/logo
	docker compose exec -T $(DOCKER_DB_SERVICE) mysql -u$(DB_DOCKER_USER) -p$(DB_DOCKER_PASSWORD) < $(COMPANY_LOGOS_SQL)

pw: pw-composer-install ## Build ProfessionalWorks
	make migrate
	chmod +x generate-auth-keys.sh
	docker compose exec $(DOCKER_PHP_SERVICE) composer auth-ssh-gen
	make clear-cache
	make setup-demodaten
	make demodaten-optional
	make install-company-logos
	make admin
	just assets

##
## DEPENDENCIES - BERATUNG
## ----------------------
##

.PHONY: beratung-setup beratung-update beratung-local-token

beratung-setup: ## Install Beratung after a clone
	make env-check
	make beratung-local-token
	bash protected/modules/Beratung/scripts/setup.sh

beratung-update: ## Update Beratung after a pull
	make env-check
	bash protected/modules/Beratung/scripts/update.sh

beratung-local-token:  ## Creates a static authtoken for Beratung in local environment
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php beratung setlocalauthtoken

##
## UTILITY COMMANDS
## ---------------------------
##

.PHONY: env-check

env-check: ## compares the keys of the .env.dist with the .env
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php env compare

##
## UTILITY COMMANDS - COMPOSER
## ---------------------------
##

.PHONY: composer clear-cache \
	pw-composer-install pw-composer-update pw-composer-update-lock

composer: ## run composer inside the php service container
	docker compose exec $(DOCKER_PHP_SERVICE) composer $(filter-out $@,$(MAKECMDGOALS))

clear-cache: clear-cache-assets clear-cache-yii clear-cache-yii-schema clear-cache-yii-app ## Clear all caches

clear-cache-assets: ## Clear cached assets
	docker compose exec $(DOCKER_PHP_SERVICE) composer clear-cache-assets

clear-cache-yii: ## Clear the Yii cache
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php clearcache

clear-cache-yii-app: ## Clear the Yii app cache
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php clearappcache

clear-cache-yii-schema: ## Clear the Yii schema cache
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php clearschemacache

dump-autoload: ## Recreate the autoloader metadata
	docker compose exec $(DOCKER_PHP_SERVICE) composer dump-autoload

pw-composer-install: ## Install dependencies
	docker compose exec -e COMPOSER_MEMORY_LIMIT=-1 $(DOCKER_PHP_SERVICE) composer install --optimize-autoloader

pw-composer-update: ## Update dependencies
	docker compose exec -e COMPOSER_MEMORY_LIMIT=-1 $(DOCKER_PHP_SERVICE) composer update

pw-composer-update-lock: ## Update the composer lock file without updating any packages
	docker compose exec $(DOCKER_PHP_SERVICE) composer update --lock

##
## UTILITY COMMANDS - DATABASE / MIGRATIONS
## ----------------------------------------
##

.PHONY: db-check-for-null wait-for-db mark-migration migrate migrate-down

db-check-for-null:
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php DbInfo listNullableColumns

wait-for-db: ## Wait for the database container to be fully functional
	echo Waiting for MariaDB to become ready...
	docker compose up --wait $(DOCKER_DB_SERVICE)

mark-migration: ## Mark a migration as having been applied
	docker compose exec $(DOCKER_PHP_SERVICE) composer migrate mark $(filter-out $@,$(MAKECMDGOALS))

migrate: wait-for-db clear-cache## Run all migrations
	docker compose exec $(DOCKER_PHP_SERVICE) composer migrate

migrate-down: wait-for-db ## Un-apply the most recent applied migration
	docker compose exec $(DOCKER_PHP_SERVICE) composer migrate down 1

db-connect:
	docker compose exec $(DOCKER_DB_SERVICE) mysql -u"$(DB_DOCKER_USER)" -p"$(DB_DOCKER_PASSWORD)"

##
## UTILITY COMMANDS - DOCKER
## -------------------------
##
.PHONY: kill up up-monitoring

kill: ## Kill all containers
	docker compose kill
	docker compose down --volumes --remove-orphans

up: ## Start all containers
	docker compose up -d

up-monitoring:
	docker compose --profile monitoring up -d

##
## TEST COMMANDS
## -------------
##

.PHONY: test tests ci-test ci-test-ff fast-test

FILTER ?= .
TEST ?=
test: ## Run the pw testsuite in PHPUnit, you can use TEST to specify a test file/directory or FILTER for a filtering regex, e.g. make tests TEST=tests/Config FILTER=SameKey
	docker compose exec $(DOCKER_PHP_SERVICE) vendor/bin/phpunit --filter $(FILTER) $(TEST)

tests: test ## Alias for test

ci-test: ## Run tests for CI
	docker compose exec $(DOCKER_PHP_SERVICE) composer ci-test

ci-test-ff: ## Run tests for CI until first failure
	docker compose exec $(DOCKER_PHP_SERVICE) composer ci-test-fail-fast

fast-test: ## Run the "fast" testsuite
	docker compose exec $(DOCKER_PHP_SERVICE) composer fast-test

lint-php: pw-phpcstd lint-php-code-sniffer ## Run php linting

lint-php-fix: pw-phpcstd-fix lint-php-code-sniffer-fix ## Run php linting with auto-fixers

pw-phpcstd: ## Run phpcstd tests (Composer Normalize, parallel-lint, phpstan)
	docker compose exec $(DOCKER_PHP_SERVICE) vendor/bin/phpcstd --ci --ansi

pw-phpcstd-fix: ## Run phpcstd tests (Composer Normalize, parallel-lint, phpstan) with auto-fixers
	docker compose exec $(DOCKER_PHP_SERVICE) vendor/bin/phpcstd --ci --ansi --fix

lint-php-code-sniffer: ## Run PHPCodeSniffer linting
	docker compose exec $(DOCKER_PHP_SERVICE) vendor/bin/phpcs -ns

lint-php-code-sniffer-fix: ## Run PHPCodeSniffer linting with auto-fixers
	docker compose exec $(DOCKER_PHP_SERVICE) vendor/bin/phpcbf -ns

##
## TEST DATA GENERATION
## --------------------
##

.PHONY: admin setup-demodaten demodaten-optional setup-gdv-files setup-bipro-inbox

admin: ## Create a default admin user
	echo -e "$(PW_ADMIN_FIRSTNAME)\n$(PW_ADMIN_LASTNAME)\n$(PW_ADMIN_USERNAME)\n$(PW_ADMIN_PASSWORD)\n$(PW_ADMIN_EMAIL)" | docker compose exec -T php-fpm php console.php setup insertAdmin

setup-demodaten: ## Create basic demo data
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php demodaten grunddaten

demodaten-optional: ## Create optional demo data
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php demodaten optional

setup-gdv-files: ## Setup GDV files
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php BiproUploadGdvContractInShare truncateTables
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php BiproUploadGdvContractInShare uploadGdv
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php BiproUploadGdvContractInShare uploadGdvVerbund
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php BiproUploadGdvContractInShare uploadNotExistingClient
	docker compose exec $(DOCKER_PHP_SERVICE) php console.php BiproUploadGdvContractInShare uploadGdvFoundByContract

setup-bipro-inbox: ## Setup BiPro inbox
	docker compose exec $(DOCKER_PHP_SERVICE) cp -fu protected/modules/bipro/backend/local.php.dist protected/modules/bipro/backend/local.php
	docker compose exec $(DOCKER_PHP_SERVICE) $(CONSOLE) wwkimport box_id=2
	docker compose exec $(DOCKER_PHP_SERVICE) $(CONSOLE) linkofflineteststouser

setup-bipro-ai-testdaten: ## Setup BiPro AI Testdaten
	docker compose exec $(DOCKER_PHP_SERVICE) cp -fu protected/modules/bipro/backend/local.php.dist protected/modules/bipro/backend/local.php
	docker compose exec $(DOCKER_PHP_SERVICE) $(CONSOLE) generateaitestdaten index

local-update-status: ## update status of gdv2bipro objects in the db
	docker compose exec $(DOCKER_PHP_SERVICE) $(CONSOLE) gdv2biprostatuscollector

##
## Documentation Generation
## --------------------
##

.PHONY: openapi openapi-ai-gateway

openapi: openapi-ai-gateway ## Generate OpenAPI documentations

openapi-ai-gateway: ## Generate OpenAPI documentation for the AI-Gateway
	docker compose exec $(DOCKER_PHP_SERVICE) ./vendor/bin/openapi protected/modules/AiGateway -o protected/modules/AiGateway/openapi.yaml -b .phpstan/bootstrap.php


##
## DEPLOYMENT
## ----------
## All commands can pass extra options to deployer via an OPTS argument
## Example: make deploy-dev OPTS=--branch=my_branch
##

.PHONY: deploy-live deploy-staging deploy-dev

deploy-live: ## Deploy current master from GitHub to live
	docker/deploy/deployer.sh deploy -p $(OPTS) -- live

deploy-staging: ## Deploy GitHub version of the checked out branch to staging
	docker/deploy/deployer.sh deploy $(OPTS) -- staging

deploy-dev: ## Deploy GitHub version of the checked out branch to dev
	docker/deploy/deployer.sh deploy $(OPTS) -- dev

deploy-lock-live: ## Lock the deployment on live with a message
	docker/deploy/deployer.sh deploy:lock-with-msg --set-lock-msg -p $(OPTS) -- live

deploy-lock-staging: ## Lock the deployment on staging with a message
	docker/deploy/deployer.sh deploy:lock-with-msg --set-lock-msg $(OPTS) -- staging

deploy-lock-dev: ## Lock the deployment on dev with a message
	docker/deploy/deployer.sh deploy:lock-with-msg --set-lock-msg $(OPTS) -- dev

deploy-unlock-live: ## Unlock the deployment on live
	docker/deploy/deployer.sh deploy:unlock -p $(OPTS) -- live

deploy-unlock-staging: ## Unlock the deployment on staging
	docker/deploy/deployer.sh deploy:unlock $(OPTS) -- staging

deploy-unlock-dev: ## Unlock the deployment on dev
	docker/deploy/deployer.sh deploy:unlock $(OPTS) -- dev
