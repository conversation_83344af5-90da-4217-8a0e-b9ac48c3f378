{"name": "demvsystems/professionalworks", "description": "Main CRM Application", "license": "proprietary", "authors": [{"name": "Development Team", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-bcmath": "*", "ext-calendar": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-gnupg": "*", "ext-igbinary": "*", "ext-imagick": "*", "ext-intl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mailparse": "*", "ext-mysqli": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-readline": "*", "ext-redis": "*", "ext-simplexml": "*", "ext-soap": "*", "ext-tidy": "*", "ext-xsl": "*", "ext-zend-opcache": "*", "ext-zip": "*", "ext-zlib": "*", "apfelbox/php-file-download": "^2.1", "aws/aws-sdk-php": "^3.288", "cbschuld/browser.php": "^1.9", "conversio/mail": "^0.2.1", "convertapi/convertapi-php": "^1.6", "cweagans/composer-patches": "^1.7", "demv/file": "^1.4.0", "demv/vorgaenge-sdk": "^6.4.1", "demvsystems/aggregation": "^4.14.1", "demvsystems/authtoken-validation": "^1.3", "demvsystems/bedarfsanalyse": "^4.1", "demvsystems/bipro-annotations": "^1.0.0", "demvsystems/bipro-service": "^4.0.2", "demvsystems/bipro-transaction-logging": "^0.2", "demvsystems/bipro-werte": "^1.2", "demvsystems/contract-number-formatter": "^2.0", "demvsystems/curl": "^0", "demvsystems/exec": "^0.2.0", "demvsystems/finanzrechner": "^1.5", "demvsystems/generic-request": "^2.0.0", "demvsystems/jsend": "^1.3", "demvsystems/php-aws": "1.0.1", "demvsystems/pw-api-client": "1.0.1", "demvsystems/sign-upload": "4.1.0", "demvsystems/smtp-autoconfig": "^1.0.0", "demvsystems/tabularaza": "^0.1.6", "demvsystems/tmpdir": "^1.0", "demvsystems/versorgung": "^1.3", "demvsystems/werte": "^2.24.0", "dgame/php-ensurance": "^2.3.0", "dgame/php-expectation": "^0", "digitick/sepa-xml": "^1.1", "dompdf/dompdf": "^2.0.4", "dragonmantank/cron-expression": "^2.3", "eddturtle/direct-upload": "^3.0", "firebase/php-jwt": "^6.4", "giggsey/libphonenumber-for-php": "^8.10", "goetas-webservices/xsd-reader": "^0.3.6", "google/apiclient": "^2.14", "guzzlehttp/psr7": "^2.0", "hashids/hashids": "^4.1", "hfig/mapi": "1.4.0", "http-interop/http-factory-guzzle": "^1.2", "illuminate/database": "^8", "illuminate/http": "^8", "illuminate/support": "^8", "imagine/imagine": "^1.3", "jschaedl/iban": "^1.3", "kartik-v/bootstrap-fileinput": "~4.4.0", "kartik-v/bootstrap-star-rating": "dev-master#36426563d86e4b208c1a05d068d06307a018cac6", "laminas/laminas-escaper": "^2.6", "laminas/laminas-hydrator": "^3 || ^4", "laminas/laminas-servicemanager": "^2 || ^3.10", "laracasts/utilities": "3.2", "league/csv": "^9.8", "league/flysystem": "^1.0", "league/flysystem-aws-s3-v3": "^1.0.25", "league/flysystem-memory": "^1.0", "league/flysystem-ziparchive": "^1.0", "league/oauth2-client": "^2.6", "league/oauth2-google": "^3.0", "leon0399/monolog-flysystem": "^1.2", "lstrojny/functional-php": "^1.9", "maennchen/zipstream-php": "^2.2", "microsoft/microsoft-graph": "^1.110", "mikehaertl/php-pdftk": "^0.13", "monolog/monolog": "^2.5", "mpdf/mpdf": "^8.0", "nesbot/carbon": "^2.72", "netresearch/jsonmapper": "^1.4", "nette/finder": "^2.5", "nicmart/tree": "^0.3.1", "norberttech/symfony-process-executor": "^2.1", "paragonie/halite": "^5.1.0", "php-http/guzzle7-adapter": "^1.0.0", "php-imap/php-imap": "^5", "phpmailer/phpmailer": "dev-master#0064f26786dc99c2fffad0e4615e54c5fa2e99fa as 6.6.5", "phpoffice/phpspreadsheet": "^1.29.6", "phpoffice/phpword": "^0.18", "promphp/prometheus_client_php": "^2.4", "promphp/prometheus_push_gateway_php": "^1.1", "psr/container": "^1", "ramsey/uuid": "^4.7", "sabre/vobject": "^4.1", "sentry/sentry": "^4.4", "setasign/fpdf": "^1.8", "setasign/setapdf-signer": "2.42.0.1871", "skyzyx/mimetypes": "^1.1", "smalot/pdfparser": "^2.9", "soundasleep/html2text": "^0.5", "spatie/data-transfer-object": "^3.9.1", "stefangabos/zebra_datepicker": "^1.9", "swiftmailer/swiftmailer": "^6.3", "symfony/http-foundation": "^5", "symfony/process": "^5", "tecnickcom/tcpdf": "^6.8", "thenetworg/oauth2-azure": "dev-master", "vernes/yiimailer": "^4.0", "vlucas/phpdotenv": "^5.1", "wapmorgan/unified-archive": "1.1.10", "weew/error-handler": "dev-master", "yiisoft/yii": "^1.1", "zbateson/mail-mime-parser": "^1"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "codeception/specify": "^2.0", "dms/phpunit-arraysubset-asserts": "^0.5", "doctrine/instantiator": "^1.4", "ergebnis/composer-normalize": "^2.4", "fakerphp/faker": "^1.13", "filp/whoops": "^2.0", "maximebf/debugbar": "^1.17", "mockery/mockery": "^1.2.2", "php-mock/php-mock-phpunit": "^2.7", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpspec/prophecy": "^1.17.0", "phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^1.11.0", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.5", "phpunit/phpunit": "^9.6", "roave/better-reflection": "^6.4.1", "roave/security-advisories": "dev-latest", "slevomat/coding-standard": "^8.0", "spaceemotion/php-coding-standard": "dev-wip/v1", "squizlabs/php_codesniffer": "^3.5", "symfony/var-dumper": "^6.4", "zircote/swagger-php": "^5.0"}, "repositories": [{"type": "git", "url": "**************:demvsystems/tabularaza.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/mimey.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/bedarfsanalyse.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/aggregation.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/werte.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/versorgung.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/error-handler.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/ivory-google-map.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/php-aws.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/authtoken-validation.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/bipro-soap.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/bipro-werte.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/bipro-service.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/generic-request.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/bipro-annotations.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/mapper.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/bipro-transaction-logging.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/tmpdir.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/sign-upload.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/php-coding-standard.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/coding-standard.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/php-optional.git"}, {"type": "git", "url": "**************:demvsystems/vorgaenge-sdk.git"}, {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer"}, {"type": "git", "url": "**************:alexmpunkt/mail.git"}, {"type": "package", "package": {"name": "stefangabos/zebra_datepicker", "version": "1.9.14", "dist": {"type": "tar", "url": "https://github.com/stefangabos/Zebra_Datepicker/archive/refs/tags/1.9.14.tar.gz", "shasum": "f73dea9d95ef4c442f271450f214da398b369ad9"}}}, {"type": "composer", "url": "https://www.setasign.com/downloads/"}, {"type": "git", "url": "**************:demvsystems/contract-number-formatter.git"}, {"type": "git", "url": "**************:demvsystems/pw-api-client.git"}], "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Abrechnungstool\\": "protected/modules/Abrechnungstool", "Admin\\Cronjob\\": "protected/modules/admin/modules/cronjob", "Admin\\Dateinamenklassifizierung\\": "protected/modules/admin/modules/Dateinamenklassifizierung", "Admin\\Daten\\": "protected/modules/admin/modules/daten", "Admin\\Doubletten\\": "protected/modules/admin/modules/integritaet/modules/Doubletten", "Admin\\Integritaet\\": "protected/modules/admin/modules/integritaet", "Admin\\Kpi\\": "protected/modules/admin/modules/Kpi", "Admin\\Smtp\\": "protected/modules/admin/modules/Smtp", "Admin\\Stammdaten\\": "protected/modules/admin/modules/Stammdaten", "Admin\\Tools\\": "protected/modules/admin/modules/tools", "Admin\\Vertraege\\": "protected/modules/admin/modules/vertraege", "AiGateway\\": "protected/modules/AiGateway", "Anfragen\\": "protected/modules/anfragen", "ApiGateway\\": "protected/modules/ApiGateway", "ApiToken\\": "protected/modules/ApiToken", "Api\\": "protected/modules/api", "Assistant\\": "protected/modules/Assistant", "Auth\\": "protected/modules/Auth", "Backend\\": "protected/modules/backend/", "Backend\\Einarbeitung\\": "protected/modules/backend/modules/einarbeitung", "BeratungAI\\": "protected/modules/BeratungAI", "Beratung\\": "protected/modules/Beratung", "Beratungsvorsprung\\": "protected/modules/Beratungsvorsprung", "Beratungsvorsprung\\Lernnachweis\\": "protected/modules/Beratungsvorsprung/modules/Lernnachweis", "Bestand\\": "protected/modules/bestand", "Bestandsexport\\": "protected/modules/Bestandsexport", "Bestandskorrektur\\": "protected/modules/Bestandskorrektur", "BestandsuebertragungCoc\\": "protected/modules/BestandsuebertragungCoc", "Bestandsuebertragung\\": "protected/modules/bestandsuebertragung", "Bipro\\Admin\\": "protected/modules/bipro/modules/backend", "Bipro\\Admin\\Components\\": "protected/modules/bipro/modules/backend/components", "Bipro\\Admin\\Email\\": "protected/modules/bipro/modules/backend/modules/Email", "Bipro\\Admin\\Import\\": "protected/modules/bipro/modules/backend/modules/Import", "Bipro\\Admin\\Monitoring\\": "protected/modules/bipro/modules/backend/modules/Monitoring", "Bipro\\Admin\\Sftp\\": "protected/modules/bipro/modules/backend/modules/Sftp", "Bipro\\Ai\\": "protected/modules/bipro/modules/Ai", "Bipro\\Backend\\": "protected/modules/bipro/backend", "Bipro\\Backend\\Gdv\\": "protected/modules/bipro/modules/backend/modules/gdv", "Bipro\\Factory\\": "protected/modules/bipro/Factory", "Bipro\\FormModels\\": "protected/modules/bipro/formModels", "Bipro\\Profile\\": "protected/modules/bipro/modules/profile", "Bipro\\Service\\": "protected/modules/bipro/Service", "Bipro\\Tracking\\": "protected/modules/bipro/Tracking", "Bipro\\Vertragsservice\\": "protected/modules/bipro/modules/Vertragsservice", "Bipro\\Vertragsservice\\Admin\\": "protected/modules/bipro/modules/Vertragsservice/modules/backend", "Bipro\\Vertragsservice\\Gdv\\": "protected/modules/bipro/modules/Vertragsservice/modules/gdv", "BlauDirekt\\": "protected/modules/BlauDirekt", "Business\\": "protected/Business", "Components\\": "protected/components", "Config\\": "protected/config", "Courtageerfassung\\": "protected/modules/Courtageerfassung", "Courtageumzug\\": "protected/modules/Courtageumzug", "Csvimport\\": "protected/modules/csvimport", "CustomImporter\\": "protected/modules/CustomImporter/", "CustomImporter\\BaumannPartner\\": "protected/modules/CustomImporter/modules/BaumannPartner", "CustomImporter\\Fondsforum\\": "protected/modules/CustomImporter/modules/fondsforum", "Dashboard\\": "protected/modules/Dashboard", "DigitalerMaklerbetreuer\\": "protected/modules/DigitalerMaklerbetreuer", "DigitalerMaklerbetreuer\\Digitalradar\\": "protected/modules/DigitalerMaklerbetreuer/modules/Digitalradar", "EventSystem\\": "protected/modules/EventSystem", "Events\\": "protected/modules/Events", "Extras\\": "protected/modules/Extras", "Faq\\": "protected/modules/Faq", "FileImport\\": "protected/modules/FileImport", "Finanzreport\\": "protected/modules/kundenakte/modules/finanzreport/Finanzreport", "Fondsfinanz\\": "protected/modules/Fondsfinanz", "Fragebogen\\": "protected/modules/fragebogen", "GdvImport\\": "protected/modules/gdvimport", "GdvImport\\Components\\": "protected/modules/gdvimport/components", "Gesellschaften\\": "protected/modules/Gesellschaften", "InfoPoint\\": "protected/modules/InfoPoint", "Inhaltsseiten\\": "protected/modules/Inhaltsseiten", "Inno\\": "protected/modules/inno", "Intercom\\": "protected/modules/Intercom", "Intercom\\modules\\Upvoty\\": "protected/modules/Intercom/Upvoty/", "Intercom\\modules\\Webhook\\": "protected/modules/Intercom/Webhook/", "Introtour\\": "protected/modules/Introtour", "Konfiguration\\": "protected/modules/Konfiguration", "Kooperation\\": "protected/modules/Kooperation", "Kundenakte\\": "protected/modules/kundenakte", "Kundenakte\\Api\\": "protected/modules/kundenakte/modules/api", "Kundenakte\\Bedarfsanalyse\\": "protected/modules/kundenakte/modules/bedarfsanalyse", "Kundenakte\\Dubletten\\": "protected/modules/kundenakte/modules/Dubletten", "LargeFileUpload\\": "protected/modules/LargeFileUpload", "Login\\": "protected/modules/Login", "Mailer\\": "protected/modules/Mailer", "Maintenance\\": "protected/modules/Maintenance", "MaklerbetreuerStatistics\\": "protected/modules/MaklerbetreuerStatistics", "Marketplace\\": "protected/modules/Marketplace", "Models\\": "protected/models", "NewsletterApi\\": "protected/modules/NewsletterApi", "Oauth\\": "protected/modules/Oauth", "Pdf\\": "protected/modules/pdf", "Postfach\\": "protected/modules/bipro/modules/Postfach", "Procheck\\": "protected/modules/procheck/components", "Procheck\\Models\\": "protected/modules/procheck/models", "Schnittstellen\\": "protected/modules/schnittstellen/backend", "Schnittstellen\\Covomo\\": "protected/modules/schnittstellen/modules/Covomo", "Schnittstellen\\Data\\": "protected/modules/schnittstellen/Data", "Schnittstellen\\Dtos\\": "protected/modules/schnittstellen/Dtos", "Schnittstellen\\Europace\\": "protected/modules/schnittstellen/modules/Europace", "Schnittstellen\\Exceptions\\": "protected/modules/schnittstellen/Exceptions", "Schnittstellen\\FKS\\": "protected/modules/schnittstellen/modules/fks", "Schnittstellen\\Factories\\": "protected/modules/schnittstellen/Factories", "Schnittstellen\\Gewerberechner\\": "protected/modules/schnittstellen/modules/Gewerberechner", "Schnittstellen\\LeistungsserviceZertifikate\\": "protected/modules/schnittstellen/modules/LeistungsserviceZertifikate", "Schnittstellen\\MM\\": "protected/modules/schnittstellen/modules/mm/backend", "Schnittstellen\\MrMoney\\": "protected/modules/schnittstellen/modules/MrMoney", "Schnittstellen\\NAFI\\": "protected/modules/schnittstellen/modules/Nafi", "Schnittstellen\\PSP\\": "protected/modules/schnittstellen/modules/psp", "Schnittstellen\\Services\\": "protected/modules/schnittstellen/Services", "Schnittstellen\\Softfair\\": "protected/modules/schnittstellen/modules/Softfair", "Schnittstellen\\Traits\\": "protected/modules/schnittstellen/Traits", "Share\\": "protected/modules/Share", "Sign\\": "protected/modules/Sign", "Stammdaten\\": "protected/modules/stammdaten", "Stammdaten\\Api\\": "protected/modules/stammdaten/modules/api", "Stammdaten\\Gdvimport\\": "protected/modules/stammdaten/modules/gdvimport", "Stammdaten\\Gesellschaften\\": "protected/modules/stammdaten/modules/gesellschaften", "Stammdaten\\Newsletter\\": "protected/modules/stammdaten/modules/Newsletter", "Stammdaten\\Sparten\\": "protected/modules/stammdaten/modules/sparten", "Startseite\\": "protected/modules/Startseite", "Support\\": "protected/modules/Support", "Tags\\": "protected/modules/Tags", "Tarifierung\\": "protected/modules/Tarifierung", "Veranstaltungen\\": "protected/modules/Veranstaltungen", "Vergleichsrechner\\": "protected/modules/vergleichsrechner", "Vertragsdaten\\": "protected/modules/Vertragsdaten", "Vorgang\\": "protected/modules/Vorgang", "VuNumber\\Backend\\": "protected/modules/stammdaten/modules/vunumber/backend", "Wissenswelt\\": "protected/modules/Wissenswelt"}, "classmap": ["protected/_professionalworks", "protected/traits"], "files": ["protected/modules/schnittstellen/backend/Helper/helper.php", "protected/modules/stammdaten/modules/vunumber/backend/html.php", "protected/extensions/Helpers.php"]}, "autoload-dev": {"psr-4": {"App\\PHPStan\\Rules\\": ".phpstan/Rules", "Tests\\": "tests", "Tests\\Backend\\": "tests/Modules/Backend", "Tests\\Bipro\\Backend\\Gdv\\": "tests/Modules/Bipro/Modules/backend/modules/gdv", "Tests\\GdvImport\\": "tests/Modules/GdvImport", "Tests\\Share\\": "tests/Modules/Share"}}, "config": {"allow-plugins": {"cweagans/composer-patches": true, "dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "php-http/discovery": true}, "platform": {"php": "8.1.33"}, "sort-packages": true}, "extra": {"google/apiclient-services": ["Gmail"], "hooks": {"pre-commit": ["docker-compose exec -T php-fpm vendor/bin/phpcstd --fix --hide-source --lint-staged"]}, "patches": {"jschaedl/iban": {"Fix IBAN generation for DSK Hyp": "iban-generation-patch.diff"}, "phpunit/php-file-iterator": {"Dummy patch to stop composer-patches from reinstalling": "/dev/null"}, "phpunit/phpunit": {"Dummy patch to stop composer-patches from reinstalling": "/dev/null"}, "smalot/pdfparser": {"Fix missing AbstractEncoding in PDFDocEncoding": "smalot-pdfparser.diff"}}, "patches-ignore": {"yiisoft/yii": {"phpunit/php-file-iterator": {"Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_path_file_iterator.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch", "Fix PHP 8.3 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php83.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}}}, "scripts": {"post-install-cmd": "mkdir -p public/assets protected/runtime", "post-update-cmd": ["if [ -f cghooks.lock ]; then cghooks update; fi"], "pre-autoload-dump": ["Google\\Task\\Composer::cleanup"], "analyze": "phpcstd", "assets": ["yarn && yarn production", "@assets2"], "assets2": ["yarn --cwd assets/v2 && yarn --cwd assets/v2 build"], "auth-ssh-gen": "./generate-auth-keys.sh", "ci-test": ["phpunit --testsuite pw"], "ci-test-fail-fast": ["phpunit --stop-on-failure --testsuite pw"], "clear-cache-assets": ["rm -rf public/assets/*", "rm -rf protected/runtime/*"], "coverage": ["phpunit --testsuite pw --coverage-clover=coverage.xml"], "coverage-html": ["phpunit --testsuite pw --coverage-html=coverage"], "fast-test": ["phpunit --testsuite fast"], "hooks:install": "cghooks add", "migrate": ["Composer\\Config::disableProcessTimeout", "php console.php migrate --interactive=0 "], "safe-install": ["@composer install", "@migrate", "@auth-ssh-gen", "@assets", "@test", "@clear-cache-assets"], "swagger": ["php protected/generateSwagger.php"], "test": ["Composer\\Config::disableProcessTimeout", "phpunit --testsuite pw"]}}