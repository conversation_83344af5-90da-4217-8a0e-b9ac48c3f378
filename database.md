# Professional Works Schemata

Dieses Dokument soll als zentrale Dokumentation für die Schemata in der PW
Datenbank dienen und Tabellen und deren Inhalte und Beziehungen untereinander
erläutern. Ebenso sollten hier Informationen zu eventuell vorhandenen anderen
Datenbankentitäten wie Prozeduren oder Triggern zu finden sein.

## bipro

TBD

## common_data

Schema für gemeinsam genutzte Daten

TBD

## demv_crm_release1

Das primäre Schema für Professional works.

TBD

## demv_crm_release1 - KPI Tabellen

Diverse Views und Tabellen in denen gefilterte und/oder aufbereitete Daten für
statistische Zwecke liegen.

### kpi_advisers

In dieser View finden sich Informationen zu den Hauptbetreuern die einem Makler
zugewiesen sind.

Jeder Makler kann mehrere Hauptbetreuer haben, umgekehrt natürlich genauso.

#### Spaltenübersicht

  | Spalte       | Beschreibung                    |
  | ---          | ---                             |
  | adviser_id   | User-ID des Maklerbetreuers     |
  | adviser_name | Voller Name des Maklerbetreuers |
  | user_id      | User-ID des Maklers             |
  | agency_id    | ID der Firma des Users          |

### kpi_user_analytics

In dieser Tabelle werden täglich für jede Firma Statistiken über die aktuellen
Bestandsdaten der zugehörigen Makler erfasst.

#### Einschränkungen und Besonderheiten

 * Es werden nur _aktive_ Hauptverträge erfasst.
   * `status = 1`, `damage = 0`, `parentId IS NULL`, `duplicate_contract_id IS NULL`
 * Es werden nur Daten zu nicht gelöschten Kunden erfasst.
   * `deleted = 0`, `duplicate_parent_client_id IS NULL`
 * Es werden nur Firmen erfasst, die mindestens einen Hauptvermittler haben.
 * Es werden Verträge _aller_ Vermittler einer Firma erfasst (inkl. Gelöschten,
   Testnutzern, etc.)
 * Das Erfassungsdatum gibt an wann die Erfassung erfolgte, die Daten spiegeln
   dann jeweils den Stand zum Ende des vorherigen Tages wider
 * Historische Daten (ca. alles vor dem 1.3.2024) sind nur bedingt akkurat, da in
   den Quelldaten teilweise keine Datumsinformationen vorliegen.

#### Spaltenübersicht

  | Spalte                          | Beschreibung                                                                                           |
  | ---                             | ---                                                                                                    |
  | capture_date                    | Das Datum an dem der Zeile erfasst wurde.                                                              |
  | agency_id                       | Die ID der Firma in der Tabelle `agency`                                                               |
  | count_contracts_gesamt          | Gesamtzahl der Veträge                                                                                 |
  | count_contracts_dv              | Anzahl der Verträge über eine Direktvereinbarung (`pool = 0`)                                          |
  | count_contracts_ff              | Anzahl der Verträge über die Fondsfinanz (`insurance_company_id = 701`)                                |
  | count_contracts_pool            | Anzahl der Veträge über einen Pool (`pool = 1`)                                                        |
  | count_contracts_unverkettet     | Anzahl der Verträge über eine unverkettete VNR                                                         |
  | count_application               | Anzahl der Anträge aus dem Vergleichrechner                                                            |
  | count_brokerid_unverkettet      | Anzahl der unverketteten Antragsnummern (`is_verkettet = 0`, `type_id = 1`, `inactive = 0`)            |
  | count_brokerid_hv_antrag        | Anzahl der verketteten Antragsnummern von HV (`is_verkettet = 1`, `type_id = 1`, `user_role = 5`)      |
  | count_brokerid_hv_bestand       | Anzahl der verketteten Bestandsnummern von HV (`is_verkettet = 1`, `type_id = 2`, `user_role = 5`)     |
  | count_brokerid_sonstige_antrag  | Anzahl der sonstigen verketteten Antragsnummern (`is_verkettet = 1`, `type_id = 1`, `user_role <> 5`)  |
  | count_brokerid_sonstige_bestand | Anzahl der sonstigen verketteten Bestandsnummern (`is_verkettet = 1`, `type_id = 2`, `user_role <> 5`) |
  | count_brokerid_mak_hv           | Anzahl der MAK von HV (`brokerid LIKE 'MAK%'`, `user_role = 5`)                                        |
  | count_brokerid_mak_sonstige     | Anzahl der sonstigen MAK (`brokerid LIKE 'MAK%'`, `user_role <> 5`)                                    |
  | count_client                    | Anzahl der Kunden                                                                                      |
  | count_client_without_contract   | Anzahl der Kunden ohne Vertrag                                                                         |
  | count_courtageumzug             | Anzahl der erstellten Courtageumzüge                                                                   |
  | count_courtageanfragen          | Anzahl der erstellten Courtageanfragen                                                                 |

to be continued ...

## log_db

Schema für diverse Log-Tabellen.

TBD

## modules

TBD
