<?php

namespace Deployer;

require_once 'recipe/common.php';
require_once 'recipe/slack.php';
require_once __DIR__ . '/deploy/recipe.php';

use Symfony\Component\Console\Input\InputOption;

option('debugging', 'd', InputOption::VALUE_OPTIONAL, 'Forces a testing-Server (mail debug & yii debug = true)', false);
set('default_timeout', 600);
set('branch', function () {
    $hostname = get('hostname');
    $stage    = get('stage');
    if ($stage === 'live') {
        //Auf dem live-pw Server wollen wir nichts erlauben, außer den master zu deployen
        return 'master';
    }
    //Ab hier beginnt der Original-Code von update_code.php
    try {
        $branch = runLocally('git rev-parse --abbrev-ref HEAD');
    } catch (\Throwable $exception) {
        $branch = null;
    }

    if ($branch === 'HEAD') {
        $branch = null; // Travis-CI fix
    }

    if (input()->hasOption('branch') && !empty(input()->getOption('branch'))) {
        $branch = input()->getOption('branch');
    }

    return $branch;
});

set('short_rev', function () {
    $repository = get('repository');
    try {
        if (input()->hasOption('revision') && input()->getOption('revision') !== null) {
            $revision = input()->getOption('revision');
        } elseif (($branch = get('branch')) !== null) {
            $revision = runLocally("git ls-remote --heads {$repository} {$branch} | cut -c1-40");
        } elseif (input()->hasOption('tag') && input()->getOption('tag') !== '') {
            $tag      = input()->getOption('tag');
            $revision = runLocally("git ls-remote --tags {$repository} {$tag} | cut -c1-40");
        }
    } catch (\Throwable $exception) {
        return null;
    }

    try {
        $shortRev = runLocally("git rev-parse --short {$revision}");
    } catch (\Throwable $exception) {
        $shortRev = $revision;
    }

    return $shortRev;
});

set('keep_releases', function () {
    return get('stage') === 'live' ? 5 : 2;
});

// Various developers had problems with multiplexing enabled, causing the
// server->client communication to fail.
set('ssh_multiplexing', false);

set('username', function () {
    try {
        return runLocally('git config --get user.name');
    } catch (\Throwable $exception) {
        return 'Developer';
    }
});

set('slack_webhook', '*****************************************************************************');
set('slack_success_text', ':white_check_mark: _{{username}}_ deployed `{{branch}}` at `{{short_rev}}` to *{{target}}* successfully');
set('slack_text', ':rocket: _{{username}}_ deploying `{{branch}}` at `{{short_rev}}` to *{{target}}*');
// Read .env file to provide credentials
set('dotenv', '.env');

after('success', 'slack:notify:success');
before('deploy:info', 'slack:notify');

task('deploy', [
    'deploy:prepare',
    'deploy:lock-with-msg',
    'deploy:info',
    'deploy:release',
    'deploy:update_code',
    'deploy:shared',
    'deploy:writable',
    'deploy:vendors',
    'deploy:set_debug_level',
    'deploy:params',
    'deploy:copy_local_files',
    'deploy:copy_auth_file',
    'deploy:frontend',
    'deploy:migrate',
    'deploy:log',
    'deploy:clear_paths',
    'deploy:symlink',
    'deploy:unlock',
    'success',
    'apache:graceful',
    'cleanup',
]);
after('deploy:failed', 'deploy:unlock');
inventory('hosts.yml');
