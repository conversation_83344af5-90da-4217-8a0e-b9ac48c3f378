networks:
  traefik:
    external: true
    name: local-docker-network_default

volumes:
  # This volume is called ...10-3 for historical reasons, we're using a different version of MariaDB
  mariadb_10-3:
  mc:
  prometheus_data:
  s3data:
  tmp:
  yarn_cache:

services:
  # professionalworks:
  #   image: ghcr.io/demvsystems/professionalworks-dev:latest@sha256:b892cb70b54c2b4649db96454ca8a59880035cda95cab8cb276ec3f4c2522ddd
  #   restart: unless-stopped
  #   # volumes:
  #   #   - "${PWD}/public:/var/www/professionalworks/public"
  #   #   - "${PWD}/protected:/var/www/professionalworks/protected"
  #   #   - "${PWD}/vendor:/var/www/professionalworks/vendor"
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.professionalworks.rule=Host(`professionalworks.${LOCAL_DOMAIN}`)"
  #     - "traefik.http.routers.professionalworks.entrypoints=web,websecure"
  #     - "traefik.docker.network=local-docker-network_default"
  #   environment:
  #     MYSQL_HOST: mariadb
  #     MYSQL_PASSWORD: demv
  #   env_file: .env
  #   networks:
  #     - default
  #     - traefik
  #   depends_on:
  #     - mariadb

  nginx:
    build: ./docker/nginx
    restart: unless-stopped
    depends_on:
      - php-fpm
    volumes:
      - ./public:/var/www/html/public:delegated
      - ./vendor:/var/www/html/vendor:delegated
      - ./docker/nginx/site.conf:/etc/nginx/conf.d/default.conf:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.professionalworks-nginx.rule=Host(`professionalworks.${LOCAL_DOMAIN}`)"
      - "traefik.http.routers.professionalworks-nginx.entrypoints=web,websecure"
      - "traefik.docker.network=local-docker-network_default"
    networks:
      - default
      - traefik
    ports:
      - "8081:80"

  php-fpm:
    container_name: professionalworks
    restart: unless-stopped
    build:
      context: .
      dockerfile: ./docker/php-fpm/Dockerfile
      args:
        GITHUB_TOKEN: $GITHUB_TOKEN
        GROUP_ID: $GROUP_ID
        USER_ID: $USER_ID
        WITH_BLACKFIRE: $WITH_BLACKFIRE
        XDEBUG_PORT: $XDEBUG_PORT
        SETASIGN_SIGNER_USER: $SETASIGN_SIGNER_USER
        SETASIGN_SIGNER_PASS: '$SETASIGN_SIGNER_PASS'
    environment:
      BLACKFIRE_AGENT_SOCKET: tcp://blackfire:8307
    env_file: .env
    volumes:
      - tmp:/tmp
      - .:/var/www/html:cached
      - ./docker/php-fpm/openssl.cnf:/usr/lib/ssl/openssl.cnf
      - ./docker/php-fpm/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./docker/php-fpm/spx.ini:/usr/local/etc/php/conf.d/spx.ini
      - ./docker/php-fpm/php_xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - mariadb
    networks:
      - default
      - traefik
    ports:
      # Support phpstan pro web interface
      - "127.0.0.1:11111:11111"
    extra_hosts:
      - host.docker.internal:host-gateway

  blackfire:
    container_name: blackfire
    #restart: unless-stopped
    image: blackfire/blackfire:2
    environment:
      BLACKFIRE_SERVER_ID: $BLACKFIRE_SERVER_ID
      BLACKFIRE_SERVER_TOKEN: $BLACKFIRE_SERVER_TOKEN
    env_file: .env
    networks:
      - default
      - traefik
    profiles:
      - monitoring

  node10:
    build:
      context: ./docker/node10
      args:
        GROUP_ID: $GROUP_ID
        USER_ID: $USER_ID
        NPM_FRONTEND_UTILS_AUTH_TOKEN: $NPM_FRONTEND_UTILS_AUTH_TOKEN
        NPM_FONTAWESOME_AUTH_TOKEN: $NPM_FONTAWESOME_AUTH_TOKEN
        NPM_TIPTAP_PRO_AUTH_TOKEN: $NPM_TIPTAP_PRO_AUTH_TOKEN
    env_file: .env
    command: sh -c "yarn install --frozen-lockfile"
    working_dir: /usr/src/app
    volumes:
      - yarn_cache:/home/<USER>/.cache/yarn
      - ./:/usr/src/app:delegated
      - ./docker/node/.yarnrc:/.yarnrc
    profiles:
      - dontstart

  node-18:
    build:
      context: ./docker/node-18
      args:
        GROUP_ID: $GROUP_ID
        USER_ID: $USER_ID
        NPM_FRONTEND_UTILS_AUTH_TOKEN: $NPM_FRONTEND_UTILS_AUTH_TOKEN
        NPM_FONTAWESOME_AUTH_TOKEN: $NPM_FONTAWESOME_AUTH_TOKEN
        NPM_TIPTAP_PRO_AUTH_TOKEN: $NPM_TIPTAP_PRO_AUTH_TOKEN
    env_file: .env
    volumes:
      - yarn_cache:/home/<USER>/.cache/yarn
      - ./docker/node/.yarnrc:/.yarnrc
      - ./pnpm-store:/.pnpm-store:cached
      - ./:/usr/src/app:delegated
    profiles:
      - dontstart

  mariadb:
    image: mariadb:10.11.11
    container_name: pw-mariadb
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD
      - MYSQL_USER=$MYSQL_USER
      - MYSQL_DATABASE=$MYSQL_DATABASE
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      start_period: 10s
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      - mariadb_10-3:/var/lib/mysql
      - ./docker/mariadb/config:/etc/mysql/conf.d
    ports:
      - "33100:3306"
    networks:
      - traefik

  # Local s3 instance using minio
  s3:
    image: minio/minio:RELEASE.2021-03-26T00-00-41Z
    restart: unless-stopped
    volumes:
      - s3data:/data
    environment:
      MINIO_ACCESS_KEY: $PW_STORAGE_S3_KEY
      MINIO_SECRET_KEY: $PW_STORAGE_S3_SECRET
    command: server /data
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.s3.rule=Host(`s3.${LOCAL_DOMAIN}`)"
      - "traefik.http.routers.s3.entrypoints=web,websecure"
      - "traefik.http.services.s3.loadbalancer.server.port=9000"
      - "traefik.docker.network=local-docker-network_default"
    networks:
      - default
      - traefik

  mc:
    image: minio/mc:RELEASE.2021-03-23T05-46-11Z
    depends_on:
      - s3
    networks:
      - default
      - traefik
    profiles:
      - dontstart
    volumes:
      - mc:/root/.mc

  redis:
    image: redis
    restart: unless-stopped
    ports:
      - '6379:6379'

  # metrics
  prometheus:
    image: prom/prometheus:v2.34.0
    container_name: prometheus
    volumes:
      - ./docker/monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - '9090:9090'
    networks:
      - default
      - traefik
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:8.4.4
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.${LOCAL_DOMAIN}`)"
      - "traefik.http.routers.grafana.entrypoints=web,websecure"
      - "traefik.docker.network=local-docker-network_default"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"
    volumes:
      - ./docker/monitoring/grafana/grafana.ini:/etc/grafana/grafana.ini
      - ./docker/monitoring/grafana/datasource.yml:/etc/grafana/provisioning/datasources/datasource.yml
    networks:
      - default
      - traefik
    profiles:
      - monitoring

  node-exporter:
    image: prom/node-exporter:v1.3.1
    container_name: monitoring_node_exporter
    pid: host
    command:
      - '--path.rootfs=/host'
    volumes:
      - '/:/host:ro,rslave'
    ports:
      - '9100:9100'
    expose:
      - 9100
    profiles:
      - monitoring

  push-gateway:
    image: prom/pushgateway:v1.4.2
    ports:
      - '9091:9091'
    expose:
      - 9091
    networks:
      - default
      - traefik
    profiles:
      - monitoring

  mysqld-exporter:
    image: prom/mysqld-exporter:v0.14.0
    environment:
      DATA_SOURCE_NAME: "root:demv@(mariadb:3306)/"
    ports:
      - '9104:9104'
    networks:
      - default
      - traefik
    profiles:
      - monitoring

  deployer:
    build:
      context: ./docker/deploy
      args:
        GROUP_ID: $GROUP_ID
        USER_ID: $USER_ID
    user: $USER_ID
    env_file: .env
    environment:
      - SSH_AUTH_SOCK=/ssh-agent
    volumes:
      - ./:/project
      - $HOME/.ssh:/home/<USER>/.ssh
      - ${SSH_AUTH_SOCK}:/ssh-agent
    profiles:
      - dontstart
