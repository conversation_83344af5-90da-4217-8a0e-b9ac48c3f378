touch .env.auth
if grep -r -e "AUTH_PRIVATE_KEY" -e "AUTH_PUBLIC_KEY" .env.auth
then
  echo ""
  echo "Keys existieren bereits in der .env.auth."
  echo "Die Einträge für AUTH_PRIVATE_KEY und AUTH_PUBLIC_KEY müssen entfernt werden"
  exit
fi

openssl genrsa -out auth_pw.pem 4096 && openssl rsa -in auth_pw.pem -pubout > auth_pw.pub

echo "AUTH_PRIVATE_KEY=\"$(cat auth_pw.pem)\"" >> .env.auth
rm auth_pw.pem

echo "AUTH_PUBLIC_KEY=\"$(cat auth_pw.pub)\"" >> .env.auth
rm auth_pw.pub
