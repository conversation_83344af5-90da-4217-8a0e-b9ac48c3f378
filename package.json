{"name": "professionalworks", "version": "1.0.0", "author": "DEMV Systems", "license": "Proprietary", "private": true, "scripts": {"dev": "cross-env webpack", "fix": "cross-env webpack --env.fix", "watch": "cross-env webpack --watch", "hot": "cross-env webpack-dev-server --inline --hot", "production": "cross-env webpack -p", "analyse": "cross-env webpack -p --profile --json > stats.json", "start": "run-s test production", "clear-start": "composer clear-cache-assets && npm run dev", "test": "cross-env NODE_ENV=test mocha-webpack --opts assets/mix/mocha.opts assets/**/*.spec.js", "watch-test": "cross-env NODE_ENV=test mocha-webpack --opts assets/mix/mocha.opts --watch assets/**/*.spec.js"}, "devDependencies": {"@vue/test-utils": "^1.0.0-beta.16", "babel-plugin-dynamic-import-webpack": "^1.0.2", "babel-plugin-lodash": "^3.3.2", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-preset-airbnb": "^2.4.0", "browser-sync": "^2.24.4", "browser-sync-webpack-plugin": "^2.2.2", "clean-webpack-plugin": "^0.1.19", "cross-env": "^5.1.6", "expect": "^25.2.7", "jsdom": "^11.10.0", "jsdom-global": "^3.0.2", "laravel-mix": "2.1.14", "lodash-webpack-plugin": "^0.11.5", "mocha": "^5.2.0", "mocha-webpack": "^1.1.0", "node-file-exists": "^1.1.0", "npm-run-all": "^4.1.3", "postcss-flexbugs-fixes": "^3.3.1"}, "dependencies": {"@demvsystems/fuzzy-date": "^2.0.0", "@sentry/browser": "5.21.1", "@sentry/integrations": "^5.21.1", "autosize": "^4.0.2", "awesome-phonenumber": "^2.36.0", "axios": "^0.18.0", "bootstrap": "^4.1.1", "bootstrap-tagsinput": "^0.7.1", "bootstrap-vue": "^2.0.0-rc.11", "date-fns": "2.0.0-alpha.7", "dateformat": "^3.0.3", "draggabilly": "^2.2.0", "dropzone": "5.4.0", "es6-promise": "^4.2.4", "fast-check": "^2.2.1", "flatpickr": "^4.5.6", "fuse.js": "^3.2.0", "googleapis": "^46.0.0", "hopscotch": "^0.3.1", "jquery": "^3.3.1", "lodash": "^4.17.10", "pdfjs-dist": "2.5.207", "popper.js": "^1.14.3", "scroll-to-element": "^2.0.2", "stickyfilljs": "^2.1.0", "sweetalert2": "^7.20.7", "uuid": "^8.3.0", "vue": "^2.5.16", "vue-content-placeholders": "^0.2.1", "vue-mugen-scroll": "^0.2.5", "vue-multiselect": "^2.1.0", "vue-router": "^3.1.6", "vue-select": "^3.1.0", "vue2-dropzone": "^3.6.0", "vue2-editor": "^2.10.2", "vuelidate": "^0.7.4", "vuex": "^3.0.1"}, "resolutions": {"minimist": "^1.2.6"}, "babel": {"plugins": ["lodash", "syntax-dynamic-import"], "presets": [["airbnb", {"targets": {"browsers": ["last 2 versions", "> 5% in DE", "edge >= 18", "iOS >= 10"]}}]]}}