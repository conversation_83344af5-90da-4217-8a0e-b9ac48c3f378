<?xml version="1.0"?>
<ruleset name="PSR12">
    <arg name="encoding" value="utf-8"/>

    <!-- Add extensions -->
    <config name="installed_paths" value="vendor/slevomat/coding-standard"/>

    <!-- Ruleset base -->
    <rule ref="PSR12">
        <!-- Allow method names like "it_validates_a_request" in tests -->
        <exclude name="PSR1.Methods.CamelCapsMethodName">
            <exclude-pattern>tests/*</exclude-pattern>
        </exclude>

        <!-- The following rules are overwritten by <PERSON>levomat as it can also fix them -->
        <exclude name="PSR12.Properties.ConstantVisibility"/>
    </rule>

    <!-- Some custom formatting preferences -->
    <rule ref="Generic.Arrays.ArrayIndent"/>
    <rule ref="Generic.WhiteSpace.ArbitraryParenthesesSpacing"/>

    <rule ref="Generic.Strings.UnnecessaryStringConcat">
        <properties>
            <property name="allowMultiline" value="true"/>
        </properties>
    </rule>

    <rule ref="Squiz.WhiteSpace.OperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
            <property name="ignoreSpacingBeforeAssignments" value="false"/>
        </properties>
    </rule>

    <rule ref="Squiz.WhiteSpace.ObjectOperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>

    <rule ref="PEAR.WhiteSpace.ObjectOperatorIndent"/>
    <rule ref="PEAR.WhiteSpace.ScopeClosingBrace"/>

    <rule ref="SlevomatCodingStandard.Arrays.MultiLineArrayEndBracketPlacement"/>
    <rule ref="SlevomatCodingStandard.Arrays.SingleLineArrayWhitespace"/>
    <rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>
    <rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility"/>
    <rule ref="SlevomatCodingStandard.Classes.ClassMemberSpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.DisallowMultiConstantDefinition"/>
    <rule ref="SlevomatCodingStandard.Classes.DisallowMultiPropertyDefinition"/>
    <rule ref="SlevomatCodingStandard.Classes.MethodSpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference"/>
    <rule ref="SlevomatCodingStandard.Classes.ParentCallSpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.PropertySpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.TraitUseDeclaration"/>
    <rule ref="SlevomatCodingStandard.Commenting.EmptyComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.InlineDocCommentDeclaration"/>
    <rule ref="SlevomatCodingStandard.Commenting.RequireOneLinePropertyDocComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.UselessInheritDocComment"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowYodaComparison"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.JumpStatementsSpacing"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.LanguageConstructWithParentheses"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireShortTernaryOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireTernaryOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessIfConditionWithReturn"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessTernaryOperator"/>
    <rule ref="SlevomatCodingStandard.Exceptions.DeadCatch"/>
    <rule ref="SlevomatCodingStandard.Functions.StaticClosure"/>
    <rule ref="SlevomatCodingStandard.Functions.StrictCall"/>
    <rule ref="SlevomatCodingStandard.Functions.UnusedInheritedVariablePassedToClosure"/>
    <rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses"/>
    <rule ref="SlevomatCodingStandard.Namespaces.DisallowGroupUse"/>
    <rule ref="SlevomatCodingStandard.Namespaces.MultipleUsesPerLine"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceDeclaration"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceSpacing"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseDoesNotStartWithBackslash"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseFromSameNamespace"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UselessAlias"/>
    <rule ref="SlevomatCodingStandard.Operators.NegationOperatorSpacing"/>
    <rule ref="SlevomatCodingStandard.Operators.SpreadOperatorSpacing"/>
    <rule ref="SlevomatCodingStandard.PHP.DisallowDirectMagicInvokeCall"/>
    <rule ref="SlevomatCodingStandard.PHP.ReferenceSpacing"/>
    <rule ref="SlevomatCodingStandard.PHP.ShortList"/>
    <rule ref="SlevomatCodingStandard.PHP.TypeCast"/>
    <rule ref="SlevomatCodingStandard.PHP.UselessParentheses"/>
    <rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>
    <rule ref="SlevomatCodingStandard.TypeHints.LongTypeHints"/>
    <rule ref="SlevomatCodingStandard.TypeHints.NullTypeHintOnLastPosition"/>
    <rule ref="SlevomatCodingStandard.TypeHints.UselessConstantTypeHint"/>
    <rule ref="SlevomatCodingStandard.Variables.UselessVariable"/>

    <rule ref="SlevomatCodingStandard.Commenting.DocCommentSpacing">
        <properties>
            <property name="linesCountBetweenDifferentAnnotationsTypes" value="1"/>
            <property name="annotationsGroups" type="array">
                <element value="@ORM\"/>
                <element value="@var"/>
                <element value="@param"/>
                <element value="@return"/>
                <element value="@throws"/>
                <element value="@see"/>
            </property>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.ClassStructure">
        <properties>
            <property name="groups" type="array">
                <element value="uses"/>

                <element value="enum cases"/>

                <element value="public constants"/>
                <element value="protected constants"/>
                <element value="private constants"/>

                <element value="public static properties, protected static properties, private static properties"/>
                <element value="public properties, protected properties, private properties"/>

                <element value="constructor"/>
                <element value="static constructors"/>
                <element value="destructor"/>

                <element value="methods"/>
                <element value="magic methods"/>
            </property>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.EmptyLinesAroundClassBraces">
        <properties>
            <property name="linesCountAfterOpeningBrace" value="0"/>
            <property name="linesCountBeforeClosingBrace" value="0"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.RequireMultiLineMethodSignature">
        <properties>
            <property name="minLineLength" value="120"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.RequireSingleLineMethodSignature">
        <properties>
            <property name="maxLineLength" value="119"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.TraitUseSpacing">
        <properties>
            <property name="linesCountBeforeFirstUse" value="0"/>
            <property name="linesCountAfterLastUseWhenLastInClass" value="0"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenAnnotations">
        <properties>
            <property name="forbiddenAnnotations" type="array">
                <element value="@author"/>
                <element value="@copyright"/>
                <element value="@created"/>
                <element value="@license"/>
                <element value="@package"/>
                <element value="@version"/>
                <element value="@since"/>
            </property>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenComments">
        <properties>
            <property name="forbiddenCommentPatterns" type="array">
                <element value="/^\s*Created by(.*)/"/>
                <element value="/^\s*Date: (.*)/"/>
                <element value="/^\s*Time: (.*)/"/>
                <element value="/^\s*User: (.*)/"/>

                <element value="/(.*)constructor.\s*$/"/>
                <element value="/^\s*Constructor(.*)/"/>

                <element value="/^\s*Class [^\s]+\s*$/"/>
                <element value="/^\s*Interface [^\s]+\s*$/"/>
                <element value="/^\s*Trait [^\s]+\s*$/"/>
            </property>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.ControlStructures.EarlyExit">
        <properties>
            <property name="ignoreStandaloneIfInScope" value="true"/>
            <property name="ignoreOneLineTrailingIf" value="true"/>
            <property name="ignoreTrailingIfWithOneInstruction" value="true"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing">
        <!-- Remove case & default from the list -->
        <properties>
            <property name="tokensToCheck" type="array">
                <element value="T_IF"/>
                <element value="T_DO"/>
                <element value="T_WHILE"/>
                <element value="T_FOR"/>
                <element value="T_FOREACH"/>
                <element value="T_SWITCH"/>
                <element value="T_TRY"/>
            </property>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.ControlStructures.RequireMultiLineTernaryOperator">
        <properties>
            <property name="lineLengthLimit" value="100"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.ControlStructures.RequireSingleLineCondition">
        <properties>
            <property name="maxLineLength" value="120" />
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.ControlStructures.RequireMultiLineCondition">
        <properties>
            <property name="minLineLength" value="121" />
            <property name="booleanOperatorOnPreviousLine" value="false" />
            <property name="alwaysSplitAllConditionParts" value="true" />
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Namespaces.UnusedUses">
        <properties>
            <property name="searchAnnotations" value="true"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Namespaces.UseSpacing">
        <properties>
            <property name="linesCountBetweenUseTypes" value="1"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Whitespaces.DuplicateSpaces">
        <properties>
            <property name="ignoreSpacesBeforeAssignment" value="false"/>
            <property name="ignoreSpacesInAnnotation" value="false"/>
            <property name="ignoreSpacesInComment" value="false"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.TypeHints.DeclareStrictTypes">
        <properties>
            <property name="linesCountBeforeDeclare" value="1"/>
            <property name="linesCountAfterDeclare" value="1"/>
            <property name="spacesCountAroundEqualsSign" value="0"/>
        </properties>
    </rule>
</ruleset>
