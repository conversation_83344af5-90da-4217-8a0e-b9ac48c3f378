includes:
  - vendor/phpstan/phpstan-deprecation-rules/rules.neon
  - vendor/phpstan/phpstan-strict-rules/rules.neon
  - vendor/phpstan/phpstan-phpunit/extension.neon
  - vendor/phpstan/phpstan-phpunit/rules.neon

rules:
  - App\PHPStan\Rules\EnsureParentCallsForEventMethodsRule

parameters:
  level: 8
  tipsOfTheDay: false
  phpVersion: 80126
  treatPhpDocTypesAsCertain: false

  paths:
    - .phpstan/Rules
    - protected/_professionalworks/application/ProfessionalWorksWebApplication.php
    - protected/_professionalworks/components/WebModule.php
    - protected/Business/Bipro
    - protected/Business/Broker
    - protected/Business/Client/Aggregation/States/ClientToKunde/BerufsdatenState.php
    - protected/Business/Client/Aggregation/States/KundeToClient/BerufsdatenState.php
    - protected/Business/Contract/Deletion/
    - protected/Business/Courtageanfragen/Mails/AnfrageAbgebrochenMail.php
    - protected/Business/Courtageanfragen/Mails/ErinnerungAnGesellschaftMail.php
    - protected/Business/Date
    - protected/Business/Dateinamenklassifizierung/
    - protected/Business/File
    - protected/Business/User/UserProfileDocument/Converter/
    - protected/commands/RetryImportFromBiproVertragsserviceCommand.php
    - protected/commands/BeratungCommand.php
    - protected/commands/CalculateContentHashOfClientFilesCommand.php
    - protected/commands/ClearCacheCommand.php
    - protected/commands/ContractDuplicateCommand.php
    - protected/commands/MigrateClientAddressToSeparateTableCommand.php
    - protected/commands/MysqlToSqliteCommand.php
    - protected/commands/UpdateBrokerIdsNormalizedCommand.php
    - protected/commands/UpdateUntouchedContractsStateCommand.php
    - protected/components/Access/
    - protected/components/Authentication/ApplicationAuthToken.php
    - protected/components/Authentication/AuthTokenRights.php
    - protected/components/Authentication/KeyPair.php
    - protected/components/Authentication/OAuth/
    - protected/components/Bipro/Enums/DeckungType.php
    - protected/components/Bipro/Models/Deckung.php
    - protected/components/Cache/AppCache.php
    - protected/components/common/Slugifier.php
    - protected/components/Debug/
    - protected/components/Duplicates/
    - protected/components/Errorhandling/Handler/SupressedErrorHandler.php
    - protected/components/File/PDF/
    - protected/components/Filesystem/
    - protected/components/Guzzle.php
    - protected/components/helper/DBHelper.php
    - protected/components/Mailer/
    - protected/components/Intercom
    - protected/components/OAuth/
    - protected/controllers/AntragController.php
    - protected/extensions/image/CImageComponent.php
    - protected/models/gesellschaft/BrokerIdPatternNormalized.php
    - protected/models/gesellschaft/BrokerIdPatternNormalizedCache.php
    - protected/models/interfaces/
    - protected/models/kunde/Kundenakte.php
    - protected/models/Sso/
    - protected/models/user/BrokerIdNormalized.php
    - protected/models/user/BrokerIdService.php
    - protected/models/user/DemoClientCreator.php
    - protected/models/vertrag/ContractDropdownData.php
    - protected/models/vertrag/ContractLabelFormatter.php
    - protected/models/vertrag/ContractsRiskAddress.php
    - protected/models/vertrag/DamageLabelFormatter.php
    - protected/modules/api/Controllers/DefaultController.php
    - protected/modules/AiGateway
    - protected/modules/ApiGateway
    - protected/modules/Beratungsvorsprung/modules
    - protected/modules/bestand/Components/BestandsStatistik.php
    - protected/modules/bipro/backend/Import/Bestand/Calculators/
    - protected/modules/bipro/commands/CleanUpBiproUnknownBrokerIdsCommand.php
    - protected/modules/bipro/commands/HfkImportCommand.php
    - protected/modules/bipro/modules/backend/FormModels/MonitoringDatenlieferungForm.php
    - protected/modules/bipro/modules/profile/Components/Vereinbarung/BaslerVereinbarung.php
    - protected/modules/bipro/modules/profile/Decorator/
    - protected/modules/bipro/modules/Vertragsservice/Backend/Inserter/VertragsTypeInserter.php
    - protected/modules/bipro/modules/Vertragsservice/Backend/Service/Translator/SpecificProductIdTranslator.php
    - protected/modules/bipro/modules/Vertragsservice/Backend/Service/Translator/SpecificProductIdTranslatorResult.php
    - protected/modules/bipro/modules/Vertragsservice/Backend/Sqs/SqsProcess.php
    - protected/modules/bipro/modules/Vertragsservice/Backend/Sync/Vertrag/Processor/FindContractByAntragsMerkmalProcessor.php
    - protected/modules/bipro/modules/Vertragsservice/Backend/Sync/Vertrag/Processor/Stages/CriteriaBuilderForApplicationSearch.php
    - protected/modules/Dashboard/
    - protected/modules/DigitalerMaklerbetreuer
    - protected/modules/Events
    - protected/modules/Fondsfinanz/commands/ImportFondsfinanzRDsCommand.php
    - protected/modules/Fondsfinanz/Gateway/
    - protected/modules/Fondsfinanz/Model/
    - protected/modules/geburtstagsmails/classes/ClientsMailingJob.php
    - protected/modules/kundenakte/controllers/ContractDuplicateController.php
    - protected/modules/kundenakte/controllers/DamageDuplicateController.php
    - protected/modules/kundenakte/controllers/SearchController.php
    - protected/modules/kundenakte/Export/
    - protected/modules/kundenakte/formModels/ContractDuplicateForm.php
    - protected/modules/kundenakte/formModels/DamageDuplicateForm.php
    - protected/modules/kundenakte/formModels/DuplicateForm.php
    - protected/modules/kundenakte/modules/api/bestand/
    - protected/modules/kundenakte/views/contractDuplicate/
    - protected/modules/kundenakte/views/duplicate/
    - protected/modules/LargeFileUpload/
    - protected/modules/Login/Controllers/TwoFactorAuthController.php
    - protected/modules/Login/Enums/
    - protected/modules/Login/Services/SsoLoginService.php
    - protected/modules/Maintenance
    - protected/modules/MaklerbetreuerStatistics
    - protected/modules/Marketplace
    - protected/modules/Oauth
    - protected/modules/stammdaten/modules/api/Controllers/stammdaten/
    - protected/modules/Startseite/Data/Einstellungen/
    - protected/modules/Tags
    - protected/modules/Intercom
    - protected/traits/
  scanDirectories:
    - protected

  bootstrapFiles:
    - .phpstan/bootstrap.php

  stubFiles:
    - .phpstan/stubs/Yii.stub
    - .phpstan/stubs/illuminate/database/Query/Builder.stub

  dynamicConstantNames:
    - YII_DEBUG

  universalObjectCratesClasses:
    - stdClass

  excludePaths:
    - protected/extensions/phpass/*
    - protected/modules/Oauth/docs/*
    - protected/modules/AiGateway/views/*

