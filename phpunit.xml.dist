<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" colors="true" printerClass="Codeception\Specify\ResultPrinter" bootstrap="tests/_bootstrap.php" cacheResult="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">
  <coverage>
    <include>
      <directory suffix=".php">./protected/_professionalworks</directory>
      <directory suffix=".php">./protected/Business</directory>
      <directory suffix=".php">./protected/commands</directory>
      <directory suffix=".php">./protected/components</directory>
      <directory suffix=".php">./protected/models</directory>
      <directory suffix=".php">./protected/modules</directory>
    </include>
    <exclude>
      <directory>./protected/modules/*/views</directory>
      <directory>./protected/modules/*/Views</directory>
      <directory>./protected/modules/*/widgets</directory>
      <directory>./protected/modules/*/Widgets</directory>
      <directory>./protected/modules/*/migrations</directory>
      <directory>./protected/modules/*/controllers</directory>
      <directory>./protected/modules/*/Controllers</directory>
      <directory>./protected/modules/*/modules/*/views</directory>
      <directory>./protected/modules/*/modules/*/Views</directory>
      <directory>./protected/modules/*/modules/*/controllers</directory>
      <directory>./protected/modules/*/modules/*/Controllers</directory>
      <directory>./protected/modules/*/modules/*/migrations</directory>
    </exclude>
  </coverage>
  <testsuites>
    <testsuite name="pw">
      <directory>tests</directory>
    </testsuite>
    <testsuite name="fast">
      <directory>tests</directory>
      <exclude>tests/Modules/Bipro/Backend/Linker</exclude>
      <exclude>tests/Modules/Bipro/Backend/Vertragsservice</exclude>
    </testsuite>
  </testsuites>
  <php>
    <env name="APP_ENV" value="test"/>
    <env name="PW_AWS_S3_CUSTOM_URL" value="http://s3.demv.internal"/>
    <env name="FINANZMANAGER_URL" value="http://finanzmanager.demv.internal"/>
  </php>
</phpunit>
