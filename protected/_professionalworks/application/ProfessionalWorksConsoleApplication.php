<?php

use Components\Guzzle;
use Components\OAuth\OAuth;
use Components\PwApi\PwApiClient;
use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Provider\Google;
use TheNetworg\OAuth2\Client\Provider\Azure;

/**
 * - Caches
 * @property ICache $appCache
 * @property ICache $cache
 * @property ICache $schemaCache
 *
 * - Http
 * @property Guzzle $guzzle
 *
 * - Metrics
 * @property PrometheusMetrics $metrics
 *
 * - OAuth
 * @property OAuth<GenericProvider> $oauthFondsfinanz
 * @property OAuth<GenericProvider> $oauthFondsfinanzSso
 * @property OAuth<Google>          $oauthGoogle
 * @property OAuth<Azure>           $oauthMicrosoft
 *
 *  - PW API
 * @property PwApiClient            $pwApiClient
 */
class ProfessionalWorksConsoleApplication extends CConsoleApplication implements ProfessionalWorksApplication
{
    //Wird benötigt, wenn über die Console Mails verschickt werden, deren Inhalt renderPartials enthält
    use RendererTrait;
    use ProfessionalWorksEnvironmentTrait;

    /**
     * @var array
     */
    public $storage = [];

    /**
     * @var array
     */
    public $queue = [];

    protected function init()
    {
        parent::init();

        $this->getUrlManager()->setBaseUrl($this->getBaseUrl());

        if ($this->hasEventHandler('onInit')) {
            $this->onInit(new CEvent($this));
        }

        $this->loadCommandsFromModules();
        $this->registerSentry();
    }

    private function loadCommandsFromModules()
    {
        $this->loadCommandsFromModule(Yii::app()->basePath . DS . WebModule::MODULE_MODULES_FOLDER . DS);
    }

    private function loadCommandsFromModule($moduleDir, $parentDependency = null)
    {
        $newDependency = null;

        if (is_dir($moduleDir)) {
            $modules = array_slice(scandir($moduleDir), 2);
            foreach ($modules as $moduleName) {
                $moduleCommandsPath = $moduleDir . $moduleName . DS . WebModule::MODULE_COMMANDS_FOLDER;
                $newDependency      = $this->getCommandRunner()->addModuleCommands($moduleCommandsPath, $moduleName, $parentDependency);
                $this->loadCommandsFromModule($moduleDir . $moduleName . DS . WebModule::MODULE_MODULES_FOLDER . DS, $newDependency);
            }
        }

        return $newDependency;
    }

    private function loadMigrationsFromModules()
    {
        $this->commandRunner->commands['migrate'] = [
            'class' => 'system.cli.commands.MigrateCommand',
        ];
    }

    /**
     * Creates a relative URL based on the given controller and action information.
     *
     * @param string $route     the URL route. This should be in the format of 'ControllerID/ActionID'.
     * @param array  $params    additional GET parameters (name=>value). Both the name and value will be URL-encoded.
     * @param string $ampersand the token separating name-value pairs in the URL.
     *
     * @return string the constructed URL
     */
    public function createUrl($route, $params = [], $ampersand = '&')
    {
        return $this->createAbsoluteUrl($route, $params, '', $ampersand);
    }

    /**
     * Creates an absolute URL based on the given controller and action information.
     *
     * @param string $route     the URL route. This should be in the format of 'ControllerID/ActionID'.
     * @param array  $params    additional GET parameters (name=>value). Both the name and value will be URL-encoded.
     * @param string $schema    schema to use (e.g. http, https). If empty, the schema used for the current request will be used.
     * @param string $ampersand the token separating name-value pairs in the URL.
     *
     * @return string the constructed URL
     */
    public function createAbsoluteUrl($route, $params = [], $schema = '', $ampersand = '&')
    {
        return $this->getUrlManager()->createUrl($route, $params, $ampersand);
    }

    protected function createCommandRunner()
    {
        return new ModuleCommandRunner(); // TODO: Change the autogenerated stub
    }

    /**
     * @param $absolute
     *
     * @return string
     */
    public function getBaseUrl($absolute = false)
    {
        return rtrim(Yii::app()->params['baseUrl'], '/');
    }

    private function registerSentry(): void
    {
        if ((Yii::app()->params['sentryDSN'] ?? '') === '') {
            return;
        }

        Sentry\init(
            [
                'dsn'                  => Yii::app()->params['sentryDSN'],
                'default_integrations' => false,
                'integrations'         => [
                    new Sentry\Integration\ErrorListenerIntegration(),
                    new Sentry\Integration\FatalErrorListenerIntegration(),
                    new Sentry\Integration\TransactionIntegration(),
                    new Sentry\Integration\FrameContextifierIntegration(),
                ],
            ]
        );
    }
}
