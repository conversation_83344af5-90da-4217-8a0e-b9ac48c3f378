<?php

declare(strict_types=1);

trait ProfessionalWorksEnvironmentTrait
{
    public function isLive(): bool
    {
        return $this->isEnvironment('live');
    }

    public function isDev(): bool
    {
        return $this->isEnvironment('dev');
    }

    public function isStaging(): bool
    {
        return $this->isEnvironment('staging');
    }

    public function isLocal(): bool
    {
        return $this->isEnvironment('local');
    }

    public function isTesting(): bool
    {
        return Yii::app()->db->getDriverName() === 'sqlite';
    }

    private function isEnvironment(string $env): bool
    {
        return Yii::app()->params['environment'] === $env;
    }
}
