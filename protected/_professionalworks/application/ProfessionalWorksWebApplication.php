<?php

use Components\Access\ControllerCreatedEvent;
use Components\AiGateway\AiGatewayClient;
use Components\AssetManager\WebpackAssetManager;
use Components\Authentication\ApiWebUser;
use Components\Database\DemvConnection;
use Components\Debug\DebugBar;
use Components\Errorhandling\ErrorHandlerFactory;
use Components\Filesystem\FilesystemFactory;
use Components\Guzzle;
use Components\OAuth\OAuth;
use Components\PwApi\PwApiClient;
use Components\Request\HttpRequest;
use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Provider\Google;
use TheNetworg\OAuth2\Client\Provider\Azure;

/**
 * - Assets
 * @property Bootstrap           $bootstrap
 * @property WebpackAssetManager $webpackAssetManager
 *
 * - Caches
 * @property ICache $appCache
 * @property ICache $cache
 * @property ICache $schemaCache
 *
 * - Configuration
 * @property DatabaseSettings     $settings
 * @property CAttributeCollection $params
 * @property EditableConfig       $editable
 *
 * - Database Connections
 * @property DemvConnection $db
 * @property DemvConnection $bipro
 * @property DemvConnection $modules
 * @property DemvConnection $common_data
 *
 * - Debugging
 * @property DebugBar|null $debugBar
 *
 * - Error Handling
 * @property ErrorHandlerFactory $errorHandler
 *
 * - Filesystems
 * @property FilesystemComponent $fileSystem
 * @property FilesystemFactory   $filesystems
 *
 * - Http
 * @property Guzzle      $guzzle
 * @property HttpRequest $request
 *
 * - Logging
 * @property LogRouter $log
 *
 * - Metrics
 * @property PrometheusMetrics $metrics
 *
 * - Misc
 * @property Formatter       $formatter
 * @property CImageComponent $image
 *
 * - OAuth
 * @property OAuth<GenericProvider> $oauthFondsfinanz
 * @property OAuth<GenericProvider> $oauthFondsfinanzSso
 * @property OAuth<Google>          $oauthGoogle
 * @property OAuth<Azure>           $oauthMicrosoft
 *
 * - User
 * @property UserCounter        $counter
 * @property ApiWebUser|WebUser $user
 *
 * - PW API
 * @property PwApiClient $pwApiClient
 *
 * - Ai Gateway
 * @property AiGatewayClient $aiGatewayClient
 */
class ProfessionalWorksWebApplication extends WebApplication implements ProfessionalWorksApplication
{
    use ProfessionalWorksEnvironmentTrait;

    const AFTER_CONTROLLER_CREATED_EVENT = 'onAfterControllerCreated';

    /** @var mixed[] */
    public $storage = [];

    /** @var mixed[] */
    public $queue = [];

    /** @var string[] */
    public $preload = ['debugBar'];

    protected function init(): void
    {
        parent::init();

        $this->registerSentry();
    }

    private function registerSentry(): void
    {
        if (!isset(Yii::app()->params['sentryDSN'])) {
            return;
        }

        Sentry\init(
            [
                'dsn'                  => Yii::app()->params['sentryDSN'],
                'default_integrations' => false,
                'integrations'         => [
                    new Sentry\Integration\ErrorListenerIntegration(),
                    new Sentry\Integration\FatalErrorListenerIntegration(),
                    new Sentry\Integration\RequestIntegration(),
                    new Sentry\Integration\TransactionIntegration(),
                    new Sentry\Integration\FrameContextifierIntegration(),
                ],
            ]
        );
    }

    public function createController($route, $owner = null)
    {
        $result = parent::createController($route, $owner);
        if ($result !== null) {
            try {
                \Components\Profiler::usedController($result[0]);
                \Components\Profiler::usedAction($result[1]);
            } catch (Throwable $exception) {
            }

            $this->raiseEvent(self::AFTER_CONTROLLER_CREATED_EVENT,
                new ControllerCreatedEvent($this,
                    Yii::app()->request->getRequestUri(),
                    $result[0],
                    $result[1]));
        }

        return $result;
    }

    /**
     * @throws CException
     */
    public function onAfterControllerCreated(CEvent $event): void
    {
        $this->raiseEvent(self::AFTER_CONTROLLER_CREATED_EVENT, $event);
    }
}
