<?php

/**
 * Created by PhpStorm.
 * User: max
 * Date: 22.05.15
 * Time: 17:43.
 *
 * Allows CamelCase Controllers
 */
class WebApplication extends CWebApplication
{
    public function createController($route, $owner = null)
    {
        if ($owner === null) {
            $owner = $this;
        }
        if (($route = trim($route, '/')) === '') {
            $route = $owner->defaultController;
        }
        $caseSensitive = $this->getUrlManager()->caseSensitive;

        $route .= '/';
        while (($pos = strpos($route, '/')) !== false) {
            $id = substr($route, 0, $pos);
            if (!preg_match('/^\w+$/', $id)) {
                return;
            }
            if (!$caseSensitive) {
                $id = strtolower($id);
            }
            $route = (string) substr($route, $pos + 1);

            if (!isset($basePath)) {
                // first segment

                if (isset($owner->controllerMap[$id])) {
                    return [
                        Yii::createComponent($owner->controllerMap[$id], $id, $owner === $this ? null : $owner),
                        $this->parseActionParams($route),
                    ];
                }

                if (($module = $owner->getModule($id)) !== null) {
                    return self::createController($route, $module);
                }

                $basePath     = $owner->getControllerPath();
                $controllerID = '';
            } else {
                $controllerID .= '/';
            }
            $className = ucfirst($id) . 'Controller';
            $classFile = $basePath . DIRECTORY_SEPARATOR . $className . '.php';

            if ($owner->controllerNamespace !== null) {
                $className = $owner->controllerNamespace . '\\' . $className;
            }
            /*
             * CaseInsensitive-Fix for Controllers
             * Date: 24.05.2013
             * Source: https://code.google.com/p/yii/issues/detail?id=2846
             */
            if (!$caseSensitive) {
                $classFile = $this->file_iexists($classFile);

                Yii::log($classFile, 'Warning');
                $id = $this->extractControllerName($classFile);
            }
            /* end of Change */

            if (is_file($classFile)) {
                if (!class_exists($className, false)) {
                    require $classFile;
                }
                if (class_exists($className, false) && is_subclass_of($className, 'CController')) {
                    $id[0] = strtolower($id[0]);

                    return [
                        new $className($controllerID . $id, $owner === $this ? null : $owner),
                        $this->parseActionParams($route),
                    ];
                }

                return;
            }
            $controllerID .= $id;
            $basePath .= DIRECTORY_SEPARATOR . $id;
        }
    }

    /**
     * CaseInsensitive-Fix for Controllers
     * necessary to Load Views where Controller-Filename is camelCase
     * Date: 24.05.2013
     * Source: https://code.google.com/p/yii/issues/detail?id=2846.
     */
    private function file_iexists($path)
    {
        $dirname  = dirname($path);
        $filename = basename($path);
        $dir      = dir($dirname);
        while (($file = $dir->read()) !== false) {
            if (strtolower($file) == strtolower($filename)) {
                $dir->close();

                return $dirname . DIRECTORY_SEPARATOR . $file;
            }
        }
        $dir->close();

        return false;
    }

    /**
     * CaseInsensitive-Fix for Controllers
     * necessary to Load Views where foldername is camelCase
     * Date: 24.05.2013
     * Source: https://code.google.com/p/yii/issues/detail?id=2846.
     */
    private function extractControllerName($fullPath)
    {
        $filename = basename($fullPath);
        $splitted = preg_split('/Controller./', $filename);

        return ucfirst($splitted[0]);
    }
}
