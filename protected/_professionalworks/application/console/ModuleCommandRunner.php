<?php

use Components\Behaviors\SentryBehavior;
use Components\Profiler;
use Sentry\State\Scope;
use function Sentry\configureScope;

/**
 * Created by PhpStorm.
 * User: sascha
 * Date: 18.05.16
 * Time: 13:49
 */
final class ModuleCommandRunner extends CConsoleCommandRunner
{

    /**
     * @var ModuleDependency[] $moduleCommands
     */
    protected $moduleCommands = [];

    public function addModuleCommands($moduleCommandsPath, $moduleName, $parentDependency)
    {
        $commands = $this->findCommands($moduleCommandsPath);

        foreach ($commands as $name => $file) {
            if ($this->commandExists($name)) {
                throw new Exception('Es gibt bereits einen Command mit dem Namen ' . $moduleName);
            }
            $dependency = $this->createModuleDependency($moduleName);
            $dependency->setCommandFile($file);
            $dependency->setParentDependency($parentDependency);

            $this->moduleCommands[$name] = $dependency;
        }

        $newdependency = $this->createModuleDependency($moduleName);
        $newdependency->setParentDependency($parentDependency);

        return $newdependency;
    }

    /**
     * @param $modulePath
     *
     * @return \ModuleDependency
     */
    private function createModuleDependency($modulename)
    {
        return new ModuleDependency($modulename);
    }

    /**
     * @param $moduleName
     *
     * @return bool
     */
    private function commandExists($moduleName)
    {
        return array_key_exists($moduleName, $this->commands) || array_key_exists($moduleName, $this->moduleCommands);
    }

    public function createCommand($name): ?CConsoleCommand
    {
        if (($command = parent::createCommand($name)) !== null) {
            $command->attachBehavior('sentry', new SentryBehavior());
        }

        return $command;
    }

    public function run($args)
    {
        if (isset($args[1])) {
            $commandName = $args[1];

            //Tell the Profiler the Used Command:
            if (is_string($commandName)) {
                Profiler::usedCommand($commandName);

                configureScope(function (Scope $scope) use ($commandName): void {
                    $scope->setTag('command', $commandName);
                });
            }

            //command hier in die Liste Drücken, falls command im Modul adressiert wird
            if (array_key_exists($commandName, $this->moduleCommands)) {
                $moduleDependency = $this->moduleCommands[$commandName];
                $moduleDependency->loadModule();
                $this->commands[$commandName] = $moduleDependency->getCommandFile();
            }
        }

        $exitCode = parent::run($args);

        configureScope(function (Scope $scope): void {
            $scope->removeTag('command');
        });

        return $exitCode;
    }
}
