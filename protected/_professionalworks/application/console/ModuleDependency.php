<?php

/**
 * Created by PhpStorm.
 * User: sascha
 * Date: 18.05.16
 * Time: 14:11
 */
class ModuleDependency
{
    /**
     * @var ModuleDependency $parentDependency
     */
    private $parentDependency;

    /**
     * @var string
     */
    private $modulename = '';

    /**
     * @var string
     */
    private $commandFile = '';

    public function __construct($modulename)
    {
        $this->modulename = $modulename;
    }

    /**
     * @return WebModule
     */
    public function loadModule()
    {
        $module = null;
        if ($this->hasDependency()) {
            $module = $this->getParentDependency()->loadModule();
        }

        if ($module === null) {
            return Yii::app()->getModule($this->modulename);
        }

        return $module->getModule($this->modulename);
    }

    /**
     * @return ModuleDependency
     */
    public function getParentDependency()
    {
        return $this->parentDependency;
    }

    /**
     * @param ModuleDependency $parentDependency
     */
    public function setParentDependency($parentDependency)
    {
        $this->parentDependency = $parentDependency;
    }

    /**
     * @return bool
     */
    private function hasDependency()
    {
        return $this->getParentDependency() !== null;
    }

    /**
     * @return string
     */
    public function getCommandFile()
    {
        return $this->commandFile;
    }

    /**
     * @param string $commandFile
     */
    public function setCommandFile($commandFile)
    {
        $this->commandFile = $commandFile;
    }

    /**
     * @return string
     */
    public function getModulename()
    {
        return $this->modulename;
    }
}
