<?php

/**
 * Created by PhpStorm.
 * User: max
 * Date: 19.05.15
 * Time: 10:21.
 */
class LogRouter extends CLogRouter
{
    public function init()
    {
        $routes = [

            [
                'class'   => 'CFileLogRoute',
                'levels'  => 'info',
                'filter'  => [
                    'class'      => 'CLogFilter',
                    'prefixUser' => true,
                    'logVars'    => [],
                ],
                'logFile' => 'application-info.log',

            ],
            [
                'class'   => 'CFileLogRoute',
                'levels'  => 'profile',
                'logFile' => 'application-profile.log',

            ],
            [
                'class'   => 'CFileLogRoute',
                'levels'  => 'error',
                'filter'  => [
                    'class'      => 'CLogFilter',
                    'prefixUser' => true,
                    'logVars'    => ['_GET', '_POST'],
                ],
                'logFile' => 'application-error.log',

            ],
        ];

//        if (YII_DEBUG) {
//            $routes[] = [
//                'class'     => 'vendor.malyshev.yii-debug-toolbar.YiiDebugToolbarRoute',
//                'ipFilters' => ['**************', '127.0.0.1', '************', '**********'],
//            ];
//        }

        $this->routes = $routes;

        parent::init();
    }
}
