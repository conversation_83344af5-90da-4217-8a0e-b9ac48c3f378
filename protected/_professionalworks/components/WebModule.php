<?php

class WebModule extends CWebModule
{
    const MODULE_COMMANDS_FOLDER   = 'commands';
    const MODULE_MIGRATIONS_FOLDER = 'migrations';
    const MODULE_MODULES_FOLDER    = 'modules';

    /** @var mixed[] */
    public $subModules = [];

    /** @var string */
    public $name;

    /** @var bool */
    public $adminOnly      = false;
    /** @var bool */
    public $superadminOnly = false;

    /**
     * @return void
     */
    public function init()
    {
        $this->setModules($this->subModules);
    }

    public function beforeControllerAction($controller, $action)
    {
        if ($this->adminOnly || $this->superadminOnly) {
            if (Yii::app()->user->getIsGuest()) {
                Yii::app()->session['returnUrl'] = Yii::app()->request->requestUri;
                Yii::app()->request->redirect('/home/<USER>');
            }
            if ($this->adminOnly && !Yii::app()->user->isAdmin()) {
                throw new AccessDeniedException('Forbidden access.');
            }
            if ($this->superadminOnly && !Yii::app()->user->isSuperAdmin()) {
                throw new AccessDeniedException('Forbidden access.');
            }
        }

        return parent::beforeControllerAction($controller, $action);
    }

    /**
     * @param mixed[] $modules
     * @param bool    $merge
     */
    public function setModules($modules, $merge = true): void
    {
        parent::setModules($modules, $merge);

        foreach ($modules as $id => $module) {
            if (is_int($id)) {
                $id = $module;
            }
            //Fix um unseren `Bug` wieder zu bekommen: https://github.com/yiisoft/yii/pull/2931 & https://github.com/yiisoft/yii/issues/2917
            //Hier setzen wir den Alias für den Fall, der uns Verloren gegangen ist neu.
            if (isset($module['class'])) {
                Yii::setPathOfAlias($id, $this->getModulePath() . DIRECTORY_SEPARATOR . $id);
            }
        }
    }
}
