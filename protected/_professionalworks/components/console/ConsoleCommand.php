<?php

class ConsoleCommand extends CConsoleCommand
{
    /** @var int */
    public const EXIT_SUCCESS = 0;

    /** @var int */
    public const EXIT_ERROR = 1;

    public const CLI_COLOR_RESET = "\033[0m";

    public const CLI_COLOR_HEADERS = [
        'red'   => "\033[31m",
        'green' => "\033[32m",
        'blue'  => "\033[34m",
    ];

    public function beforeAction($action, $params)
    {
        echo '---------------------------------------------------------' . PHP_EOL;
        echo '> START ' . $this->name . ' at: ' . date('H:i:s - d.m.Y') . PHP_EOL;
        echo '---------------------------------------------------------' . PHP_EOL . PHP_EOL;

        return true;
    }

    public function afterAction($action, $params, $exitCode=0)
    {
        echo PHP_EOL;
        echo '---------------------------------------------------------' . PHP_EOL;
        echo '> STOP ' . $this->name . ' at: ' . date('H:i:s - d.m.Y') . PHP_EOL;
        echo '---------------------------------------------------------' . PHP_EOL;

        return $exitCode;
    }

    /**
     * @throws Exception
     */
    private function inColor(string $color, string $string): string
    {
        if (!array_key_exists($color, self::CLI_COLOR_HEADERS)) {
            throw new Exception('Trying to use an undefined CLI color scheme');
        }

        return self::CLI_COLOR_HEADERS[$color] . $string . self::CLI_COLOR_RESET;
    }

    private function showMessage(string $message): void
    {
        echo "> {$message}" . PHP_EOL;
    }

    public function info(string $message): void
    {
        $this->showMessage(
            sprintf('[%s] %s', $this->inColor('blue', 'INFO'), $message)
        );
    }

    public function success(string $message): void
    {
        $this->showMessage(
            sprintf('[%s] %s', $this->inColor('green', 'SUCCESS'), $message)
        );
    }

    public function error(string $message): void
    {
        $this->showMessage(
            sprintf('[%s] %s', $this->inColor('red', 'ERROR'), $message)
        );
    }
}
