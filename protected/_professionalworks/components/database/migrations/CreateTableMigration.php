<?php
/**
 * Created by PhpStorm.
 * User: alex
 * Date: 28.09.15
 * Time: 10:45
 */

abstract class CreateTableMigration extends DbMigration
{
    public $tableName = '';

    /**
     * @return array
     */
    abstract public function getColumns();

    /**
     * @return void
     */
    public function addForeignKeys()
    {
    }

    public function up()
    {
        $this->createTable($this->tableName, $this->getColumns());
        $this->addForeignKeys();
    }

    public function down()
    {
        $this->dropTable($this->tableName);

        return true;
    }

    public function addOwnFk($suffix, $column, $refTable, $onDelete, $onUpdate)
    {
        $this->addForeignKey('fk_' . $this->tableName . '_' . $suffix, $this->tableName, $column, $refTable, 'id', $onDelete, $onUpdate);
    }
}
