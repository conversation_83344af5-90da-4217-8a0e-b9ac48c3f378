<?php

use Components\Database\ValueObject\ColumnDefinition;

/**
 * Created by PhpStorm.
 * User: max
 * Date: 06.05.15
 * Time: 14:50.
 */
class DbMigration extends CDbMigration
{
    /**
     * import sql from a *.sql file
     *
     * <AUTHOR>
     *
     * @param string $file : with the path and the file name
     *
     * @return mixed
     */
    public function import($file = '')
    {
        $pdo = Yii::app()->db->pdoInstance;
        try {
            if (file_exists($file)) {
                $sqlStream = file_get_contents($file);
                $sqlStream = rtrim($sqlStream);
                $newStream = preg_replace_callback("/\((.*)\)/", static function (array $matches) {
                    return str_replace(';', ' $$$ ', $matches[0]);
                }, $sqlStream);
                $sqlArray  = explode(';', $newStream ?? '');
                foreach ($sqlArray as $value) {
                    if (!empty($value)) {
                        $sql = str_replace(' $$$ ', ';', $value) . ';';
                        $pdo->exec($sql);
                    }
                }

                //echo "succeed to import the sql data!";
                return true;
            }
        } catch (PDOException $e) {
            echo $e->getMessage();
            exit;
        }
    }

    public function createTable($table, $columns, $options = null, $replace = false): void
    {
        if ($replace) {
            $this->dropTableIfExists($table);
        }
        parent::createTable($table, $columns, $options);
    }

    public function dropTableIfExists($table): void
    {
        if ($this->tableExists($table)) {
            echo " table $table exists, drop it";
            $this->dropTable($table);
        }
    }

    public function tableExists($table)
    {
        return $this->getDbConnection()->schema->getTable($table, true) !== null;
    }

    /**
     * @var EMigrateCommand
     */
    private $migrateCommand;
    protected $interactive = true;

    /**
     * @param EMigrateCommand $migrateCommand
     */
    public function setCommand($migrateCommand): void
    {
        $this->migrateCommand = $migrateCommand;
        $this->interactive    = $migrateCommand->interactive;
    }

    /**
     * @see CConsoleCommand::confirm()
     *
     * @param string $message
     *
     * @return bool
     */
    public function confirm($message)
    {
        if (!$this->interactive) {
            return true;
        }

        return $this->migrateCommand->confirm($message);
    }

    /**
     * @see CConsoleCommand::prompt()
     *
     * @param string $message
     * @param mixed  $defaultValue will be returned when interactive is false
     *
     * @return string
     */
    public function prompt($message, $defaultValue)
    {
        if (!$this->interactive) {
            return $defaultValue;
        }

        return $this->migrateCommand->prompt($message);
    }

    /**
     * Executes a SQL statement. Silently. (only show sql on exception)
     * This method executes the specified SQL statement using {@link dbConnection}.
     *
     * @param string  $sql the SQL statement to be executed
     * @param array   $params input parameters (name=>value) for the SQL execution. See {@link CDbCommand::execute} for more details.
     * @param boolean $verbose
     */
    public function execute($sql, $params = [], $verbose = true): void
    {
        if ($verbose) {
            parent::execute($sql, $params);
        } else {
            try {
                echo '    > execute SQL ...';
                $time = microtime(true);
                $this->getDbConnection()->createCommand($sql)->execute($params);
                echo ' done (time: ' . sprintf('%.3f', microtime(true) - $time) . "s)\n";
            } catch (CException $e) {
                echo " failed.\n\n";
                throw $e;
            }
        }
    }

    /**
     * Builds and executes a SQL statement for adding new DB multiple columns
     *
     * @param string           $table
     * @param ColumnDefinition ...$columns
     *
     * @return void
     * @throws CDbException
     * @throws CException
     */
    public function addColumns(string $table, ColumnDefinition ...$columns): void
    {
        echo "    > add columns :\n";
        foreach ($columns as $column) {
            echo "         - {$column->getName()} {$column->getType()} \n";
        }
        echo "      to table $table ...";

        $time        = microtime(true);
        $schema      = $this->getDbConnection()->getSchema();
        $definitions = array_map(static fn (ColumnDefinition $column): string => "ADD {$schema->quoteColumnName($column->getName())} {$schema->getColumnType($column->getType())}", $columns);
        $query       = "ALTER TABLE {$schema->quoteTableName($table)}" . implode(',', $definitions);
        $this->getDbConnection()->createCommand($query)->query();

        echo ' done (time: ' . sprintf('%.3f', microtime(true) - $time) . "s)\n";
    }

    /**
     * Builds and executes a SQL statement for dropping DB multiple columns
     *
     * @param string           $table
     * @param ColumnDefinition ...$columns
     *
     * @return void
     * @throws CDbException
     * @throws CException
     */
    public function dropColumns(string $table, ColumnDefinition ...$columns): void
    {
        echo "    > drop columns :\n";
        foreach ($columns as $column) {
            echo "         - {$column->getName()} \n";
        }
        echo "      from table $table ...";

        $time        = microtime(true);
        $schema      = $this->getDbConnection()->getSchema();
        $definitions = array_map(static fn (ColumnDefinition $column): string => "DROP COLUMN {$schema->quoteColumnName($column->getName())}", $columns);
        $query       = "ALTER TABLE {$schema->quoteTableName($table)}" . implode(',', $definitions);
        $this->getDbConnection()->createCommand($query)->query();

        echo ' done (time: ' . sprintf('%.3f', microtime(true) - $time) . "s)\n";
    }
}
