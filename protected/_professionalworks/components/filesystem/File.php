<?php

namespace ProfessionalWorks\Components\Filesystem;

use Components\Behaviors\CreateLogBehavior;
use FilesystemComponent;
use League\Flysystem\FileNotFoundException;
use League\Flysystem\Filesystem;

/**
 * This is the model class for table "file".
 *
 * The followings are the available columns in table 'file':
 *
 * @property integer $id
 * @property string  $name
 * @property string  $path
 * @property string  $extension
 * @property string  $mime
 * @property string  $hash
 * @property string  $size
 * @property string  $create_datetime
 * @property string  $update_datetime
 */
class File extends \CActiveRecord
{
    /**
     * @var string $sourceFilePath
     */
    public $sourceFilePath;

    /**
     * @var string
     */
    private $sourceContent;

    private $sourceStream;
    /**
     * @var Filesystem
     */
    private $fileSystem;

    public $path;

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     *
     * @param string $className active record class name.
     *
     * @return static the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'file';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return [
            ['name, extension, mime, hash, size', 'required'],
            ['name, extension, mime, hash, size', 'length', 'max' => 255],
            ['create_datetime, update_datetime', 'safe'],
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            ['id, name, extension, mime, hash, size, create_datetime, update_datetime', 'safe', 'on' => 'search'],
        ];
    }

    public function afterDelete()
    {
        try {
            $this->getFileSystem()->delete(self::getFileName());
        } catch (FileNotFoundException $e) {
        } catch (\Exception $e) {
        }

        return parent::afterDelete();
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return [
        ];
    }

    public function setFileSystem($fileSystem)
    {
        $this->fileSystem = $fileSystem;
    }

    /**
     * @return FilesystemComponent
     */
    public function getFileSystem()
    {
        if (empty($this->fileSystem)) {
            $this->fileSystem = \Yii::app()->fileSystem;
        }

        return $this->fileSystem;
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return [
            'id'              => 'ID',
            'name'            => 'Name',
            'extension'       => 'Extension',
            'mime'            => 'Mime',
            'hash'            => 'Hash',
            'size'            => 'Size',
            'create_datetime' => 'Create Datetime',
            'update_datetime' => 'Update Datetime',
        ];
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return \CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new \CDbCriteria();

        $criteria->compare('id', $this->id);
        $criteria->compare('name', $this->name, true);
        $criteria->compare('extension', $this->extension, true);
        $criteria->compare('mime', $this->mime, true);
        $criteria->compare('hash', $this->hash, true);
        $criteria->compare('size', $this->size, true);
        $criteria->compare('create_datetime', $this->create_datetime, true);
        $criteria->compare('update_datetime', $this->update_datetime, true);

        return new \CActiveDataProvider($this, [
            'criteria' => $criteria,
        ]);
    }

    public function beforeSave()
    {
        $success = $this->saveFile();

        return $success ? parent::beforeSave() : false;
    }

    protected function saveFile()
    {
        $success = true;
        if (!empty($this->sourceFilePath)) {
            $stream  = fopen($this->sourceFilePath, 'r');
            $success = $this->getFileSystem()->writeStream(self::getFileName(), $stream);
            fclose($stream);
        } elseif (!empty($this->sourceContent)) {
            $success             = $this->getFileSystem()->write(self::getFileName(), $this->sourceContent);
            $this->sourceContent = null;
        } elseif ($this->sourceStream !== null) {
            $success            = $this->getFileSystem()->writeStream(self::getFileName(), $this->sourceStream);
            $this->sourceStream = null;
        }

        return $success;
    }

    public function getContent()
    {
        if ($this->getFileSystem()->has(self::getFileName())) {
            return $this->getFileSystem()->read(self::getFileName());
        }

        return '';
    }

    public function getContentAsStream()
    {
        return $this->getFileSystem()->readStream(self::getFileName());
    }

    /**
     * @return string
     */
    public function getFileName()
    {
        return rtrim($this->hash . '.' . $this->extension, '.');
    }

    /**
     * Gibt den Originale-Dateinamen zurück
     *
     * @return string
     */
    public function getOrigFileName()
    {
        return $this->name . '.' . $this->extension;
    }

    public function getContents()
    {
        return $this->getFileSystem()->read(self::getFileName());
    }

    public function loadFromUpload($uploadedFile)
    {
        if (!empty($uploadedFile)) {
            $this->sourceFilePath = $uploadedFile->tempName;
            $path_parts           = pathinfo($uploadedFile->name);
            $this->name           = $path_parts['filename'];
            $this->generateHash();
            $this->extension = $uploadedFile->extensionName;
            $this->mime      = $uploadedFile->type;
            $this->size      = $uploadedFile->size;
        }
    }

    public function loadFromFile($filePath)
    {
        if (file_exists($filePath)) {
            $this->sourceFilePath = $filePath;
            $path_parts           = pathinfo($filePath);
            $this->name           = $path_parts['filename'];
            $this->generateHash();
            $this->extension = $path_parts['extension'] ?? 'txt';
            $this->mime      = \CFileHelper::getMimeType($filePath);
            $this->size      = filesize($filePath);

            return true;
        }

        return false;
    }

    /**
     * Setzt den Inhalt der Datei mit Dateinamen und Inhalt
     *
     * @param $filename
     * @param $content
     */
    public function loadFromContent($filename, $content)
    {
        $path_parts          = pathinfo($filename);
        $this->name          = $path_parts['filename'];
        $this->extension     = $path_parts['extension'];
        $this->mime          = \CFileHelper::getMimeTypeByExtension($filename);
        $this->sourceContent = $content;
        $this->size          = strlen($content ?? '');
        $this->generateHash();
    }

    /**
     * @param string $filename
     * @param        $stream
     *
     * @throws \Exception
     */
    public function loadFromStream(string $filename, $stream): void
    {
        $path_parts         = pathinfo($filename);
        $this->name         = $path_parts['filename'];
        $this->extension    = $path_parts['extension'];
        $this->mime         = \CFileHelper::getMimeTypeByExtension($filename);
        $this->sourceStream = $stream;
        $this->size         = fstat($stream)['size'];
        $this->generateHash();
    }

    public function generateHash()
    {
        if (empty($this->name)) {
            throw new \Exception('Need a name to generate a Hash');
        }

        $this->hash = md5($this->name . uniqid());
    }

    public function behaviors()
    {
        return [
            'CreateLogBehavior' => [
                'class'               => CreateLogBehavior::class,
                'disable_user_id_log' => true
            ],
        ];
    }

    public function getIcon()
    {
        switch ($this->extension) {
            case 'pdf':
                return 'fa fa-file-pdf-o';
            case 'doc':
            case 'docx':
                return 'fa fa-file-word-o';
            case 'txt':
                return 'fa fa-file-text-o';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'bmp':
                return 'fa fa-file-image-o';
            case 'xls':
            case 'xlsx':
                return 'fa fa-file-excel-o';
            default:
                return 'fa fa-file-o';
        }
    }
}
