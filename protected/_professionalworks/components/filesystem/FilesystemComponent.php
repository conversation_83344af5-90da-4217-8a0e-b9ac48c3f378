<?php

/**
 * Created by PhpStorm.
 * User: max
 * Date: 19.06.15
 * Time: 11:18
 */

use League\Flysystem\Adapter\Local;
use League\Flysystem\Filesystem;

class FilesystemComponent extends CApplicationComponent
{
    /*
     * main alias for local storage
     */
    public $path;

    public $config;

    protected $filesystem;

    public function init()
    {
        parent::init();

        if ($this->path === null) {
            throw new CException('The "path" property must be set.');
        }
        $this->path = Yii::getPathOfAlias($this->path);

        $adapter          = new Local($this->path);
        $this->filesystem = new Filesystem($adapter, $this->config);
    }

    /**
     * @param string $method
     * @param array  $parameters
     *
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        return call_user_func_array([$this->filesystem, $method], $parameters);
    }

    /**
     * @return Filesystem
     */
    public function getFilesystem(): Filesystem
    {
        return $this->filesystem;
    }

    /**
     * @param mixed $filesystem
     */
    public function setFilesystem(Filesystem $filesystem): void
    {
        $this->filesystem = $filesystem;
    }
}
