<?php

declare(strict_types=1);

use Components\Request\HttpRequest;
use Symfony\Component\HttpFoundation\Response;

trait ApiUtilsTrait
{
    use \RenderJsonTrait;

    public function abortIf($condition, $code = 400, $message = null): void
    {
        if (!$condition) {
            return;
        }

        if ($message !== null) {
            $this->respondError(['message' => $message], $code);
        } else {
            $this->respondError([], $code);
        }
    }

    public function respondOk($data = []): void
    {
        $this->renderJsonResponse($data, Response::HTTP_OK);
    }

    public function respondError($data = [], $status = Response::HTTP_INTERNAL_SERVER_ERROR): void
    {
        $this->renderJsonResponse($data, $status);
    }

    public function failUnlessPostMethod(): void
    {
        if (Yii::app()->request->getRequestType() !== HttpRequest::POST) {
            $this->renderJsonResponse([], Response::HTTP_METHOD_NOT_ALLOWED);
        }
    }

    public function failUnlessPutMethod(): void
    {
        if (Yii::app()->request->getRequestType() !== HttpRequest::PUT) {
            $this->renderJsonResponse([], Response::HTTP_METHOD_NOT_ALLOWED);
        }
    }

    public function failUnlessGetMethod(): void
    {
        if (Yii::app()->request->getRequestType() !== HttpRequest::GET) {
            $this->renderJsonResponse([], Response::HTTP_METHOD_NOT_ALLOWED);
        }
    }

    public function failUnlessDeleteMethod(): void
    {
        if (Yii::app()->request->getRequestType() !== HttpRequest::DELETE) {
            $this->renderJsonResponse([], Response::HTTP_METHOD_NOT_ALLOWED);
        }
    }

    public function failUnlessPatchMethod(): void
    {
        if (Yii::app()->request->getRequestType() !== HttpRequest::PATCH) {
            $this->renderJsonResponse([], Response::HTTP_METHOD_NOT_ALLOWED);
        }
    }
}
