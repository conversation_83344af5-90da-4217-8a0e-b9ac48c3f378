<?php

use Laracasts\Utilities\JavaScript\Transformers\Transformer;

trait AppendJavaScriptVariablesTrait
{
    /**
     * @param array $vars
     * @param string $namespace
     * @throws Exception
     */
    public function appendJavascriptVariables($vars = [], $namespace = 'window')
    {
        $binder     = new ApplicationViewBinder();
        $javascript = new Transformer($binder, $namespace);
        $javascript->put($vars);
    }
}

//todo refactor struktur
class ApplicationViewBinder implements Laracasts\Utilities\JavaScript\ViewBinder
{
    // $js will contain your JS-formatted variable initializations
    public function bind($js)
    {
        /** @var ProfessionalWorksWebApplication $app */
        Yii::app()->clientScript->registerScript('vars', $js);
    }
}
