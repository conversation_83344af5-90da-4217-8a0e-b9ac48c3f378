<?php

/**
 * Created by PhpStorm.
 * User: max
 * Date: 15.07.15
 * Time: 13:19
 */
trait RenderAjaxTrait
{
    public function renderAjax($view, $data = null)
    {
        if (Yii::app()->request->isAjaxRequest) {

            // to avoid jQuery and other core scripts from loading when the fourth parameter of renderPartial() is TRUE.
            // this is useful if you want another ajaxButton in the modal or anything with scripts.
            // http://www.yiiframework.com/forum/index.php/topic/5455-creating-ajax-elements-from-inside-ajax-request/page__p__30114#entry30114
            /**
             * Debug Version
             */
            Yii::app()->clientscript->scriptMap['jquery.js'] = false;

            /**
             * Live Version
             */
            Yii::app()->clientscript->scriptMap['jquery.min.js']         = false;
            Yii::app()->clientScript->scriptMap['jquery.ba-bbq.js']      = false;
            Yii::app()->clientScript->scriptMap['jquery.yiigridview.js'] = false;

            echo $this->renderPartial($view, $data, true, true);
        } else {
            echo 'No Ajax Request';
        }

        Yii::app()->end();
    }
}
