<?php

declare(strict_types=1);

trait RenderJsonTrait
{
    public function renderJson($data = null)
    {
        header('Content-Type: application/json; charset="UTF-8"');
        echo CJson::encode($data);
        Yii::app()->end();
    }

    /**
     * A general purpose JSON based response generator
     *
     * @param mixed $data
     * @param int   $responseCode
     */
    public function renderJsonResponse($data = [], int $responseCode = 200): void
    {
        $this->layout = false;
        header('Content-Type: application/json; charset="UTF-8"');
        http_response_code($responseCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);

        foreach (Yii::app()->log->routes as $route) {
            if ($route instanceof CWebLogRoute) {
                $route->enabled = false;
            }
        }

        Yii::app()->end();
    }
}
