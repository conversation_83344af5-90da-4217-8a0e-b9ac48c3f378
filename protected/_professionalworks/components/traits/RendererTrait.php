<?php

/**
 * Trait RendererTrait
 */
trait RendererTrait
{
    private $_controller;

    /**
     * @return Controller
     */
    public function getController(): Controller
    {
        if ($this->_controller === null) {
            $this->_controller = new Controller('site');
        }

        return $this->_controller;
    }

    /**
     * @return null
     */
    public function getViewRenderer()
    {
        return null;
    }

    /**
     * @return string
     */
    public function getViewPath(): string
    {
        return $this->getBasePath() . DIRECTORY_SEPARATOR . 'views';
    }

    /**
     * @return null
     */
    public function getTheme()
    {
        return null;
    }
}
