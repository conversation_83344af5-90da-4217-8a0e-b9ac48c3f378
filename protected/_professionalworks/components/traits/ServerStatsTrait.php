<?php

/**
 * Created by PhpStorm.
 * User: max
 * Date: 20.07.15
 * Time: 13:35
 */
trait ServerStatsTrait
{
    public function getServerStatus()
    {
        $dir = '/var/www';

//        $status['disk']['free'] = $this->formatSize(disk_free_space($dir));
//        $status['disk']['total'] = $this->formatSize(disk_total_space($dir));
//        $status['disk']['percentage'] = sprintf('%.2f',
//            (($status['disk']['total'] - $status['disk']['free']) / $status['disk']['total']) * 100);
//
//        $status['memory'] = $this->get_server_memory_usage();
//
//        $status['cpu'] = sys_getloadavg()[0] * 100 / 4;

        $status['disk']['free']       = '';
        $status['disk']['total']      = '';
        $status['disk']['percentage'] = '';

        $status['memory'] = '';

        $status['cpu'] = '';

        return $status;
    }

    public function formatSize($bytes)
    {
        $types = [
            'B',
            'KB',
            'MB',
            'GB',
            'TB'
        ];
        for ($i = 0; $bytes >= 1024 && $i < (count($types) - 1); $bytes /= 1024, $i++) {
        }

        return round($bytes, 2) . ' ' . $types[$i];
    }

    public function get_server_memory_usage()
    {
        $free         = shell_exec('free');
        $free         = (string) trim($free);
        $free_arr     = explode("\n", $free);
        $mem          = explode(' ', $free_arr[1] ?? '');
        $mem          = array_filter($mem);
        $mem          = array_merge($mem);
        $memory_usage = $mem[2] / $mem[1] * 100;
        $memory_usage = sprintf('%.2f', $memory_usage);

        return $memory_usage;
    }
}
