<?php

Yii::import('application.modules.admin.modules.menu.models.*');

trait SideMenuRendererTrait
{
    /**
     * @param null $menuGroup
     *
     * @return array
     */
    public function generateSideMenus($menuGroup = null)
    {
        $menuGroup = MenuGroup::model()->cache(600)->findByAttributes(['name' => $this->menuGroup]);

        if (empty($menuGroup)) {
            return [];
        }
        $menus = [];
        /** @var Menu[] $menuEntries */
        $menuEntries = Menu::model()->findAllByAttributes(['menu_group_id' => $menuGroup->id], ['order' => 'sort']);

        foreach ($menuEntries as $menu) {
            $menuItems = $menu->items;
            if ($menu->active && count($menuItems) > 0) {
                $menus[$menu->title] = [
                    [
                        'label' => $menu->title,
                        'icon'  => $menu->icon,
                    ],
                ];

                foreach ($menuItems as $item) {
                    if ($item->isVisible()) {
                        $data = [
                            'label'       => $item->title,
                            'url'         => [$item->url],
                            'icon'        => $item->icon,
                            'regex'       => $item->regex,
                            'linkOptions' => ['target' => $item->target ?? '_top']
                        ];

                        $menus[$menu->title][] = $data;
                    }
                }

                // Do we have any items?
                if (count($menus[$menu->title]) <= 1) {
                    unset($menus[$menu->title]);
                }
            }
        }

        return $menus;
    }
}
