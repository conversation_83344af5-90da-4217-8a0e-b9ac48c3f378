<?php

declare(strict_types=1);

use Demv\Tmpdir\TmpDirRegistry;

trait TmpFileTrait
{
    /**
     * Create a temporary file for a resource (stream or content) and return the path to it on success.
     *
     * @param string $name
     * @param resource|mixed $resource
     * @throws IOException
     */
    protected function createTmpFile($filename, $resource): string
    {
        $content = false;

        if (is_resource($resource) && get_resource_type($resource) === 'stream') {
            $content = stream_get_contents($resource);

            if ($content === false) {
                throw new IOException('Could not read stream content', 500);
            }
        } else {
            $content = $resource;
        }

        $tmpdir = TmpDirRegistry::instance()->createDirInSystemTmp('tmp');

        $filePath = $tmpdir . $filename;

        $success = file_put_contents(
            $filePath,
            $content,
        );

        if ($success === false) {
            throw new IOException("Could not write content to $filename", 500);
        }

        return $filePath;
    }
}
