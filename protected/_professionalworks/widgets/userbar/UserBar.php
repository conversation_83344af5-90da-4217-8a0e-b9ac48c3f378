<?php

use Components\AWS\Environment;

/**
 * Description of UserBar.
 *
 * <AUTHOR>
 */
class UserBar extends CWidget
{
    public function run()
    {
        if (YII_DEBUG) {
            vprintf(
                'MAIL_DEBUG: %s | Server: %s | Datenbank: %s | Branch: %s | BiPRO-Environment: %s',
                [
                    MAIL_DEBUG,
                    $_SERVER['HTTP_HOST'],
                    explode('=', Yii::app()->db->connectionString)[2],
                    Yii::app()->params['branch'],
                    Environment::get()
                ]
            );
        } elseif (in_array(Yii::app()->user->getId(), Yii::app()->params['showStatistics'])) {
            vprintf(
                'online: %s | heute: %s | gestern: %s | gesamt: %s',
                [
                    Yii::app()->counter->getOnline(),
                    Yii::app()->counter->getToday(),
                    Yii::app()->counter->getYesterday(),
                    Yii::app()->counter->getTotal(),
                ]
            );
        }
    }
}
