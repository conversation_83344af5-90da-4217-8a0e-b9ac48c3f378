<button class="btn btn-mini btn-inverse dropdown-toggle" data-toggle="dropdown"><?= Yii::app()->user->name ?> <span
        class="caret"></span></button>
<ul class="dropdown-menu pull-right">
    <?php if (Yii::app()->user->hasRight(Rights::SYSTEMNUTZER_BEARBEITEN)) {
    ?>
        <li><a href="<?= Yii::app()->createUrl('systemuser/update', ['id' => Yii::app()->user->getId()]) ?>"><i
                    class="fa fa-user margin-right"></i> Mein Account</a></li>
    <?php
}
    if (Yii::app()->user->hasRight(Rights::PROFIL)) {
        ?>
        <li><a href="<?= Yii::app()->createUrl('profile/update', ['id' => Yii::app()->user->getId()]) ?>"><i
                    class="fa fa-certificate margin-right"></i> Mein <PERSON>il</a></li>
    <?php
    } ?>
    <li>
        <a href="<?= Yii::app()->createUrl('inhaltsseiten/checklist/index') ?>">
            <i class="fa fa-list-ol margin-right"></i> Checkliste
        </a>
    </li>
    <li class="divider"></li>
    <?php if (Yii::app()->user->isAdmin()) {
        ?>
        <li><a href="<?= Yii::app()->createUrl('admin') ?>">
                <i class="fa fa-wrench margin-right"></i> Adminbereich</a></li>
        <li class="divider"></li>
    <?php
    } ?>
    <li>
        <a href="/home/<USER>">
            <i class="fa fa-power-off margin-right"></i>
            Logout
        </a>
    </li>
</ul>
<style>
    .margin-right {
        margin-right: 5px;
    }
</style>
