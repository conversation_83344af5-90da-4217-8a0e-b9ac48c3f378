<?php

class AddressController extends AllowAllController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Diese Methode erstellt ein neues Address -Model
     *
     * @param type $data
     *          Die Address -Daten als Array
     *
     * @return $address
     *          Das erstellte Address -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $address             = new Address();
        $address->attributes = $data['Address'];

        if ($address->save()) {
            return $address;
        }

        return null;
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model Address handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['Address']) &&
            isset($data['Address']['street']) &&
            $data['Address']['street'] != '' &&
            isset($data['Address']['zip']) &&
            $data['Address']['zip'] != '' &&
            isset($data['Address']['nr'])
//                &&
//                $data['Address']['nr'] != ''
        ;
//
    }

    /**
     * Diese Methode aktualisiert ein Address-Exemplar
     *
     * @param type $data
     *          Die Address-Daten als Array
     *
     * @return $address
     *          Das aktualisierte Address-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $address             = $this->loadModel($data['Address']['id']);
            $address->attributes = $data['Address'];
            if ($address->save()) {
                return $address;
            }

            return null;
        }

        return null;
    }

    /**
     * Lädt das Address-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des Address-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $address = Address::model()->findByPk($id);

        return $address;
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Address $address the address to be validated
     */
    protected function performAjaxValidation($address)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'address-form') {
            echo CActiveForm::validate($address);
            Yii::app()->end();
        }
    }
}
