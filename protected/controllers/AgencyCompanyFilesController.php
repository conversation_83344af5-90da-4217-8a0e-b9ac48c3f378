<?php

class AgencyCompanyFilesController extends Controller
{
    use RenderAjaxTrait;

    public function accessRules(): array
    {
        return [
            [
                'allow',
                'actions'    => ['download', 'list', 'upload', 'ajaxEdit', 'ajaxupload', 'ajaxUpdate', 'delete', 'update'],
                'expression' => static function () {
                    return Yii::app()->user->getPermissions()->hasRight(Rights::FIRMENDOKUMENTE_ZU_GESELLSCHAFT);
                }
            ],
            [
                'deny',
            ],
        ];
    }

    /**
     * @param int $id
     *
     * @return AgencyFiles
     * @throws CHttpException
     */
    private function loadModel(int $id): AgencyFiles
    {
        $model = AgencyFiles::model()->forAgencyOfUser(currentUser())->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404);
        }

        return $model;
    }

    /**
     * @param      $id
     * @param bool $download
     *
     * @return mixed|void
     * @throws CException
     * @throws CHttpException
     * @throws \League\Flysystem\FileNotFoundException
     */
    public function actionDownload($id, $download = true)
    {
        $file       = $this->loadModel($id);
        $filesystem = $file->getFilesystem();
        if (!$filesystem->has($file->name)) {
            throw new CHttpException(404, 'Logo nicht vorhanden');
        }
        Yii::import('ext.EDownloadHelper');
        $filename = $file->name;
        EDownloadHelper::downloadResource(
            $filesystem->readStream($filename),
            $filename,
            $filesystem->getSize($filename),
            $filesystem->getMimetype($filename)
        );
        Yii::app()->end();
    }

    public function actionList($companyId = null)
    {
        $this->layout    = 'application.views.layouts.main_breadcrumbs';
        $view            = AgencyFilesView::forUser(currentUser());
        $view->companyId = $companyId !== null ? (int) $companyId : null;
        $view->setAttributes($_GET);
        $this->render('admin', ['view' => $view]);
    }

    public function actionUpload(int $companyId = null)
    {
        $this->layout    = 'application.views.layouts.main_breadcrumbs';
        $view            = AgencyFilesView::forUser(currentUser());
        $view->companyId = $companyId;
        $this->render('upload', ['view' => $view]);
    }

    /**
     * @param int|null $companyId
     *
     * @throws CHttpException
     */
    public function actionAjaxupload(int $companyId = null)
    {
        $uploadForm = AgencyFilesUploadForm::forUser(currentUser());
        if ($companyId !== null) {
            $uploadForm->setCompanyId($companyId);
        }

        if (!empty($_FILES)) {
            $uploadForm->setFileData($_FILES['document'] ?? $_FILES['file'] ?? null);
        }
        $uploadForm->uploadFiles();

        if ($uploadForm->getUploadSuccess()) {
            $fileId     = $uploadForm->getFileId();
            $agencyFile = AgencyFiles::model()->forAgencyOfUser(currentUser())->findByAttributes(['id' => $fileId]);
            echo CJavaScript::jsonEncode(
                [
                    'fileId'               => $fileId,
                    'loadForm'             => true,
                    'initialPreview'       => [
                        $this->createUrl('/agencyCompanyFiles/download/', ['id' => $fileId]),
                    ],
                    'initialPreviewConfig' => [
                        [
                            'caption' => $agencyFile->name,
                            'type'    => $uploadForm->getFileType(),
                        ],
                    ],
                ]);
            Yii::app()->end();
        }
        if ($uploadForm->isFileValid()) {
            echo CJavaScript::jsonEncode(['error' => 'Fehler: Datei konnte nicht Hochgeladen werden - Der Dateiname sollte maximal 150 Zeichen lang sein.']);
        } else {
            echo CJavaScript::jsonEncode(['error' => 'Fehler: Datei konnte nicht Hochgeladen werden']);
        }
        Yii::app()->end();
    }

    public function actionAjaxedit()
    {
        $fileId    = Yii::app()->request->getParam('fileId');
        $previewId = Yii::app()->request->getParam('previewId');
        $model     = new AgencyFilesEditForm();
        /**
         * @var ClientFiles $clientFile
         */
        $file = AgencyFiles::model()->forAgencyOfUser(currentUser())->findByAttributes(['id' => $fileId]);
        if ($file === null) {
            throw new CHttpException(404, 'Eintrag nicht gefunden');
        }
        $model->setFile($file);
        $this->renderAjax('updateajax', [
            'form'      => $model,
            'newFile'   => true,
            'previewId' => $previewId,
        ]);
    }

    /**
     * @param int|null $companyId
     */
    public function actionAjaxUpdate(int $companyId = null): void
    {
        $agencyId = currentUser()->agency_id;
        $this->updateFiles(AgencyFiles::model()->findAllByAttributes(['agency_id' => $agencyId, 'id' => array_keys($_POST)]), $_POST);
        $this->redirect(['list', 'companyId' => $companyId]);
    }

    /**
     * @param AgencyFiles[] $files
     * @param array         $data
     */
    private function updateFiles(array $files, array $data)
    {
        foreach ($files as $file) {
            $filesData = $data[$file->id] ?? [];
            if (!empty($filesData['document_type_id'])) {
                $file->document_type_id = $filesData['document_type_id'];
            }
            if (!empty($filesData['upload_date'])) {
                $file->upload_date = UtilHelper::parseToDBDate($filesData['upload_date']);
            }
            $file->save();
        }
    }

    public function actionUpdate(int $id)
    {
        $this->layout = 'application.views.layouts.main_breadcrumbs';
        $model        = $this->loadModel($id);
        $view         = new AgencyFilesEditForm();
        $view->setFile($model);

        $this->render('update', ['view' => $view]);
    }

    /**
     * @param $id
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['UploadLogo']);
        }
    }
}
