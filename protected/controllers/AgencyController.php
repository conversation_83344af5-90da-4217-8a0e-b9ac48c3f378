<?php

declare(strict_types=1);

class AgencyController extends RightController
{
    use RenderJsonTrait;

    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout           = '//layouts/column2';

    private $prefix = 'Agency';

    public $excludedActions = [
        'list',
    ];

    /**
     * Die Methode ermittelt alle Systemuser, die hierarchisch unter dem angemeldeten Systemsuer stehen und
     * gibt die eine Condition als String zurück, mit der eine SQL-Anfrage gestellt werden kann
     *
     * @param String $attributename
     *                  Name des Attributes, für das die IDs der Systemsuer geprüft werden soll.
     *                  Defaultwert ist hier 'user_id', da dies meist der Name des Attributes ist
     *
     * @return String
     *          Eine SQL-Condition als String
     */
    public static function getUnderlingsCondition($attributename = 'user_id')
    {
        if (Yii::app()->user->isAdmin()) {
            return '(1)';
        }

        $ids = Yii::app()->user->getUnderlingsIds();
        if ($ids === []) {
            return '1=0';
        }

        $idList = implode(',', $ids);

        return "{$attributename} IN ({$idList})";
    }

    public static function getParentsCondition($attributename = 'user_id')
    {
        if (Yii::app()->user->isAdmin()) {
            return '(' . $attributename . ' = ' . Yii::app()->user->getId() . ')';
        }

        $ids = self::getParentIDs();

        $condition = '(' . $attributename . ' IN (' . implode(',', $ids) . '))';

        if (!empty($condition)) {
            $condition = '(' . $condition . ')';
        }

        return $condition;
    }

    public static function getParentIDs()
    {
        $systemuser = Yii::app()->user->getSystemuserObject();
        $ids        = [];
        $ids[]      = $systemuser->id;

        while (
            !$systemuser->user_role != UserRole::MAINBROKER
            && !empty($systemuser->parent)
            && $systemuser->parent_id != $systemuser->id
        ) {
            $systemuser = $systemuser->parent;
            $ids[]      = $systemuser->id;
        }

        return $ids;
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein Data-Array für die Agency handelt
     *
     * @param array $data
     *              $Data-Array
     *
     * @return bool
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        return isset($data) &&
               isset($data['Agency']) &&
               isset($data['Agency']['name']) &&
               ($data['Agency']['name'] != '');
    }

    public function actionView($id)
    {
        $model = $this->loadModel($id);
        $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Lädt das Agency-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $agency
     *          Das Exemplar der Agency mit der übergebenen ID falls dieses existiert, sonst null
     */
    public function loadModel($id)
    {
        if ((int) currentUser()->user_role !== UserRole::ADMIN && (int) currentUser()->agency_id !== (int) $id) {
            throw new CHttpException(404);
        }

        $model = Agency::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404);
        }

        return $model;
    }

    /**
     *  Diese Methode wird aufgerufen wenn eine Agency aktualisiert werden soll.
     *  Die Methode öffnet das Agency-Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden Agency
     */
    public function actionUpdate($id, $fromuser = null)
    {
        $agency = $this->loadModel($id);

        if ($agency != null) {
            $data = $this->getUpdateFormularData($id, $fromuser);
            if (isset($data['Agency']['update'])) {
                if (isset($data[$this->prefix])) {
                    $data = $data[$this->prefix];
                }
                if ($this->validData($data)) {
                    if (($agency = $this->update($data)) != null) {
                        Yii::app()->user->setFlash('success', 'Änderungen wurden gespeichert');
                        $this->redirect(['update?id=' . $agency->id . '&fromuser=' . $fromuser]);
                    }
                    Yii::app()->user->setFlash('error', 'Änderungen konnten nicht gespeichert');
                    $this->redirect(['systemsuer/admin']);
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Öffnet das Agency-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $data
     *          die ID des zu aktualisierenden Agency-Exemplares
     *
     * @return $agency
     *          Das aktualierte Data-Array
     */
    private function getUpdateFormularData($id, $fromuser)
    {
        $agency = $this->loadModel($id);
        if (!isset($_POST[$this->prefix]) || !$this->validData($_POST[$this->prefix])) {
            $this->render('update', ['agency' => $agency, 'prefix' => $this->prefix, 'fromuser' => $fromuser]);

            return null;
        }

        $formulardata = $_POST;

        $formulardata[$this->prefix]['Agency']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein Agency-Exemplar
     *
     * @param type $data
     *          Die Agency-Daten als Array
     *
     * @return $agency
     *          Das aktualisierte Agency-Exemplar
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $agency = $this->loadModel($data['Agency']['id']);

            $data['Address']['id'] = $agency->address_id;
            $addressController     = new AddressController('address');
            $addressController->update($data);

            $data['Contact']['id'] = $agency->contact_id;
            $contactController     = new ContactController('contact');
            $contactController->update($data);

            //            if ($addressController->update($data) != null && $contactController->update($data) != null)
            //            {
            $agency->attributes = $data['Agency'];
            $agency->reg_nr     = @$data['Agency']['reg_nr'];
            if ($agency->save()) {
                return $agency;
            }
        }

        return null;
    }

    public function actionAdmin()
    {
        $model = new Agency('search');
        $model->unsetAttributes(); // clear any default values
        if (isset($_GET['Agency'])) {
            $model->attributes = $_GET['Agency'];
        }

        $this->render('admin', [
            'model' => $model,
        ]);
    }

    /**
     * @param $id
     *
     * @return bool
     *
     * @throws CDbException
     * @throws CHttpException
     */
    public function actionDelete($id): bool
    {
        $model = $this->loadModel($id);

        $this->cancelIfUserNotAllowed();
        $this->cancelIfAgencyUsersExist($model);

        try {
            User::model()->deleteAllByAttributes(['agency_id' => $id, 'deleted' => 1]);

            return $model->delete();
        } catch (Exception $exception) {
            self::renderJsonResponse(
                'Blockiert: Diese Aktion konnte nicht durchgeführt werden.',
                500
            );
        }

        return false;
    }

    private function cancelIfUserNotAllowed(): void
    {
        if (Yii::app()->user->isAdmin()) {
            return;
        }

        self::renderJsonResponse(
            'Blockiert: Sie haben keine Berechtigung, diese Aktion durchzuführen.',
            403
        );
    }

    /**
     * @param Agency $model
     *
     * @return void
     */
    private function cancelIfAgencyUsersExist(Agency $model): void
    {
        $agencyUsers = $model->getUsers();

        if (count($agencyUsers) === 0) {
            return;
        }

        $userLabelFunc = static fn (User $agencyUser): string => sprintf('%s (%d)', $agencyUser->getFullname(), (int) $agencyUser->id);

        $usersList = array_map($userLabelFunc, $agencyUsers);

        self::renderJsonResponse(
            sprintf('Blockiert: es existieren noch Benutzer in der Firma: %s', implode($usersList)),
            403
        );
    }

    /**
     * Erstellt die Form
     */
    public function createForm($form, $data)
    {
        $this->render($form, [
            'agency' => $data,
        ]);
    }

    public function actionDublicates()
    {
        $model = new Agency('search');
        $model->unsetAttributes(); // clear any default values
        if (isset($_GET['Agency'])) {
            $model->attributes = $_GET['Agency'];
        }

        $this->render('admin', [
            'model'         => $model,
            'DDataProvider' => $model->searchDublicates(),
        ]);
    }

    public function actionRechtsform()
    {
        $model = new Agency('search');
        $model->unsetAttributes(); // clear any default values
        if (isset($_GET['Agency'])) {
            $model->attributes = $_GET['Agency'];
        }

        $this->render('admin', [
            'model'         => $model,
            'DDataProvider' => $model->searchRechtsformen(),
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Agency $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'agency-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }

    /**
     * @param null $q
     * @param null $id
     * @param int  $limit
     */
    public function actionList($q = null, $id = null, $limit = 50)
    {
        $criteria        = new CDbCriteria();
        $criteria->limit = $limit;
        if (!Yii::app()->user->isAdmin()) {
            $criteria->addInCondition('id', [currentUser()->agency_id]);
        }
        if ($q !== null && $q !== '') {
            $criteria->compare('name', $q, true);
        }
        if ($id !== null) {
            $criteria->compare('id', $id);
        }
        $models = Agency::model()->findAll($criteria);
        $data   = [];
        foreach ($models as $model) {
            $data[] = [
                'id'   => $model->id,
                'text' => $model->name . (Yii::app()->user->isAdmin() ? ' (' . $model->id . ')' : ''),
            ];
        }
        $this->renderJson($data);
    }
}
