<?php

class AgencyFilesController extends FileController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * @param $id
     *
     * @return AgencyFiles
     */
    public function loadModel($id)
    {
        $criteria = new CDbCriteria();
        //Dokumente, die zu einer Gesellschaft abgelegt wurden sollen hier nicht heruntergeladen werden können
        $criteria->addCondition('insurance_company_id is null');
        $criteria->compare('id', $id);

        return AgencyFiles::model()->find($criteria);
    }

    /**
     * @param $id
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['UploadLogo']);
        }
    }

    public function actionUploadLogo()
    {
        $this->redirect('/konfiguration/firmenlogo');
    }

    /**
     * @param      $id
     * @param bool $download
     *
     * @return mixed|void
     * @throws CException
     * @throws CHttpException
     * @throws \League\Flysystem\FileNotFoundException
     */
    public function actionDownload($id, $download = true)
    {
        $file       = $this->loadModel($id);
        $filesystem = $file->getFilesystem();
        if (!$filesystem->has($file->name)) {
            throw new CHttpException(404, 'Logo nicht vorhanden');
        }
        Yii::import('ext.EDownloadHelper');
        $filename = $file->name;
        EDownloadHelper::downloadResource(
            $filesystem->readStream($filename),
            $filename,
            $filesystem->getSize($filename),
            $filesystem->getMimetype($filename)
        );
        Yii::app()->end();
    }
}
