<?php

class AgencyJuristicPersonController extends GlobalRightController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * <PERSON><PERSON>dt das AgencyJuristicPerson-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des AgencyJuristicPerson-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = AgencyJuristicPerson::model()->findByPk($id);

        return $model;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues AgencyJuristicPerson-Model erstellt werden soll.
     *  Die Methode öffnet das AgencyJuristicPerson-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();

        if ($this->validData($data)) {
            if ($model = $this->create($data) != null) {
                //echo "Das Erstellen war erfolgreich";
                //ToDo -> View aktualiseren
                $this->redirect('admin');

                return $model;
            }

            return null;
        }
    }

    /**
     *  Öffnet das AgencyJuristicPerson -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des AgencyJuristicPerson -Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $model        = new AgencyJuristicPerson();

        if (!$this->validData($_POST)) {
            $this->render('create', ['model' => $model]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model AgencyJuristicPerson handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['AgencyJuristicPerson']) &&
            (isset($data['AgencyJuristicPerson']['juristic_person_id']) &&
                $data['AgencyJuristicPerson']['juristic_person_id'] != '') ||
            (isset($data['AgencyJuristicPerson']['user_id']) &&
                $data['AgencyJuristicPerson']['user_id'] != '');
//
    }

    /**
     * Diese Methode erstellt ein neues AgencyJuristicPerson -Model
     *
     * @param type $data
     *          Die AgencyJuristicPerson -Daten als Array
     *
     * @return $model
     *          Das erstellte AgencyJuristicPerson -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $agencyJuristicPerson             = new AgencyJuristicPerson();
        $agencyJuristicPerson->attributes = $data['AgencyJuristicPerson'];

        if ($agencyJuristicPerson->save()) {
            return $agencyJuristicPerson;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein AgencyJuristicPerson -Model aktualisiert werden soll.
     *  Die Methode öffnet das AgencyJuristicPerson -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden AgencyJuristicPerson -Models
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($model = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $model;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das AgencyJuristicPerson-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden AgencyJuristicPerson-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('update', ['model' => $model]);
        }

        $formulardata                               = $_POST;
        $formulardata['AgencyJuristicPerson']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein AgencyJuristicPerson-Exemplar
     *
     * @param type $data
     *          Die AgencyJuristicPerson-Daten als Array
     *
     * @return $model
     *          Das aktualisierte AgencyJuristicPerson-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $agencyJuristicPerson             = $this->loadModel($data['AgencyJuristicPerson']['id']);
            $agencyJuristicPerson->attributes = $data['AgencyJuristicPerson'];
            if ($model->save()) {
                return $agencyJuristicPerson;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new AgencyJuristicPerson('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['AgencyJuristicPerson'])) {
            $model->attributes = $_GET['AgencyJuristicPerson'];
        }

        $this->render('admin', [
            'model' => $model,
        ]);
    }

    /**
     * Löscht das AgencyJuristicPerson-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden AgencyJuristicPerson-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
//        if (!isset($_GET['ajax']))
//                $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $success = $this->loadModel($id)->delete();

        return $success;
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('AgencyJuristicPerson');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param AgencyJuristicPerson $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'agency-juristic-person-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
