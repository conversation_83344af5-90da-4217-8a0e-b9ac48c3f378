<?php

use Business\Ansprechpartner\AnsprechpartnerSuche;
use Components\Behaviors\DoubleBehavior;
use Components\Client\Providers\ClientfileProvider;

/**
 * This Controller ist managing Ajax-Requests. It returns HTML-Code.
 */
class AjaxHtmlController extends CController
{
    use RenderJsonTrait;

    /**
     * @return array[]
     */
    public function filters()
    {
        return [
            'accessControl',
        ];
    }

    public function accessRules()
    {
        return [
            [
                'allow', // allow no auth users to do bank ajax
                'actions' => ['GetBanknameAndBLZ', 'GetKntnrAndBlzFromIBAN'],
                'users'   => ['*'],
            ],
            [
                'allow', // allow auth users to do all
                'users'   => ['@'],
            ],
            [
                'deny', // fail safe
            ],
        ];
    }

    public function actionGetJuristicPerson(): void
    {
        if (isset($_POST['counter'])) {
            $counter = $_POST['counter'];
        } else {
            $counter = 0;
        }
        if (isset($_POST['prefix'])) {
            $prefix = $_POST['prefix'];
        } else {
            $prefix = '';
        }
        //$data = $_POST;
        //        echo 'test';

        $this->renderFile(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'juristicPerson' . DIRECTORY_SEPARATOR . '_formFields.php',
            [
                'juristicperson' => new JuristicPerson(),
                'prefix'         => $prefix,
                'counter'        => $counter,
                'form'           => new CActiveForm(),
            ]);
        //        $this->render(Yii::app()->basePath . '\views\juristicPerson\_formFields.php',
        //                array('juristicperson' => new JuristicPerson(), 'form'           => ''));
        //$this->render('juristicPerson/_formfields', array('juristicPerso'))
        Yii::app()->end();
    }

    public function actionGetBankname(): void
    {
        $bankcode = $_POST['bankcode'];
        $bankname = MiscBanknumber::model()->findByAttributes(['banknumber' => $bankcode]);
        if ($bankname !== null) {
            echo CHtml::encode($bankname->bankname);
        }
        Yii::app()->end();
    }

    /**
     * Prüft, ob eine übergebene ProductCombo-ID ein Unterprodukt von bestimmten anderen ProductCombos ist
     */
    public function actionCheckProductCombo(): void
    {
        $product_combo_id = Yii::app()->request->getPost('product_combo_id');
        $check            = Yii::app()->request->getPost('check');

        if (!empty($check)) {
            $productCombo = ProductCombo::model()->findByPk($product_combo_id);
            if (!empty($productCombo)) {
                $checks = ProductCombo::model()->findAllByPk($check);
                foreach ($checks as $c) {
                    if ($productCombo->isChildOf($c)) {
                        echo 1;
                        Yii::app()->end();
                    }
                }
            }
        }
        echo 0;
        Yii::app()->end();
    }

    public function actionGetNotificationDate($user_id, $procedure_id, $mail): void
    {
        $days = ProcedureNotificationDefaultValue::model()->getDays($user_id, $procedure_id, $mail);
        echo date('d.m.Y', strtotime('+' . $days . ' days'));
        Yii::app()->end();
    }

    /**
     * Erstellt eine Link per Ajax
     */
    public function actionCreateInsuranceCompanyLink(): void
    {
        Yii::import('stammdaten.modules.gesellschaften.controllers.InsuranceCompanyLinkController');
        $linkController = new InsuranceCompanyLinkController('link');

        $data = $this->getInsuranceCompanyLinkDataFromPost();

        //        print_r ($data);
        $linkController->createMultiple($data);
        Yii::app()->end();
    }

    /**
     * Generiert ein Data-Array für den Linkcontroller aus dem $_POST-Array
     *
     * @return array Data-Array für den Linkcontroller
     */
    private function getInsuranceCompanyLinkDataFromPost()
    {
        $data = [
            'InsuranceCompanyLink' => [
                'product_combo_id' => $_POST['product_combo_id'],
                'company_id'       => $_POST['company_id'],
                'direct_login'     => $_POST['direct_login'] ?? null,
                'link_type'        => $_POST['link_type'] ?? null,
                'link_url'         => $_POST['link_url'] ?? null,
                'password'         => $_POST['password'] ?? null,
                'global_password'  => $_POST['global_password'] ?? null,
                'global_username'  => $_POST['global_username'] ?? null,
            ]
        ];

        return $data;
    }

    /**
     * Updatet eine Link per Ajax
     */
    public function actionUpdateInsuranceCompanyLink(): void
    {
        Yii::import('stammdaten.modules.gesellschaften.controllers.InsuranceCompanyLinkController');
        $linkController = new InsuranceCompanyLinkController('link');

        $data = $this->getInsuranceCompanyLinkDataFromPost();

        $data['InsuranceCompanyLink']['id'] = $_POST['id'];
        $linkController->update($data);
        Yii::app()->end();
    }

    /**
     * Erstellt eine JuristicPerson per Ajax
     */
    public function actionCreateJuristicPerson(): void
    {
        $juristicpersonController = new JuristicPersonController('juristicperson');

        $data = $this->getJuristicPersonDataFromPost();

        $juristicpersonController->create($data);
        Yii::app()->end();
    }

    private function getJuristicPersonDataFromPost()
    {
        $data                                      = [];
        $data['JuristicPerson']['firstname']       = $_POST['firstname'];
        $data['JuristicPerson']['lastname']        = $_POST['lastname'];
        $data['JuristicPerson']['birthday']        = $_POST['birthday'];
        $data['JuristicPerson']['ihk_reg_nr']      = $_POST['ihk_reg_nr'];
        $data['Address']['street']                 = $_POST['street'];
        $data['Address']['nr']                     = $_POST['nr'];
        $data['Address']['zip']                    = $_POST['zip'];
        $data['Address']['city']                   = $_POST['city'];
        $data['Address']['country_id']             = $_POST['country_id'];
        $data['AgencyJuristicPerson']['agency_id'] = $_POST['agency_id'];

        return $data;
    }

    /**
     * Erstellt eine JuristicPerson per Ajax
     */
    public function actionUpdateJuristicPerson(): void
    {
        $juristicpersonController = new JuristicPersonController('juristicperson');

        $data = $this->getJuristicPersonDataFromPost();

        $data['JuristicPerson']['id'] = $_POST['id'];
        $juristicpersonController->update($data);

        Yii::app()->end();
    }

    /**
     * Erstellt eine BrokerId per Ajax
     */
    public function actionCreateBrokerId(): void
    {
        $brokeridController                       = new BrokerIdController('brokerid');
        $data                                     = [];
        $data['BrokerId']['user_id']              = $_POST['user_id'];
        $data['BrokerId']['insurance_company_id'] = $_POST['insurance_company_id'];
        $data['BrokerId']['brokerid']             = $_POST['brokerid'];
        $data['BrokerId']['type_id']              = $_POST['type_id'];
        $data['BrokerId']['status']               = $_POST['status'];
        $data['BrokerId']['status_info']          = $_POST['status_info'];
        $data['BrokerId']['sparten']              = is_array($_POST['sparten']) ? $_POST['sparten'] : [];
        $data['BrokerId']['is_pool']              = (int) $_POST['is_pool'];
        $data['BrokerId']['is_verkettet']         = (int) $_POST['is_verkettet'];
        $brokeridController->create($data);
        Yii::app()->end();
    }

    public function actionCreateContractUserIdManualAssignment(): void
    {
        $contractUserIdManualAssigmentController               = new ContractUserIdManualAssignmentController('ContractUserIdManualAssignment');
        $data                                                  = [];
        $data['ContractUserIdManualAssignment']['user_id']     = $_POST['user_id'];
        $data['ContractUserIdManualAssignment']['contract_nr'] = $_POST['contract_nr'];
        $contractUserIdManualAssigmentController->create($data);
        Yii::app()->end();
    }

    public function actionGetClientFilesDropDownData($order = 'name'): void
    {
        if (empty($_POST['client_id'])) {
            Yii::app()->end();
        }

        $allowedOrders = ['name', 'upload_date DESC'];
        $order         = in_array($order, $allowedOrders) ? $order : 'name';

        $order .= ', t.id DESC';

        $clientFilesData = CHtml::listData(
            ClientfileProvider::forUser(currentUser())->find()->findAllByAttributes(
                ['client_id' => $_POST['client_id']],
                ['order' => $order]), 'id', 'DetailedName');

        foreach ($clientFilesData as $key => $value) {
            echo '<option value="' . $key . '">' . $value . '</option>';
        }
        Yii::app()->end();
    }

    public function actionGetUserFilesDropDownData(): void
    {
        if (!empty($_POST['user_id'])) {
            $userFilesData = CHtml::listData(UserFiles::model()->findAllByAttributes(['user_id' => $_POST['user_id']]),
                'id', 'DetailedName');

            foreach ($userFilesData as $key => $value) {
                echo '<option value="' . $key . '">' . $value . '</option>';
            }
        }
        Yii::app()->end();
    }

    /**
     * Bearbeitet eine BrokerId per Ajax
     */
    public function actionUpdateBrokerId(): void
    {
        $brokeridController                       = new BrokerIdController('brokerid');
        $data                                     = [];
        $data['BrokerId']['id']                   = $_POST['id'];
        $data['BrokerId']['user_id']              = $_POST['user_id'];
        $data['BrokerId']['insurance_company_id'] = $_POST['insurance_company_id'];
        $data['BrokerId']['brokerid']             = $_POST['brokerid'];
        $data['BrokerId']['type_id']              = $_POST['type_id'];
        $data['BrokerId']['status']               = $_POST['status'];
        $data['BrokerId']['status_info']          = $_POST['status_info'];
        $data['BrokerId']['sparten']              = is_array($_POST['sparten']) ? $_POST['sparten'] : [];
        $data['BrokerId']['is_pool']              = (int) $_POST['is_pool'];
        $data['BrokerId']['is_verkettet']         = (int) $_POST['is_verkettet'];
        $brokeridController->update($data);
        Yii::app()->end();
    }

    /**
     * Erstellt eine MailUserTemplate
     */
    public function actionCreateMailUserTemplate(): void
    {
        $data                                          = [];
        $data['MailUserTemplate']['name']              = $_POST['template_name'];
        $data['MailUserTemplate']['description']       = $_POST['template_description'];
        $data['MailUserTemplate']['mail_procedure_id'] = $_POST['mail_procedure_id'];
        if (!empty($_POST['product_combo_id'])) {
            $data['MailUserTemplate']['product_combo_id'] = $_POST['product_combo_id'];
        }
        $data['MailUserTemplate']['reciever'] = $_POST['reciever'];
        $data['Mail']['subject']              = $_POST['subject'];
        $data['Mail']['content']              = $_POST['content'];
        $data['Mail']['bcc']                  = $_POST['bcc'];

        $mailusertemplateController = new MailUserTemplateController('mailusertemplate');
        $mailusertemplate           = $mailusertemplateController->create($data);
        if (!empty($mailusertemplate)) {
            echo 'success';
        } else {
            echo 'failed';
        }
        Yii::app()->end();
    }

    /**
     * Erstellt eine MailProcedure
     */
    public function actionCreateMailProcedure(): void
    {
        $data                                                   = [];
        $data['MailProcedure']['name']                          = $_POST['name'];
        $data['MailProcedure']['support_assignment_subject_id'] = $_POST['support_assignment_subject_id'];
        $mailprocedureController                                = new MailProcedureController('mailprocedure');
        $mailprocedureController->create($data);
        Yii::app()->end();
    }

    public function actionUpdateMailProcedure(): void
    {
        $data                                                   = [];
        $data['MailProcedure']['name']                          = $_POST['name'];
        $data['MailProcedure']['id']                            = $_POST['id'];
        $data['MailProcedure']['support_assignment_subject_id'] = $_POST['support_assignment_subject_id'];
        $mailprocedureController                                = new MailProcedureController('mailprocedure');
        $mailprocedureController->update($data);
        Yii::app()->end();
    }

    /**
     * Gibt ein Array zurück, was abhängig von dem User und dem ausgewählen Vorgang die Templates bereit stellt
     *
     * @return void
     */
    public function actionGetMailTemplatesDropDownData(): void
    {
        echo '<option value="">&#160;</option>';
        if (!empty($_POST['UserMail']['UserMail']['user_id']) && !empty($_POST['UserMail']['UserMail']['mail_procedure_id'])) {
            $mailProcedureId = $_POST['UserMail']['UserMail']['mail_procedure_id'];
            $criteria        = new CDbCriteria();
            $criteria->addInCondition('user_id', Yii::app()->user->getPermissions()->getUserIdsInSameAgency());
            if (!is_array($mailProcedureId)) {
                $mailProcedureId = [$mailProcedureId];
            }
            $criteria->addInCondition('mail_procedure_id', $mailProcedureId);

            $demvtemplates = MailDemvTemplate::model()->findAllByAttributes(['mail_procedure_id' => $mailProcedureId], ['order' => 'id DESC']);
            $usertemplates = MailUserTemplate::model()->findAll($criteria);

            if (!empty($demvtemplates)) {
                echo '<optgroup label="Standard-Vorlagen">';
                foreach ($demvtemplates as $dt) {
                    echo '<option value="MailDemvTemplate_' . $dt->id . '">' . $dt->name . '</option>';
                }
                echo '</optgroup>';
            }

            if (!empty($usertemplates)) {
                echo '<optgroup label="Eigene Vorlagen">';
                foreach ($usertemplates as $ut) {
                    echo '<option value="MailUserTemplate_' . $ut->id . '">' . $ut->name . '</option>';
                }
                echo '</optgroup>';
            }
        }
        Yii::app()->end();
    }

    /**
     *
     */
    public function actionGetWritingTemplatesDropDownData(): void
    {
        echo '<option value="">&#160;</option>';
        if (!empty($_POST['UserWriting']['user_id']) && !empty($_POST['UserWriting']['writing_procedure_id'])) {
            $writingProcedureId = (int) $_POST['UserWriting']['writing_procedure_id'];
            $productComboId     = $_POST['UserWriting']['product_combo_id'] ?? null;

            $criteria = new CDbCriteria();
            $criteria->compare('writing_procedure_id', $writingProcedureId);
            if (!empty($productComboId)) {
                $criteria->addCondition('product_combo_id = :pcid or product_combo_id is null');
                $criteria->params[':pcid'] = $productComboId;
            }
            $demvCriteria = clone $criteria;
            $demvCriteria->addCondition('user_id is null');
            $demvtemplates = WritingTemplate::model()->findAll($demvCriteria);
            $criteria->addInCondition('user_id', Yii::app()->user->getPermissions()->getUserIdsInSameAgency());
            $usertemplates = WritingTemplate::model()->findAll($criteria);

            if (!empty($demvtemplates)) {
                echo '<optgroup label="Standard-Vorlagen">';
                foreach ($demvtemplates as $dt) {
                    echo '<option value="WritingTemplate_' . $dt->id . '">' . $dt->name . '</option>';
                }
                echo '</optgroup>';
            }

            if (!empty($usertemplates)) {
                echo '<optgroup label="Eigene Vorlagen">';
                foreach ($usertemplates as $ut) {
                    echo '<option value="WritingTemplate_' . $ut->id . '">' . $ut->name . '</option>';
                }
                echo '</optgroup>';
            }
        }
        Yii::app()->end();
    }

    public function actionGetWritingTemplateData(): void
    {
        $returnArray = [];
        if (isset($_POST['mixedid'])) {
            $mixedid = $_POST['mixedid'];
            if (strpos($mixedid, 'WritingTemplate') !== false) {
                $id = substr($mixedid, 16);
                /** @var WritingTemplate $template */
                $template = WritingTemplate::model()->findByPk($id);
                if (null !== $template) {
                    $returnArray['content']        = $template->content;
                    $returnArray['reciever']       = $template->reciever;
                    $returnArray['writing_header'] = $template->writing_header;
                }
            } elseif (strpos($mixedid, 'Sent') !== false) {
                $id = substr($mixedid, 5);
                /** @var UserWriting $template */
                $template = UserWriting::model()->findByPk($id);
                if (null !== $template) {
                    $returnArray['content']  = $template->content;
                    $returnArray['reciever'] = 0;
                }
            }
        }
        echo function_exists('json_encode') ? json_encode($returnArray) : CJSON::encode($returnArray);
        Yii::app()->end();
    }

    /**
     * Gibt die Werte des Templates abhängig von der übergebenen ID zurück
     *
     */
    public function actionGetMailTemplateData(): void
    {
        $returnArray            = [];
        $returnArray['subject'] = '';
        $returnArray['content'] = '';
        $returnArray['bcc']     = '';
        if (!isset($_POST['mixedid'])) {
            Yii::app()->end();
        }
        $mixedid = $_POST['mixedid'];

        if (strpos($mixedid, 'Sent') !== false) {
            $id       = substr($mixedid, 5);
            $template = UserMail::model()->findByPk($id);
            if (null !== $template) {
                $returnArray['subject'] = $template->mail->subject;
                $returnArray['content'] = $template->mail->getContent();
                $returnArray['bcc']     = ViewHelper::parseArrayToString($template->mail->getBccs(), ';');
            }
            echo function_exists('json_encode') ? json_encode($returnArray) : CJSON::encode($returnArray);
            Yii::app()->end();
        }
        $template = null;
        if (strpos($mixedid, 'MailDemvTemplate') !== false) {
            $id       = substr($mixedid, 17);
            $template = MailDemvTemplate::model()->findByPk($id);
        } elseif (strpos($mixedid, 'MailUserTemplate') !== false) {
            $id                 = substr($mixedid, 17);
            $template           = MailUserTemplate::model()->findByPk($id);
            $returnArray['bcc'] = ViewHelper::parseArrayToString($template->mail->getBccs(), ';');
        }

        if ($template !== null) {
            $templateData = new TemplateData($template);
            if ((int) $template->reciever === MailHelper::RECEIVER_CLIENT) {
                /** @var Client $client */
                $client = ClientProvider::forUser(currentUser())->mask404()->mask403()->load($_POST['clientId'] ?? null);
                if (!empty($client->informal)) {
                    $templateData->informal();
                }
            }
            $returnArray['subject'] = $templateData->getSubject();
            $returnArray['content'] = $templateData->getContent();

            $returnArray['reciever'] = $template->reciever;
        }

        echo function_exists('json_encode') ? json_encode($returnArray) : CJSON::encode($returnArray);
        Yii::app()->end();
    }

    /**
     * Gibt die Email des Clienten zurück
     */
    public function actionGetClientEMail(): void
    {
        if (isset($_POST['clientid']) && $_POST['clientid'] != '') {
            $client = Client::model()->findByPk($_POST['clientid']);
            echo (null !== $client) ? $client->email : '';
        } else {
            echo 'Kunde wählen';
        }
        Yii::app()->end();
    }

    /**
     * Gibt die E-Mail des jeweiligen Anpsrechpartners zurück
     */
    public function actionGetUserEMail(): void
    {
        if (isset($_POST['user_id'])) {
            $systemuser = Systemuser::model()->personalAccess()->findByPk($_POST['user_id']);
            if (!empty($systemuser)) {
                echo $systemuser->getMailAddress();
            }
        }
    }

    public function actionGetInsuranceCompanyEmail(): void
    {
        if (isset($_POST['insurance_company_id']) && $_POST['insurance_company_id'] != '') {
            $insurancecompanyid = $_POST['insurance_company_id'];

            $productcomboid = null;
            if (isset($_POST['product_combo_id']) && $_POST['product_combo_id'] != '') {
                $productcomboid = $_POST['product_combo_id'];
            }

            $subjectid = null;

            if (isset($_POST['mail_procedure_id']) && $_POST['mail_procedure_id'] != '') {
                $mailprocedure = MailProcedure::model()->findByPk($_POST['mail_procedure_id']);
                if (null !== $mailprocedure) {
                    $subjectid = $mailprocedure->support_assignment_subject_id;
                }
            }
            $contactperson = AnsprechpartnerSuche::new($insurancecompanyid, User::model()->findByPk($_POST['user_id']))
                                                 ->forProductComboId($productcomboid)
                                                 ->forSubject($subjectid)
                                                 ->one();
            if (null !== $contactperson) {
                echo $contactperson->contact->email;
            } else {
                echo 'Keine Kontaktperson gefunden';
            }
        } else {
            echo 'Gesellschaft wählen';
        }
        Yii::app()->end();
    }

    /**
     * Gibt die Address der first-contact-person zurück
     */
    public function actionGetInsuranceCompanyAddress(): void
    {
        $data = [];
        if (isset($_POST['insurance_company_id']) && $_POST['insurance_company_id'] != '') {
            $contactperson = AnsprechpartnerSuche::new($_POST['insurance_company_id'])->ersterAnsprechpartner();
            if (!empty($contactperson)) {
                $address = $contactperson->getAddress();
                if (!empty($address)) {
                    $data                  = [];
                    $data['reciever_name'] = $contactperson->firstname . ' ' . $contactperson->lastname;
                    $data['street']        = $address->street;
                    $data['nr']            = $address->nr;
                    $data['zip']           = $address->zip;
                    $data['city']          = $address->city;
                    $data['country_id']    = $address->country_id;
                    $data['fax']           = '';
                    if (!empty($contactperson->contact->fax)) {
                        $data['fax'] = $contactperson->contact->fax;
                    }
                }
            }
        }
        echo function_exists('json_encode') ? json_encode($data) : CJSON::encode($data);
        Yii::app()->end();
    }

    public function actionGetDemvAddress(): void
    {
        $data = MailHelper::getDEMVAddress();
        echo function_exists('json_encode') ? json_encode($data) : CJSON::encode($data);
        Yii::app()->end();
    }

    /**
     * Gibt die Adresse eine Clients zurück
     */
    public function actionGetClientAddress(): void
    {
        $data = [];
        if (isset($_POST['client_id']) && $_POST['client_id'] != '') {
            $client = Client::model()->findByPk($_POST['client_id']);
            if (null !== $client && null !== $client->address) {
                $address                     = $client->address;
                $salutation                  = in_array($client->salutation_id,
                    [Salutation::$mr, Salutation::$mrs]) ? $client->salutation->name . ' ' : '';
                $title                       = !empty($client->title) ? $client->title . ' ' : '';
                $data                        = [];
                $data['reciever_name']       = $salutation . $title . $client->Fullname;
                $data['reciever_name_affix'] = $client->name_addition;
                $data['street']              = $address->street;
                $data['nr']                  = $address->nr;
                $data['zip']                 = $address->zip;
                $data['city']                = $address->city;
                $data['country_id']          = $address->country_id ?? Country::DEUTSCHLAND;
                $data['addition']            = $address->addition;
            }
        }
        echo function_exists('json_encode') ? json_encode($data) : CJSON::encode($data);
        Yii::app()->end();
    }

    public function actionGetUserAddress(): void
    {
        $user = Yii::app()->user->getSystemuserObject();
        if (null !== $user && null !== $user->address) {
            $address               = $user->address;
            $data                  = [];
            $data['reciever_name'] = $user->Fullname;
            $data['street']        = $address->street;
            $data['nr']            = $address->nr;
            $data['zip']           = $address->zip;
            $data['city']          = $address->city;
            $data['country_id']    = $address->country_id;
            echo function_exists('json_encode') ? json_encode($data) : CJSON::encode($data);
        }
        Yii::app()->end();
    }

    /**
     * Erstellt eine MailProcedure
     */
    public function actionCreateWritingProcedure(): void
    {
        $data                             = [];
        $data['WritingProcedure']['name'] = $_POST['name'];
        $writingprocedureController       = new WritingProcedureController('writingprocedure');
        $writingprocedureController->create($data);
        Yii::app()->end();
    }

    public function actionUpdateWritingProcedure(): void
    {
        $data                             = [];
        $data['WritingProcedure']['name'] = $_POST['name'];
        $data['WritingProcedure']['id']   = $_POST['id'];
        $writingprocedureController       = new WritingProcedureController('writingprocedure');
        $writingprocedureController->update($data);
        Yii::app()->end();
    }

    /**
     * Erstellt eine MailUserTemplate
     */
    public function actionCreateWritingTemplate(): void
    {
        $data                                            = [];
        $data['WritingTemplate']['name']                 = $_POST['name'];
        $data['WritingTemplate']['description']          = $_POST['description'];
        $data['WritingTemplate']['writing_procedure_id'] = $_POST['writing_procedure_id'];
        if (isset($_POST['product_combo_id'])) {
            $data['WritingTemplate']['product_combo_id'] = $_POST['product_combo_id'];
        }
        $data['WritingTemplate']['content']        = $_POST['content'];
        $data['WritingTemplate']['user_id']        = $_POST['user_id'];
        $data['WritingTemplate']['reciever']       = $_POST['reciever'];
        $data['WritingTemplate']['writing_header'] = $_POST['writing_header'];

        $writingtemplateController = new WritingTemplateController('writingtemplate');
        $writingtemplateController->create($data);
        Yii::app()->end();
    }

    public function actionGetNotificationState(): void
    {
        if (isset($_POST['itemname'])) {
            echo NotificationCenter::getInstance()->getNotification($_POST['itemname']);
        }
        Yii::app()->end();
    }

    /**
     * Gibt per Ajax eine Voransicht des geschriebenen Textes zurück, in dem alle Tags ersetzt werden (falls möglich)
     */
    public function actionGetUserMailPreview(): void
    {
        if (!isset($_POST['content'])) {
            Yii::app()->end();
        }

        $tags   = new Tags();
        $tags->forUserId((int) $_POST['user_id']);

        if (!empty($_POST['insurance_company_id'])) {
            $tags->forCompanyId((int) $_POST['insurance_company_id']);
        }

        if (!empty($_POST['product_combo_id'])) {
            $tags->forProductComboId((int) $_POST['product_combo_id']);
        }

        if (!empty($_POST['client_id'])) {
            $tags->forClientId((int) $_POST['client_id']);
        }

        if (!empty($_POST['contract_id'])) {
            $tags->forContractId($_POST['contract_id']);
        }

        $mailprocedure = MailProcedure::model()->findByPk($_POST['mail_procedure_id']);
        if (!empty($mailprocedure)) {
            $tags->forSubjectId((int) $mailprocedure->support_assignment_subject_id);
        }

        $tags->allowCustomSalutation();

        $data            = [];
        $data['content'] = $tags->replaceAvailable($_POST['content']);
        $data['subject'] = $tags->replaceAvailable($_POST['subject']);

        $this->renderJsonResponse($data);
    }

    public function actionUpdateBasicField(): void
    {
        $data    = [];
        $checked = $_POST['checked'];

        if ($checked == 'true') {
            $data['ContractField']['basic_field'] = 1;
        } else {
            $data['ContractField']['basic_field'] = 0;
        }

        $data['ContractField']['id'] = $_POST['id'];

        $contractFieldController = new ContractFieldController('ContractFieldController');
        $contractFieldController->UpdateBasicField($data);
        Yii::app()->end();
    }

    public function actionGetAddressFields(): void
    {
        isset($_POST['prefix']) ? $prefix = $_POST['prefix'] : $prefix = '';

        isset($_POST['form']) ? $form = $_POST['form'] : $form = new CActiveForm();

        $address = isset($_POST['address_id']) && $_POST['address_id'] != '' ?
            Address::model()->findByPk($_POST['address_id']) :
            new Address();

        echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'address' . DIRECTORY_SEPARATOR . '_formFields.php',
            ['address' => $address, 'form' => $form, 'prefix' => $prefix]);
        Yii::app()->end();
    }

    public function actionGetAgencyFields(): void
    {
        isset($_POST['prefix']) ? $prefix = $_POST['prefix'] : $prefix = '';

        isset($_POST['form']) ? $form = $_POST['form'] : $form = new CActiveForm();

        echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'agency' . DIRECTORY_SEPARATOR . '_formFields.php',
            ['agency' => new Agency(), 'form' => $form, 'prefix' => $prefix]);
        Yii::app()->end();
    }

    public function actionGetContactFields(): void
    {
        isset($_POST['prefix']) ? $prefix = $_POST['prefix'] : $prefix = '';

        isset($_POST['form']) && $_POST['address_id'] != '' ? $form = $_POST['form'] : $form = new CActiveForm();

        $contact = isset($_POST['contact_id']) && $_POST['contact_id'] != '' ?
            Contact::model()->findByPk($_POST['contact_id'])
            : new Contact();

        echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'contact' . DIRECTORY_SEPARATOR . '_formFields.php',
            ['contact' => $contact, 'form' => $form, 'prefix' => $prefix]);
        Yii::app()->end();
    }

    public function actionUpdateBasicFieldAssignment(): void
    {
        $data = [];

        $data['ProductComboBasicFieldAssignment']['field_id']      = $_POST['field_id'];
        $data['ProductComboBasicFieldAssignment']['product_combo'] = $_POST['product_combo_id'];

        $productComboBasicFieldAssignmentController = new ProductComboBasicFieldAssignmentController('ProductComboBasicFieldAssignmentController');
        $productComboBasicFieldAssignmentController->UpdateBasicField($data);
        Yii::app()->end();
    }

    public function actionGetRiskCheckInsuranceCompanies(): void
    {
        $sector         = $_POST['sector'];
        $productcomboid = $_POST['productcomboid'];

        echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'riskCheck' . DIRECTORY_SEPARATOR . 'insuranceCompanies.php',
            ['sector' => $sector, 'productcomboid' => $productcomboid]);
        Yii::app()->end();
    }

    public function actionGetContractDropdownData(): void
    {
        $clientid           = null;
        $insurancecompanyid = null;
        if (!empty($_POST['UserMail']['UserMail']['client_id']) && !empty($_POST['UserMail']['UserMail']['insurance_company_id'])) {
            $clientid           = $_POST['UserMail']['UserMail']['client_id'];
            $insurancecompanyid = $_POST['UserMail']['UserMail']['insurance_company_id'];
        } else {
            if (!empty($_POST['UserWriting']['client_id'])) {
                $clientid = $_POST['UserWriting']['client_id'];
            } else {
                if (!empty($_POST['clientid'])) {
                    $clientid = @$_POST['clientid'];
                }
            }
            if (!empty($_POST['UserWriting']['insurance_company_id'])) {
                $insurancecompanyid = $_POST['UserWriting']['insurance_company_id'];
            }
        }
        if (!empty($_POST['insurancecompanyid'])) {
            $insurancecompanyid = $_POST['insurancecompanyid'];
        }
        if (!empty($insurancecompanyid)) {
            $company = InsuranceCompany::model()->findByPk($insurancecompanyid);
            if (!empty($company)) {
                $insurancecompanyid = $company->getVertriebswegGesellschaftenIds();
            }
        }
        $damages   = !empty($_POST['ignoreDamages']) ? false : true;
        $productId = $_POST['product_combo_id'] ?? null;
        $productId = $_POST['UserWriting']['insurance_company_id'] ?? $productId;

        if (null !== $clientid) {
            $data   = [];
            $params = ['client_id' => $clientid, 'parentId' => null];
            if (!empty($insurancecompanyid)) {
                $params['company_id'] = $insurancecompanyid;
            }
            if (!empty($productId)) {
                $product = ProductCombo::model()->findByPk($productId);
                if (!empty($product)) {
                    $productIds                           = array_column($product->getChildren(), 'id');
                    $productParams                        = $params;
                    $productParams['specific_product_id'] = $productIds;
                    $productdata                          = Contracts::model()->getDropdownDataByAttributes($productParams, $damages);
                    foreach ($productdata as $label => $daten) {
                        $data[$label . ' in der Sparte'] = $daten;
                    }
                }
            }

            $data += Contracts::model()->getDropdownDataByAttributes($params, $damages);

            echo '<option value="">&#160;</option>';
            $included = [];
            foreach ($data as $title => $d) {
                echo '<optgroup label="' . CHtml::encode($title) . '">';
                foreach ($d as $key => $value) {
                    if (!in_array($key, $included)) {
                        $included[] = $key;
                        echo '<option value="' . $key . '">' . CHtml::encode($value) . '</option>';
                    }
                }
            }
        } else {
            echo '<option value="">&#160;</option>';
        }
        Yii::app()->end();
    }

    public function actionGetAttachmentsDropdownData(): void
    {
        $clientId          = (int) Yii::app()->request->getPost('client_id', null);
        $companyId         = (int) Yii::app()->request->getPost('insurance_company_id', null);
        $contractId        = Yii::app()->request->getPost('contract_id', null);
        $clientfilesSearch = ClientfileProvider::forUser(currentUser())->find();
        $preselected       = !empty($_POST['preselectedIds']) && is_string($_POST['preselectedIds'])
            ? unserialize($_POST['preselectedIds'])
            : [];

        $preselected       = array_column($preselected, 'value', 'key');
        $attachmentData    = new AttachmentData($preselected);
        if (!empty($companyId)) {
            $company = InsuranceCompany::model()->findByPk($companyId);
            if (!empty($company)) {
                $companyId = $company->getVertriebswegGesellschaftenIds();
            }
        }
        if ($clientId) {
            if (!empty($companyId) && !empty($contractId)) {
                $contractId    = $contractId[0];
                $contractModel = Contracts::model()->findByPk($contractId);
                if (!empty($contractModel)) {
                    $contract_ids = $contractModel->getAllSubcontracts(true);
                    $contract     = $clientfilesSearch->findAllByAttributes(
                        [
                            'client_id'   => $clientId,
                            'contract_id' => $contract_ids
                        ],
                        [
                            'order' => 'upload_date DESC'
                        ]
                    );
                    $criteria     = new CDbCriteria();
                    $criteria->compare('client_id', $clientId);
                    $criteria->addInCondition('insurance_company_id', $companyId);
                    $criteria->addCondition('contract_id is null');
                    $criteria->order = 'upload_date DESC';
                    $company         = $clientfilesSearch->findAll($criteria);

                    $filesCriteria = new CDbCriteria();
                    $filesCriteria->compare('client_id', $clientId);
                    $filesCriteria->addCondition('insurance_company_id is NULL');
                    $filesCriteria->addCondition('contract_id is NULL');
                    $filesCriteria->order = 'upload_date DESC';
                    $files                = $clientfilesSearch->findAll($filesCriteria);
                }
            } elseif (!empty($companyId)) {
                $contract      = [];
                $company       = $clientfilesSearch->findAllByAttributes(
                    [
                        'client_id'            => $clientId,
                        'insurance_company_id' => $companyId
                    ],
                    [
                        'order' => 'upload_date DESC'
                    ]
                );
                $filesCriteria = new CDbCriteria();
                $filesCriteria->compare('client_id', $clientId);
                $filesCriteria->addCondition('insurance_company_id is NULL');
                $filesCriteria->order = 'upload_date DESC';
                $files                = $clientfilesSearch->findAll($filesCriteria);
            } else {
                $company  = [];
                $contract = [];
                $criteria = new CDbCriteria();
                $criteria->compare('client_id', $clientId);
                $criteria->order = 'upload_date DESC';
                $files           = $clientfilesSearch->findAll($criteria);
            }

            foreach ($contract ?? [] as $value) {
                $attachmentData->addEntryToGroup('Kunde (Zugeord. zu Vertrag)', 'client_' . $value->id, $value->getDetailedName());
            }
            foreach ($company ?? [] as $value) {
                /* @link https://sentry.demv-systems.de/organizations/demv-systems/issues/8289/ */
                /* @link https://sentry.demv-systems.de/organizations/demv-systems/issues/8287/ */
                if (!is_object($value)) {
                    continue;
                }

                $attachmentData->addEntryToGroup('Kunde (Zugeord. zu Gesellschaft)', 'client_' . $value->id, $value->getDetailedName());
            }
            $grouplabel = empty($company) && empty($contract) ? 'Kunde' : 'Kunde (Restliche)';
            foreach ($files ?? [] as $value) {
                $attachmentData->addEntryToGroup($grouplabel, 'client_' . $value->id, $value->getDetailedName());
            }
        }
        if (!empty($_POST['procedure_id'])) {
            $procedureid = $_POST['procedure_id'];
            $files       = ProcedureFiles::model()->findAllByAttributes(['procedure_id' => $procedureid]);
            foreach ($files as $value) {
                $attachmentData->addEntryToGroup('Vorgang', 'procedure_' . $value->id, $value->name);
            }
        }

        echo $attachmentData->asHtml();
        Yii::app()->end();
    }

    public function actionUpdateContractFieldAssignment(): void
    {
        $data                        = [];
        $data['field_id']            = $_POST['field_id'];
        $data['assignment_field_id'] = $_POST['assignment_field_id'];
        $data['product_combo_id']    = $_POST['product_combo_id'];

        $productComboContractFieldAssignmentController = new ProductComboContractFieldAssignmentController('ProductComboContractFieldAssignmentController');
        $productComboContractFieldAssignmentController->UpdateAssignment($data);
        Yii::app()->end();
    }

    public function actionGetDemvMail(): void
    {
        echo Yii::app()->settings->get('Mail', 'systemMail')['address'];
        Yii::app()->end();
    }

    public function actionUpdateNetGrossVal(): void
    {
        $productComboController = new ProductComboController('ProductComboController');
        $productComboController->UpdateNetGrossVal($_POST['id']);
        Yii::app()->end();
    }

    public function actionUpdateNetGrossValCourtage(): void
    {
        $productComboController = new ProductComboController('ProductComboController');
        $productComboController->UpdateNetGrossValCourtage($_POST['id']);
        Yii::app()->end();
    }

    public function actionCreateProcedureFiles(): void
    {
        $data                                   = [];
        $data['ProcedureFiles']['procedure_id'] = $_POST['procedure_id'];
        $procedurefilesController               = new ProcedureFilesController('procedurefiles');
        $procedurefilesController->create($data);
        Yii::app()->end();
    }

    public function actionCreateClientTargetGroup(): void
    {
        $data                              = [];
        $data['ClientTargetGroup']['name'] = $_POST['name'];
        $clienttargetgroupController       = new ClientTargetGroupController('clienttargetgroup');
        $clienttargetgroupController->create($data);
        Yii::app()->end();
    }

    public function actionGetClientTargetGroupPCADowpDown(): void
    {
        $ctgpcaob = CHtml::listData(
            ClientTargetGroupProductComboAssignment::model()->findAllByAttributes(
                [
                    'client_target_group_id' => $_POST['client_target_group_id'],
                    'obligation'             => 1,
                ]
            ),
            'product_combo_id',
            'product_combo_id'
        );

        ExtHelper::createMultiSelectDropDown(ViewHelper::checkIfPrefix('ClientTargetGroupProductComboAssignment',
            'product_combo_id_obligation'),
            CHtml::listData(
                ProductCombo::model()->findAllByAttributes([], ['order' => 'name asc']),
                'id',
                'name'),
            $this,
            $ctgpcaob,
            'span20',
            ['height' => '200']);
        $ctgpca = CHtml::listData(
            ClientTargetGroupProductComboAssignment::model()->findAllByAttributes(
                [
                    'client_target_group_id' => $_POST['client_target_group_id'],
                    'obligation'             => 0,
                ]
            ),
            'product_combo_id',
            'product_combo_id'
        );

        echo '<br>';
        ExtHelper::createMultiSelectDropDown(ViewHelper::checkIfPrefix('ClientTargetGroupProductComboAssignment',
            'product_combo_id'),
            CHtml::listData(
                ProductCombo::model()->findAllByAttributes([], ['order' => 'name asc']),
                'id',
                'name'),
            $this,
            $ctgpca,
            'span20',
            ['height' => '200']);
        Yii::app()->end();
    }

    public function actionCreateClientTargetGroupProductComboAssignment(): void
    {
        $data                                                                           = [];
        $data['ClientTargetGroupProductComboAssignment']['client_target_group_id']      = $_POST['client_target_group_id'];
        $data['ClientTargetGroupProductComboAssignment']['product_combo_id']            = $_POST['product_combo_id'];
        $data['ClientTargetGroupProductComboAssignment']['product_combo_id_obligation'] = $_POST['product_combo_id_obligation'];
        $ctgpcaController                                                               = new ClientTargetGroupProductComboAssignmentController('clienttargetgroupproductcomboassignment');
        $ctgpcaController->create($data);
        Yii::app()->end();
    }

    public function actionGetClientDuplicateView(): void
    {
        $firstclientid  = $_POST['firstclientid'];
        $secondclientid = $_POST['secondclientid'];

        if ($firstclientid != $secondclientid && $firstclientid != '' && $secondclientid != '') {
            $clientcontroller = new ClientController('client');
            echo $clientcontroller->getClientDuplicateView($firstclientid, $secondclientid);
        } else {
            echo false;
        }
        Yii::app()->end();
    }

    public function actionGetFileUploadField(): void
    {
        $counter = $_POST['counter'] ?? 0;

        echo '<div id="uploaddiv' . $counter . '">';
        echo '<table><tr><td>';
        echo CHtml::fileField(ViewHelper::checkIfPrefix('ProcedureFiles', 'file[' . $counter . ']'), '',
            ['onChange' => 'validateFileExtension(this)']);
        echo '</td>';

        echo '<td>';
        echo CHtml::link(
            '<i class = "icon-remove"></i>', '',
            [
                'class'   => 'btn btn-mini',
                'onClick' =>
                    '$("#uploaddiv' . $counter . '").remove();' .
                    'reduceCounter()',
            ]);
        echo '</td>';
        echo '</tr></table>';
        echo '</div>';
        Yii::app()->end();
    }

    public function actionUpdateContractDone(): void
    {
        $contract = Contracts::model()->personalAccess()->findByPk($_POST['contract_id']);
        if ($contract === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        $contract->done      = 1;
        $contract->done_date = date('Y-m-d');

        $contract->save();

        Yii::app()->end();
    }

    public function actionAssignClientFileToContract(): void
    {
        if (!empty($_POST['clientfileid'])) {
            $clientfile = ClientfileProvider::forUser(currentUser())->find()->findByPk($_POST['clientfileid']);
            if (null !== $clientfile) {
                if (!empty($_POST['contract_id'])) {
                    $contract                = Contracts::model()->findByPk($_POST['contract_id']);
                    $clientfile->contract_id = $contract->id;
                    $clientfile->save();
                } else {
                    $clientfile->contract_id = null;
                    $clientfile->save();
                }
            }
        }
        Yii::app()->end();
    }

    public function actionShowContractSearchFields(): void
    {
        $data = $_POST;

        $output = $this->renderPartial('/client/_contractFieldSearch', ['data' => $data], false,
            false);

        Yii::app()->clientScript->renderBodyEnd($output);
        echo $output;
        Yii::app()->end();
    }

    /**
     * Für ProductComboStandartBasicField
     */
    public function actionUpdateValue(): void
    {
        $data                                                    = [];
        $data['ProductComboStandartBasicField']['attributeName'] = $_POST['attributeName'];
        $data['ProductComboStandartBasicField']['id']            = $_POST['id'];

        $controller = new ProductComboStandartBasicFieldsController('ProductComboStandartBasicFields');
        $controller->updateValue($data);
    }

    public function actionGetCalculatorTariffs(): void
    {
        //ProductComboId => Modelname
        $relevantProductCombos = [
            170 => 'ThpTarife', //Unfall
            165 => 'PhpTarif', //Tierhalter
        ];

        $insuranceId = Yii::app()->request->getParam('insurance');
        $product     = Yii::app()->request->getParam('product');

        $data = [];
        if (!empty($insuranceId) && !empty($product) && array_key_exists($product,
            $relevantProductCombos)
        ) {
        }
        switch ($product) {
            case 165:
                $data = CHtml::listData(PhpTarif::model()->findAllByAttributes(['gesellschaft' => $insuranceId]),
                    'id', 'bezeichnung');
                break;
            case 170:
                $data = CHtml::listData(ThpTarife::model()->findAllByAttributes(['gesellschaftID' => $insuranceId]),
                    'ID', 'name');
                break;
        }
        $back = '';
        foreach ($data as $key => $value) {
            $back .= '<option value = "' . $key . '">' . $value . '</option>';
        }
        echo $back;
    }

    public function actionUpdateNewAgencyDiv(): void
    {
        if ($_POST['newAgency'] == 'false') {
            echo ViewHelper::labelFor('Neue Firma', '', '');
            $output = ExtHelper::createDropDown('AgencyChange[agency_id]', $_POST['data'], $this);
            Yii::app()->clientScript->renderBodyEnd($output);
            echo $output;
        } else {
            echo '<b> Bitte erstellen Sie eine neue Firma. Die neu erstellte Firma wird dann mit ihrem Account verknüpft. </b> <br> <br>';

            $agency = new Agency();
            $html   = $this->renderPartial('/agency/_formFields',
                ['agency' => $agency, 'prefix' => 'Agency', 'form' => 'form', 'fromuser' => $_POST['user_id']]);
            echo $html;
        }
    }

    /**
     * Gibt, abhängig von der IBAN, die Kontonummer und Bankleitzahl als JSON zurück.
     *
     * @param JSON
     */
    public function actionGetKntnrAndBlzFromIBAN(): void
    {
        $ibanClass = new Iban();
        $iban      = str_replace(' ', '', $_POST['iban'] ?? '');

        $return = [];
        if ($ibanClass->validate($iban)) {
            if (substr($iban, 0, 2) == 'DE') {
                $return['blz'] = substr($iban, 4, 8);
                $return['ktn'] = substr($iban, 12, strlen($iban));

                $country = Country::model()->findByAttributes(['iso' => substr($iban, 0, 2)]);
                if (!empty($country->isNewRecord)) {
                    $return['country'] = $country->id;
                } else {
                    $return['country'] = Country::DEUTSCHLAND;
                }

                if (isset($return['blz']) && !empty($return['blz'])) {
                    $miscBanknumber = MiscBanknumber::model()->findByAttributes(['banknumber' => $return['blz']]);
                    if (isset($miscBanknumber) || null !== $miscBanknumber) {
                        $return['bankname'] = $miscBanknumber->bankname;
                    }

                    $bic           = $this->getBIC($return['blz']);
                    $return['bic'] = $bic;
                }
            }
        } else {
            $return['fail'] = 'IBAN ist nicht korrekt! Bitte überprüfen Sie Ihre Eingabe.';
        }
        echo function_exists('json_encode') ? json_encode($return) : CJSON::encode($return);
        Yii::app()->end();
    }

    /**
     * Gibt die $bic zurück. Sollten mehrere Einträge in der DB gefunden werden, so wird trotzdem nur
     * ein element der Form 1567891XXX zurückgegeben. (Also XXX statt den letzten drei Zeichen)
     *
     * @return type
     */
    public function getBIC($blz)
    {
        return Iban::getBic($blz);
    }

    public function actionGetBanknameAndBLZ(): void
    {
        $result     = [];
        $bankcode   = DoubleBehavior::extractNumbers($_POST['bankcode']);
        $banknumber = @DoubleBehavior::extractNumbers($_POST['banknumber']);

        $miscBanknumber = MiscBanknumber::model()->findByAttributes(['banknumber' => $bankcode]);
        if (isset($miscBanknumber) || null !== $miscBanknumber) {
            $result['bankname'] = $miscBanknumber->bankname;
        }

        $bic = $this->getBIC($bankcode);

        $result['bic'] = $bic;

        $country_code = !empty($_POST['country_code']) ? $_POST['country_code'] : Country::DEUTSCHLAND;
        if (!empty($bankcode) && !empty($banknumber) && !empty($country_code)) {
            $country = Country::model()->findByPk($country_code);

            if (!empty($country)) {
                $ibanClass = new Iban();
                $Iban      = $ibanClass->calculate($country->iso, $banknumber,
                    $bankcode);
                if ($ibanClass->validate($Iban)) {
                    $result['iban'] = $Iban;
                }
            }
            if (!isset($result['iban'])) {
                $result['iban'] = '';
            }
        }
        echo function_exists('json_encode') ? json_encode($result) : CJSON::encode($result);

        Yii::app()->end();
    }

    public function actionUpdateIban(): void
    {
        $banknumber = $_POST['banknumber'] ?? null;
        $bankcode   = $_POST['bankcode'] ?? null;

        if (!empty($banknumber) && !empty($bankcode)) {
            $bankcode     = DoubleBehavior::extractNumbers($bankcode);
            $banknumber   = DoubleBehavior::extractNumbers($banknumber);
            $country_code = !empty($_POST['country_code']) ? $_POST['country_code'] : Country::DEUTSCHLAND;
            $country      = Country::model()->findByPk($country_code);
            if (!empty($country)) {
                $ibanClass = new Iban();
                $Iban      = $ibanClass->calculate($country->iso, $banknumber,
                    $bankcode);
                if ($ibanClass->validate($Iban)) {
                    ob_clean();
                    echo trim($Iban);
                }
            }
        }
    }

    public function actionChangeCalculatorActive()
    {
        $id = $_POST['id'];

        if (!empty($id)) {
            $profile = ProfileCalculator::model()->findByPk($id);

            if (null !== $profile) {
                if ($profile->active == 1) {
                    $profile->active = 0;
                } elseif ($profile->active == 0) {
                    $profile->active = 1;
                }
            }
            if ($profile->save()) {
                return 1;
            }

            return -1;
        }
    }

    public function actionUpdateInnoInsuranceCompany(): void
    {
        $id    = Yii::app()->request->getParam('id');
        $value = Yii::app()->request->getParam('value');
        if (!empty($id)) {
            $innoInsuranceCompanyAssignment = InnoInsuranceCompanyAssignment::model()->findByPk($id);
            if (null !== $innoInsuranceCompanyAssignment) {
                $innoInsuranceCompanyAssignment->demv_id = $value;
                $innoInsuranceCompanyAssignment->save();
            }
        }
        Yii::app()->end();
    }
}
