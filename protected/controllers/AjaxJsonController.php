<?php

use Business\Geo\CityProvider;

/**
 * Class AjaxJsonController
 */
class AjaxJsonController extends CController
{
    /**
     * @return array[]
     */
    public function filters()
    {
        return [
            'accessControl',
        ];
    }

    public function accessRules()
    {
        return [
            [
                'allow',
                'actions' => ['GetCityStateByZip'],
                'users'   => ['*'],
            ],
            [
                'allow', // allow auth users to do all
                'users'   => ['@'],
            ],
            [
                'deny', // fail safe
            ],
        ];
    }

    public function actionCheckProcedure()
    {
        $procedure = Procedure::model()->findByPk($_POST['procedure_id']);
        if (!empty($procedure)) {
            $ret = $procedure->Empty;
        } else {
            $ret = false;
        }
        echo $ret;
    }

    public function actionGetTarifData()
    {
        $data         = [];
        $productCombo = null;
        if (!empty($_POST['product_combo_id'])) {
            $productCombo = ProductCombo::model()->findByPk($_POST['product_combo_id']);
        }
        if (!empty($_POST['user_id']) && !empty($_POST['insurance_company_id']) && !empty($_POST['product_combo_id'])) {
            $tarif = TarifController::getTarif((int) $_POST['user_id'], $_POST['insurance_company_id'], $_POST['product_combo_id']);
            if (!empty($tarif)) {
                $data = $tarif->attributes;
                //                $tarif->addType();
                if ($tarif->commission_promille && !empty($tarif->commission)) {
                    $data['commission'] = (float) $tarif->commission / 10.00;
                }
                if ($tarif->dynamic_promille && !empty($tarif->dynamic)) {
                    $data['dynamic'] = (float) $tarif->dynamic / 10.00;
                }
                $data['storno_reserve'] = $tarif->reversal;
                if (!empty($_POST['from']) && !empty($_POST['to'])) {
                    $d1             = new DateTime(UtilHelper::parseToDBDate($_POST['from']));
                    $d2             = new DateTime(UtilHelper::parseToDBDate($_POST['to']));
                    $diff           = $d2->diff($d1);
                    $data['factor'] = $tarif->getFactor($diff->y);
                }
            }
        } elseif (!empty($productCombo)) {
            $productCombo = $productCombo->getFirstProductCombo();
            if ($productCombo->id == ProductCombo::VORSORGE) {
                $data['responseability_period'] = 60;
                $data['max']                    = 35;
            } elseif ($productCombo->id == ProductCombo::KV) {
                $data['responseability_period'] = 60;
                $data['max']                    = 1;
            } elseif ($productCombo->id == ProductCombo::SACH_GEWERBE || $productCombo->id == ProductCombo::SACH_PRIVAT) {
                $data['responseability_period'] = 12;
                $data['max']                    = 1;
            }
        }
        if (!empty($productCombo)) {
            $productCombo = $productCombo->getFirstProductCombo();
            if ($productCombo->id == ProductCombo::VORSORGE || $productCombo->id == ProductCombo::KV) {
                $data['commission_payment_type'] = CourtageData::COMMISSION_PAYMENTTYPE_VORDISKONTIERT;
            }
        }
        $conv = [
            'commission',
            'trailer_commission',
            'dynamic',
            'storno_reserve',
            'factor',
            'lap'
        ];
        foreach ($conv as $c) {
            if (isset($data[$c])) {
                $data[$c] = Yii::app()->formatter->formatDouble($data[$c]);
            }
        }
        echo function_exists('json_encode') ? json_encode($data) : CJSON::encode($data);
    }

    public function actionGetClientData()
    {
        $client = Client::model()->findByPk($_POST['clientid']);
        if (null === $client) {
            $client = new Client();
        }
        $data = $client->attributes;
        foreach ($client->getJobdata()->attributes as $key => $value) {
            $data[$key] = $value;
        }
        foreach ($client->getRisks()->attributes as $key => $value) {
            $data[$key] = $value;
        }
        $address                = $client->getAddress();
        $data['Address_street'] = $address->street;
        $data['Address_nr']     = $address->nr;
        $data['Address_zip']    = $address->zip;
        $data['Address_city']   = $address->city;
        $data['id']             = $client->id;
        echo function_exists('json_encode') ? json_encode($data) : CJSON::encode($data);
    }

    public function actionGetClients()
    {
        $clients = Client::model()->findAllByAttributes(['user_id' => Yii::app()->user->getID()]);
        $results = CHtml::listData($clients, 'id', 'Fullname');
        echo function_exists('json_encode') ? json_encode($results) : CJSON::encode($results);
    }

    public function actionLazyDropDownQuery()
    {
        $criteria = new CDbCriteria();
        $criteria->addInCondition('user_id', Yii::app()->user->getUnderlingsIds());
        $criteria1 = new CDbCriteria();
        $criteria1->compare('t.firstname', $_GET['q'], true);
        $criteria1->compare('t.lastname', $_GET['q'], true, 'OR');
        $criteria->mergeWith($criteria1, 'AND');
        $clients = Client::model()->findAll($criteria);
        $result  = [];
        foreach ($clients as $client) {
            $result[] = [
                'id'   => $client->id,
                'text' => @$client->getFullname(),
            ];
        }
        echo CJSON::encode($result);
        Yii::app()->end();
    }

    /**
     * Action nutzt die Rules der Models um eine einzelne Eingabe zu validieren.
     */
    public function actionValidateforajax()
    {
        $return = '[]';
        echo function_exists('json_encode') ? json_encode($return) : CJSON::encode($return);
    }

    /**
     *  city and state from zip code ajax handler
     */
    public function actionGetCityStateByZip($zipcode = null)
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');

        if (Yii::app()->request->isAjaxRequest) {
            $result = (new CityProvider())->findByPlz($_POST['zipcode']);

            echo CJSON::encode([
                                   'zipcode'   => $result->getZipcode(),
                                   'cityname'  => $result->getCityname(),
                                   'statename' => $result->getStatename(),
                                   'multiple'  => $result->getCities(),
                               ]);
            Yii::app()->end();
        }
    }

    /**
     * Validiert alle Werte einer Form Durchläluft die Verschachtelten Array rekursiv und zieht sich alle zu validierenden objecte raus
     */
    public function actionValidateForm()
    {
        $data = $_POST;
        //        CActiveForm::validate liefert [] zurück, wenn alle Werte positiv validiert wurden
        //        $return = '[]';
        $return = [];
        $models = [];
        $this->formValidateLogik($models, $data, '');
        {
            foreach ($data as $businessObjectKey => $businessObjectValues) {
                if (is_array($businessObjectValues)) {
                    $models = $this->formValidateLogik($models, [$businessObjectKey => $businessObjectValues], '');
                }
            }
        }
        if (!empty($models)) {
            foreach ($models as $modelId => $model) {
                foreach ($model['model'] ?? [] as $attributeId => $attribute) {
                    if (array_key_exists($model['idname'] . '_' . get_class($model['model']) . '_' . $attributeId,
                        $model['checkAttributes'])) {
                        $validated = CActiveForm::validate($model['model'], [$attributeId]);
                        $validated = json_decode($validated);
                        $validated = (array) $validated;
                        if (!empty($validated) && is_array($validated)) {
                            foreach ($validated as $k => $v) {
                                $test = str_replace('slkdjf_', '', $model['idname']);
                                $test = str_replace('slkdjf', '', $test);
                                if ($test == '') {
                                    $return[$test . $k] = $v;
                                } else {
                                    $return[$test . '_' . $k] = $v;
                                }
                            }
                        }
                    }
                }
            }
        }
        echo function_exists('json_encode') ? json_encode($return) : CJSON::encode($return);
    }

    /**
     * Rekursionsteil zu $this->actionValidateForm
     *
     * @param type $models
     * @param type $businessObjectValues
     *
     * @return \modelId
     */
    private function formValidateLogik($models, $businessObjectValues, $prefix)
    {
        $attributes = [];
        foreach ($businessObjectValues as $modelId => $attributes) {
            if (is_array($attributes) && $this->containsArray($attributes)) { //Hier prüfen, wenn array enthalten;
                if (!empty($prefix)) {
                    $models = $this->formValidateLogik($models, $attributes, $prefix . '_' . $modelId);
                } else {
                    $models = $this->formValidateLogik($models, $attributes, $modelId);
                }
            }
            if (@class_exists($modelId)) {
                if (is_array($attributes)) {
                    $keys = [];
                    foreach ($attributes as $key => $value) {
                        $keys[$prefix . '_' . $modelId . '_' . $key] = $value;
                    }
                    $model                            = new $modelId();
                    $model->attributes                = $attributes;
                    $models[$prefix . '_' . $modelId] = ['model' => $model, 'checkAttributes' => $keys, 'idname' => $prefix];
                }
            }
        }

        return $models;
    }

    /**
     * Überprüft, ob eion Array ein subArray enthält
     *
     * @param array $array
     *
     * @return bool
     */
    private function containsArray($array)
    {
        $containsArray = false;
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $containsArray = true;
            }
        }

        return $containsArray;
    }

    public function actionCheckIfTagsReplaceable()
    {
        if (!empty($_POST['content'])) {
            $content = $_POST['content'];
            $found   = preg_match('/\{.*\}/', $content);
            echo $found;
        }
    }
}
