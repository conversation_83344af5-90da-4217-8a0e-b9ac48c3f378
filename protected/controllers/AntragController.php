<?php

use Auth\Services;
use Business\Vorgaenge\Aufgabe;
use Carbon\Carbon;
use Components\Authentication\OAuth\OAuthAccessible;
use Demv\JSend\ResponseFactory;
use League\Flysystem\FileExistsException;

final class AntragController extends AllowAllController implements OAuthAccessible
{
    private const REQUIRED_KEYS = [
        'antragsdatum',
        'insurance_company_id',
        'distribution_insurance_id',
        'sparte',
        'beginn',
//        'laufzeit', optional - gibt es nicht bei kfz
        'beitrag_brutto',
        'beitrag_netto',
        'zahlweise',
        'client_id',
        'vn_anrede',
        'vn_vorname',
        'vn_nachname',
        'vn_geburtsdatum',
        'vn_strasse',
        'vn_hausnummer',
        'vn_plz',
        'vn_ort',
        'request_url',
        'response_url',
        'dispatch_mode'
    ];

    public static function getAllowedServices(): array
    {
        return Services::DEMV_INTERNAL_SERVICES;
    }

    public function actionNotification(): void
    {
        $rawBody = Yii::app()->request->getRawBody();
        Yii::log("actionErstantragGestellt: {$rawBody}", CLogger::LEVEL_INFO, 'application.rest');

        $attributes   = json_decode($rawBody, true);
        $userId       = (int) (Yii::app()->user->id ?? 0);
        $clientFileId = (int) ($attributes['clientFileId'] ?? 0);

        if ($clientFileId === 0 || $userId === 0) {
            ResponseFactory::instance()->error(
                ['message' => 'Die uebermittelten Parameter waren nicht valide oder vorhanden'],
                420
            )->respond();
        }

        $clientFile = ClientFiles::model()->findByPk($clientFileId);

        if ($clientFile === null) {
            ResponseFactory::instance()->error(['message' => 'Antrag konnte nicht gestellt werden', 500])->respond();
        }

        $companyId = (int) ($attributes['company_id'] ?? $clientFile->insurance_company_id);
        $userFile  = new UserFiles();

        if (
            // VGL - HV und ID können nun auf sämtliche Vermittlernummern zugreifen, die in der Firma vorhanden sind.
            // D.H. die Prüfung des BrokerIdService, welche nur auf die direkte Hierarchie des Users zugreift, ist unzureichend
            // Da es ein Bug ist soll der Aufwand gering sein, weshalb wir die Info hier simpel aus dem VGL mitgeben
            (bool) ($attributes['brokerIdExists'] ?? false) || BrokerIdService::new()->mainBrokerIdExists($userId, $companyId)
        ) {
            // Vermittlernummer existiert und es ist kein Erstantrag nötig.
            ResponseFactory::instance()->success()->respond();
        }

        /** @var User $user */
        $user          = User::model()->findByPk($userId);
        $name          = $clientFile->client->getFullname();
        $insuranceName = $clientFile->insuranceCompany->name;

        try {
            $userFile->saveFromClientFile(
                $clientFile,
                $user->getMainBroker()->id,
                DocumentType::ERSTANTRAG,
                $companyId
            );
        } catch (FileExistsException $e) {
            Yii::log(
                'Error while creating saving Erstantrag. (' . $e->getMessage() . ').',
                CLogger::LEVEL_ERROR,
                'application.rest'
            );

            ResponseFactory::instance()
                ->error([
                    'message'   => $e->getMessage(),
                    'Client'    => $name . '(' . $clientFile->client->id . ')',
                    'Insurance' => $insuranceName,
                    'user'      => $user->getFullname() . '(' . $user->id . ')'
                ])
                ->respond();
        }

        try {
            $vorgang = (new Aufgabe())
                ->fuerUser($user)
                ->fuerKunde((int) $clientFile->client_id)
                ->fuerGesellschaft($companyId)
                ->mitTitel('Kooperation beantragen für Antragseinreichung')
                ->mitInhalt(
                    "Achtung: Der Antrag für $name kann erst zur $insuranceName versendet werden, wenn alle Kooperationsunterlagen vorliegen. Hierzu haben Sie soeben eine Mail erhalten."
                )
                ->mitFaelligkeit(Carbon::now()->addWeeks(2)->toDate())
                ->anlegen();
        } catch (Exception $e) {
            Yii::log(
                'Error while creating Aufgabe during Erstantragsprozess (' . $e->getMessage() . ').',
                CLogger::LEVEL_ERROR,
                'application.rest'
            );
        }

        $vorgangsnummer = isset($vorgang->attributes['vorgangsnummer']) && $vorgang->attributes['vorgangsnummer'] !== ''
            ? $vorgang->attributes['vorgangsnummer']
            : 'Fehler beim Anlegen der Aufgabe';

        ResponseFactory::instance()
            ->success(['vorgangsnummer' => $vorgangsnummer])
            ->respond();
    }

    /**
     * Ermöglicht das Abrufen des Logs für Anträge
     */
    public function actionGet(): void
    {
        $userId    = Yii::app()->request->getParam('userId');
        $dates     = Yii::app()->request->getQuery('dates', '');
        $dateArray = explode(',', $dates);

        if ($userId === null || $dates === '') {
            ResponseFactory::instance()
                ->error(['message' => 'User ID and date is required', 'result' => []])
                ->respond();
        }

        $sparte = Yii::app()->request->getParam('sparte', '');

        $query = Yii::app()->db->createCommand()
            ->select([
                'id',
                'user_id',
                'antragsdatum',
                'distribution_insurance_id',
                'gesellschaft',
                'insurance_company_id',
                'sparte',
                'beginn',
                'laufzeit',
                'beitrag_brutto',
                'beitrag_netto',
                'zahlweise',
                'client_id',
                'vn_anrede',
                'vn_vorname',
                'vn_nachname',
                'vn_geburtsdatum',
                'vn_strasse',
                'vn_hausnummer',
                'vn_plz',
                'vn_ort',
                'dispatch_mode',
            ])
            ->from('rechner__antrag_log');

        foreach ($dateArray as $key => $date) {
            $query->orWhere("antragsdatum between :{$key}from and :{$key}till", [
                ":{$key}from" => "{$date} 00:00:00",
                ":{$key}till" => "{$date} 23:59:59"
            ]);
        }

        $query->andWhere('user_id = :userId', [':userId' => $userId]);

        if ($sparte !== '') {
            $query->andWhere('sparte = :sparte', [':sparte' => $sparte]);
        }

        try {
            $logRows = $query->queryAll();

            ResponseFactory::instance()
                ->success(['message' => 'OK', 'result' => $logRows])
                ->respond();
        } catch (Exception $e) {
            ResponseFactory::instance()
                ->error(['message' => $e->getMessage(), 'result' => []])
                ->respond();
        }
    }

    public function actionLog(): void
    {
        $post = json_decode(Yii::app()->request->getRawBody(), true);

        $keysMissingInRequest = array_diff(self::REQUIRED_KEYS, array_keys($post));
        if (count($keysMissingInRequest) !== 0) {
            throw new InvalidArgumentException(
                'There are parameters missing in Request: "' . implode(', ', $keysMissingInRequest) . '"'
            );
        }

        $dataToLog                 = $post;
        $dataToLog['user_id']      = currentUser()->id;
        $dataToLog['antragsdatum'] = date('Y-m-d H:i:s');

        $company = InsuranceCompany::model()->findByPk($post['insurance_company_id']);

        if ($company === null) {
            throw new InvalidArgumentException('Given Company id ' . $post['insurance_company_id'] . ' is unknown.');
        }

        $dataToLog['gesellschaft'] = $company->name;

        $dbCommand = Yii::app()->db->createCommand();

        ResponseFactory::instance()
            ->success(['message' => 'OK', 'insert' => $dbCommand->insert('rechner__antrag_log', $dataToLog)])
            ->respond();
    }
}
