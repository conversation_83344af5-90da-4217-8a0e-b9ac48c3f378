<?php

class BirthdayMailController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    private $controllername = 'birthdayMail';

    public static function getTags()
    {
        $tags = [
            'Anrede'          => '{Anredekomplett}',
            'Makler-Vorname'  => '{Maklervorname}',
            'Makler-Nachname' => '{Maklernachname}',
        ];

        return $tags;
    }

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das BirthdayMail-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des BirthdayMail-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = BirthdayMail::model()->findByPk($id);

        return $model;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues BirthdayMail-Model erstellt werden soll.
     *  Die Methode öffnet das BirthdayMail-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     *
     */
    public function actionCreate()
    {
//        print_r($_POST);
//        exit();
        $data = $this->getCreateFormularData();

//        if (isset($data['create']))
//        {
        if ($this->validData($data)) {
            if ($model = $this->create($data) != null) {
                //echo "Das Erstellen war erfolgreich";
                //ToDo -> View aktualiseren
                $this->redirect('admin');

                return $model;
            }

            return null;
        }
//        }
    }

    /**
     *  Öffnet das BirthdayMail -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des BirthdayMail -Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $model        = new BirthdayMail();

        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/templateVerwaltung/geburstagsmail/create', ['model' => $model]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model BirthdayMail handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['BirthdayMail'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues BirthdayMail -Model
     *
     * @param type $data
     *          Die BirthdayMail -Daten als Array
     *
     * @return $model
     *          Das erstellte BirthdayMail -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $model             = new BirthdayMail();
        $model->attributes = $data['BirthdayMail'];
        if ($model->isActive == '1') {
            BirthdayMail::model()->updateAll(['isActive' => 0]);
        }
        if ($model->save()) {
            return $model;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein BirthdayMail -Model aktualisiert werden soll.
     *  Die Methode öffnet das BirthdayMail -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden BirthdayMail -Models
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if ($this->validData($data)) {
                if ($model = $this->update($data) != null) {
                    $this->redirect([$this->controllername . '/admin']);

                    return $model;
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das BirthdayMail-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden BirthdayMail-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/templateVerwaltung/geburstagsmail/update', ['model' => $model]);
        }

        $formulardata                       = $_POST;
        $formulardata['BirthdayMail']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein BirthdayMail-Exemplar
     *
     * @param type $data
     *          Die BirthdayMail-Daten als Array
     *
     * @return $model
     *          Das aktualisierte BirthdayMail-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $model             = $this->loadModel($data['BirthdayMail']['id']);
            $model->attributes = $data['BirthdayMail'];
            if ($model->isActive == '1') {
                BirthdayMail::model()->updateAll(['isActive' => 0]);
            }
            if ($model->save()) {
                return $model;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new BirthdayMail('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['BirthdayMail'])) {
            $model->attributes = $_GET['BirthdayMail'];
        }

        $this->render('//stammdaten/templateVerwaltung/geburstagsmail/admin', [
            'model' => $model,
        ]);
    }

    public function actionAjaxUpdateActiveMail()
    {
        BirthdayMail::model()->updateAll(['isActive' => 0]);
        BirthdayMail::model()->updateByPk(Yii::app()->request->getParam('active'), ['isActive' => 1]);
    }

    /**
     * Löscht das BirthdayMail-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden BirthdayMail-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('BirthdayMail');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Ersetzt die Tags in der Geburtstagsmail
     *
     * @param BirthdayMail $birthdayMail Birthdaymail, in der die Tags ersetzt werden sollen
     * @param Systemuser   $systemUser Systemuser, an den die Mail verschickt werden soll
     *
     * @return array
     */
    public function replaceTags(BirthdayMail $birthdayMail, Systemuser $systemUser): array
    {
        $tags = new Tags();

        $anredeKomplett = new CustomTag('{Anredekomplett}', sprintf('%s %s', $systemUser->getSalutation(), $systemUser->getFullname()));
        $maklerName     = new CustomTag('{Maklervorname}', $systemUser->firstname);
        $maklerNachname = new CustomTag('{Maklernachname}', $systemUser->lastname);

        $tags->addCustomTag($anredeKomplett);
        $tags->addCustomTag($maklerName);
        $tags->addCustomTag($maklerNachname);

        $subject  = $tags->replaceAvailable($birthdayMail->subject);
        $template = $tags->replaceAvailable($birthdayMail->template);

        return [
            'subject'  => $subject,
            'template' => $template
        ];
    }

    /**
     * Performs the AJAX validation.
     *
     * @param BirthdayMail $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'birthday-mail-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
