<?php

use Business\Bipro\TransactionIdGenerator;
use Business\Broker\DataProvider\CourtagezusageExportDataProvider;
use Business\User\Users;
use Business\Vermittlernummer\BrokerIdStatus;
use Business\Vermittlernummer\BrokerIdValidator;
use Components\AWS\Queue\StatusQueueService;
use Components\Bipro\Models\GdvFileMetadaten;
use Components\Bipro\Models\GdvFileRelationWithPostfach;
use Components\Bipro\Models\GdvFileRelationWithShare;
use Components\Bipro\Models\GdvInGesellschaftLog;
use Components\Bipro\Vertragsservice\PostfachGdv2BiproUploadService;
use Components\Share\Share;
use Components\Share\ShareGdv2BiproUploadService;
use Components\Vermittlernummern\BrokeridVariation;
use Demv\AWS\Bipro\Status\State;

/**
 * @method getDataArray()
 */
class BrokerIdController extends RightController
{
    use RenderJsonTrait;

    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     *             using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    public $excludedActions = [
        'AutocompleteBrokerid',
        'GeneratePDF',
        'GenerateExcel',
        'AjaxGetUserInfo',
    ];

    public function actions()
    {
        return [
            'validate' => [
                'class'     => 'application.components.actions.ModelAttributeEditAction',
                'modelname' => 'BrokerId',
                'type'      => AjaxAction::TYPE_GET,
                'attribute' => 'validated',
            ],
        ];
    }

    /**
     * Eine Funktion um eine Autovervollständigung auszuführen
     *
     * @return array action filters
     */
    public function actionAutocompleteBrokerid()
    {
        $res = [];

        if (isset($_GET['term'])) {
            $qtxt    = 'SELECT DISTINCT brokerid FROM broker_id WHERE brokerid LIKE :brokerid';
            $command = Yii::app()->db->createCommand($qtxt);
            $command->bindValue(':brokerid', '%' . $_GET['term'] . '%', PDO::PARAM_STR);
            $res = $command->queryColumn();
        }

        echo CJSON::encode($res);
        Yii::app()->end();
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues BrokerId-Model erstellt werden soll.
     *  Die Methode öffnet das BrokerId-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     * @param mixed $userID
     * @param mixed $insurancecompanyID
     */
    public function actionCreate($userID = '', $insurancecompanyID = '')
    {
        $data = $this->getCreateFormularData($userID, $insurancecompanyID);
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if ($brokerid = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $brokerid;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das BrokerId -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @param mixed $userID
     * @param mixed $insurancecompanyID
     *
     * @return type
     *              $Data-Array des BrokerId -Models
     */
    private function getCreateFormularData($userID, $insurancecompanyID)
    {
        $brokerid = new BrokerId();

        if (!$this->validData($_POST)) {
            $output = $this->renderInternal(
                Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'brokerId' . DIRECTORY_SEPARATOR . '_formFields.php',
                [
                    'brokerid'           => $brokerid,
                    'form'               => new CActiveForm(),
                    'prefix'             => 'Create',
                    'userid'             => $userID,
                    'insurancecompanyid' => $insurancecompanyID,
                ],
                true
            );
            Yii::app()->clientScript->renderBodyEnd($output);
            echo $output;
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model BrokerId handelt
     *
     * @param type $data
     *                   $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) && isset($data['BrokerId']);
    }

    /**
     * Diese Methode erstellt ein neues BrokerId -Model
     *
     * @param array $data
     *                            Die BrokerId -Daten als Array
     * @param mixed $closerequest
     *
     * @return $brokerid
     *                   Das erstellte BrokerId -Exemplar
     */
    public function create($data, $closerequest = true)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $brokerid                    = new BrokerId();
        $brokerid->attributes        = $data['BrokerId'];
        $brokerid->sparten           = $data['BrokerId']['sparten'] ?? [];
        $brokerid->last_edit_date    = date('Y-m-d');
        $brokerid->last_edit_user_id = Yii::app()->user->getID();

        if (in_array(
            (int) Yii::app()->user->getUserRole(),
            [
                UserRole::INSURANCECOMPANY,
                UserRole::INSURANCECOMPANY_CONTACTPERSON,
            ],
            true)
        ) {
            $brokerid->is_verkettet = 1;
        }

        if ($brokerid->save()) {
            if ($closerequest === true) {
                $courtagerequest = CourtageRequest::model()->findByAttributes(['broker_id_id' => $brokerid->id]);
                if ($courtagerequest !== null) {
                    $courtagerequest->close();
                }
            }

            if ($brokerid->status === BrokerIdStatus::AKTIV && $brokerid->brokerid !== null) {
                $this->retriggerGdvFileUpload($brokerid->brokerid);
            }

            return $brokerid;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein BrokerId -Model aktualisiert werden soll.
     *  Die Methode öffnet das BrokerId -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden BrokerId -Models
     */
    public function actionUpdate($id)
    {
        $brokerid = $this->loadModel($id);
        if ($brokerid != null) {
            $data = $this->getUpdateFormularData($id);
            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($brokerid = $this->update($data) != null) {
                        $this->actionAdmin(true);

                        return $brokerid;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das BrokerId-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *                 ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *                  Das Exemplar des BrokerId-Models mit der übergebenen ID falls dieses existiert, sonst null
     */
    public function loadModel($id)
    {
        $brokerid = BrokerId::model()->findByPk($id);

        return $brokerid;
    }

    /**
     * Öffnet das BrokerId-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *                 die ID des zu aktualisierenden BrokerId-Exemplares
     *
     * @return $data
     *               Das aktualierte Data-Array
     */
    private function getUpdateFormularData($id)
    {
        $brokerid = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $output = $this->renderInternal(
                Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'brokerId' . DIRECTORY_SEPARATOR . '_formFields.php',
                [
                    'brokerid' => $brokerid,
                    'form'     => new CActiveForm(),
                    'prefix'   => 'Update',
                ],
                true
            );
            Yii::app()->clientScript->renderBodyEnd($output);
            echo $output;
        }

        $formulardata                   = $_POST;
        $formulardata['BrokerId']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein BrokerId-Exemplar
     *
     * @param array $data
     *                    Die BrokerId-Daten als Array
     *
     * @return $brokerid
     *                   Das aktualisierte BrokerId-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $brokerid = $this->loadModel($data['BrokerId']['id']);

            $preclosed = false;
            if (empty($brokerid->brokerid)) {
                if (!empty($data['BrokerId']['brokerid'])) {
                    $data['BrokerId']['status'] = BrokerIdStatus::AKTIV;
                }
            }

            $brokerid->attributes        = $data['BrokerId'];
            $brokerid->sparten           = $data['BrokerId']['sparten'] ?? [];
            $brokerid->last_edit_date    = ViewHelper::getDate(true);
            $brokerid->last_edit_user_id = Yii::app()->user->getID();

            if (in_array(
                (int) Yii::app()->user->getUserRole(),
                [
                    UserRole::INSURANCECOMPANY,
                    UserRole::INSURANCECOMPANY_CONTACTPERSON,
                ],
                true)
            ) {
                $brokerid->is_verkettet = 1;
            }

            if ($brokerid->save()) {
                if (!$preclosed && $brokerid->status == BrokerIdStatus::AKTIV) {
                    foreach ($brokerid->courtageRequests as $value) {
                        $value->close();
                    }
                }

                return $brokerid;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all brokerids.
     *
     * @param mixed $lastSearch
     */
    public function actionAdmin($lastSearch = false)
    {
        $brokerid = new BrokerId('search');
        $brokerid->unsetAttributes();  // clear any default values
        if ($lastSearch) {
            if (method_exists($brokerid, 'useLastSearch')) {
                $brokerid->useLastSearch();
            }
        } else {
            if (isset($_GET['BrokerId'])) {
                $brokerid->attributes = $_GET['BrokerId'];
            }
        }
        $failMessage = Yii::app()->request->getParam('failMessage');
        $this->render(
            'admin',
            [
                'brokerid'    => $brokerid,
                'search'      => 'on',
                'failMessage' => $failMessage,
            ]
        );
    }

    /**
     * Löscht das BrokerId-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *                 $ID des zu löschenden BrokerId-Models
     *
     * @return type true || false
     *              true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);
    }

    /**
     * @param type $id
     *
     * @return true/false
     *                    true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    public function actionGeneratePDF()
    {
        $dataProvider    = new CourtagezusageExportDataProvider();
        $loggedUser      = Yii::app()->user->getSystemuserObject();
        $selectedColumns = [];
        $headerLabels    = [];

        foreach ($_POST['Broker_PDF'] as $key => $value) {
            if ($value === '1') {
                $selectedColumns[] = $key;
                $headerLabels[]    = CourtagezusageExportDataProvider::getColumnLabel($key);
            }
        }

        $data = [];
        foreach ($dataProvider->getData() as $rowData) {
            $row = [];

            foreach ($selectedColumns as $name) {
                $row[] = $rowData[$name];
            }

            $data[] = $row;
        }

        $pdf = new PDFGenerator($loggedUser, 'Systemuserauflistung');
        $pdf->Open();
        $pdf->generateTableMulticell(
            $data,
            $headerLabels,
            5,
            'Systemuserauflistung'
        );
        $pdf->Close();
        $pdf->Output();
    }

    public function actionGenerateExcel()
    {
        $dataProvider = new CourtagezusageExportDataProvider();
        $params       = Yii::app()->request->getPost('Broker_Excel');
        $names        = [];
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                if ($value == 1) {
                    $names[] = $key;
                }
            }
            $names = array_keys(
                array_filter(
                    $params,
                    static function (string $value): bool {
                        return $value === '1';
                    }
                )
            );

            $columns = array_map(
                static function ($value) {
                    return $value . ':string:' . CourtagezusageExportDataProvider::getColumnLabel($value);
                },
                $names,
            );

            if (!empty($names)) {
                $table = new ExcelGenerator($dataProvider, $columns);
                $table->generateTable();
            } else {
                echo 'Bitte wählen Sie mindestens eine Spalte für den Export aus!';
            }
        } else {
            $this->redirect([
                                'admin',
                                'failMessage' => 'Leider gab es ein Problem bei dem Export.',
                            ]);
        }
    }

    public function actionAjaxGetUserInfo()
    {
        $id       = $_POST['broker_id'];
        $brokerId = BrokerId::model()->findByPk($id);
        $user     = $brokerId->user;
        $address  = $user->address;
        $contact  = $user->contact;
        $data     = [];
        if (Yii::app()->user->isAdmin()) {
            $data['Vermittler-ID'] = $user->id;
        }

        $data += [
            'Name'             => Yii::app()->user->isAdmin() ?
                CHtml::link(
                    $user->getFullname(),
                    Yii::app()->controller->createUrl('/stammdaten/systemnutzer/' . $user->id),
                    [
                        'target' => '_blank',
                    ]
                ) :
                $user->getFullname(),
            'Vermittlernummer' => $brokerId->brokerid,
            'Adresse'          => $address->street . ' ' . $address->nr,
            'Postleitzahl'     => $address->zip,
            'Ort'              => $address->city,
            'Fax'              => $contact->fax,
            'Telefon'          => $contact->phone_business,
            'Mobil'            => $contact->phone_mobile,
            'E-Mail'           => $user->getMailAddress(),
        ];
        $output = $this->renderFile(
            Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'brokerId' . DIRECTORY_SEPARATOR . 'userInfoContent.php',
            [
                'data' => $data,
            ]
        );
        echo $output;
        Yii::app()->end();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param BrokerId $brokerid the brokerid to be validated
     */
    protected function performAjaxValidation($brokerid)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'broker-id-form') {
            echo CActiveForm::validate($brokerid);
            Yii::app()->end();
        }
    }

    /**
     * @param string   $brokerId
     * @param string   $insuranceCompanyId
     * @param int|null $brokerIdId
     *
     * @throws Exception
     */
    public function actionCheckBrokerId(string $brokerId, string $insuranceCompanyId, int $brokerIdId = null): void
    {
        $result = [
            'inputs' => [
                'brokerId'           => $brokerId,
                'insuranceCompanyId' => $insuranceCompanyId,
                'brokerIdId'         => $brokerIdId,
            ],
        ];

        // Check if insurance company exists
        if (!InsuranceCompany::model()->exists('id = :insurance_company_id', ['insurance_company_id' => $insuranceCompanyId])) {
            $result['status'] = 'error';
            $result['error']  = 'Gesellschaft nicht gefunden';
            $this->renderJsonResponse($result, 404);
        }

        // Check if brokerId is valid
        if (empty($brokerId)) {
            $result['status'] = 'error';
            $result['error']  = 'Vermittlernummer nicht gesetzt';
            $this->renderJsonResponse($result, 400);
        }

        // Check if broker exists
        if (null !== $brokerIdId && !BrokerId::model()->exists('id = :id', ['id' => $brokerIdId])) {
            $result['status'] = 'error';
            $result['error']  = 'Benutzer nicht gefunden';
            $this->renderJsonResponse($result, 404);
        }

        // Find potential broker ID conflicts
        $criteria = new CDbCriteria();
        $criteria->compare('insurance_company_id', $insuranceCompanyId);
        $patterns = BrokeridPattern::model()->findAll($criteria);

        $brokerIdVariation  = new BrokeridVariation($patterns, $brokerId);
        $brokerIdCandidates = $brokerIdVariation->includeOriginal()->getAllBrokerids();

        // Return a list of conflicted broker ids
        $conflictedCriteria = new CDbCriteria();
        $conflictedCriteria->compare('insurance_company_id', $insuranceCompanyId);
        $conflictedCriteria->addInCondition('brokerid', $brokerIdCandidates);

        $conflictedBrokers = BrokerId::model()->with(['user'])->findAll($conflictedCriteria);

        // Exclude the currently update-viewed user
        if (null !== $brokerIdId) {
            $conflictedBrokers = array_filter($conflictedBrokers, function (BrokerId $brokerId) use ($brokerIdId): bool {
                return (int) $brokerId->id !== $brokerIdId;
            });
        }

        // Return success if no conflicts found
        if (count($conflictedBrokers) === 0) {
            $result['status'] = 'success';
            $this->renderJsonResponse($result);
        } else {
            $result['status'] = 'error';
            $result['error']  = 'Vermittlernummern existieren schon in dieser gesellschaft:';
        }

        $result['conflicts'] = [];

        foreach ($conflictedBrokers as $conflictedBroker) {
            array_push($result['conflicts'], [
                'id'       => $conflictedBroker->id,
                'brokerId' => $conflictedBroker->brokerid,
                'status'   => $conflictedBroker->status,
                'byUser'   => $conflictedBroker->by_user,
                'active'   => !$conflictedBroker->inactive,
                'name'     => $conflictedBroker->user->getFullname(),
                'url'      => Yii::app()->createAbsoluteUrl('/stammdaten/systemnutzer/' . $conflictedBroker->user->id),
            ]);
        }

        $this->renderJsonResponse($result, 409);
    }

    public function actionValidateBrokerIdFormat(int $insuranceCompanyId, string $brokerId, int $typeId)
    {
        $validator        = new BrokerIdValidator(BrokerIdValidationRule::model());
        $validationResult = $validator->validateConsiderTypeIfDefined($brokerId, $insuranceCompanyId, $typeId);

        if ($validationResult->hasRule() && !$validationResult->isValid()) {
            $this->renderJsonResponse(['status' => 'success', 'isValid' => false, 'example' => $validationResult->getExample()], 200);
        }

        $this->renderJsonResponse(['status' => 'success', 'isValid' => true, 'example' => ''], 200);
    }

    /**
     * @throws CException
     */
    private function retriggerGdvFileUpload(string $brokerId): void
    {
        $this->retriggerShareUploads($brokerId);
        $this->retriggerPostfachUploads($brokerId);
    }

    /**
     * @param string $brokerId
     *
     * @throws CException
     */
    private function retriggerShareUploads(string $brokerId): void
    {
        $relationTable        = GdvFileRelationWithShare::model()->tableName();
        $gdvFileMetadataTable = GdvFileMetadaten::model()->tableName();

        $criteria           = new CDbCriteria();
        $criteria->distinct = true;
        $criteria->join     = "JOIN {$relationTable} r ON r.share_id = t.id "
                              . "JOIN {$gdvFileMetadataTable} gfm USING (gdv_file_id)";
        $criteria->addColumnCondition(
            [
                'gfm.vermittlernummer'   => $brokerId,
                't.is_gdvtobipro_import' => 0,
            ]
        );
        $criteria->addNotInCondition('t.user_id', [Users::DEMV_POSTFACH, Users::DEUTSCHER_MAKLERVERBUND]);

        $shares = Share::model()->findAll($criteria);

        foreach ($shares as $share) {
            if (!$share->isGdv2BiPRORelevant()) {
                continue;
            }
            $transactionId = TransactionIdGenerator::new()->create();
            StatusQueueService::share(State::started())->withTransactionId($transactionId)->send();
            if (ShareGdv2BiproUploadService::upload($share, $transactionId)) {
                $share->is_gdvtobipro_import = true;
            }

            $share->is_gdvtobipro_processed = true;

            if ($share?->status?->metastatus->is_terminierend) {
                $share->status->delete();
            }

            $share->save();
        }
    }

    private function retriggerPostfachUploads(string $brokerId): void
    {
        $relationTable        = GdvFileRelationWithPostfach::model()->tableName();
        $gdvFileMetadataTable = GdvFileMetadaten::model()->tableName();

        $criteria           = new CDbCriteria();
        $criteria->distinct = true;
        $criteria->join     = "JOIN {$relationTable} r ON r.gdv_in_gesellschaft_log_id = t.id "
                              . "JOIN {$gdvFileMetadataTable} gfm USING (gdv_file_id)";
        $criteria->addColumnCondition(
            [
                'gfm.vermittlernummer'   => $brokerId,
                't.is_gdvtobipro_import' => 0,
            ]
        );
        $criteria->addNotInCondition('t.user_id', [Users::DEMV_POSTFACH, Users::DEUTSCHER_MAKLERVERBUND]);

        $entries = GdvInGesellschaftLog::model()->findAll($criteria);

        foreach ($entries as $gdvInGesellschaftLog) {
            $transactionId = TransactionIdGenerator::new()->create();
            StatusQueueService::postfach(State::started())->withTransactionId($transactionId)->send();

            if (PostfachGdv2BiproUploadService::upload($gdvInGesellschaftLog, $transactionId)) {
                $gdvInGesellschaftLog->is_gdvtobipro_import = true;
            }

            $gdvInGesellschaftLog->is_gdvtobipro_processed = true;
            if ($gdvInGesellschaftLog?->status?->metastatus->is_terminierend) {
                $gdvInGesellschaftLog->status->delete();
            }

            $gdvInGesellschaftLog->save();
        }
    }
}
