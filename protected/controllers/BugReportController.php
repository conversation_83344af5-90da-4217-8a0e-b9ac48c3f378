<?php

/**
 * Class BugReportController
 */
class BugReportController extends AdminController
{
    /**
     * @var string
     */
    public $layout = '//layouts/column2';
    /**
     * @var string
     */
    private $controllername = 'bugReport';

    /**
     * @return array
     */
    public function accessRules(): array
    {
        if (Yii::app()->user->isAdmin()) {
            return [
                [
                    'allow',
                    'users' => ['*'],
                ],
            ];
        }

        return [
            [
                'allow',
                'actions' => ['Create'],
                'users'   => ['*'],
            ],
            [
                'deny', // deny all users
                'users' => ['*'],
            ],
        ];
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues BugReport-Model erstellt werden soll.
     *  Die Methode öffnet das BugReport-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionView($id)
    {
        $model = BugReport::model()->findByPk($id);
        $this->render('view', ['model' => $model]);
    }

    public function actionChangeStatus()
    {
        $bugreport = BugReport::model()->findByPk($_POST['id']);
        if (!empty($bugreport)) {
            $bugreport->status = $_POST['status'];
            if ($bugreport->save()) {
                echo 'success';
            }
        }
    }

    public function actionChangePriority()
    {
        $bugreport = BugReport::model()->findByPk($_POST['id']);
        if (!empty($bugreport)) {
            $bugreport->priority = $_POST['priority'];
            if ($bugreport->save()) {
                echo 'success';
            }
        }
    }

    /**
     * @param bool $error
     *
     * @return BugReport|null
     */
    public function actionCreate($error = false)
    {
        try {
            $data = $this->getCreateFormularData($error);
            if (isset($data['create'])) {
                if ($this->validData($data)) {
                    if (($model = $this->create($data)) != null) {
                        Yii::app()->user->setFlash('success',
                                                   'Ihre Fehlerbeschreibung wurde erfolgreich übermittelt (Report-ID: ' . $model->id . '). Vielen Dank!');
                        $this->redirect(['home/index']);
                    } else {
                        $this->redirect(['home/index']);

                        return null;
                    }
                }
            }
        } catch (Exception $exception) {
            //Nothing
        }
    }

    /**
     *  Öffnet das BugReport -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return mixed
     *          $Data-Array des BugReport -Models
     */
    private function getCreateFormularData($error)
    {
        $model = new BugReport();

        if (!$this->validData($_POST)) {
            $this->render('create', ['model' => $model, 'error' => $error]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model BugReport handelt
     *
     * @param mixed $data
     *              $Data-Array
     *
     * @return bool
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data): bool
    {
        return isset($data) && isset($data['BugReport']);
    }

    /**
     * Diese Methode erstellt ein neues BugReport -Model
     *
     * @param mixed $data
     *          Die BugReport -Daten als Array
     *
     * @return $model
     *          Das erstellte BugReport -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $model             = new BugReport();
        $model->attributes = $data['BugReport'];
        $model->user_id    = Yii::app()->user->getID();
        if (!empty($model->report)) {
            if ($model->save()) {
                return $model;
            }
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein BugReport -Model aktualisiert werden soll.
     *  Die Methode öffnet das BugReport -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden BugReport -Models
     *
     * @return BugReport | null
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update']) && $this->validData($data) && $model = $this->update($data) != null) {
                $this->redirect([$this->controllername . '/admin']);
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das BugReport-Exemplar mit der übergebenen ID
     *
     * @param int $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return BugReport|null
     *          Das Exemplar des BugReport-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = BugReport::model()->findByPk($id);

        return $model;
    }

    /**
     *
     * Öffnet das BugReport-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param int $id
     *          die ID des zu aktualisierenden BugReport-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('update', ['model' => $model]);
        }

        $formulardata                    = $_POST;
        $formulardata['BugReport']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein BugReport-Exemplar
     *
     * @param mixed $data
     *          Die BugReport-Daten als Array
     *
     * @return $model
     *          Das aktualisierte BugReport-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $model             = $this->loadModel($data['BugReport']['id']);
            $model->attributes = $data['BugReport'];
            if ($model->save()) {
                return $model;
            }
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new BugReport('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['BugReport'])) {
            $model->attributes = $_GET['BugReport'];
        }

        $this->render('admin', [
            'model' => $model,
        ]);
    }

    /**
     * Löscht das BugReport-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param int $id ID des zu löschenden BugReport-Models
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
        }
    }

    /**
     * @param int $id
     *
     * @return bool
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id): bool
    {
        $bugReport = $this->loadModel($id);
        if (empty($bugReport)) {
            return true;
        }

        return $bugReport->delete();
    }

    /**
     * @param int $id
     */
    public function actionShowLog($id)
    {
        if (Yii::app()->user->isAdmin()) {
            $error = LogPhpError::model()->findByPk($id);

            $this->render('error', [
                'model' => $error,
            ]);
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param BugReport $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'bug-report-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
