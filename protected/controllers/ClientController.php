<?php

use Bestandskorrektur\CommandFactory;
use Business\Client\Search\ExtendedClientSearch;

class ClientController extends RightController
{
    use RenderJsonTrait;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    public $excludedActions = [
        'BirthdayClients',
        'clientBirthdayExcelExport',
        'AjaxQuery',
        'AjaxQueryById',
        'ShowContractSearchFields',
        'clientReport',
    ];

    /**
     * @refactoring ES sollte an allen Stellen die Clientdropdown verwendet werden, Methode sollte daher veraltet sein
     * Gibt die Daten zurück, die in einem DropdownMenü mit Kunden angezeigt werden sollen
     *
     * @param String $condition
     *          Optional eine Condition, mit deren Hilfe die Kunden gefiltert werden
     *
     * @return Array
     *          Kundendaten in der Form id => fullname
     *
     */
    public static function getClientDropdownData($condition = '1')
    {
        $command = CommandFactory::build();
        $command->select(['id', 'firstname', 'lastname'])->from('client')->where($condition);
        $results = $command->queryAll();
        $data    = [];
        foreach ($results as $result) {
            $data[$result['id']] = $result['firstname'] . ' ' . $result['lastname'];
        }

        return $data;
    }

    /**
     * Bearbeitet den Kunden anhand der Post-Date order rendert die UpdateView, wenn das Post-Array leer sein sollte
     *
     * @param $id
     *      ID des Kunden
     * @param $activetab
     */
    public function actionUpdate($id, $activetab = '', $current_tab = null)
    {
        $tab = Yii::app()->request->getParam('tab');
        if (empty($tab)) {
            $tab = $activetab;
        }
        $subs = [
            'verträge' => KundenakteTabs::VERTRAEGE_BEDARF
        ];
        $this->redirect(Kundenakte::urlTo($id, isset($subs[$tab]) ?? $tab));
    }

    public function actionAdmin($duplicates = 0, $useLastSearch = 0, $new = 0)
    {
        $this->redirect('/kundenakte/index/admin', ['new' => $new, 'duplicates' => $duplicates]);
    }

    public function actionBirthdayClients($from = null, $to = null, $user_id = null)
    {
        $this->render('//home/<USER>/kundengeburtstage/birthdayClients', ['from' => $from, 'to' => $to, 'user_id' => $user_id]);
    }

    /**
     * @refactoring $_GET['q'] sollte als Parameter in den Methodenkopf
     * Dienst als Dataprovider für die ClientDropdown des ExtHelpers
     *
     * @param $q Eingabe der Dropdown, mit Hilfe welcher gesucht wird
     *
     * @echo JSON mit den wichtigsten Daten der gefundenen Kunden
     */
    public function actionAjaxQuery()
    {
        $clientSearch           = new ExtendedClientSearch();
        $clientSearch->user     = currentUser();
        $clientSearch->fullname = $_GET['q'];
        $dataProvider           = $clientSearch->searchAll();
        $result                 = [];
        /** @var Client $client */
        foreach ($dataProvider->getData() as $client) {
            $result[] = [
                'id'        => $client->id,
                'text'      => $this->buildText($client),
                'firstname' => $client->firstname,
                'lastname'  => $client->lastname,
                'birthday'  => $client->birthday,
            ];
        }
        echo CJSON::encode($result);
        Yii::app()->end();
    }

    public function actionAjaxQueryById(int $clientId)
    {
        $client = Client::model()->findByPk($clientId);

        if ($client === null) {
            $this->renderJsonResponse([], 404);
        }

        $this->renderJson([
                              'id'        => $client->id,
                              'text'      => $this->buildText($client),
                              'firstname' => $client->firstname,
                              'lastname'  => $client->lastname,
                              'birthday'  => $client->birthday,
                          ]);
    }

    public function actionClientBirthdayExcelExport()
    {
        set_time_limit(0);
        $dataProvider                     = ExportSessionManager::getDataProvider(new Client(), 'clientBirthdaySearch');
        $dataProvider->criteria->distinct = 'client_id';
        $names                            = array_keys(
            array_filter($_POST['Client'], function (string $value): bool {
                return $value === '1';
            })
        );
        $names[]                          = 'id';

        $table = new ExcelGenerator($dataProvider, $names, new Client());

        $table->generateTable();
    }

    /**
     * @param Client $client
     *
     * @return string
     */
    private function buildText(Client $client): string
    {
        $text = $client->getFullname();
        $text .= $client->birthday !== null ? sprintf(' (%s)', $client->birthday) : null;
        $text .= $client->broker !== null ? sprintf(' (%s)', $client->broker->Fullname) : null;

        return $text;
    }
}
