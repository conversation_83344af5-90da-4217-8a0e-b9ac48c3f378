<?php

use <PERSON>pfelbox\FileDownload\FileDownload;
use Components\Client\Providers\ClientfileProvider;
use Components\Document\DocumentStream;
use Demv\JSend\ResponseFactory;

class ClientFilesController extends Controller
{
    /**
     * @param $id
     *
     * @return ClientFiles
     */
    public function loadModel($id)
    {
        $clientfiles = ClientfileProvider::forUser(currentUser())->find()->findByPk($id);

        if ($clientfiles === null) {
            throw new CHttpException(404, 'Wir konnten die Datei nicht finden');
        }

        return $clientfiles;
    }

    public function actionPreview(int $id, $download = false)
    {
        $model      = $this->loadModel($id);
        $fileStream = DocumentStream::create($model)->getFileStream();
        if ($fileStream === null) {
            return ResponseFactory::instance()->error(['message' => 'Konvertierung fehlgeschlagen'])->respond();
        }
        // Convert file extension to lower case because FileDownload gets the MIME type wrong otherwise
        $filename   = preg_replace_callback('/\.[^.]*$/', static fn ($s) => mb_strtolower($s[0]), $model->getFileName());

        @(new FileDownload($fileStream))->sendDownload($filename, $download);
    }

    /**
     * @param int  $id
     * @param bool $download
     *
     * @throws \League\Flysystem\FileNotFoundException
     */
    public function actionDownload(int $id, $download = false)
    {
        $model = $this->loadModel($id);

        // Convert file extension to lower case because FileDownload gets the MIME type wrong otherwise
        $filename = preg_replace_callback('/\.[^.]*$/', static fn ($s) => mb_strtolower($s[0]), $model->getFileName());

        @(new FileDownload($model->getContentStream()))->sendDownload($filename, $download);
    }
}
