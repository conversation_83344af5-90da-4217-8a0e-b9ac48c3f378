<?php

class ClientTargetGroupController extends AdminController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    private $controllername = 'clientTargetGroup';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the clienttargetgroup to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'clienttargetgroup' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das ClientTargetGroup-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des ClientTargetGroup-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $clienttargetgroup = ClientTargetGroup::model()->findByPk($id);

        return $clienttargetgroup;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues ClientTargetGroup-Model erstellt werden soll.
     *  Die Methode öffnet das ClientTargetGroup-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($clienttargetgroup = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $clienttargetgroup;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das ClientTargetGroup -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des ClientTargetGroup -Models
     */
    private function getCreateFormularData()
    {
        $formulardata      = [];
        $clienttargetgroup = new ClientTargetGroup();

        if (!$this->validData($_POST)) {
            $output = $this->renderPartial('//stammdaten/verwaltung/zielgruppen/gruppe/_form', ['clienttargetgroup' => $clienttargetgroup], true);
            Yii::app()->clientScript->renderBodyEnd($output);
            echo $output;
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model ClientTargetGroup handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['ClientTargetGroup'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues ClientTargetGroup -Model
     *
     * @param type $data
     *          Die ClientTargetGroup -Daten als Array
     *
     * @return $clienttargetgroup
     *          Das erstellte ClientTargetGroup -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $clienttargetgroup             = new ClientTargetGroup();
        $clienttargetgroup->attributes = $data['ClientTargetGroup'];

        if ($clienttargetgroup->save()) {
            return $clienttargetgroup;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein ClientTargetGroup -Model aktualisiert werden soll.
     *  Die Methode öffnet das ClientTargetGroup -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden ClientTargetGroup -Models
     */
    public function actionUpdate($id)
    {
        $clienttargetgroup = $this->loadModel($id);
        if ($clienttargetgroup != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($clienttargetgroup = $this->update($data) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $clienttargetgroup;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das ClientTargetGroup-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden ClientTargetGroup-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $clienttargetgroup = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/verwaltung/zielgruppen/gruppe/update', ['clienttargetgroup' => $clienttargetgroup]);
        }

        $formulardata                            = $_POST;
        $formulardata['ClientTargetGroup']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein ClientTargetGroup-Exemplar
     *
     * @param type $data
     *          Die ClientTargetGroup-Daten als Array
     *
     * @return $clienttargetgroup
     *          Das aktualisierte ClientTargetGroup-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $clienttargetgroup             = $this->loadModel($data['ClientTargetGroup']['id']);
            $clienttargetgroup->attributes = $data['ClientTargetGroup'];
            if ($clienttargetgroup->save()) {
                return $clienttargetgroup;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all clienttargetgroups.
     */
    public function actionAdmin()
    {
        $clienttargetgroup = new ClientTargetGroup('search');
        $clienttargetgroup->unsetAttributes();  // clear any default values
        if (isset($_GET['ClientTargetGroup'])) {
            $clienttargetgroup->attributes = $_GET['ClientTargetGroup'];
        }

        $this->render('//stammdaten/verwaltung/zielgruppen/gruppe/admin', [
            'clienttargetgroup' => $clienttargetgroup,
        ]);
    }

    /**
     * Löscht das ClientTargetGroup-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden ClientTargetGroup-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all clienttargetgroups.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('ClientTargetGroup');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ClientTargetGroup $clienttargetgroup the clienttargetgroup to be validated
     */
    protected function performAjaxValidation($clienttargetgroup)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'client-target-group-form') {
            echo CActiveForm::validate($clienttargetgroup);
            Yii::app()->end();
        }
    }
}
