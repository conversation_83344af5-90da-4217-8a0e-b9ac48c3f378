<?php

class ClientTargetGroupProductComboAssignmentController extends AdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    private $controllername = 'clientTargetGroupProductComboAssignment';

    /**
     * Displays a particular ctgpca.
     *
     * @param integer $id the ID of the ctgpca to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'ctgpca' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das ClientTargetGroupProductComboAssignment-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des ClientTargetGroupProductComboAssignment-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $ctgpca = ClientTargetGroupProductComboAssignment::model()->findByPk($id);

        return $ctgpca;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues ClientTargetGroupProductComboAssignment-Model erstellt werden soll.
     *  Die Methode öffnet das ClientTargetGroupProductComboAssignment-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($ctgpca = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $ctgpca;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das ClientTargetGroupProductComboAssignment -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des ClientTargetGroupProductComboAssignment -Models
     */
    private function getCreateFormularData()
    {
        $ctgpca = new ClientTargetGroupProductComboAssignment();

        if (!$this->validData($_POST)) {
            $output = $this->renderPartial('//stammdaten/verwaltung/zielgruppen/produktzuweisung/_form', ['ctgpca' => $ctgpca], true);
            Yii::app()->clientScript->renderBodyEnd($output);
            echo $output;
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model ClientTargetGroupProductComboAssignment handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['ClientTargetGroupProductComboAssignment'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues ClientTargetGroupProductComboAssignment -Model
     *
     * @param type $data
     *          Die ClientTargetGroupProductComboAssignment -Daten als Array
     *
     * @return $ctgpca
     *          Das erstellte ClientTargetGroupProductComboAssignment -Exemplar
     */
    public function create($data)
    {
//        echo '<pre>';
//        print_r ($data);
//        echo '</pre>';
//        exit();
        if (!$this->validData($data)) {
            return null;
        }

        $ctgpcas = ClientTargetGroupProductComboAssignment::model()->findAllByAttributes(['client_target_group_id' => $data['ClientTargetGroupProductComboAssignment']['client_target_group_id']]);
        foreach ($ctgpcas as $value) {
            $value->delete();
        }

        if (!empty($data['ClientTargetGroupProductComboAssignment']['product_combo_id']) && is_array($data['ClientTargetGroupProductComboAssignment']['product_combo_id'])) {
            foreach ($data['ClientTargetGroupProductComboAssignment']['product_combo_id'] as $value) {
                $ctgpca                         = new ClientTargetGroupProductComboAssignment();
                $ctgpca->client_target_group_id = $data['ClientTargetGroupProductComboAssignment']['client_target_group_id'];
                $ctgpca->product_combo_id       = $value;
                $ctgpca->save();
            }
        }
        if (!empty($data['ClientTargetGroupProductComboAssignment']['product_combo_id_obligation']) && is_array($data['ClientTargetGroupProductComboAssignment']['product_combo_id_obligation'])) {
            foreach ($data['ClientTargetGroupProductComboAssignment']['product_combo_id_obligation'] as $value) {
                $ctgpca                         = new ClientTargetGroupProductComboAssignment();
                $ctgpca->client_target_group_id = $data['ClientTargetGroupProductComboAssignment']['client_target_group_id'];
                $ctgpca->product_combo_id       = $value;
                $ctgpca->obligation             = 1;
                $ctgpca->save();
            }
        }
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein ClientTargetGroupProductComboAssignment -Model aktualisiert werden soll.
     *  Die Methode öffnet das ClientTargetGroupProductComboAssignment -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden ClientTargetGroupProductComboAssignment -Models
     */
    public function actionUpdate($id)
    {
        $ctgpca = $this->loadModel($id);
        if ($ctgpca != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($ctgpca = $this->update($data) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $ctgpca;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das ClientTargetGroupProductComboAssignment-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden ClientTargetGroupProductComboAssignment-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $ctgpca = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/verwaltung/zielgruppen/produktzuweisung/update', ['ctgpca' => $ctgpca]);
        }

        $formulardata                                                  = $_POST;
        $formulardata['ClientTargetGroupProductComboAssignment']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein ClientTargetGroupProductComboAssignment-Exemplar
     *
     * @param type $data
     *          Die ClientTargetGroupProductComboAssignment-Daten als Array
     *
     * @return $ctgpca
     *          Das aktualisierte ClientTargetGroupProductComboAssignment-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $ctgpca             = $this->loadModel($data['ClientTargetGroupProductComboAssignment']['id']);
            $ctgpca->attributes = $data['ClientTargetGroupProductComboAssignment'];
            if ($ctgpca->save()) {
                return $ctgpca;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all ctgpcas.
     */
    public function actionAdmin()
    {
        $ctgpca = new ClientTargetGroupProductComboAssignment('search');
        $ctgpca->unsetAttributes();  // clear any default values
        if (isset($_GET['ClientTargetGroupProductComboAssignment'])) {
            $ctgpca->attributes = $_GET['ClientTargetGroupProductComboAssignment'];
        }

        $this->render('//stammdaten/verwaltung/zielgruppen/produktzuweisung/admin', [
            'ctgpca' => $ctgpca,
        ]);
    }

    /**
     * Löscht das ClientTargetGroupProductComboAssignment-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden ClientTargetGroupProductComboAssignment-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all ctgpcas.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('ClientTargetGroupProductComboAssignment');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ClientTargetGroupProductComboAssignment $ctgpca the ctgpca to be validated
     */
    protected function performAjaxValidation($ctgpca)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'client-target-group-product-combo-assignment-form') {
            echo CActiveForm::validate($ctgpca);
            Yii::app()->end();
        }
    }
}
