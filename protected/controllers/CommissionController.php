<?php

class CommissionController extends GlobalRightController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout           = '//layouts/column2';
    public $excludedActions  = [
        'CommissionException',
        'CommissionExceptionUpdate',
    ];
    private $prefix           = 'Commission';

    /**
     *  initializes the needed datas that need for the form and  the create of
     * company exceptions for a specific user in method createException
     */
    public function actionCommissionException()
    {
        if (isset($_POST['insuranceCompany_select'])) {
            $result = InsuranceCompany::model()->findAllByPk($_POST['insuranceCompany_select']);

            $insuranceCompanies = [];
            foreach ($result as $value) {
                $insuranceCompanies[$value->id] = $value->name;
            }

            $procedure                                            = [];
            $procedure['insuranceCompany_select']                 = $insuranceCompanies;
            $systemuserId                                         = $_POST['exceptionSystemuserId'];
            $procedure['systemuser']                              = $systemuserId;
            Yii::app()->session['procedure_commission_exception'] = $procedure;

            $userCommission        = '';
            $userTrailerCommission = '';

            $mainCommission        = '';
            $mainTrailerCommission = '';

            $this->render('_exceptionForm',
                [
                    'systemuser'            => Systemuser::model()->findByPk($systemuserId),
                    'prefix'                => $this->prefix,
                    'fromSystemuser'        => true,
                    'insuranceCompanies'    => $insuranceCompanies,
                    'mainTrailerCommission' => $mainTrailerCommission,
                    'mainCommission'        => $mainCommission,
                    'UserCourtage'          => $userCommission,
                    'userTrailerCommission' => $userTrailerCommission
                ]);
        }
        //ToDO: Verweis auf update
    }

    /**
     *  initializes the needed datas that need for the updateAdmin form.
     */
    public function actionCommissionExceptionUpdate()
    {
        $systemuserId = (int) $_POST['exceptionSystemuserId'];

        $criteria = new CDbCriteria();
        $criteria->addCondition('user_id = :user_id');
        $criteria->params['user_id'] = $systemuserId;
        $criteria->addCondition('company_id is not NULL');
        $criteria->group = 'company_id ASC';

        $wholeCommissions        = Commission::model()->findAll($criteria);
        $wholeTrailerCommissions = TrailerCommission::model()->findAll($criteria);

        $commissionCounter = 0;
        foreach ($wholeCommissions as $companyCommission => $commissionData) {
            foreach ($wholeTrailerCommissions as $companyTrailerCommission => $trailerCommissionData) {
                if ($commissionData->company_id == $trailerCommissionData->company_id) {
                    unset($wholeCommissions[$commissionCounter]);
                }
            }
            $commissionCounter++;
        }

        $this->render('updateAdmin',
            [
                'systemuser'              => Systemuser::model()->findByPk($systemuserId),
                'prefix'                  => $this->prefix,
                'wholeCommissions'        => $wholeCommissions,
                'wholeTrailerCommissions' => $wholeTrailerCommissions,
            ]);
    }

    /**
     *  initializes the needed datas that need for the form and  the update of
     * company exceptions for a specific user in method createException
     */
    public function actionUpdateCompany(int $company_id, int $user_id)
    {
        $result = InsuranceCompany::model()->findByPk($company_id);

        $insuranceCompanies              = [];
        $insuranceCompanies[$result->id] = $result->name;

        $criteria = new CDbCriteria();
        $criteria->addCondition('user_id = :user_id');
        $criteria->addCondition('company_id = :company_id');
        $criteria->addCondition('product_combo_id is NULL');
        $criteria->params = [
            ':user_id'    => $user_id,
            ':company_id' => (int) $result->id
        ];

        $mainCommission        = Commission::model()->find($criteria);
        $mainTrailerCommission = TrailerCommission::model()->find($criteria);

        $userCommission        = Commission::groupByProduct($user_id, $result->id);
        $userTrailerCommission = TrailerCommission::groupByProduct($user_id, $result->id);

        $this->render('_exceptionForm',
            [
                'systemuser'            => Systemuser::model()->findByPk($user_id),
                'prefix'                => $this->prefix,
                'UserCourtage'          => $userCommission,
                'userTrailerCommission' => $userTrailerCommission,
                'fromUpdate'            => true,
                'insuranceCompanies'    => $insuranceCompanies,
                'mainCommission'        => $mainCommission,
                'mainTrailerCommission' => $mainTrailerCommission,
                'UserCourtage'          => $userCommission,
                'userTrailerCommission' => $userTrailerCommission
            ]);
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues Commission-Model erstellt werden soll.
     *  Die Methode öffnet das Commission-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $trailerCommissionController = new TrailerCommissionController('trailerCommissionController');
        $trailerCommissionController->actionCreate($_POST);
        $systemuserID = Yii::app()->session['procedure_commission_exception']['systemuser'];

        $_POST = ViewHelper::parseToArray($_POST);

        $this->createExceptions($_POST, $systemuserID);
    }

    /**
     * deletes all commissions exceptions that have a forwarded company_id and generates them
     * with the new values
     *
     * @param type $data
     *                  $needed datas for the commissions
     * @param type $userId
     *                  $userid
     */
    public function createExceptions($data, $userId)
    {
        if (isset($_POST['insuranceCompany_select'])) {
            $companies = Yii::app()->request->getParam('insuranceCompany_select');
        }
        $commissionArray = Yii::app()->request->getParam('Commission');

        if (count($companies) == 1) {
        }

        foreach ($companies as $key => $companyID) {
            foreach ((Commission::model()->findAllByAttributes(['user_id' => $userId])) as $value) {
                if ($value->company_id == $companyID) {
                    $this->delete($value->id);
                }
            }
        }

        $newDataArray = [];

        foreach ($companies as $companieID) {
            foreach ($commissionArray as $key => $value) {
                if ($key == 'CommissionMain') {
                    $newDataArray[] = [
                        'user_id'            => $userId,
                        'general'            => 1,
                        'product_combo_id'   => null,
                        'commission'         => $value['commission'],
                        'trailer_commission' => $value['trailer_commission'],
                        'promille'           => $value['choosen']
                    ];
                } else {
                    $commission        = $value['commission'];
                    $trailercommission = $value['trailer_commission'];
                    $newDataArray[]    = [
                        'user_id'            => $userId,
                        'product_combo_id'   => $value['id'],
                        'commission'         => $commission,
                        'trailer_commission' => $trailercommission,
                        'promille'           => $value['choosen']
                    ];
                }
            }
        }
        foreach ($newDataArray as $value1) {
            if ($value1['commission'] != '') {
                $this->createSingle($value1);
            }
        }
        if (isset(Yii::app()->session['procedure_commission_exception'])) {
            $procedure       = Yii::app()->session['procedure_commission_exception'];
            $companie_select = $procedure['insuranceCompany_select'];

            //!Start! update Yii::app()->session['insuranceCompany_select'] for next Eselect (unset selected companies)
            foreach ($companie_select as $companieID => $companyName) {
                foreach ($data['insuranceCompany_select'] as $exceptionName => $exceptionID) {
                    if ($companieID == $exceptionID) {
                        unset($companie_select[$companieID]);
                    }
                }
            }
            $procedure['insuranceCompany_select']                 = $companie_select;
            Yii::app()->session['procedure_commission_exception'] = $procedure;
        }
        //!End! update Yii::app()->session['insuranceCompany_select'] for next Eselect (unset selected companies)

        $systemuser = Systemuser::model()->findByPk($userId);

        $mainCommission        = '';
        $mainTrailerCommission = '';
        $userTrailerCommission = '';
        $userTrailerCommission = '';

        if (isset($companie_select) && empty($companie_select)) {
            echo '<script>window.close();</script>';
        } else {
            $procedure = Yii::app()->session['procedure_commission_exception'];
            $this->render('_exceptionForm',
                [
                    'systemuser'            => $systemuser,
                    'prefix'                => $this->prefix,
                    'insuranceCompanies'    => $procedure['insuranceCompany_select'],
                    'UserCourtage'          => new Commission(),
                    'userTrailerCommission' => $userTrailerCommission,
                    'mainCommission'        => $mainCommission,
                    'mainTrailerCommission' => $mainTrailerCommission
                ]);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $this->loadModel($id)->delete();
    }

    /**
     * Lädt das Commission-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des Commission-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $commission = Commission::model()->findByPk($id);

        return $commission;
    }

    /**
     * Diese Methode erstellt ein Commissions-Eintrag
     *
     * @param type $data
     *          Die Commission -Daten als Array
     *
     * @return $commission
     *          Das erstellte Commission -Exemplar
     */
    public function createSingle($data)
    {
        $commission = new Commission();
        $commission->unsetAttributes();
        $commission->attributes = $data;
        if ($commission->save()) {
            return $commission;
        }

        return null;
    }

    /**
     * Diese Methode erstellt ein neues Commission -Model
     * Es wird für jeder Commission ein einzelner Eintrag in der Datenbank vorgenommen.
     *
     * @param type $data
     *          Die Commission -Daten als Array
     *
     * @return $commission
     *          Das erstellte Commission -Exemplar
     */
    public function create($data, $userId, $createCustom = true, $commissionType = 0)
    {
        foreach ((Commission::model()->findAllByAttributes(['user_id' => $userId])) as $value) {
            if ($value->company_id == null) {
                $this->delete($value->id);
            }
        }
        if ($createCustom) {
            $newDataArray    = [];
            $commissionArray = $data;
            foreach ($commissionArray as $key => $value) {
                if ($key == 'CommissionMain' && $commissionType == 0) {
                    $newDataArray[] = [
                        'user_id'            => $userId,
                        'general'            => 1,
                        'product_combo_id'   => null,
                        'commission'         => $value['commission'],
                        'trailer_commission' => $value['trailer_commission'],
                        'promille'           => $value['choosen']
                    ];
                } else {
                    if ($key != 'commission_type' && $key != 'CommissionMain' && $commissionType == 1) {
                        $commission        = $value['commission'];
                        $trailercommission = $value['trailer_commission'];
                        $newDataArray[]    = [
                            'user_id'            => $userId,
                            'product_combo_id'   => $value['id'],
                            'commission'         => $commission,
                            'trailer_commission' => $trailercommission,
                            'promille'           => $value['choosen']
                        ];
                    }
                }
            }
            foreach ($newDataArray as $key1 => $value1) {
                if ($value1['commission'] != '') {
                    $this->createSingle($value1);
                }
            }
        }
    }

    /**
     * Löscht das Commission-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden Commission-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($company_id, $user_id)
    {
        $trailerCommissionController = new TrailerCommissionController('trailerCommissionController');
        $trailerCommissionController->deleteUpdate($company_id, $user_id);
        $this->deleteUpdate($company_id, $user_id);
    }

    public function deleteUpdate($company_id, $user_id)
    {
        foreach ((Commission::model()->findAllByAttributes(['user_id' => $user_id])) as $value) {
            if ($value->company_id == $company_id) {
                $this->loadModel($value->id)->delete();
            }
        }
    }

    /**
     * Manages all commissions.
     */
    public function actionAdmin()
    {
        $commission = new Commission('search');
        $commission->unsetAttributes();  // clear any default values
        if (isset($_GET['Commission'])) {
            $commission->attributes = $_GET['Commission'];
        }

        $this->render('admin', [
            'commission' => $commission,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Commission $commission the commission to be validated
     */
    protected function performAjaxValidation($commission)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'commission-form') {
            echo CActiveForm::validate($commission);
            Yii::app()->end();
        }
    }

    /**
     *  Öffnet das Commission -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des Commission -Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $commission   = new Commission();
        $_POST        = ViewHelper::parseToArray($_POST);
        if (!isset($_POST[$this->prefix]) || !$this->validData($_POST[$this->prefix])) {
            $this->render('create',
                ['commission' => $commission, 'prefix' => $this->prefix, 'systemuser' => new Systemuser()]);

            return null;
        }
        $formulardata = $_POST;

        return $formulardata;
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model Commission handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) && (isset($data['CommissionMain']))
//                isset($data['Commission']['user_id']) &&
//                $data['Commission']['user_id'] != '' &&
        ;
    }

    /**
     *
     * Öffnet das Commission-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden Commission-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $formulardata = null;
        $commission   = $this->loadModel($id);
        $_POST        = ViewHelper::parseToArray($_POST);
        if (!isset($_POST[$this->prefix])) {
            $this->render('update', ['commission' => $commission, 'prefix' => $this->prefix]);
        }
        $formulardata = $_POST;

//        $formulardata[$this->prefix]['Commission']['id'] = $id;
        return $formulardata;
    }
}
