<?php

/**
 * Created by PhpStorm.
 * User: alex
 * Date: 01.09.16
 * Time: 16:14
 */
class CompanyInfoController extends Controller
{
    use RenderAjaxTrait;
    
    public function actionInfo($id, $productId)
    {
        $company      = $this->loadModel($id);
        $productCombo = null;
        if (!empty($productId)) {
            $productCombo = ProductCombo::model()->findByPk($productId);
        }
        $companyData = new CompanyData($company, currentUser(), $productCombo);
        $this->renderAjax('companyInfo', ['companyData' => $companyData]);
        Yii::app()->end();
    }

    /**
     * @param $id
     *
     * @return InsuranceCompany
     * @throws HttpException
     */
    private function loadModel($id)
    {
        $company = InsuranceCompany::model()->findByPk($id);
        if ($company === null) {
            throw new HttpException(404, 'Gesellschaft nicht gefunden');
        }

        return $company;
    }
}
