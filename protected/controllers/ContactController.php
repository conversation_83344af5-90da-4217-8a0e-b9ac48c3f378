<?php

class ContactController extends AllowAllController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout      = '//layouts/column2';

    /**
     * Diese Methode erstellt ein neues Contact -Model
     *
     * @param type $data
     *          Die Contact -Daten als Array
     *
     * @return $contact
     *          Das erstellte Contact -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $contact             = new Contact();
        $contact->attributes = $data['Contact'];

        if ($contact->save()) {
            return $contact;
        }

        return null;
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model Contact handelt
     *
     * @param array $data
     *
     * @return bool
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        return isset($data) && isset($data['Contact']);
    }

    /**
     * Diese Methode aktualisiert ein Contact-Exemplar
     *
     * @param type $data
     *          Die Contact-Daten als Array
     *
     * @return $contact
     *          Das aktualisierte Contact-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $contact = $this->loadModel($data['Contact']['id']);

            $contact->attributes = $data['Contact'];
            if ($contact->save()) {
                return $contact;
            }

            return null;
        }

        return null;
    }

    /**
     * Lädt das Contact-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des Contact-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $contact = Contact::model()->findByPk($id);

        return $contact;
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Contact $contact the contact to be validated
     */
    protected function performAjaxValidation($contact)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'contact-form') {
            echo CActiveForm::validate($contact);
            Yii::app()->end();
        }
    }

    /**
     *  Öffnet das Contact -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des Contact -Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $contact      = new Contact();

        if (!$this->validData($_POST)) {
            $this->render('create', ['contact' => $contact]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     *
     * Öffnet das Contact-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden Contact-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $contact = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('update', ['contact' => $contact]);
        }

        $formulardata                  = $_POST;
        $formulardata['Contact']['id'] = $id;

        return $formulardata;
    }
}
