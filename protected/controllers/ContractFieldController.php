<?php

class ContractFieldController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $prefix = 'ContractField';
    public $layout = '//layouts/column2';

    private $controllername = 'contractField';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das ContractField-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des ContractField-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $contractField = ContractField::model()->findByPk($id);

        return $contractField;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues ContractField-Model erstellt werden soll.
     *  Die Methode öffnet das ContractField-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($contractField = $this->create($data) != null) {
                    $this->redirect('admin');

                    return $contractField;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das ContractField -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des ContractField -Models
     */
    private function getCreateFormularData()
    {
        $formulardata  = [];
        $contractField = new ContractField();

        if (!$this->validData($_POST)) {
            $this->render('create', ['contractField' => $contractField]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model ContractField handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        return isset($data) &&
               isset($data['ContractField']);
    }

    /**
     * Diese Methode erstellt ein neues ContractField -Model
     *
     * @param type $data
     *          Die ContractField -Daten als Array
     *
     * @return $contractField
     *          Das erstellte ContractField -Exemplar
     */
    public function create($data)
    {
        $contractField              = new ContractField();
        $contractField->fieldtitle  = $data['ProductComboBasicFieldAssignment']['fieldtitle'];
        $contractField->fieldname   = $data['ProductComboBasicFieldAssignment']['fieldtitle'];
        $contractField->basic_field = 0;
        $contractField->manual      = 1;

        if ($contractField->save()) {
            return $contractField;
        }

        return null;
    }

    /**
     * Diese Methode aktualisiert ein ContractField-Exemplar
     *
     * @param type $data
     *          Die ContractField-Daten als Array
     *
     * @return $contractField
     *          Das aktualisierte ContractField-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $contractField             = $this->loadModel($data['ContractField']['id']);
            $contractField->attributes = $data['ContractField'];
            if ($contractField->save()) {
                return $contractField;
            }

            return null;
        }

        return null;
    }

    public function actionQuickUpdate()
    {
        $data = [
            'ContractField' => [
                'id'                                  => Yii::app()->request->getParam('pk'),
                Yii::app()->request->getParam('name') => Yii::app()->request->getParam('value'),
            ]
        ];
        $this->update($data);

        echo 'ok';
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein ContractField -Model aktualisiert werden soll.
     *  Die Methode öffnet das ContractField -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden ContractField -Models
     */
    public function actionUpdate($id)
    {
        $contractField = $this->loadModel($id);
        if ($contractField != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($contractField = $this->update($data) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $contractField;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $contractField = new ContractField('search');
        $contractField->unsetAttributes();  // clear any default values
        if (isset($_GET['ContractField'])) {
            $contractField->attributes = $_GET['ContractField'];
        }

        $this->render('//stammdaten/vertragsdaten/benutzerdefinierteBezeichnung/admin',
                      [
                          'contractField' => $contractField,
                      ]);
    }

    /**
     * Löscht das ContractField-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden ContractField-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Speichert die ausgewählte zuordnung zu dem GDV-Produkt
     *
     * @param type $id
     */
    public function actionUpdateAssignment($data)
    {
        $contractField = ContractField::model()->findByPk($data['field_id']);
        if ($data['assignment_field_id'] != '' || $data['assignment_field_id'] != null || empty($data['assignment_field_id'])) {
            $contractField->field_assignment = $data['assignment_field_id'];
        } else {
            $contractField->unsetAttributes(['field_assignment']);
        }
        if ($contractField->save()) {
        }
    }

    /**
     * is called from a ajax function to update the basic_field column value
     */
    public function UpdateBasicField($data)
    {
        $contractField              = $this->loadModel($data['ContractField']['id']);
        $contractField->basic_field = $data['ContractField']['basic_field'];

        if ($contractField->save()) {
            $gdv_products = $this->getAllGdvAsKeyValue();

            return $contractField;
        }
        $gdv_products = $this->getAllGdvAsKeyValue();
    }

    /**
     * returns a array of key value pairs (need for Dropdownlists)
     *
     * @return
     *         form: array([0] => name1, [1] => name2...) etc.
     */
    public function getAllGdvAsKeyValue()
    {
        $gdvContractAssigments = GdvProduct::model()->findAll(['order' => 'gdv_name']);
        $datas                 = [];
        foreach ($gdvContractAssigments as $gdvContractAssigment) {
            $datas[$gdvContractAssigment->id] = $gdvContractAssigment->gdv_name;
        }

        return $datas;
    }

    /**
     * returns a array of key value pairs (need for Dropdownlists)
     *
     * @return
     *         form: array([0] => name1, [1] => name2...) etc.
     */
    public function getAllProductsAsKeyValue()
    {
        $productCombos = ProductCombo::model()->findAll(['order' => 'name']);
        $datas         = [];
        foreach ($productCombos as $productCombo) {
            $datas[$productCombo->id] = $productCombo->name;
        }

        return $datas;
    }

    public function actions()
    {
        return [
            'OrderContractFields' => [
                'class' => 'ext.yiisortablemodel.actions.AjaxSortingAction',
            ],
        ];
    }

    /**
     * Ajax-Methode für die Vertragseinstellungen.
     */
    public function actionChangeVisibility()
    {
        $existListData = [];
        $contract_id   = Yii::app()->request->getParam('contract_id');
        $contract      = Contracts::model()->findByPk($contract_id);
        $id            = Yii::app()->request->getParam('id');
        $cf            = ContractField::model()->findByPk($id);
        if (!empty($contract->specific_product_id)) {
            $product_combo = ProductCombo::model()->findByPk($contract->specific_product_id);
            $children      = $product_combo->getChildren(true);
            $fields        = $cf->getSameNamedFields($contract->specific_product_id, true);
            foreach ($fields as $field) {
                $criteria = new CDbCriteria();
                $criteria->compare('product_combo_id', $product_combo->id);
                $criteria->compare('field_id', $field->id);
                $exists                    = ContractFieldDeleted::model()->exists($criteria);
                $existListData[$field->id] = $exists;
            }
            try {
                $transaction = Yii::app()->db->beginTransaction();
                foreach ($children as $product) {
                    foreach ($fields as $field) {
                        $criteria = new CDbCriteria();
                        $criteria->compare('product_combo_id', $product);
                        $criteria->compare('field_id', $field->id);
                        if (ContractFieldDeleted::model()->exists($criteria) && $existListData[$field->id]) {
                            $model = ContractFieldDeleted::model()->find($criteria);
                            $model->delete();
                        } elseif (!ContractFieldDeleted::model()->exists($criteria) && !$existListData[$field->id]) {
                            $model                   = new ContractFieldDeleted();
                            $model->field_id         = $field->id;
                            $model->product_combo_id = $product;
                            $model->save();
                        }
                    }
                }
                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollback();
            }
        } elseif ($contract->isGdvSettingContract() || (!empty($contract->contract_product_id) && !empty($contract->parentId))) {
            //Übernehme die gleiche Änderung an den Vertragsfeldern mit dem gleichen Feldtitel
            $cf     = ContractField::model()->findByPk($id);
            $toHide = $cf->hide == 0 ? true : false;
            $cfs    = ContractField::model()->findAllbyAttributes(['fieldtitle' => $cf->fieldtitle]);
            foreach ($cfs as $model) {
                if ($toHide && !$model->hide) {
                    $model->hide = 1;
                } elseif (!$toHide && $model->hide) {
                    $model->hide = 0;
                }
                $model->save();
            }
            //Übernehme die Änderungen für die Produktsparten, die die übereintreffenden Vertragsfelder benutzen.
            foreach ($cfs as $cf) {
                $criteria = new CDbCriteria();
                $criteria->compare('gdv_product_id', $cf->gdv_product);
                $assignmentModels = GdvProductAssignmentProductComboAssignment::model()->findAll($criteria);
                if (!empty($assignmentModels)) {
                    $ids = [];
                    foreach ($assignmentModels as $assignmentModel) {
                        $ids[] = $assignmentModel->product_combo_id;
                    }
                    $productCombos = ProductCombo::model()->findAllByPk($ids);
                    foreach ($productCombos as $productCombo) {
                        $obersparten = $productCombo->getChildren();
                        foreach ($obersparten as $sparte) {
                            $criteria = new CDbCriteria();
                            $criteria->compare('field_id', $cf->id);
                            $criteria->compare('product_combo_id', $sparte->id);
                            if ($toHide && !ContractFieldDeleted::model()->exists($criteria)) {
                                $model                   = new ContractFieldDeleted();
                                $model->field_id         = $cf->id;
                                $model->product_combo_id = $sparte->id;
                                $model->save();
                            } elseif (!$toHide && ContractFieldDeleted::model()->exists($criteria)) {
                                $model = ContractFieldDeleted::model()->find($criteria);
                                $model->delete();
                            }
                        }
                    }
                }
            }
        }
        echo Yii::app()->end();
    }
}
