<?php

class ContractUserIdManualAssignmentController extends AdminController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';
    public $model;

    /**
     * @return array action filters
     */
    /**
     * Specifies the access control rules.
     * This method is used by the 'accessControl' filter.
     *
     * @return array access control rules
     */

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param integer the ID of the model to be loaded
     */
    public function loadModel($id)
    {
        $model = ContractUserIdManualAssignment::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $model;
    }

    /**
     * Creates a new model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     */
    public function actionCreate()
    {
        $this->model = new ContractUserIdManualAssignment();
        $this->getCreateFormularData();
    }

    private function getCreateFormularData()
    {
        $formulardata                   = [];
        $ContractUserIdManualAssignment = new ContractUserIdManualAssignment();


        $output = $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'contractUserIdManualAssignment' . DIRECTORY_SEPARATOR . '_formFields.php',
            null, true);
        Yii::app()->clientScript->renderBodyEnd($output);
        echo $output;

        if (!$this->validData($_POST)) {
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    private function validData($data)
    {
        //ToDo
//        return ( isset($data) &&
//                isset($data['BrokerId'])
//                );
//
        return true;
    }

    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $assignment              = new ContractUserIdManualAssignment();
        $assignment->contract_nr = $data['ContractUserIdManualAssignment']['contract_nr'];
        $assignment->user_id     = $data['ContractUserIdManualAssignment']['user_id'];

        if ($assignment->save()) {
            return $assignment;
        }

        return null;
    }

    /**
     * Updates a particular model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id the ID of the model to be updated
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);

        // Uncomment the following line if AJAX validation is needed
        // $this->performAjaxValidation($model);

        if (isset($_POST['ContractUserIdManualAssignment'])) {
            $model->attributes = $_POST['ContractUserIdManualAssignment'];
            if ($model->save()) {
                $this->redirect(['view', 'id' => $model->id]);
            }
        }

        $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes a particular model.
     * If deletion is successful, the browser will be redirected to the 'admin' page.
     *
     * @param integer $id the ID of the model to be deleted
     */
    public function actionDelete($id)
    {
        if (Yii::app()->request->isPostRequest) {
            // we only allow deletion via POST request
            $this->loadModel($id)->delete();

            // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
            if (!isset($_GET['ajax'])) {
                $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
            }
        } else {
            throw new CHttpException(400, 'Invalid request. Please do not repeat this request again.');
        }
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('ContractUserIdManualAssignment');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new ContractUserIdManualAssignment('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['ContractUserIdManualAssignment'])) {
            $model->attributes = $_GET['ContractUserIdManualAssignment'];
        }

        $this->render('admin', [
            'model' => $model,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param CModel the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'contract-user-id-manual-assignment-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
