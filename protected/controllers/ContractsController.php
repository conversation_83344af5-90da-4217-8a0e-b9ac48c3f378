<?php

use Business\Common\CreateType;
use Business\Date\DateProvider;
use Business\Sparten\Services\ProductComboService;
use Components\Contract\AttributeLockService;
use Components\EventSystem\RegisteredCommands;
use Components\EventSystem\Service\EventSystemService;
use Components\Pdfs\Kuendigung\DismissalContract;
use Components\Pdfs\Kuendigung\DismissalGenerator;
use Components\Pdfs\Kuendigung\DismissalRecipient;
use Components\Pdfs\Kuendigung\DismissalSender;
use Fragebogen\Notification\ClientNotifier;

Yii::import('kundenakte.components.traits.*');

class ContractsController extends RightController implements KundenakteControllerInterface
{
    use KundenakteControllerTrait;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    public $prefix          = 'Contracts';
    public $excludedActions = [
        'UpdateStorno',
        'TerminateContracts',
        'Dismissial',
        'TerminateContractExcelExport',
        'EditDuplicate',
        'CheckConstraintsAjax',
        'finishContracts',
        'refreshContractAdmin',
        'getContractHtml',
        'AjaxContractValidation',
        'editproduct',
        'renewal',
        'renewalMultiple',
    ];

    public function actions()
    {
        return [
            'editproduct' => [
                'class'     => 'application.components.actions.ModelAttributeEditAction',
                'modelname' => 'Contracts',
                'attribute' => 'specific_product_id',
            ],
        ];
    }

    public function actionContractView($id)
    {
        $contract = $this->loadModel($id);

        if ($contract->damage === '1') {
            if (empty($contract->uuid) || !feature_ensure_any(Feature::GDV2BIPRO_SHARE, Feature::GDV2BIPRO_POSTFACH)) {
                $this->renderConventionalView($contract);
                Yii::app()->end();
            }

            $this->render('//kundenakte/vertraege/vertrag/bipro/show', [
                'id'    => $id,
                'model' => $contract,
            ]);

            return;
        }

        $this->redirect(
            [
                '/kundenakte/view/view',
                'id'  => $contract->client_id,
                'tab' => KundenakteTabs::VERTRAEGE_BEDARF,
                '#'   => "/vertrag/{$contract->id}",
            ]
        );
    }

    /**
     * @param Contracts $contract
     */
    private function renderConventionalView(Contracts $contract): void
    {
        $contractFields = $this->getContractFields($contract, [], false, true);
        $action         = [
            'name'          => 'ContractView',
            'parameterName' => 'id',
            'value'         => $contract->id
        ];
        $showMetadaten = Yii::app()->user->isAdmin();
        $this->render('//kundenakte/vertraege/vertrag/contractForm',
            [
                'basicFields'    => $contractFields['basicFields'],
                'optionalFields' => $contractFields['optionalFields'],
                'view'           => true,
                'model'          => $contract,
                'showMetadaten'  => $showMetadaten,
                'action'         => $action,
                'prefix'         => 'Contract',
                'settings'       => false
            ]);
    }

    /**
     * @param $id
     *
     * @return void
     * @throws CDbException
     * @throws CHttpException
     */
    public function actionUpdate($id): void
    {
        $contract = $this->loadModel($id);

        $this->setClient($contract->client);
        $this->setTab(KundenakteTabs::VERTRAEGE_BEDARF);
        $this->setTitle('Vertrag: ' . $contract->getContractNumber(true));

        $contractFields = Yii::app()->request->getParam('ContractFields');
        if ($contract->getProductStage() == ProductStage::GDV) {
            $contract->setScenario('gdv');
        }
        if (!empty(Yii::app()->request->getParam('Contract'))) {
            $statusBeforeChange = $contract->status;
            $newAttributes      = Yii::app()->request->getParam('Contract');
            AttributeLockService::createAttributeLocks($contract, $newAttributes);
            $this->informFondsfinanzAboutContractChanges($contract, $newAttributes);
            $contract->attributes = $newAttributes;
            $contract->checkStatus($statusBeforeChange);
        }
        if (!$this->checkUpdateData($contract, $contractFields)) {
            //Wenn die View gerendert wird, soll danach nichts mehr passieren
            Yii::app()->end();
        }
        if ($contract->save()) {
            $params = Yii::app()->request->getParam('Contract');
            if (!empty($params['assigned_bankaccount'])) {
                $contract->assignNewBankaccountById((int) $params['assigned_bankaccount']);
            }

            if (isset($params['assigned_bankaccount']) && $params['assigned_bankaccount'] === '') {
                $contract->removeAssignedBankaccount();
            }

            $this->saveContractFields($contract, $contractFields);
            if (Yii::app()->request->getParam('Kennzeichen') !== null) {
                $this->storeOrDropKennzeichenToContract($contract);
            }

            if (Yii::app()->request->getParam('Risikoort') !== null) {
                $this->storeOrDropRisikoortToContract($contract);
            }

            Yii::app()->user->setFlash('success', 'Der Vertrag wurde erfolgreich bearbeitet.');
        }
        $this->redirect(['update', 'id' => $id]);
    }

    /**
     * @param Contracts            $contract
     * @param array<string, mixed> $newAttributes
     */
    private function informFondsfinanzAboutContractChanges(Contracts $contract, array $newAttributes): void
    {
        if (!ContractsExternalMetadaten::model()->isFondsfinanzContract((int) $contract->id)) {
            return;
        }

        /**
         *  [
         *      event action_1 => [
         *          'attribute_1',
         *          'attribute_2',
         *          ...
         *      ],
         *      event action_2 => [
         *          'attribute_3',
         *          'attribute_4',
         *          ...
         *      ],
         * ]
         */
        $relevantAttributes = [
            'payment'  => [
                'net_fee',
                'gross_fee',
            ],
            'duration' => [
                'date',
                'due_date',
            ],
        ];

        foreach ($relevantAttributes as $action => $attributes) {
            foreach ($attributes as $attribute) {
                if (!array_key_exists($attribute, $newAttributes) ||
                    $newAttributes[$attribute] === null ||
                    (string) $contract->getAttribute($attribute) === $newAttributes[$attribute]
                ) {
                    continue;
                }

                EventSystemService::command(RegisteredCommands::FONDSFINANZ_SYNC)
                                  ->withParams(
                                      [
                                          'contractId' => $contract->id,
                                      ]
                                  )
                                  ->action($action)
                                  ->createEvent();

                // If the contract for an attribute of the group has been updated, the event sends a request with all the attribute
                // data from the group.
                // This eliminates the need to check the other attributes of the group.
                break;
            }
        }
    }

    private function checkUpdateData(Contracts $contract, $cfData)
    {
        if (empty(Yii::app()->request->getParam('Contract')) || !$contract->validate()) {
            $action         = [
                'name'          => 'Update',
                'parameterName' => 'id',
                'value'         => $contract->id,
            ];
            $contractFields = $this->getContractFields($contract);
            $this->setUserVauleToCfs($contractFields, $cfData);

            $this->render(
                '//kundenakte/vertraege/vertrag/contractForm',
                [
                    'view'           => false,
                    'model'          => $contract,
                    'optionalFields' => $contractFields['optionalFields'],
                    'basicFields'    => $contractFields['basicFields'],
                    'action'         => $action,
                    'prefix'         => 'Contract',
                ]
            );

            return false;
        }

        return true;
    }

    private function saveContractFields($contract, $cfs)
    {
        if (is_array($cfs)) {
            foreach ($cfs as $id => $value) {
                $model = ContractField::model()->findByPk($id);
                $model->saveValue($contract->id, $value);
            }
        }
    }

    /**
     * @param          $client_id
     * @param int|null $sparten_id
     *
     * @throws CDbException
     */
    public function actionCreate($client_id, int $sparten_id = null): void
    {
        $contract = new Contracts('empty');
        $client   = Client::model()->findByPk($client_id);

        $this->setClient($client);
        $this->setTab(KundenakteTabs::VERTRAEGE_BEDARF);

        $data              = Yii::app()->request->getParam('Contract');
        $contractFieldData = Yii::app()->request->getParam('ContractFields');
        if (!empty($data)) {
            $contract->attributes = $data;
            $contract->checkStatus();
            $contract->import_type_id = Contracts::IMPORT_TYPE_MANUELL;
        }
        $contract->user_id   = $client->user_id ?? Yii::app()->user->getId();
        $contract->client_id = $client_id;
        if (!empty($sparten_id) && (new ProductComboProvider())->exists($sparten_id)) {
            $contract->specific_product_id = $sparten_id;
        }

        $contract->create_type = CreateType::MANUAL;

        if ($this->checkCreateData($contract, $contractFieldData) && $contract->save()) {
            if (!empty($_POST['Contract']['assigned_bankaccount'])) {
                $contract->assignNewBankaccountById((int) $_POST['Contract']['assigned_bankaccount']);
            }

            if (Yii::app()->request->getParam('ContractFields') !== null) {
                $this->storeOrDropKennzeichenToContract($contract);
            }
            if (Yii::app()->request->getParam('Risikoort') !== null) {
                $this->storeOrDropRisikoortToContract($contract);
            }
            $this->saveContractFields($contract, $contractFieldData);

            ClientNotifier::for($client)->contract((int) $contract->id);

            $this->redirect(Kundenakte::urlTo($client_id, KundenakteTabs::VERTRAEGE_BEDARF));
        }
    }

    private function checkCreateData($contract, $cfData)
    {
        $data = Yii::app()->request->getParam('Contract');
        if (empty($data) || !$contract->validate()) {
            $action = [
                'name'          => 'Create',
                'parameterName' => 'client_id',
                'value'         => $contract->client_id,
            ];

            $contractFields = $this->getContractFields($contract);
            $this->setUserVauleToCfs($contractFields, $cfData);

            $this->render(
                '//kundenakte/vertraege/vertrag/contractForm',
                [
                    'prefix'         => 'Contract',
                    'view'           => false,
                    'model'          => $contract,
                    'optionalFields' => $contractFields['optionalFields'],
                    'basicFields'    => $contractFields['basicFields'],
                    'create'         => true,
                    'action'         => $action,
                ]
            );

            return false;
        }

        return true;
    }

    private function getContractFields($contract, $view = false)
    {
        $basicFields    = [];
        $optionalFields = [];
        //Hole alle Vertragsfelder des aufgerufenen Vertrages, wenn view oder gdv sollen nur befüllte Felder gesucht werden
        if ($contract->getProductStage() != ProductStage::GDV && !$view) {
            $contractFields = $contract->getContractFields(true, true, false);
        } else {
            $contractFields = $contract->getContractFields(true, true, true);
        }
        //splite Grund- und Optionalfelder, wenn Vertragsfelder gefunden wurden
        if (!empty($contractFields)) {
            $basicFields    = $contractFields['basic'];
            $optionalFields = $contractFields['optional'];
        }

        return ['basicFields' => $basicFields, 'optionalFields' => $optionalFields];
    }

    private function setUserVauleToCfs($cfs, $valueArray)
    {
        if (!empty($valueArray) && array_key_exists('optionalFields', $cfs)) {
            foreach ($cfs['optionalFields'] as $cf) {
                if (array_key_exists($cf->id, $valueArray)) {
                    $cf->value = $valueArray[$cf->id];
                }
            }
        }
        if (!empty($valueArray) && array_key_exists('basicFields', $cfs)) {
            foreach ($cfs['basicFields'] as $cf) {
                if (array_key_exists($cf->id, $valueArray)) {
                    $cf->value = $valueArray[$cf->id];
                }
            }
        }
    }

    public function actionGetContractHtml()
    {
        $form     = new CActiveForm($this);
        $form->id = 'contract-form';
        $contract = new Contracts('search');
        $view     = Yii::app()->request->getParam('view');
        if ($view == 'false') {
            $view = false;
        } elseif ($view == 'true') {
            $view = true;
        }

        if (!empty(Yii::app()->request->getParam('contract_id'))) {
            $contract = $this->loadModel(Yii::app()->request->getParam('contract_id'));
        } else {
            $contract                      = new Contracts();
            $contract->specific_product_id = Yii::app()->request->getParam('specific_product_id');
            $contract->client_id           = Yii::app()->request->getParam('client_id');
        }
        $contractFields = $this->getContractFields($contract, $view);
        $html           = $this->renderPartial(
            '//kundenakte/vertraege/vertrag/contract',
            [
                'view'           => $view,
                'model'          => $contract,
                'optionalFields' => $contractFields['optionalFields'],
                'basicFields'    => $contractFields['basicFields'],
                'prefix'         => 'Contract',
                'form'           => $form,
            ],
            true,
            true
        );
        echo $html;
        Yii::app()->end();
    }

    /**
     * Löscht das Contracts-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden Contracts-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);
        Yii::app()->end();
    }

    /**
     * delete
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $contract = $this->loadModel($id);
        if (!empty($contract)) {
            return (Yii::app()->user->isAdmin() || $contract->isDeleteable()) && $contract->delete();
        }

        return false;
    }

    /**
     * Lädt das Contracts-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return Contracts
     *
     * @throws CHttpException
     */
    public function loadModel($id): Contracts
    {
        $model = Contracts::model()->with(['risikoOrt', 'contractAttributeLocks'])->personalAccess()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $model;
    }

    /**
     * Returns all products that have a entrie in the gdvProductProductComboAssignment table
     *
     * @return array
     */
    public function getProducts(): array
    {
        return ProductComboService::new()->getAllIdAndNameWithGdvProductAssignment();
    }

    public function actionTerminateContracts()
    {
        $this->disableClientLayout();
        $contracts    = new Contracts();
        $formularData = $this->getProfileFields(UserProfileSearch::ablaufendeVertraege);
        if (!empty($_GET)) {
            $searchData = $_GET;
            if (isset($searchData['Contracts_page'])) {
                unset($searchData['Contracts_page']);
            }
            if (isset($searchData['ajax'])) {
                unset($searchData['ajax']);
            }
            if (array_key_exists('done', $_GET) && $_GET['done'] == 1) {
                Yii::app()->user->setFlash(
                    'warning',
                    'Bei der Suche nach den erledigten Verträgen werden nur die Verträge der vergangenen 365 Tage angezeigt.'
                );
            }
        } else {
            $_GET       = array_merge($formularData, $_GET);
            $searchData = $_GET;
        }

        $dataProvider = $contracts->searchTerminateContracts($searchData);
        $this->render('//home/<USER>/ablaufendeVertraege/terminateContracts', [
            'searchData' => $searchData,
            'tabs'       => $contracts->getTabs($dataProvider),
        ]);
    }

    public static function getProfileFields($functionName)
    {
        $model      = new UserProfileSearch();
        $foundModel = $model->loadSearch(new Contracts(), $functionName);
        if (null !== $foundModel) {
            return json_decode($foundModel->formular, true);
        }

        return [];
    }

    public function actionDismissial()
    {
        $contract = $this->loadModel($_GET['contract_id']);
        $client   = $contract->client;
        $user     = User::model()->findByPk($contract->user_id);
        $type     = $_GET['type'];

        $pdf = new DismissalGenerator();
        $pdf->Open();
        $sender = $type === 'client'
            ? DismissalSender::fromClient($client)
            : DismissalSender::fromUser($user, $client->getFullname());
        $pdf->generate(
            $sender,
            DismissalRecipient::fromInsuranceCompany($contract->insurance_company),
            DismissalContract::fromContract($contract)
        );
        $pdf->Close();
        $pdf->output('Kundigungsschreiben ' . $client->firstname . ' ' . $client->lastname . '.pdf', 'I');
    }

    /**
     * @throws CHttpException
     * @throws Exception
     */
    public function actionRenewal()
    {
        $contractId = Yii::app()->request->getParam('contract_id', null);

        if ($contractId === null) {
            throw new CHttpException(400, 'Es wurde keine contractId im Request gesendet');
        }

        $contract = $this->loadModel($contractId);
        $this->renew($contract);
    }

    /**
     * @throws CHttpException
     * @throws Exception
     */
    public function actionRenewalMultiple()
    {
        $contractIds = explode(',', Yii::app()->request->getParam('contract_ids', ''));

        foreach ($contractIds as $contractId) {
            $contract = $this->loadModel($contractId);
            $this->renew($contract);
        }
    }

    /**
     * @param Contracts $contract
     *
     * @throws Exception
     */
    private function renew(Contracts $contract): void
    {
        $contract->due_date = (new DateProvider())->findNextDateInFuture(new DateTime($contract->due_date))->format('d.m.Y');
        $contract->save();

        if ($contract->hasCourtage()) {
            $userCourtages = $contract->userCourtages;

            foreach ($userCourtages as $courtage) {
                /** @var CourtageData $courtage */
                $courtage->end_date = (new DateProvider())->findNextDateInFuture(new DateTime($courtage->end_date))->format('Y-m-d');
                if ($courtage->save()) {
                    $courtage->renew();
                }
            }
        }
    }

    public function actionTerminateContractExcelExport(): void
    {
        $data         = $_POST['Contracts'];
        $dataProvider = ExportSessionManager::getDataProvider(new Contracts(), 'terminateContracts');

        $propertyMapper = [
            // Client related properties
            'firstname'             => 'client.firstname:string:Vorname',
            'lastname'              => 'client.lastname:string:Nachname',
            'phone'                 => 'client.phone:string:Tel.-Nr.',
            'mobil'                 => 'client.mobil:string:Mobil-Nr.',
            'street'                => 'client.street:string:Straße',
            'street_number'         => 'client.street_number:string:Hausnummer',
            'zip_code'              => 'client.zip_code:string:PLZ',
            'city'                  => 'client.city:string:Stadt',

            // Contract related properties
            'specific_product_name' => 'specific_product_name:string:Sparte',

            // We're sending here the ID, but actually want the company name in the export
            'company_id'            => 'insurance_company.name:string:Gesellschaft',
        ];

        $propertyKeys = array_keys($propertyMapper);

        /** @var string[] $names */
        $names = [];

        foreach ($data as $name => $value) {
            if ((int) $value !== 1) {
                continue;
            }

            // Assign custom Excel headers - <model.path>:<type>:<label>
            if (in_array($name, $propertyKeys)) {
                $names[] = $propertyMapper[$name];

                continue;
            }

            $names[] = $name;
        }

        $table = new ExcelGenerator($dataProvider, $names, new Contracts());

        $table->generateTable();
    }

    /**
     * For basic and optional Fields in show ContractFields
     */
    public function getHTMLOptions($field)
    {
        if ($field->datatype == 0) {
            return ['class' => 'span18 myDate'];
        }

        return ['class' => 'span18'];
    }

    public function actionFinishContracts()
    {
        $ids = explode(',', Yii::app()->request->getParam('finishedContracts', ''));

        foreach ($ids as $id) {
            $contract            = $this->loadModel($id);
            $contract->done      = 1;
            $contract->done_date = date('Y-m-d');
            $contract->save();
        }
        Yii::app()->end();
    }

    /**
     * Ajaxaufruf um die Gridview in der Digitalen Kundenakte zu aktuallisieren.
     */
    public function actionRefreshContractAdmin()
    {
        $client_id = Yii::app()->request->getParam('client_id');
        if (!empty($client_id)) {
            $client = Client::model()->findByPk($client_id);
            $status = Yii::app()->request->getParam('status');
            $this->renderPartial('//kundenakte/vertraege/admin', ['client' => $client, 'status' => $status], false, true);
        }
        Yii::app()->end();
    }

    public function getAllContractIds($client_id, $status = [])
    {
        if (!is_array($client_id)) {
            $client_id = [$client_id];
        }
        $criteria = new CDbCriteria();
        $criteria->compare('client_id', $client_id);
        $criteria->compare('status', $status);
        $contracts = Contracts::model()->personalAccess()->relevant()->findAll($criteria);
        $ids       = [];
        foreach ($contracts as $contract) {
            $ids[] = $contract->id;
        }

        return $ids;
    }

    /**
     * returns as a list-data all contracts a client has.
     */
    public function getAllContracts($clients, $onlyIds = false)
    {
        $listData   = [];
        $client_ids = [];
        if (!is_array($clients)) {
            $clients = [$clients];
        }
        $criteria = new CDbCriteria();
        foreach ($clients as $client => $id) {
            $client_ids[] = $id;
        }
        $criteria->addInCondition('client_id', $client_ids);
        $contracts = Contracts::model()->relevant()->findAll($criteria);

        /** @var Contracts $contract */
        foreach ($contracts as $contract) {
            if (isset($contract->contract_nr) && !empty($contract->contract_nr)) {
                $listData[$contract->id] = $contract->getSpecificProductName(true) . ' (Vertr.Nr.: ' . $contract->contract_nr . ')';
            } else {
                $listData[$contract->id] = $contract->getSpecificProductName(true);
            }
        }
        if (!$onlyIds) {
            return $listData;
        }

        return array_keys($listData);
    }

    public function actionAjaxContractValidation()
    {
        $errors     = [];
        $dataString = Yii::app()->request->getPost('data');
        $fieldId    = Yii::app()->request->getPost('field_id');
        $prefix     = Yii::app()->request->getPost('prefix');
        $fullValid  = Yii::app()->request->getPost('fullValidation');
        $szenario   = Yii::app()->request->getParam('scenario');

        $attribute = str_replace('Damage_', '', str_replace('Contract_', '', $fieldId));
        if (!empty($fieldId)) {
            $exploded = explode('_', $fieldId);
            if (!empty($exploded)) {
                $fieldId = array_pop($exploded);
            } else {
                $fieldId = null;
            }
        }
        parse_str($dataString, $data);
        if ($prefix == 'Contract') {
            $model = new Contracts('validate');
        } else {
            $model = new Damage('validate');
        }
        if (!empty($szenario)) {
            $model->setScenario($szenario);
        }
        $model->user_id    = 1;
        $model->client_id  = 1;
        $model->attributes = @$data[$prefix];
        if ($fullValid == 'true') {
            $contractFieldsData = $data['ContractFields'] ?? [];
            if (!$model->validate()) {
                $errors[$prefix] = $model->errors;
            }
            foreach ($contractFieldsData as $field_id => $fieldValue) {
                if (empty($fieldValue)) {
                    unset($contractFieldsData[$field_id]);
                }
            }
            if (empty($fieldId) || is_numeric($fieldId)) {
                if (!empty($contractFieldsData)) {
                    if (empty($fieldId)) {
                        $models = ContractField::model()->findAllByPk(array_keys($contractFieldsData));
                    } else {
                        $models = ContractField::model()->findAllByPk([$fieldId]);
                    }
                    foreach ($models as $model) {
                        $validateModel        = $model->getContractDataModelInstance();
                        $validateModel->value = @$contractFieldsData[$model->id];
                        if (!$validateModel->validate()) {
                            $errors['ContractFields'][$model->id] = $validateModel->errors;
                        }
                    }
                }
            }
        } else {
            if (is_numeric($fieldId) && ContractField::model()->exists(['condition' => 'id = :field_id', 'params' => [':field_id' => $fieldId]])) {
                $model         = ContractField::model()->findByPk($fieldId);
                $validateModel = $model->getContractDataModelInstance();
                if (array_key_exists('ContractFields', $data)) {
                    $validateModel->value = $data['ContractFields'][$model->id];
                }
                if (!$validateModel->validate()) {
                    $errors['ContractFields'][$model->id] = $validateModel->errors;
                }
            } elseif (!empty($model->$attribute)) {
                if (!$model->validate([$attribute])) {
                    $errors[$prefix] = $model->errors;
                }
            }
        }
        echo CJSON::encode($errors);
        Yii::app()->end();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Contracts $contracts the model to be validated
     */
    protected function performAjaxValidation($contracts)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'contracts-form') {
            echo CActiveForm::validate($contracts);
            Yii::app()->end();
        }
    }

    /**
     * @param Contracts $contract
     *
     * @throws CDbException
     */
    private function storeOrDropKennzeichenToContract(Contracts $contract): void
    {
        $daten = Yii::app()->request->getParam('Kennzeichen');
        if ($daten === null) {
            return;
        }

        $contract->refresh();
        $kennzeichen = $contract->getKennzeichenOfContract();

        if (!empty($daten['unterscheidungszeichen']) &&
            !empty($daten['erkennungszeichen']) &&
            !empty($daten['erkennungsnummer'])
        ) {
            $kennzeichen->setAttributes(Yii::app()->request->getParam('Kennzeichen'));
            $kennzeichen->save();

            return;
        }

        if (!$kennzeichen->isNewRecord) {
            $kennzeichen->delete();
        }
    }

    /**
     * @param Contracts $contract
     *
     * @throws CDbException
     */
    private function storeOrDropRisikoortToContract(Contracts $contract): void
    {
        $daten = Yii::app()->request->getParam('Risikoort');
        if ($daten === null) {
            return;
        }

        $risikoOrt = $contract->getRisikoOrt();

        if (!empty($daten['street'])) {
            $risikoOrt->setAttributes($daten);
            $risikoOrt->setAttribute('contract_id', $contract->id);
            $risikoOrt->save();

            return;
        }

        if (!$risikoOrt->isNewRecord) {
            $risikoOrt->delete();
        }
    }
}
