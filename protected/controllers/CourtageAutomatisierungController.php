<?php

/**
 * Created by PhpStorm.
 * User: alex
 * Date: 24.04.15
 * Time: 09:59
 */
class CourtageAutomatisierungController extends Controller
{
    const AUTO_MATCHES      = 1;
    const AUTO_POSSIBLE     = 2;
    const AUTO_NOT_POSSIBLE = 3;

    public static function getAutoBadge($type)
    {
        $count = 0;
        if ($type == self::AUTO_MATCHES) {
            $count = 0;
        } elseif ($type == self::AUTO_POSSIBLE) {
            $count = CourtageAutomatisierung::model()->my()->notDeleted()->possible()->notIgnored()->count();
        } elseif ($type == self::AUTO_NOT_POSSIBLE) {
            $count = CourtageAutomatisierung::model()->my()->notDeleted()->notPossible()->notIgnored()->count();
        }
        if ($count > 0) {
            return '<span class="badge badge-important">' . $count . '</span>';
        }

        return '';
    }

    public function actions()
    {
        return [
            'ignorecontract' => [
                'class'     => 'application.components.actions.AjaxSaveModelAction',
                'modelname' => 'CourtageIgnoredContract',
            ],
        ];
    }

    public function accessRules()
    {
        if (Yii::app()->user->isAdmin() || CourtageController::hasRight(CourtageController::CHECK_RIGHT_ALL)) {
            return [
                [
                    'allow', // allow all users
                    'users' => ['*'],
                ],
            ];
        }

        return [
            [
                'deny', // deny all users
                'users' => ['*'],
            ],
        ];
    }

    public function actionAuto($contract_id = null, $view = self::AUTO_POSSIBLE)
    {
        if (!empty($contract_id)) {
            /**
             * @var CourtageAutomatisierung $auto
             */
            $auto = CourtageAutomatisierung::model()->my()->findByAttributes(['contract_id' => $contract_id]);
            if (!empty($auto)) {
                if ($auto->check()) {
                    Yii::app()->user->setFlash('success', 'Es sind alle Informationen vorhanden. Sie können nun eine Erfassung anlegen');
                    $this->redirect(['courtageAutomatisierung/autoPossible']);
                } else {
                    Yii::app()->user->setFlash('error', 'Es sind noch nicht alle benötigten Informationen vorhanden, bitte bearbeitet Sie den Vertrag.');
                    $this->redirect(['courtageAutomatisierung/autoNotPossible']);
                }
            }
        }
        $view    = empty($view) ? self::AUTO_POSSIBLE : $view;
        $actions = [
            self::AUTO_MATCHES      => 'autoMatches',
            self::AUTO_POSSIBLE     => 'autoPossible',
            self::AUTO_NOT_POSSIBLE => 'autoNotPossible',
        ];
        $this->redirect(['courtageAutomatisierung/' . $actions[$view]]);
    }

    /**
     * Überprüft alle Einträge, bei denen es Probleme gab, die die gleiche Gesellschaft und Sparte haben, wie der übergebene Vertrag
     *
     * @param $contract_id
     */
    public function actionSame($contract_id)
    {
        /**
         * @var CourtageAutomatisierung[] $all
         * @var CourtageAutomatisierung   $model
         */
        $model = CourtageAutomatisierung::model()->my()->findByAttributes(['contract_id' => $contract_id]);
        $all   = CourtageAutomatisierung::model()->my()->notPossible()->with(['contract'])->findAll();

        foreach ($all as $item) {
            if ($item->isSimilar($model)) {
                $item->check();
            }
        }
        $this->redirect(['autoNotPossible']);
    }

    public function actionAutoPossible()
    {
        $model             = new CourtageAutomatisierung('search');
        $model->attributes = Yii::app()->request->getParam('CourtageAutomatisierung', []);

        $this->render('//courtage/automatisierung/possible/admin', compact('model'));
    }

    public function actionAutoNotPossible()
    {
        $model             = new CourtageAutomatisierung('search');
        $model->attributes = Yii::app()->request->getParam('CourtageAutomatisierung', []);
        $this->render('//courtage/automatisierung/notPossible/admin', compact('model'));
    }

    public function actionAjaxAutoPreviewModal()
    {
        $courtageDataId    = Yii::app()->request->getParam('courtageDataId');
        $automatisierungId = Yii::app()->request->getParam('automatisierungId');

        if (!empty($courtageDataId)) {
            $courtageData = CourtageData::model()->findByPk($courtageDataId);
            if (!empty($courtageData)) {
                $contract = $courtageData->findMatchingContract();
                if (!empty($contract)) {
                    $container = new ContractCourtageContainer();
                    $container->setContract($contract);
                    $container->setCourtageData($courtageData);
                    $buttons = $this->renderPartial('//courtage/automatisierung/matches/_modalButtons', compact('container'), true, true);
                    $this->renderPartial('//courtage/automatisierung/_modalContent', compact('container', 'buttons'));
                }
            }
        } elseif (!empty($automatisierungId)) {
            $automatisierung = CourtageAutomatisierung::model()->my()->findByPk($automatisierungId);
            $container       = $automatisierung->getContractCourtageContainer();
            $buttons         = $this->renderPartial('//courtage//automatisierung/possible/_modalButtons', compact('container'), true, true);
            $this->renderPartial('//courtage/automatisierung/_modalContent', compact('container', 'buttons'));
        }
    }

    public function actionLinkAutomatisierung($id)
    {
        $automatisierung = CourtageAutomatisierung::model()->my()->findByPk($id);
        $success         = false;
        if (!empty($automatisierung) && !empty($automatisierung->contract) && !empty($automatisierung->courtageData)) {
            $success                                    = false;
            $automatisierung->courtageData->contract_id = $automatisierung->contract->id;
            $success                                    = $automatisierung->courtageData->save();
            if ($success) {
                //Zur Sicherheit noch mal löschen
                CourtageAutomatisierung::model()->deleteAllByAttributes(['contract_id' => $automatisierung->contract->id]);
            }
        }
        if (Yii::app()->request->isAjaxRequest) {
            echo $success;
            Yii::app()->end();
        } else {
            if ($success) {
                Yii::app()->user->setFlash('success', 'Der Vertrag wurde erfolgreich mit der Erfassung vernüpft');
            } else {
                Yii::app()->user->setFlash('error', 'Der Vertrag konnte nicht mit der Erfassung vernüpft werden');
            }
            $this->redirect(['auto']);
        }
    }

    public function actionCreateCourtages()
    {
        set_time_limit(0);
        $model             = new CourtageAutomatisierung('search');
        $model->attributes = Yii::app()->request->getParam('CourtageAutomatisierung', []);
        $counter           = 0;
        /**
         * @var CourtageAutomatisierung $model
         */
        foreach ($model->my()->possible()->notIgnored()->search(1000)->data as $model) {
            $courtageData                 = $model->getContractCourtageContainer()->getCourtageData();
            $courtageData->auto_generated = 1;
            if ($courtageData->save()) {
                $counter++;
            }
        }
        Yii::app()->user->setFlash('success', 'Es wurden ' . $counter . ' Erfassungen erstellt.');
        $this->redirect(array_merge(['auto'], $_GET));
    }

    public function actionAutoCreateCourtage($contract_id)
    {
        $contract = Contracts::model()->personalAccess()->findByPK($contract_id);
        if (!empty($contract)) {
            $container = new ContractCourtageContainer();
            $container->setContract($contract);
            $courtageData                 = $container->getCourtageData();
            $courtageData->auto_generated = 1;
            $success                      = $courtageData->save();
            $courtageData->renew();
        }
        NotificationCenter::getInstance()->updateNotification(new CourtageAutomatisierung());
        if (!Yii::app()->request->isAjaxRequest) {
            if ($success) {
                Yii::app()->user->setFlash('success', 'Die Erfassung wurde erfolgreich angelegt (' .
                                                      CHtml::link(CHtml::encode($courtageData->getProcedureNumber()),
                                                                  $this->createAbsoluteUrl('courtage/admin', ['courtage_data_id' => $courtageData->id]), ['target' => '_blank']) .
                                                      ')');
            } else {
                Yii::app()->user->setFlash('error', 'Die Erfassung konnte nicht angelegt werden');
            }
            $this->redirect('autoPossible');
        } else {
            echo $success;
            Yii::app()->end();
        }
    }

    public function actionSetAutomatisierungen($user_id = null, $view = null)
    {
        if (empty($user_id)) {
            $user_id = Yii::app()->user->getId();
        }
        if (!Yii::app()->user->isAdmin()) {
            $underlings = Yii::app()->user->getUnderlingsIds();
            if (!in_array($user_id, $underlings)) {
                $user_id = Yii::app()->user->getId();
            }
        }
        $console = new Console();
        $dump    = $console->runCommand('courtageautomatisierung setforuser --user_id=' . $user_id, [], true);
        $this->redirect(['auto', 'view' => $view]);
    }

    public function actionIgnoredContracts()
    {
        $model = new CourtageIgnoredContract('search');
        $this->render('//courtage/automatisierung/ignored/admin', compact('model'));
    }

    public function actionAutoGenerated()
    {
        $courtageData = new CourtageData('search');
        $courtageData->unsetAttributes();
        $courtageData->auto_generated = 1;
        $courtageData->user_id        = Yii::app()->user->getUnderlingsIds();
        $this->render('//courtage/automatisierung/generated/admin', ['courtageData' => $courtageData]);
    }

    public function actionAjaxCancelIgnoringContract($id)
    {
        $model = CourtageIgnoredContract::model()->my()->findByPk((int) $id);
        if (!empty($model)) {
            echo $model->delete();
        } else {
            echo 0;
        }
        Yii::app()->end();
    }

    public function actionAdminView()
    {
        $this->layout = 'application.views.layouts.main_breadcrumbs';
        if (!Yii::app()->user->isAdmin()) {
            throw new CHttpException(403);
        }

        $this->render('//courtage/automatisierung/adminView/index', ['model' => new AutomatisierungAdminView()]);
    }

    public function getStatus()
    {
        $all = Contracts::model()->main()->active()->nonDuplicate()->personalAccess()->cache(60)->count();

        if ($all == 0) {
            return [
                'alle'    => 0,
                'erfasst' => 0
            ];
        }
        $result = CourtageData::model()->getDbConnection()->createCommand()
                              ->select('DISTINCT(contract_id)')
                              ->from('courtage_data')
                              ->where('contract_id IS NOT NULL')
                              ->andWhere(AgencyController::getUnderlingsCondition())
                              ->queryAll();

        return [
            'alle'    => $all,
            'erfasst' => count($result)
        ];
    }
}
