<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

use Business\Vorgaenge\Korrespondenz;

/**
 * Description of CourtageController
 *
 * <AUTHOR>
 */
class CourtageController extends Controller
{
    const CHECK_RIGHT_ALL   = 1;
    const CHECK_RIGHT_VIEW  = 2;
    const AUTO_MATCHES      = 1;
    const AUTO_POSSIBLE     = 2;
    const AUTO_NOT_POSSIBLE = 3;

    public function init()
    {
        parent::init();
        $this->layout      = 'application.views.layouts.main_breadcrumbs';
        $this->breadcrumbs = [
            'Courtageerfassung' => ['courtage/admin']
        ];
    }

    public function actions()
    {
        return [
            'changepaymentstatus'        => [
                'class'     => 'application.components.actions.ModelAttributeEditAction',
                'modelname' => 'CourtagePayment',
                'attribute' => 'status',
            ],
            'changepaymentpaymentstatus' => [
                'class'     => 'application.components.actions.ModelAttributeEditAction',
                'modelname' => 'CourtagePayment',
                'attribute' => 'payment_status',
            ],
            'ajaxsaveremark'             => [
                'class'     => 'application.components.actions.ModelAttributeEditAction',
                'modelname' => 'CourtageData',
                'attribute' => 'remark',
            ],
            'getsettingsdata'            => [
                'class'              => 'application.components.actions.AjaxModelAttributesAction',
                'modelname'          => 'UserCourtageInsuranceCompanySettings',
                'permissionCallback' => function (CActiveRecord $settings) {
                    /**@var UserCourtageInsuranceCompanySettings $settings */
                    return Yii::app()->user->getPermissions()->canSeeUser($settings->user_id);
                }
            ],
            'savesettings'               => [
                'class'      => 'application.components.actions.AjaxSaveModelAction',
                'modelname'  => 'UserCourtageInsuranceCompanySettings',
                'use_prefix' => true
            ],
            'deletesettings'             => [
                'class'     => 'application.components.actions.ModelDeletionAction',
                'modelname' => 'UserCourtageInsuranceCompanySettings',
                'type'      => AjaxAction::TYPE_GET
            ],
            'delete'                     => [
                'class'     => 'application.components.actions.ModelDeletionAction',
                'modelname' => 'CourtageData',
                'type'      => AjaxAction::TYPE_GET
            ],
            'ajaxsavestatus'             => [
                'class'     => 'application.components.actions.AjaxSaveModelAction',
                'modelname' => 'CourtageData',
            ],
            'ajaxgetstatusdata'          => [
                'class'     => 'application.components.actions.AjaxModelAttributesAction',
                'modelname' => 'CourtageData',
            ],
        ];
    }

    public function accessRules()
    {
        if (Yii::app()->user->isAdmin() || self::hasRight(self::CHECK_RIGHT_ALL)) {
            return [
                [
                    'allow', // allow all users
                    'users' => ['*'],
                ],
            ];
        }
        if (self::hasRight(self::CHECK_RIGHT_VIEW)) {
            return [
                [
                    'allow',
                    'actions' => [
                        'admin',
                        'ajaxCourtagePreview',
                        'viewCourtage',
                        'viewData',
                        'abrechnungstool'
                    ],
                ],
                [
                    'deny', // deny all users
                    'users' => ['*'],
                ],
            ];
        }

        return [
            [
                'deny', // deny all users
                'users' => ['*'],
            ],
        ];
    }

    public static function hasRight($check_right)
    {
        if (Yii::app()->user->isAdmin()) {
            return true;
        }
        if (Yii::app()->user->hasRight(Rights::COURTAGEERFASSUNG_ALLES)) {
            return true;
        }
        if ($check_right == self::CHECK_RIGHT_VIEW) {
            return Yii::app()->user->hasRight(Rights::COURTAGEERFASSUNG_SICHTFUNKTION);
        }

        return false;
    }

    public function actionAdmin($new = false, $courtage_data_id = null, $open_modal = false, $courtage_id = null)
    {
        $data = Yii::app()->request->getParam('CourtagePayment');
        if (empty($data)) {
            $data = Yii::app()->request->getParam('cpar');
        }
        $courtagePayment = new CourtagePaymentAdminRow('search');
        $courtagePayment->unsetAttributes();
        //Soll letzte Suche geladen werden?
        $internal_referrer = $this->isInternalReferrerURL();
        if (empty($courtage_data_id) && empty($data) && !$new && $internal_referrer) {
            $courtagePayment->useLastSearch();
        } elseif (!empty($data)) {
            $courtagePayment->attributes = $data;
        } elseif (!empty($courtage_data_id)) {
            $open_modal = true;
            $courtagePayment->loadSearchCourtageData($courtage_data_id, $courtage_id);
        } elseif (empty($_GET) && Yii::app()->user->isAdmin()) {
            //Admins sollen die Erfassungen erstmal leer angezeigt bekommen, damit wir keine unnötige Suche machen müssen
            $courtagePayment->user_id = -1;
            Yii::app()->user->setFlash('info', 'Für Admins werden beim ersten Öffnen keine Daten angezeigt.');
        } else {
            $courtagePayment->loadDefaultSearchData();
        }
        $courtagePayment->open_modal = $open_modal;
        $this->render('admin/admin', ['courtagePayment' => $courtagePayment]);
    }

    /**
     * Gibt zurück, ob es sich bei der ReferrerURL um eine URL aus der Courtageerfassung handelt
     */
    private function isInternalReferrerURL()
    {
        $internal = [
            'courtage',
            'courtageerfassung',
            'userCourtageSettlement',
        ];
        $referrer = Yii::app()->request->getUrlReferrer();
        foreach ($internal as $i) {
            if (strpos($referrer ?? '', $i) !== false) {
                return true;
            }
        }

        return false;
    }

    public function render($view, $data = null, $return = false)
    {
        Yii::app()->clientScript->registerCoreScript('jquery.ui');
        Yii::app()->clientScript->registerScript('', "$('.ipopover').popover();", CClientScript::POS_READY);

        return parent::render($view, $data, $return);
    }

    public function actionCreateStorno($courtage_id = null, $courtage_data_id = null)
    {
        $courtageData     = $this->getCourtageData($courtage_data_id, $courtage_id);
        $courtage_data_id = $courtageData->id;
        $data             = Yii::app()->request->getPost('CourtageStorno');
        //Wenn Erfassung noch nicht Storniert wurde
        if (CourtageStorno::model()->exists('courtage_data_id = :courtage_data_id and type = :type',
            [':courtage_data_id' => $courtage_data_id, ':type' => CourtageStorno::TYPE_VOLLSTORNO])
        ) {
            Yii::app()->user->setFlash('error', 'Die Courtage wurde bereits Storniert.');
            $this->redirect(['courtage/admin', 'courtage_data_id' => $courtage_data_id]);
        } elseif ($courtageData->istGekuendigt()) {
            Yii::app()->user->setFlash('error', 'Die Courtage wurde bereits Gekündigt.');
            $this->redirect(['courtage/admin', 'courtage_data_id' => $courtage_data_id]);
        } else {
            if (!empty($courtageData)) {
                if (!empty($data)) {
                    $storno = $courtageData->createStorno($data);
                    if ($storno->hasErrors()) {
                        Yii::app()->user->setFlash('error', 'Courtage konnte nicht storniert werden.');
                        $this->render('storno/create', ['courtageStorno' => $storno]);
                    } else {
                        Yii::app()->user->setFlash('success', 'Courtage wurde erfolgreich storniert');
                        $this->redirect(['courtage/viewData', 'id' => $courtageData->id]);
                    }
                } else {
                    $storno       = new CourtageStorno('create');
                    $storno->date = date('d.m.Y');

                    $storno->courtage_data_id = $courtage_data_id;
                    $this->render('storno/create', ['courtageStorno' => $storno]);
                }
            } else {
                Yii::app()->user->setFlash('error', 'Courtage konnte nicht storniert werden.');
                $this->redirect(['courtage/admin']);
            }
        }
    }

    public function actionDeleteCourtage($id)
    {
        $courtageData = $this->loadCourtageData($id, null);
        if ($courtageData->delete()) {
            Yii::app()->user->setFlash('success', 'Die Erfassung ' . CHtml::encode($courtageData->getProcedureNumber()) . ' wurde gelöscht');
        }
        $this->redirect(['courtage/admin']);
    }

    /**
     * @param $courtage_data_id
     * @param $courtage_id
     *
     * @return \CourtageData
     * @throws \CHttpException
     */
    private function loadCourtageData($courtage_data_id, $courtage_id)
    {
        $courtageData = $this->getCourtageData($courtage_data_id, $courtage_id);
        if (empty($courtageData)) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $courtageData;
    }

    /**
     * @param $courtage_data_id
     * @param $courtage_id
     *
     * @return CourtageData
     * @throws CHttpException
     */
    private function getCourtageData($courtage_data_id, $courtage_id)
    {
        $courtageData = null;
        if (empty($courtage_data_id)) {
            $courtageData = CourtageData::model()->getCourtageDataByCourtageId($courtage_id);
        } else {
            $courtageData = CourtageData::model()->findByPk($courtage_data_id);
        }
        if (empty($courtageData)) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $courtageData;
    }

    public function actionSachFolgecourtagen()
    {
        $log             = new CourtageSachCommandLog('search');
        $log->attributes = Yii::app()->request->getParam('CourtageSachCommandLog');
        $this->render('admin/sach_folgecourtagen/admin', ['log' => $log]);
    }

    public function actionCreateKuendigung($courtage_id = null, $courtage_data_id = null)
    {
        $courtageData     = $this->getCourtageData($courtage_data_id, $courtage_id);
        $courtage_data_id = $courtageData->id;
        $data             = Yii::app()->request->getPost('CourtageKuendigung');
        //Wenn Erfassung noch nicht Storniert wurde
        if (!CourtageKuendigung::model()->exists('courtage_data_id = :courtage_data_id', [':courtage_data_id' => $courtage_data_id])) {
            if (!empty($courtageData)) {
                if (!empty($data)) {
                    $kuendigung = $courtageData->createKuendigung($data);
                    if ($kuendigung->hasErrors()) {
                        Yii::app()->user->setFlash('error', 'Courtage konnte nicht gekündigt werden.');
                        $this->render('kuendigung/create', ['courtageKuendigung' => $kuendigung]);
                    } else {
                        Yii::app()->user->setFlash('success', 'Courtage wurde erfolgreich gekündigt.');
                        $this->redirect(['courtage/viewData', 'id' => $courtageData->id]);
                    }
                } else {
                    $kuendigung       = new CourtageKuendigung('create');
                    $kuendigung->date = date('d.m.Y');

                    $kuendigung->courtage_data_id = $courtage_data_id;
                    $this->render('kuendigung/create', ['courtageKuendigung' => $kuendigung]);
                }
            } else {
                Yii::app()->user->setFlash('error', 'Courtage konnte nicht gekündigt werden.');
                $this->redirect(['courtage/admin']);
            }
        } else {
            Yii::app()->user->setFlash('error', 'Die Courtage wurde bereits gekündigt.');
            $this->redirect(['courtage/admin']);
        }
    }

    public function actionCreateAnpassung($courtage_id = null, $courtage_data_id = null)
    {
        $courtageData     = $this->getCourtageData($courtage_data_id, $courtage_id);
        $courtage_data_id = $courtageData->id;
        $data             = Yii::app()->request->getPost('CourtageAnpassung');
        //Wenn Erfassung noch nicht Storniert wurde
        if (!empty($courtageData)) {
            if (!empty($data)) {
                $anpassung = $courtageData->createAnpassung($data);
                if ($anpassung->hasErrors()) {
                    Yii::app()->user->setFlash('error', 'Courtage konnte nicht angepasst werden.');
                    $this->render('anpassung/create', ['courtageAnpassung' => $anpassung]);
                } else {
                    $courtageData = $this->getCourtageData($courtage_data_id, $courtage_id);
                    $courtageData->renew();
                    Yii::app()->user->setFlash('success', 'Courtage wurde erfolgreich angepasst.');
                    $this->redirect(['courtage/viewData', 'id' => $courtageData->id]);
                }
            } else {
                $anpassung       = new CourtageAnpassung('create');
                $anpassung->date = date('d.m.Y');

                $anpassung->courtage_data_id = $courtage_data_id;
                $this->render('anpassung/create', ['courtageAnpassung' => $anpassung]);
            }
        } else {
            Yii::app()->user->setFlash('error', 'Courtage konnte nicht angepasst werden.');
            $this->redirect(['courtage/admin']);
        }
    }

    public function actionCreateWiderruf($courtage_id = null, $courtage_data_id = null)
    {
        $courtageData     = $this->getCourtageData($courtage_data_id, $courtage_id);
        $courtage_data_id = $courtageData->id;
        $data             = Yii::app()->request->getPost('CourtageWiderruf');
        if (!empty($courtageData)) {
            if (!empty($data)) {
                $widerruf = $courtageData->createWiderruf($data);
                if ($widerruf->hasErrors()) {
                    Yii::app()->user->setFlash('error', 'Courtage konnte nicht widerrufen werden.');
                    $this->render('widerruf/create', ['courtageWiderruf' => $widerruf]);
                } else {
                    Yii::app()->user->setFlash('success', 'Courtage wurde erfolgreich widerrufen');
                    $this->redirect(['courtage/viewData', 'id' => $courtageData->id]);
                }
            } else {
                $widerruf                   = new CourtageWiderruf('create');
                $widerruf->date             = ViewHelper::getDate();
                $widerruf->courtage_data_id = $courtage_data_id;
                $this->render('widerruf/create', ['courtageWiderruf' => $widerruf]);
            }
        } else {
            Yii::app()->user->setFlash('error', 'Courtage konnte nicht widerrufen werden.');
            $this->redirect(['courtage/admin']);
        }
    }

    public function actionCreatePayment($courtage_id)
    {
        $data            = Yii::app()->request->getPost('CourtagePayment');
        $payment         = new CourtagePayment('create');
        $payment->type   = CourtagePayment::TYPE_MANUAL;
        $payment->manual = 1;
        if (!empty($data)) {
            $courtage = Courtage::model()->findByPk($courtage_id);
            if (!empty($courtage)) {
                $payment->attributes  = $data;
                $payment->courtage_id = $courtage_id;
                if (!$payment->save()) {
                    Yii::app()->user->setFlash('error', 'Die Zahlung konnte nicht erstellt werden.');
                    $this->render('payment/create', ['courtagePayment' => $payment]);
                } else {
                    Yii::app()->user->setFlash('success', 'Die Zahlung wurde erfolgreich erstellt.');
                    $this->redirect(['courtage/viewCourtage', 'id' => $courtage->id]);
                }
            } else {
                Yii::app()->user->setFlash('error', 'Die Zahlung konnte nicht erstellt werden.');
                $this->redirect(['courtage/viewCourtage', 'id' => $courtage->id]);
            }
        } else {
            $payment->date        = ViewHelper::getDate();
            $payment->courtage_id = $courtage_id;
            $this->render('payment/create', ['courtagePayment' => $payment]);
        }
    }

    public function actionViewData($id)
    {
        $this->redirect(['courtage/admin', 'courtage_data_id' => $id, 'open_modal' => 1]);
    }

    public function actionViewCourtage($id)
    {
        $courtage = Courtage::model()->extendedInfo()->findByPk($id);

        $this->redirect([
                            'courtage/admin',
                            'courtage_data_id' => $courtage->courtage_data_id,
                            'open_modal'       => 1,
                            'courtage_id'      => $id
                        ]);
    }

    public function actionAjaxCreateDynamic($courtage_data_id, $date, $accept = 1)
    {
        $courtageData = CourtageData::model()->findByPk($courtage_data_id);
        $success      = false;
        if (!empty($courtageData)) {
            $possibleDynamics = $courtageData->getPossibleDynamics();
            if (isset($possibleDynamics[$date])) {
                $dynamic           = $possibleDynamics[$date];
                $dynamic->accepted = $accept;
                $success           = $courtageData->addDynamic($dynamic);
            }
        }
        echo $success ? 1 : 0;
        Yii::app()->end();
    }

    public function actionDynamics()
    {
        $this->render('dynamic/admin');
    }

    public function actionRenew($courtage_data_id)
    {
        if (Yii::app()->user->isAdmin()) {
            $courtageData = CourtageData::model()->findByPk($courtage_data_id);
            if (!empty($courtageData)) {
                echo '<pre>';
                print_r('Renew');
                echo '</pre>';
                $courtageData->renew();
            } else {
                echo '<pre>';
                print_r('Nicht gefunden');
                echo '</pre>';
            }
        }
    }

    public function actionFixAnpassungen()
    {
        if (Yii::app()->user->isAdmin()) {
            $anpassungen = CourtageAnpassung::model()->findAll();
            foreach ($anpassungen as $anpassung) {
                $anpassung->calcRatingAmount();
                $anpassung->save();
            }
        }
    }

    public function actionSettings()
    {
        $settings             = new UserCourtageInsuranceCompanySettings('search');
        $settings->attributes = Yii::app()->request->getParam('UserCourtageInsuranceCompanySettingsSearch');
        $userIds              = Yii::app()->user->getUnderlingsIds();
        if (!Yii::app()->user->isAdmin()) {
            if (empty($settings->user_id) || !in_array($settings->user_id, $userIds)) {
                $settings->user_id = $userIds;
            }
        }
        $this->render('settings/settings', ['settings' => $settings]);
    }

    public function actionCreate(
        $user_mail_id = null,
        $contract_id = null,
        $courtage_data_id = null,
        $courtage_id = null,
        $sequel = false,
        $user_writing_id = null
    ) {
        $post_courtage_data = Yii::app()->request->getPost('CourtageData') ?? Yii::app()->request->getParam('CourtageData');
        $post_courtage      = Yii::app()->request->getPost('Courtage') ?? Yii::app()->request->getParam('Courtage');
        $courtageData       = new CourtageData('create');

        $courtage        = new Courtage('create');
        $success         = null;
        $updateCourtages = false;
        //Mögliche Daten werden geladen
        if (!empty($courtage_id)) {
            $preLoadCourtage = Courtage::model()->findByPk($courtage_id);
            if (!empty($preLoadCourtage)) {
                $courtage_data_id = $preLoadCourtage->courtage_data_id;
            }
        }
        $this->preLoad($courtageData, 'UserMail', $user_mail_id);
        $this->preLoad($courtageData, 'UserWriting', $user_writing_id);
        $this->preLoad($courtageData, 'Contract', $contract_id);
        $this->preLoad($courtageData, 'CourtageData', $courtage_data_id, $sequel);
        $this->preLoad($courtage, 'CourtageData', $courtage_data_id);

        if (!empty($post_courtage_data)) {
            $transaction = CourtageData::model()->getDbConnection()->beginTransaction();
            try {
                if (empty($post_courtage_data['end_date'])) {
                    $post_courtage_data['end_date'] = CourtageData::DEFAULT_END_DATE;
                }
                $courtageData->setDefaultValues(true);
                $courtageData->attributes = $post_courtage_data;

                $courtageData->validate();
                $courtage->attributes = $post_courtage;
                $courtage->user_id    = $courtageData->user_id;
                if ($courtageData->addCourtage($courtage)) {
                    $updateCourtages = $courtage->createSuperiorCourtages();
                    if ($courtageData->save()) {
                        $transaction->commit();
                        $success = true;
                    } else {
                        $transaction->rollback();
                        $success = false;
                    }
                } else {
                    $transaction->rollback();
                    $success = false;
                }
            } catch (Exception $e) {
                $transaction->rollback();
                $success = false;
            }
        }
        if (empty($success)) {
            if (null !== $success) {
                Yii::app()->user->setFlash('error', 'Die Courtage konnte nicht erfasst werden');
            }
            $this->render('courtageData/create', ['courtageData' => $courtageData, 'courtage' => $courtage]);
        } else {
            Yii::app()->user->setFlash('success', 'Die Courtage wurde erfolgreich erfasst');
            if (!$updateCourtages) {
                $this->redirect(['courtage/admin', 'courtage_data_id' => $courtageData->id]);
            } else {
                $this->redirect(['courtage/updateCourtages', 'courtage_data_id' => $courtageData->id]);
            }
        }
    }

    private function preLoad($model, $type, $id, $sequel = false)
    {
        if (!empty($id)) {
            $exceptions  = [
            ];
            $method_name = $exceptions[$type] ?? ('preload' . $type);
            if (method_exists($model, $method_name)) {
                return $model->$method_name($id, $sequel);
            }
        }

        return false;
    }

    public function actionUpdate($courtage_data_id = null, $courtage_id = null)
    {
        $courtageData = $this->getCourtageData($courtage_data_id, $courtage_id);
        if ($courtageData->isCustomised()) {
            $this->redirect(['updateCustomised', 'id' => $courtageData->id]);
        }
        $courtage = $courtageData->getFirstCourtage();

        $courtage_data_data = Yii::app()->request->getPost('CourtageData');
        $courtage_data      = Yii::app()->request->getPost('Courtage');
        if (!empty($courtage_data_data)) {
            $courtage_data_data['status'] = $courtageData->status;
            $courtageData->setDefaultValues(true);
            $courtageData->attributes = $courtage_data_data;
            if (empty($courtage)) {
                Yii::app()->user->setFlash('error', 'Die Erfassung konnte nicht bearbeitet werden');
                $this->redirect(['courtage/admin', 'courtage_data_id' => $courtageData->id]);
            }
            $courtage->attributes = $courtage_data;
            if ($courtage->save() && $courtageData->save()) {
                $courtageData->renew();
                Yii::app()->user->setFlash('success', 'Die Erfassung wurde erfolgreich bearbeitet.');
                if (!$courtageData->hasMultipleCourtages(!Yii::app()->user->isAdmin())) {
                    $this->redirect(['courtage/admin', 'courtage_data_id' => $courtageData->id]);
                } else {
                    $this->redirect(['courtage/updateCourtages', 'courtage_data_id' => $courtageData->id]);
                }
            } else {
                Yii::app()->user->setFlash('error', 'Die Erfassung konnte nicht bearbeitet werden.');
                $this->render('courtageData/update', ['courtageData' => $courtageData, 'courtage' => $courtage]);
            }
        } else {
            $this->render('courtageData/update', ['courtageData' => $courtageData, 'courtage' => $courtage]);
        }
    }

    public function actionAjaxCourtagePreview()
    {
        //Vorhandene Courtage laden
        $courtage_id      = Yii::app()->request->getPost('courtage_id');
        $payment_id       = Yii::app()->request->getPost('payment_id');
        $postCourtageData = Yii::app()->request->getPost('CourtageData');
        $postStornoData   = Yii::app()->request->getPost('CourtageStorno');
        $postCourtage     = Yii::app()->request->getPost('Courtage');
        $active_tab       = Yii::app()->request->getPost('active_tab');
        $date             = null;
        $courtageData     = new CourtageData();
        if (!empty($courtage_id)) {
            $courtage = Courtage::model()->findByPk($courtage_id);
            if (!empty($courtage)) {
                $courtageData = @$courtage->courtageData;
            }
        }
        //        $courtageData->preview = true;
        if (!empty($postCourtageData)) {
            $courtageData->preview    = true;
            $courtageData->attributes = $postCourtageData;
            $courtageData->beforeSave();
        }
        if (!empty($postStornoData)) {
            $courtageData->preview = true;
            $storno                = new CourtageStorno('create');
            $storno->attributes    = $postStornoData;
            $courtageData->addStorno($storno);
            $courtageData->beforeSave();
            //kommt erstmal raus da hier keine neue BS errechnet werden soll, bin aber skeptisch warum das überhaupt drin war...
            //$ra   = $courtageData->getRatingAmount(true);
            $date = $storno->date;
        }
        //Versuchen Mit Postdaten zu füllen
        if (!empty($postCourtage)) {
            try {
                $courtageData->preview = true;
                $courtage              = new Courtage();
                $courtage->attributes  = $postCourtage;
                $courtage->beforeSave();
                $courtageData->beforeSave();
                $courtageData->addCourtage($courtage);
            } catch (Exception $e) {
                $courtageData = null;
                $courtage     = null;
                echo '<div class="alert alert-error">Bitte überprüfen Sie Ihre Eingaben</div>';
            }
        }
        $courtageData = !empty($courtageData) ? $courtageData : new CourtageData();
        $payment      = null;
        if (!empty($payment_id)) {
            $payment = CourtagePayment::model()->findByPk($payment_id);
            if (!empty($payment)) {
                $date = $payment->date;
            }
        }
        if (!empty($courtage)) {
            $courtage->preview = $courtageData->preview;
            $courtage->setSubordinateCourtage();
            echo $this->renderPartial('courtage/view/_container',
                ['courtage' => $courtage, 'payment' => $payment, 'active_tab' => $active_tab, 'date' => $date], true, true);
        } else {
            echo CHtml::tag('div', ['class' => 'alert alert-danger'], 'Die Erfassung konnte nicht gefunden werden');
        }
        Yii::app()->end();
    }

    public function actionAjaxGetPreviewButtons($courtage_id)
    {
        if (!empty($courtage_id) && self::hasRight(self::CHECK_RIGHT_ALL)) {
            $courtage           = Courtage::model()->findByPk($courtage_id);
            $anpassung_moeglich = false;
            if (!empty($courtage)) {
                $anpassung_moeglich = @$courtage->courtageData->anpassungMoeglich();
                $view               = !$courtage->courtageData->isCustomised() ? 'courtage/view/_modalButtons' : 'courtage/view/_modalButtonsCustomised';
                echo $this->renderPartial($view,
                    ['courtage' => $courtage, 'courtage_id' => $courtage->id, 'anpassung_moeglich' => $anpassung_moeglich], true, true);
            }
        } else {
            echo '';
        }
    }

    public function actionUpdateCourtages($courtage_data_id = null, $courtage_id = null)
    {
        $courtageData     = $this->getCourtageData($courtage_data_id, $courtage_id);
        $courtage_data_id = $courtageData->id;
        $courtages        = $courtageData->getCourtages(!Yii::app()->user->isAdmin());
        $post             = Yii::app()->request->getPost('Courtage');
        if (!empty($post)) {
            $sub_courtage = null;
            $error        = false;
            foreach ($courtages as $courtage) {
                foreach ($post as $data) {
                    if ($data['id'] == $courtage->id) {
                        $courtage->attributes = $data;
                        if (!empty($sub_courtage)) {
                            $error = $error || !$courtage->plausibilityCheck($sub_courtage);
                        }
                    }
                }
                $sub_courtage = $courtage;
            }
            if (!$error) {
                foreach ($courtages as $courtage) {
                    $courtageData->renew();
                }
                $this->redirect(['courtage/admin', 'courtage_data_id' => $courtageData->id]);
            } else {
                $this->render('courtage/updateMultiple', ['courtageData' => $courtageData, 'courtages' => $courtages]);
            }
        } else {
            $this->render('courtage/updateMultiple', ['courtageData' => $courtageData, 'courtages' => $courtages]);
        }
    }

    public function actionAjaxRatingAmount($courtage_data_id = null)
    {
        if (Yii::app()->request->isAjaxRequest) {
            if (!empty($courtage_data_id)) {
                $courtage = CourtageData::model()->findByPk($courtage_data_id);
            }
            if (empty($courtage)) {
                $courtage = new CourtageData();
            }
            $origContribution     = $courtage->contribution;
            $courtage->attributes = Yii::app()->request->getParam('CourtageData');
            $courtage->assignValues(Yii::app()->request->getParam('CourtageStorno'));
            $courtage->assignValues(Yii::app()->request->getParam('CourtageAnpassung'));
            $courtage->beforeSave();
            if (!empty($origContribution)) {
                $newContribution        = $courtage->contribution;
                $courtage->contribution = $origContribution;
                $rating_amount          = Yii::app()->formatter->format($courtage->getRatingAmountForNewContribution($newContribution), 'Double');
            } else {
                $rating_amount = Yii::app()->formatter->format($courtage->getRatingAmount(true), 'Double');
            }
            $rating_amount = str_replace('.', '', $rating_amount);
            echo !empty($rating_amount) ? $rating_amount : '';
        } else {
            throw new CHttpException('403', 'Forbidden access.');
        }
        Yii::app()->end();
    }

    public function actionDeleteRelationalModel($class, $id)
    {
        $renew = true;
        if ($class == 'CourtageKuendigung') {
            $renew = false;
        }
        echo $this->deleteRelationalModel($class, $id, $renew);
    }

    private function deleteRelationalModel($modelclass, $id, $renew = true)
    {
        if (class_exists($modelclass)) {
            $model = $modelclass::model()->findByPk($id);
            if (!empty($model)) {
                //Prüfen, ob der eingeloggte User das Recht besitzt, die Daten zu laden
                $data = CourtageData::model()->findByPk($model->courtage_data_id);
                if (!empty($data)) {
                    if ($model->delete()) {
                        if ($renew) {
                            $data->renew();
                        }

                        return true;
                    }
                }
            }
        }

        return false;
    }

    public function actionDeleteStorno($id)
    {
        echo $this->deleteRelationalModel('CourtageStorno', $id);
    }

    public function actionDeleteAnpassung($id)
    {
        echo $this->deleteRelationalModel('CourtageAnpassung', $id);
    }

    public function actionDeleteKuendigung($id)
    {
        echo $this->deleteRelationalModel('CourtageKuendigung', $id, false);
    }

    public function actionDeleteDynamic($id)
    {
        echo $this->deleteRelationalModel('CourtageDynamic', $id);
    }

    public function actionDeleteWiderruf($id)
    {
        echo $this->deleteRelationalModel('CourtageWiderruf', $id);
    }

    public function actionReclaim($courtage_id)
    {
        $courtage = Courtage::model()->checkAccess()->findByPk($courtage_id);
        if (!empty($courtage)) {
            $courtageData         = $courtage->courtageData;
            $courtageData->status = CourtageData::STATUS_COURTAGE_REKLAMIERT;
            $courtageData->save();

            $userMail     = $courtageData->generateUserMail();
            $usermaildata = $userMail->attributes;
            foreach ($usermaildata as $key => $value) {
                if (empty($usermaildata)) {
                    unset($usermaildata[$key]);
                }
            }
            $return = serialize($usermaildata);

            $this->redirect(Korrespondenz::mail()->ausUserMailData($usermaildata)->getUrl());
        } else {
            Yii::app()->user->setFlash('error', 'Die Courtage konnte nicht reklamiert werden');
            $this->redirect(['courtage/admin']);
        }
    }

    public function actionAjaxGetFieldsByPC()
    {
        $data  = [];
        $pc_id = Yii::app()->request->getPost('product_combo_id');
        if (!empty($pc_id)) {
            $data = self::getFieldArrayByPC($pc_id);
        }
        echo function_exists('json_encode') ? json_encode($data) : CJSON::encode($data);
    }

    public function actionCreateCustomised()
    {
        $model                        = new CourtageCustomisedDataForm();
        $model->courtageData->user_id = Yii::app()->user->getId();
        $model->courtagePayment->date = date('d.m.Y');
        if (!empty($_POST)) {
            $model->setAttributes($_POST);
            if ($model->save()) {
                Yii::app()->user->setFlash('success', 'Der Eintrag wurde erfolgreich gespeichert');
                $this->redirect(['admin', 'courtage_data_id' => $model->courtageData->id]);
            } else {
                Yii::app()->user->setFlash('error', 'Der Eintrag konnte nicht gespeichert werden');
            }
        }
        $this->render('//courtage/customised/create', compact('model'));
    }

    public function actionUpdateCustomised($id)
    {
        $record = CourtageData::model()->custom()->findByPk($id);
        if (null === $record) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }
        $model = new CourtageCustomisedDataForm();
        $model->setData($record);
        if (!empty($_POST)) {
            $model->setAttributes($_POST);
            if ($model->save()) {
                Yii::app()->user->setFlash('success', 'Der Eintrag wurde erfolgreich gespeichert');
                $this->redirect(['admin', 'courtage_data_id' => $model->courtageData->id]);
            } else {
                Yii::app()->user->setFlash('error', 'Der Eintrag konnte nicht gespeichert werden');
            }
        }
        $this->render('//courtage/customised/update', compact('model'));
    }

    public static function getFieldArrayByPC($productComboId)
    {
        $productCombo = ProductCombo::model()->findByPk($productComboId);
        //        $productCombo = $productCombo->getFirstProductCombo();
        $courtageData                   = new CourtageData();
        $courtage_data_attributes       = $courtageData->attributes;
        $courtageData->product_combo_id = $productCombo->id;

        $courtage            = new Courtage();
        $courtage_attributes = $courtage->attributes;

        $exclude = [
            'user_id',
            'client_id',
            'broker_user_id',
            'insurance_company_id',
            'product_combo_id',
        ];
        switch ($productCombo->level1_id) {
            case Product::VORSORGE:
                $hide = [
                ];
                break;
            case Product::GEWERBE_SACH:
            case Product::PRIVAT_SACH:
            case Product::INVESTMENTDEPOT:
                $hide = [
                    'Courtage'     => [
                        'trailer_commission' => [
                            'content' => 'Analog AC'
                        ],
                        'dynamic'            => [
                            'content' => 'Analog AC'
                        ],
                    ],
                    'CourtageData' => [
                        'trailer_commission_payment_type' => [
                            'hide' => true
                        ],
                        'max'                             => [
                            'content' => 1
                        ],
                        'responseability_period'          => [
                            'content' => 12
                        ],
                    ]
                ];
                break;
            case Product::KRANKENVERSICHERUNG:
                $hide = [
                    'CourtageData' => [
                        'end_date' => [
                            'hide'    => true,
                            'content' => CourtageData::DEFAULT_END_DATE,
                        ],
                    ]
                ];
                break;
            default:
                $hide = [];
        }

        if (!$productCombo->hasTrailerContribution()) {
            $hide['CourtageData']['trailer_contribution'] = [
                'hide' => true,
            ];
        }
        //Lap für alles außer Vorsorge ausblenden
        if ($productCombo->level1_id != Product::VORSORGE) {
            $hide['Courtage']['lap']                  = [
                'hide' => true,
            ];
            $hide['CourtageData']['lap_payment_type'] = [
                'hide' => true,
            ];
        }

        foreach ([
                     'CourtageData' => $courtage_data_attributes,
                     'Courtage'     => $courtage_attributes,
                 ] as $name => $attributes) {
            if (empty($hide[$name])) {
                $hide[$name] = [];
            }
            foreach ($attributes as $attribute => $val) {
                if (!in_array($attribute, $exclude)) {
                    if (in_array($attribute, array_keys($hide[$name]))) {
                        $hide[$name][$attribute]['hide'] = true;
                    } else {
                        $hide[$name][$attribute]['hide'] = false;
                    }
                    if ($name == 'CourtageData') {
                        $hide[$name][$attribute]['label'] = $courtageData->getAttributeLabel($attribute);
                    } else {
                        $hide[$name][$attribute]['label'] = $courtage->getAttributeLabel($attribute);
                    }
                }
            }
        }

        return $hide;
    }

    public function actionAjaxUserCommissionExists()
    {
        if (!empty($_POST['user_id']) && (Yii::app()->user->getUserRole() == UserRole::MAINBROKER || Yii::app()->user->getUserRole() == UserRole::ADMIN)) {
            $ignore = UserCourtageIgnoreWarning::model()->findByAttributes([
                                                                               'user_id'        => Yii::app()->user->getId(),
                                                                               'ignore_user_id' => $_POST['user_id']
                                                                           ]);
            if (empty($ignore)) {
                $user = Systemuser::model()->findByPk($_POST['user_id']);
                if ($user->user_role !== UserRole::MAINBROKER) {
                    $commissions = Commission::model()->findAllByAttributes([
                                                                                'user_id' => $_POST['user_id']
                                                                            ]);
                    echo (count($commissions) > 0) ? 1 : 0;
                } else {
                    echo 1;
                }
            } else {
                echo 1;
            }
        } else {
            echo 1;
        }
        Yii::app()->end();
    }

    public function actionAdminErrors()
    {
        $model = new AdminErrors();
        $model->setAttributes($_GET);
        $this->render('//courtage/admin/errors/adminErrors', ['model' => $model]);
    }

    public function actionAdminErrorsView($courtageDataId)
    {
        $model = $this->loadCourtageData($courtageDataId, null);
        $this->renderPartial('//courtage/admin/errors/view', ['model' => $model]);
        Yii::app()->end();
    }

    public function actionAjaxIgnoreWarningForUser()
    {
        if (!empty($_POST['user_id'])) {
            $underlings = Yii::app()->user->getUnderlingsIds();
            if (Yii::app()->user->isAdmin() || in_array($_POST['user_id'], $underlings)) {
                UserCourtageIgnoreWarning::ignoreWarning($_POST['user_id']);
            }
        }
        Yii::app()->end();
    }

    public function actionDeletePayment($id)
    {
        /**
         * @var CourtagePayment $payment
         */
        $payment = CourtagePayment::model()->findByPk($id);
        if (!empty($payment) && $payment->manual) {
            //Rechte
            $courtageData = $this->loadCourtageData(null, $payment->courtage_id);
            if (!empty($courtageData)) {
                $payment->delete();
            }
        }
        $this->redirect(['admin', 'courtage_data_id' => $payment->courtage->courtage_data_id]);
    }

    public function actionAbrechnungstool()
    {
        $this->redirect(env('ABRECHNUNGSTOOL_URL'));
    }
}
