<?php

/**
 * Created by PhpStorm.
 * User: alex
 * Date: 22.04.16
 * Time: 13:17
 */
class CourtageDuplicateController extends Controller
{
    public function accessRules()
    {
        if (Yii::app()->user->isAdmin() || CourtageController::hasRight(CourtageController::CHECK_RIGHT_ALL)) {
            return [
                [
                    'allow', // allow all users
                    'users' => ['*'],
                ],
            ];
        }

        return [
                [
                    'deny', // deny all users
                    'users' => ['*'],
                ],
            ];
    }

    public function init()
    {
        parent::init();
        $this->layout = 'application.views.layouts.main_breadcrumbs';
    }

    public function actionAdmin()
    {
        $model = new CourtageDuplicates();
        $model->setUserIds(Yii::app()->user->getUnderlingsIds());
        $model->setAttributes($_GET);
        $this->render('//courtage/duplicates/admin', ['model' => $model]);
    }

    /**
     * @param $id
     *
     * @return \CourtageDuplicate|null
     * @throws \CHttpException
     */
    public function loadModel($id)
    {
        $model = CourtageDuplicate::getById($id);
        if ($model === null) {
            throw new CHttpException(404);
        }

        return $model;
    }

    public function actionCompare($duplicateId)
    {
        $model = $this->loadModel($duplicateId);
        if ($model->hasDuplicates()) {
            echo $this->renderPartial('//courtage/duplicates/compareModal', ['model' => $model]);
        } else {
            echo $this->renderPartial('//courtage/duplicates/noDuplicateView', ['model' => $model]);
        }
        Yii::app()->end();
    }

    public function actionViewData($duplicateId, $courtageDataId)
    {
        $model        = $this->loadModel($duplicateId);
        $courtageData = $model->getCourtageDataById($courtageDataId);
        if (!empty($courtageData)) {
            echo $this->renderPartial('//courtage/duplicates/viewData', ['model' => $model, 'courtageData' => $courtageData]);
            Yii::app()->end();
        }
        echo $this->renderPartial('//courtage/duplicates/noDuplicateView', ['model' => $model]);
        Yii::app()->end();
    }

    public function actionDelete($duplicateId, $courtageDataId)
    {
        $model        = $this->loadModel($duplicateId);
        $courtageData = $model->getCourtageDataById($courtageDataId);
        if (!empty($courtageData)) {
            if ($courtageData->delete()) {
                Yii::app()->user->setFlash('success', 'Die Erfassung wurde erfolgreich gelöscht');
                if ($model->hasMultipleDuplicates()) {
                    Yii::app()->user->setFlash('warning', 'Es bestehen weitere Dubletten zu dieser Erfassung');
                }
            }
        }
        $this->redirect(['admin']);
    }
}
