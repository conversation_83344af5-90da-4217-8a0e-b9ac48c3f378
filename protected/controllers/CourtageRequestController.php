<?php

use Business\Courtageanfragen\Documents\CourtageRequestDocumentsFactory;
use Business\Courtageanfragen\Documents\RequiredDocuments;
use Business\Courtageanfragen\Mails\AnbindungNichtMoeglichMail;
use Business\Courtageanfragen\Mails\CourtageRequestMailFactory;
use Business\User\Users;
use Business\Vermittlernummer\BrokerIdStatus;
use Carbon\Carbon;
use Demv\JSend\JSendResponse;

class CourtageRequestController extends GlobalRightController
{
    private const ALLOW_DIREKTVEREINBARUNG = true;
    public static $status_sent             = 1;
    public static $status_notsent          = 2;
    public static $status_reminder_sent    = 3;
    public static $status_error            = -1;
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     *             using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    public static function firstApplicationUpload($user_id, $insurance_company_id, $first_application)
    {
        $broker_id = BrokerId::model()->findByAttributes([
                'user_id'              => $user_id,
                'insurance_company_id' => $insurance_company_id,
                'status'               => BrokerIdStatus::AKTIV,
                'type_id'              => BrokerIdType::ANTRAGSNUMMER,
        ]);

        $remind = false;

        if (empty($broker_id)) {
            //Wenn bereits eine Vermittlernummer eingetragen wurde mit dem Status "Courtagezusage nicht möglich", soll die Mail mit dieser Information an den Makler gehen
            $notPossibleBrokerId = BrokerId::model()->findByAttributes(
                [
                    'user_id'              => $user_id,
                    'insurance_company_id' => $insurance_company_id,
                    'status'               => BrokerIdStatus::NICHT_MOEGLICH,
                ]
            );
            if (!empty($notPossibleBrokerId)) {
                (new AnbindungNichtMoeglichMail($notPossibleBrokerId))->fromErstantrag()->send();

                return;
            }

            //Bereits ein anderer Erstantrag vorhanden?
            //        $alreadyUploaded = UserFilesController::userFilesExists($user_id, DocumentType::$erstantrag, array($user_file_id));
            $courtageRequest           = self::courtageRequestExists($user_id, $insurance_company_id);
            $courtageRequestController = new self('courtagerequest');
            //Wenn die Courtageanfrage bereits zu der Gesellschaft rausgegangen ist
            if (!empty($courtageRequest) && in_array($courtageRequest->courtage_request_status_id,
                [
                    CourtageRequestStatus::SENT_WAITING,
                    CourtageRequestStatus::DOCUMENTS_MISSING_SEND,
                ])
            ) {
                $criteria = new CDbCriteria();
                $criteria->compare('user_id', $first_application->user_id);
                $criteria->compare('document_type_id', $first_application->document_type_id);
                $criteria->compare('insurance_company_id', $first_application->insurance_company_id);
                $criteria->addCondition('id <> ' . $first_application->id);
                $sequel  = UserFiles::model()->exists($criteria);
                $user    = User::model()->findByPk($user_id);
                $company = InsuranceCompany::model()->findByPk($insurance_company_id);
                ErstantragUpload::new($user, $company, $first_application)
                                ->istFolgeantrag($sequel)
                                ->erstellen();
                if (!empty($courtageRequest->request_date)) {
                    $remind = $courtageRequestController->getTimeDiffinDays(ViewHelper::getDate(true), $courtageRequest->request_date) >= 1;
                } else {
                    $remind = $courtageRequestController->getTimeDiffinDays(ViewHelper::getDate(true), $courtageRequest->last_reminder_date)
                              >= 1;
                }
            }
            //Wenn bereits ein Erstantrag hochgeladen wurde, wird geprüft, ob zu dieser Gesellschaft eine Courtageanfrage besteht
            //Besteht beireits eine Courtageanfrage?
            //Wenn nicht
            elseif (empty($courtageRequest)) {
                //Sende Courtageanfrage
                $courtageRequest                    = $courtageRequestController->createCourtageRequest($insurance_company_id, $user_id, 0,
                    ViewHelper::getDate(true), 'Angelegt durch einen hochgeladenen Erstantrag.');
                $courtageRequest->first_application = true;
                $courtageRequestController->sendCourtageRequest($courtageRequest, false, false, true);
            } elseif (!empty($courtageRequest) && in_array($courtageRequest->courtage_request_status_id,
                [
                    CourtageRequestStatus::DOCUMENTS_MISSING_NOT_SEND,
                    CourtageRequestStatus::NO_REPLY_FROM_BROKER,
                ])
            ) {
                if (!empty($courtageRequest->request_date)) {
                    $remind = $courtageRequestController->getTimeDiffinDays(ViewHelper::getDate(true), $courtageRequest->request_date) >= 1;
                } else {
                    $remind = $courtageRequestController->getTimeDiffinDays(ViewHelper::getDate(true), $courtageRequest->last_reminder_date)
                              >= 1;
                }
            }
            if ($remind) {
                $courtageRequest->no_reminders = 0;
                $courtageRequest->save(false);
                $courtageRequestController->updateCourtageRequest($courtageRequest, true);
            }
        } else {
            $insurance_company_id = $insurance_company_id === null ? null : (int) $insurance_company_id;

            $procedure = AntragEinreichenVorgang::make(User::model()->findByPk($user_id))
                                                ->forCompany($insurance_company_id)
                                                ->withAttachments([$first_application->getPath()])
                                                ->autoloadTemplate()
                                                ->findSupportPerson();
            if (!$procedure->send()) {
            }
        }
    }

    public static function courtageRequestExists($user_id, $insurance_company_id)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('t.user_id', $user_id);
        $criteria->compare('t.insurance_company_id', $insurance_company_id);
        $criteria->addNotInCondition('t.courtage_request_status_id',
            [
                CourtageRequestStatus::CANCELED_NOT_SENT,
                CourtageRequestStatus::CANCELED_SENT,
                CourtageRequestStatus::CLOSED,
            ]
        );
        $model                   = new CourtageRequest();
        $model::$ignoreDemvScope = true;
        $courtageRequest         = $model->find($criteria);
        $model::$ignoreDemvScope = false;

        return !empty($courtageRequest) ? $courtageRequest : false;
    }

    private function getTimeDiffinDays($date1, $date2)
    {
        $timestamp1 = strtotime($date1);
        $timestamp2 = strtotime($date2);

        return floor(($timestamp1 - $timestamp2) / 86400);
    }

    /**
     * Dies ist die Methode die von dem CronJob ausgeführt wird.
     * Diese Methode überprüft
     *
     * @param CourtageRequest $courtagerequest
     * @param                 $invs            Gibt an, ob die Erinnerung sichbar sein soll
     *                                         Wenn die Erinnerung nicht sichtbar sein soll ($invis = false), fann wird das Datum und die
     *                                         Anzahl der Erinnerung nicht gespeichert und es wird unabhängig von der letzten Erinnerung
     *                                         erinnert
     * @param mixed           $invis
     * @param null|mixed      $today
     */
    public function updateCourtageRequest($courtagerequest, $invis = false, $today = null)
    {
        $status = [
            CourtageRequestStatus::DOCUMENTS_MISSING_NOT_SEND,
            CourtageRequestStatus::DOCUMENTS_MISSING_SEND,
            CourtageRequestStatus::SENT_WAITING,
            CourtageRequestStatus::NOT_SET,
            CourtageRequestStatus::NO_REPLY_FROM_BROKER,
        ];
        if (empty($today)) {
            $today = ViewHelper::getDate(true);
        } else {
            $today = ViewHelper::parseToDBDate($today);
        }

        if (in_array($courtagerequest->courtage_request_status_id, $status) && !$courtagerequest->remindersRefused()) {
            $lastDate = match ((int)$courtagerequest->reminders) {
                                0       => $courtagerequest->request_date ?? $courtagerequest->last_reminder_date,
                                1, 2    => $courtagerequest->last_reminder_date,
                                default => $today, // ensures that no reminder is sent
            };
            
            $remind = $this->getTimeDiffinDays($today, $lastDate) >= 21;

            //Ist hier nur drin, damit ich die if-Anweisung oben auskommentieren konnte
            if ($remind || $invis) {
                $statusid = $courtagerequest->courtage_request_status_id;

                if ($statusid == CourtageRequestStatus::DOCUMENTS_MISSING_NOT_SEND || $statusid == CourtageRequestStatus::DOCUMENTS_MISSING_SEND) {
                    CourtageRequestMailFactory::new($courtagerequest)
                                              ->erinnerungFehlendeUnterlagen()
                                              ->markAsReminder($courtagerequest->reminders + 1)
                                              ->send();
                } elseif ($statusid == CourtageRequestStatus::SENT_WAITING) {
                    CourtageRequestMailFactory::new($courtagerequest)
                                              ->erinnerungAnGesellschaft()
                                              ->markAsReminder($courtagerequest->reminders + 1)
                                              ->send();
                } elseif ($statusid == CourtageRequestStatus::NOT_SET) {
                    $this->sendCourtageRequest($courtagerequest, false, false);
                }

                if (!$invis) {
                    $courtagerequest->reminders          = $courtagerequest->reminders + 1;
                    $courtagerequest->last_reminder_date = ViewHelper::getDate(true);
                    $courtagerequest->save(false);
                }
            }
        }
    }

    public function actions()
    {
        return [
            'changereminderstatus' => [
                'class'     => 'application.components.actions.ModelAttributeEditAction',
                'modelname' => 'CourtageRequest',
                'attribute' => 'no_reminders',
            ],
        ];
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues CourtageRequest-Model erstellt werden soll.
     *  Die Methode öffnet das CourtageRequest-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if (($courtagerequest = $this->create($data)) != null) {
                    $this->redirect('admin');

                    return $courtagerequest;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das CourtageRequest -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *              $Data-Array des CourtageRequest -Models
     */
    private function getCreateFormularData()
    {
        $formulardata    = [];
        $courtagerequest = new CourtageRequest();

        if (!$this->validData($_POST)) {
            $this->render('create', ['courtagerequest' => $courtagerequest]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model CourtageRequest handelt
     *
     * @param type $data
     *                   $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        return isset($data) &&
               isset($data['CourtageRequest']);
    }

    /**
     * Diese Methode erstellt ein neues CourtageRequest -Model
     *
     * @param type $data
     *                   Die CourtageRequest -Daten als Array
     *
     * @return $courtagerequest
     *                          Das erstellte CourtageRequest -Exemplar
     */
    public function create(array $data)
    {
        if (!isset($data['CourtageRequest'])) {
            return null;
        }

        $courtagerequest                        = new CourtageRequest();
        $courtagerequest->attributes            = $data['CourtageRequest'];
        $courtagerequest->last_status_edit_date = ViewHelper::getDate(true);
        $courtagerequest->last_edit_date        = ViewHelper::getDate(true);
        $courtagerequest->last_edit_user_id     = currentUser()->id ?? Users::DEMV_SYSTEM;

        $brokeridController = new BrokerIdController('brokerid');
        $bidarray           = [
            'BrokerId' => [
                'user_id'              => $courtagerequest->user_id,
                'insurance_company_id' => $courtagerequest->insurance_company_id,
                'type_id'              => 1,
                'status'               => BrokerIdStatus::OFFEN,
            ],
        ];

        $rearrangementFile = UserFiles::model()->findByAttributes([
                                                                      'user_id'              => $courtagerequest->user_id,
                                                                      'insurance_company_id' => $courtagerequest->insurance_company_id,
                                                                      'document_type_id'     => DocumentType::UMZUG_COURTAGEANFRAGE,
                                                                  ]);

        if (!empty($rearrangementFile)) {
            $courtagerequest->rearrangement = 1;
        }

        if ($courtagerequest->save()) {
            $courtagerequest->refresh();

            if (($brokerid = $brokeridController->create($bidarray, false)) != null) {
                $courtagerequest->broker_id_id = $brokerid->id;
                $courtagerequest->save();
            }

            return $courtagerequest;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein CourtageRequest -Model aktualisiert werden soll.
     *  Die Methode öffnet das CourtageRequest -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden CourtageRequest -Models
     */
    public function actionUpdate($id)
    {
        $this->menuGroup = MenuGroup::STAMMDATEN;
        $courtagerequest = $this->loadModel($id);
        if ($courtagerequest != null) {
            $data = $this->getUpdateFormularData($id);
            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if (($courtagerequest = $this->update($data)) != null) {
                        $this->redirect(['systemuser/update?id=' . $courtagerequest->user_id . '&activetab=Status-Courtagezusage']);

                        return $courtagerequest;
                    }
                }
            }
        } else {
            throw new CHttpException(404);
        }
    }

    /**
     * Lädt das CourtageRequest-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *                 ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *                  Das Exemplar des CourtageRequest-Models mit der übergebenen ID falls dieses existiert, sonst null
     */
    public function loadModel($id)
    {
        $courtagerequest = CourtageRequest::model()->findByPk($id);

        return !empty($courtagerequest) && Yii::app()->user->getPermissions()->canSeeUser($courtagerequest->user_id) ? $courtagerequest : null;
    }

    /**
     * Öffnet das CourtageRequest-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *                 die ID des zu aktualisierenden CourtageRequest-Exemplares
     *
     * @return $data
     *               Das aktualierte Data-Array
     */
    private function getUpdateFormularData($id)
    {
        $courtagerequest = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('update', ['courtagerequest' => $courtagerequest]);
        }

        $formulardata                          = $_POST;
        $formulardata['CourtageRequest']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein CourtageRequest-Exemplar
     *
     * @param type $data
     *                   Die CourtageRequest-Daten als Array
     *
     * @return $courtagerequest
     *                          Das aktualisierte CourtageRequest-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        $courtagerequest = $this->loadModel($data['CourtageRequest']['id']);
        if ($this->validData($data)) {
            if ($courtagerequest->status->sent == 0) {
                if (isset($data['CourtageRequest']['visit'])) {
                    $courtagerequest->visit = !empty($data['CourtageRequest']['visit']);
                }
                if (isset($data['CourtageRequest']['courtage_commitment_date'])) {
                    $courtagerequest->courtage_commitment_date = UtilHelper::parseToDBDate($data['CourtageRequest']['courtage_commitment_date']);
                }
            }

            if (isset($data['CourtageRequest']['comment'])) {
                $courtagerequest->comment = $data['CourtageRequest']['comment'];
            }
            if (isset($data['CourtageRequest']['courtage_request_status_id']) && $courtagerequest->courtage_request_status_id != $data['CourtageRequest']['courtage_request_status_id']) {
                $courtagerequest->courtage_request_status_id = $data['CourtageRequest']['courtage_request_status_id'];
                $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
            }

            $courtagerequest->last_edit_date    = ViewHelper::getDate(true);
            $courtagerequest->last_edit_user_id = Yii::app()->user->getID();
            if ($courtagerequest->save()) {
                return $courtagerequest;
            }

            return null;
        }

        return null;
    }

    /**
     * @param type $id
     *
     * @return true/false
     *                    true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Sendet eine CourtageAnfrage an eine InsuranceCompany, sofern die benötigten Documents vorhanden sind
     *
     * @param type  $insuranceCompanyID
     *                                  ID der InsuranceCompany, an die die CourtageAnfrage gesendet werden soll
     * @param type  $userID
     *                                  ID des Users, für den die Anfrage gesendet werden soll
     * @param type  $visit
     *                                  True, falls ein Besuch möglich ist, sonst false     *
     * @param type  $date
     *                                  Das Datum, zu dem die Courtagezusage erteilt werden soll
     * @param mixed $comment
     */
    public function createCourtageRequest($insuranceCompanyID, $userID, $visit, $date, $comment = '')
    {
        $courtageRequest = [
            'CourtageRequest' => [
                'user_id'                    => $userID,
                'insurance_company_id'       => $insuranceCompanyID,
                'visit'                      => $visit,
                'courtage_commitment_date'   => ViewHelper::parseToDBDate($date), //ToDO
                'courtage_request_status_id' => CourtageRequestStatus::NOT_SET,
                'comment'                    => $comment,
            ],
        ];
        $request         = $this->create($courtageRequest);

        return $request;
    }

    /**
     * Sendet eine CourtageAnfrage an eine InsuranceCompany, sofern die benötigten Documents vorhanden sind
     *
     * @param type            $icourtagerequestid
     *                                  Die ID der CourtageRequest, die verschickt werden soll
     * @param type            $forced   (false)
     *                                  Gibt an, ob die Courtageanfragen erzwungen werden soll
     * @param type            $redirect
     *                                  Gibt an, ob ein redirect erfolgen soll
     * @param CourtageRequest $courtagerequest
     * @param mixed           $initialTry
     */
    public function sendCourtageRequest($courtagerequest, $forced = false, $redirect = false, $initialTry = false)
    {
        $model                   = new UserFiles();
        $model::$ignoreDemvScope = true;
        $requireddocuments       = RequiredDocuments::createForCourtageRequest($courtagerequest)->getAll();

        $notuploadeddocuments = CourtageRequestDocumentsFactory::createForCourtageRequest($courtagerequest)->getMissing()->getDocumentTypeIds();
        $return               = self::$status_error;

        //Wenn keine Dokumente Fehlen wird die CourtageRequest rausgeschickt
        if (empty($notuploadeddocuments)) {
            //            echo "Alle Dokumente vorhanden: Mail verschickt";
            CourtageRequestMailFactory::new($courtagerequest)->kooperationsUnterlagenAnGesellschaft()->send();
            $courtagerequest->courtage_request_status_id = CourtageRequestStatus::SENT_WAITING;
            $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
            $courtagerequest->request_date               = ViewHelper::getDate(true);
            $courtagerequest->save();
            $return = self::$status_sent;
        } else {
            //Es sind Teile der benötigten Dokumente vorhanden. Die Mail soll Manuell
            //verschickt werden können
            if (count($notuploadeddocuments) < count($requireddocuments)) {
                //Falls Nein
                if (!$forced) {
                    $courtagerequest->courtage_request_status_id = CourtageRequestStatus::DOCUMENTS_MISSING_NOT_SEND;
                    $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
                    $_POST                                       = [];
                    CourtageRequestMailFactory::new($courtagerequest)->erinnerungFehlendeUnterlagen()->send();
                    if (!$initialTry) {
                        $courtagerequest->reminders = $courtagerequest->reminders + 1;
                    }
                    $courtagerequest->last_reminder_date = ViewHelper::getDate(true);
                    $courtagerequest->save();
                    $return = self::$status_reminder_sent;
                } //Falls Ja
                else {
                    //Mail verschicken
                    $courtagerequest->courtage_request_status_id = CourtageRequestStatus::DOCUMENTS_MISSING_SEND;
                    $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
                    $courtagerequest->request_date               = ViewHelper::getDate(true);
                    $courtagerequest->reminders                  = 0;
                    $courtagerequest->last_reminder_date         = null;
                    $courtagerequest->save();
                    CourtageRequestMailFactory::new($courtagerequest)->kooperationsUnterlagenAnGesellschaft()->send();
                    CourtageRequestMailFactory::new($courtagerequest)->erinnerungFehlendeUnterlagen()->send();
                    $return = self::$status_sent;
                }
            }

            //Die benötigten Dokumente fehlen komplett
            //Der Makler soll erinnert werden
            else {
                $courtagerequest->courtage_request_status_id = CourtageRequestStatus::DOCUMENTS_MISSING_NOT_SEND;
                $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
                if (!$courtagerequest->save()) {
                    $return = self::$status_error;
                }

                // Mail nicht verschickt. Makler wird erinnert
                if ($courtagerequest->reminders >= 0 && $courtagerequest->reminders <= 2) {
                    CourtageRequestMailFactory::new($courtagerequest)->erinnerungFehlendeUnterlagen()->send();
                    if (!$initialTry) {
                        $courtagerequest->reminders = $courtagerequest->reminders + 1;
                    }
                    $courtagerequest->last_reminder_date = ViewHelper::getDate(true);
                    if (!$courtagerequest->save()) {
                    }

                    $return = self::$status_reminder_sent;
                }
            }
        }
        if ($redirect) {
            $_POST = [];
            $this->redirect(['Systemuser/update/' . $courtagerequest->user_id . '?activetab=Status+Courtagezusage']);
        } else {
            return $return;
        }
    }

    private function setFlash($successcounter, $remindercounter, $failcounter)
    {
        if ($successcounter > 0) {
            if ($successcounter == 1) {
                Yii::app()->user->setFlash('success', 'Eine Courtageanfrage wurde erfolgreich verschickt');
            } else {
                Yii::app()->user->setFlash('success', 'Es wurden erfolgreich ' . $successcounter . ' Courtageanfragen verschickt');
            }
        }
        if ($remindercounter > 0) {
            if ($remindercounter == 1) {
                Yii::app()->user->setFlash('notice', 'Es wurde an eine Courtageanfrage erinnert');
            } else {
                Yii::app()->user->setFlash('notice', 'Es wurde erfolgreich an ' . $remindercounter . ' Courtageanfragen erinnert' . '<br>');
            }
        }
        if ($failcounter > 0) {
            if ($failcounter == 1) {
                Yii::app()->user->setFlash('error', 'Eine Courtageanfrage konnte nicht verschickt werden');
            } else {
                Yii::app()->user->setFlash('error', 'Es konnten ' . $failcounter . ' Courtageanfragen nicht verschickt werden');
            }
        }
    }

    public function cancelCourtageRequest($courtagerequest)
    {
        if ($courtagerequest->status->sent == 1) {
            //Anfrage wird storniert
            CourtageRequestMailFactory::new($courtagerequest)->anfrageAbgebrochen()->send();
            $courtagerequest->courtage_request_status_id = CourtageRequestStatus::CANCELED_SENT;
            $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
            $courtagerequest->last_edit_date             = ViewHelper::getDate(true);
            $courtagerequest->save();

            $brokerid         = $courtagerequest->brokerId;
            $brokerid->status = BrokerIdStatus::STORNIERT;
            $brokerid->save();
        } else {
            //Anfrage wird nur abgebrochen
            $courtagerequest->courtage_request_status_id = CourtageRequestStatus::CANCELED_NOT_SENT;
            $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
            $courtagerequest->last_edit_date             = ViewHelper::getDate(true);
            $courtagerequest->save();

            $brokerid = $courtagerequest->brokerId;
            if (!empty($brokerid)) {
                $brokerid->status = BrokerIdStatus::STORNIERT;
                $brokerid->save();
            }
        }
    }

    /**
     * Diese Methode aktualisert alle CourtageAnfragen an eine Gesellschaft, nachdem etwas an den Dokumenten der Gesellschaft geändert wurde
     *
     * @param type $insurancecompanyid
     *                                 ID der Gesellschaft, deren Dokumente verändert wurden
     * @param type $documenttypeid
     *                                 ID des DokumentTyps, der Datei, die geändert wurde
     */
    public function updateCourageRequestsAfterInsuranceCompanyChange($insurancecompanyid)
    {
        $courtagerequests = CourtageRequest::model()->findAllByAttributes(['insurance_company_id' => $insurancecompanyid]);
        foreach ($courtagerequests as $courtagerequest) {
            if ($courtagerequest->courtage_request_status_id == CourtageRequestStatus::DOCUMENTS_MISSING_NOT_SEND || $courtagerequest->courtage_request_status_id
                                                                                                                     == CourtageRequestStatus::DOCUMENTS_MISSING_SEND
            ) {
                $missingdocuments = CourtageRequestDocumentsFactory::createForCourtageRequest($courtagerequest)->getMissing()->getDocumentTypeIds();
                if (empty($missingdocuments)) {
                    if ($courtagerequest->courtage_request_status_id == CourtageRequestStatus::DOCUMENTS_MISSING_NOT_SEND) {
                        CourtageRequestMailFactory::new($courtagerequest)->kooperationsUnterlagenAnGesellschaft()->send();
                        $courtagerequest->request_date = ViewHelper::getDate(true);
                    }
                    $courtagerequest->courtage_request_status_id = CourtageRequestStatus::SENT_WAITING;
                    $courtagerequest->last_status_edit_date      = ViewHelper::getDate(true);
                    $courtagerequest->reminders                  = 0;
                    $courtagerequest->last_reminder_date         = null;
                    $courtagerequest->save();
                }
            }
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param CourtageRequest $courtagerequest the courtagerequest to be validated
     */
    protected function performAjaxValidation($courtagerequest)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'courtage-request-form') {
            echo CActiveForm::validate($courtagerequest);
            Yii::app()->end();
        }
    }

    // ###########################################################################################################
    // ##############################################  Actions  ##################################################
    // ###########################################################################################################

    public function actionChangeStatus($id, $status_id)
    {
        $courtageRequest = $this->loadModel($id);
        if (!empty($courtageRequest)) {
            if ($courtageRequest->isChangeAbleToStatus($status_id)) {
                if ($status_id == CourtageRequestStatus::CANCELED_SENT || $status_id == CourtageRequestStatus::CANCELED_NOT_SENT) {
                    $this->cancelCourtageRequest($courtageRequest);
                } else {
                    $courtageRequest->courtage_request_status_id = $status_id;
                    $courtageRequest->last_edit_date             = ViewHelper::getDate(true);
                    $courtageRequest->last_edit_user_id          = Yii::app()->user->getId();
                    $courtageRequest->last_status_edit_date      = ViewHelper::getDate(true);
                    $courtageRequest->reminders                  = 0;
                    $courtageRequest->last_reminder_date         = null;
                    $courtageRequest->save();
                    if ($status_id == CourtageRequestStatus::DOCUMENTS_MISSING_SEND) {
                        CourtageRequestMailFactory::new($courtageRequest)->erinnerungFehlendeUnterlagen()->send();
                    }
                }
                Yii::app()->user->setFlash('success', 'Der Status wurde aktualisiert');
            } else {
                Yii::app()->user->setFlash('error', 'Der Status konnte nicht geändert werden');
            }
        } else {
            Yii::app()->user->setFlash('error', 'Der Status konnte nicht geändert werden');
        }
        $this->redirect(['courtageRequest/update', 'id' => $id]);
    }

    public function actionRemindCompany($id)
    {
        $courtagerequest                     = $this->loadModel($id);
        $courtagerequest->reminders          = $courtagerequest->reminders + 1;
        $courtagerequest->last_reminder_date = ViewHelper::getDate(true);
        $courtagerequest->save();
        CourtageRequestMailFactory::new($courtagerequest)
                                  ->erinnerungAnGesellschaft()
                                  ->markAsReminder((int) $courtagerequest->reminders)
                                  ->send();

        Yii::app()->user->setFlash('success', 'Es wurde eine Erinnerungsmail an die Gesellschaft verschickt');
        $_POST = [];
        $this->redirect(['courtageRequest/update', 'id' => $courtagerequest->id]);
    }

    /**
     * Storniert eine CourtageRequest bzw. bricht dieses ab, falls diese noch nicht
     * gesendet wurde
     *
     * @param type $id
     *                 ID der CourtageRequest, die storniert bzw abgebrochen werden soll
     */
    public function actionCancelCourtageRequest($id)
    {
        $courtagerequest = $this->loadModel($id);
        $this->cancelCourtageRequest($courtagerequest);

        $_POST                = [];
        $systemuserController = new SystemuserController('systemuser');
        $systemuserController->actionUpdate($courtagerequest->user_id, 'Status-Courtagezusage');
    }

    /**
     * Sendet eine ErinnerungsMail an den Makler
     *
     * @param type $id
     *                 ID der CourtageRequest, an die der Makler erinnert werden soll
     */
    public function actionRemindBroker($id): void
    {
        $courtagerequest = $this->loadModel($id);

        if ($courtagerequest->remindBroker()) {
            Yii::app()->user->setFlash('success', 'Es wurde eine Erinnerungsmail an den Makler verschickt');
        } else {
            Yii::app()->user->setFlash('error', 'Die Erinnerungsmail konnte nicht verschickt werden');
        }

        $_POST = [];
        $this->redirect(['courtageRequest/update', 'id' => $courtagerequest->id]);
    }

    /**
     * Verarbeitet das POST Array der CourtageAnfrage-View
     *
     * @param null|mixed $id
     * @param mixed      $forced
     */
    public function actionSendCourtageRequest($id = null, $forced = false)
    {
        $successcounter  = 0;
        $remindercounter = 0;
        $failcounter     = 0;
        if (null === $id) {
            $data = $_POST;
            if (isset($data['CourtageRequest'])) {
                $visit  = $data['CourtageRequest']['Configuration']['visit'];
                $date   = $data['CourtageRequest']['Configuration']['date'];
                $userID = $data['CourtageRequest']['Configuration']['user_id'];

                if (isset($data['CourtageRequest']['Companys'])) {
                    foreach ($data['CourtageRequest']['Companys'] as $key => $value) {
                        if (strpos($key, 'company-') !== false) {
                            $key             = str_replace('company-', '', $key);
                            $courtagerequest = $this->createCourtageRequest($key, $userID, $visit, $date);
                            if (null !== $courtagerequest) {
                                $status = $this->sendCourtageRequest($courtagerequest, false, false, true);
                                switch ($status) {
                                    case self::$status_sent:
                                        $successcounter++;
                                        break;
                                    case self::$status_notsent:
                                        $failcounter++;
                                        break;
                                    case self::$status_reminder_sent:
                                        $remindercounter++;
                                        break;
                                    default:
                                        true;
                                }
                            }
                        }
                    }
                }
            }
        } else {
            $courtagerequest = $this->loadModel($id);
            $userID          = $courtagerequest->user_id;
            $status          = $this->sendCourtageRequest($courtagerequest, $forced, false);
            switch ($status) {
                case self::$status_sent:
                    $successcounter++;
                    break;
                case self::$status_notsent:
                    $failcounter++;
                    break;
                case self::$status_reminder_sent:
                    $remindercounter++;
                    break;
                default:
                    true;
            }
        }
        $this->setFlash($successcounter, $remindercounter, $failcounter);
        $this->redirect(['Systemuser/update/' . $userID . '?activetab=Status-Courtagezusage']);
    }

    /**
     * Manages all courtagerequests.
     */
    public function actionAdmin()
    {
        $courtagerequest = new CourtageRequest('search');
        $courtagerequest->unsetAttributes();  // clear any default values
        if (isset($_GET['CourtageRequest'])) {
            $courtagerequest->attributes = $_GET['CourtageRequest'];
        }

        $this->render('admin', [
            'courtagerequest' => $courtagerequest,
        ]);
    }

    /**
     * Löscht das CourtageRequest-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *                 $ID des zu löschenden CourtageRequest-Models
     *
     * @return type true || false
     *              true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     * Löscht eine vorhandene Courtageanfrage und erstellt diese neu.
     * Wird aus dem Maklerbetreuer aufgerufen, deswegen der Redirect am ende.
     *
     * @param $id
     *
     * @throws CDbException
     * @throws CHttpException
     */
    public function actionRecreate($id)
    {
        $model = $this->loadModel($id);

        if ($model === null) {
            JSendResponse::fail()->respond(404);

            return;
        }

        if (!UserProvider::forUser(currentUser())->load($model->user_id)) {
            JSendResponse::fail()->respond(403);

            return;
        }

        $data['CourtageRequest'] = [
            'user_id'                    => $model->user_id,
            'insurance_company_id'       => $model->insurance_company_id,
            'courtage_request_status_id' => CourtageRequestStatus::NOT_SET,
            'courtage_commitment_date'   => Carbon::now()->toDateString(),
            'comment'                    => 'Der Makler wurde bereits zuvor abgelehnt.',
            'visit'                      => 0,
        ];

        $model->delete();

        $newRequest = $this->create($data);

        if ($newRequest == null) {
            JSendResponse::fail()->respond(500);

            return;
        }

        $this->sendCourtageRequest($newRequest, false, false, true);
        $this->redirect(Yii::app()->getRequest()->getUrlReferrer());
    }
}
