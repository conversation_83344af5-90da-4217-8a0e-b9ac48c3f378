<?php

/**
 * Created by PhpStorm.
 * User: alex
 * Date: 03.11.16
 * Time: 16:48
 */
class CourtageSyncController extends AdminController
{
    public function actionSync($date = null, $hanseaticOnly = false)
    {
        $date     = !empty($date) ? UtilHelper::parseToDBDate($date) : date('Y-m-d');
        $builder  = new ContributionSyncCriteriaBuilder($date);
        $criteria = $builder->getCriteria();
        if ($hanseaticOnly) {
            $criteria->addInCondition('t.user_id', CHtml::listData(User::model()->findAllByAttributes(['agency_id' => 3]), 'id', 'id'));
        }

        $temp          = CourtageData::model()->findAll($criteria);
        $courtageDatas = [];
        foreach ($temp as $item) {
            $courtageDatas[$item->id] = $item;
        }

        $syncer = new ContributionSyncer($courtageDatas, $date);
        $syncer->sync(true);

        $message = '';
        $data    = [
            'Aktualisierte Courtagen'       => $syncer->getSyncedIds(),
            'Nicht aktualisierte Courtagen' => $syncer->getNotSyncedIds(),
            'Fehler'                        => $syncer->getErrors(),
        ];

        //Ist eh nur zum testen für Admins
        $message .= '<div class="row">';
        foreach ($data as $label => $ids) {
            if (!empty($ids)) {
                $message .= '<div class="span6">';
                $message .= '<b>' . $label . ': </b><br/>';
                $message .= '<ul>';
                foreach ($ids as $syncedId) {
                    $message .= '<li>';
                    $message .= CHtml::link(CHtml::encode($courtageDatas[$syncedId]->getProcedureNumber()), ['/courtage/admin', 'courtage_data_id' => $syncedId], ['target' => '_blank']);
                    $message .= '</li>';
                }
                $message .= '</ul>';
                $message .= '</div>';
            }
        }
        $message .= '</div>';
        Yii::app()->user->setFlash('success', $message);
        $this->redirect(['courtage/admin']);
    }
}
