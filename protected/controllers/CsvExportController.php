<?php

/**
 * Class CsvExportController
 */
class CsvExportController extends GlobalRightController
{
    /**
     * @var CsvExport
     */
    private $_csvExport;
    /**
     * @var null
     */
    private $_fileHandler;
    /**
     * @var array
     */
    private $_currentRow  = [];

    public function actionExport()
    {
        $this->beginCsvExport();
        $this->export();
        fclose($this->_fileHandler);
    }

    private function beginCsvExport()
    {
        $fileName = 'DEMV_Bestandsdaten_' . date('d-m-Y') . '.csv';

        if (Yii::app()->request->isAjaxRequest) {
            $this->_fileHandler = fopen($this->getUniquePath(), 'w+');
            $this->savePath($this->getUniquePath());
        } else {
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Content-Description: File Transfer');
            header('Content-type: text/csv; charset=ISO-8859-1');
            header('Content-Disposition: attachment; filename=' . $fileName);
            header('Expires: 0');
            header('Pragma: public');

            flush();
            $this->_fileHandler = @fopen('php://output', 'w+');
            $this->_csvExport   = new CsvExport($_POST);
        }
        ob_get_clean();
    }

    /**
     * @param string $path
     */
    private function savePath(string $path)
    {
        Yii::app()->session['CsvExport_path'] = $path;
    }

    /**
     * @return string
     */
    private function getUniquePath(): string
    {
        $dir = 'uploads/export';
        if (!is_dir($dir)) {
            mkdir($dir);
        }

        return $dir . '/bestandsdaten-export-' . Yii::app()->user->getId() . '-' . date('d-m-Y') . '.csv';
    }

    private function export()
    {
        fputcsv($this->_fileHandler, $this->_csvExport->getHeaderRow(), ';');
        Yii::app()->session->close();
        while (!$this->finish()) {
            fputcsv($this->_fileHandler, $this->_currentRow, ';');
        }
    }

    /**
     * @return bool
     *
     */
    private function finish(): bool
    {
        $this->_currentRow = $this->_csvExport->getNextRow();

        return !$this->_currentRow;
    }

    /**
     * @throws Exception
     */
    public function actionGetExport()
    {
        $path = $this->getPath();
        if (!file_exists($path)) {
            throw new Exception('File not found');
        }
        $fileName = 'DEMV_Bestandsdaten_' . date('d-m-Y') . '.csv';
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Content-Description: File Transfer');
        header('Content-type: text/csv; charset=ISO-8859-1');
        header('Content-Disposition: attachment; filename=' . $fileName);
        header('Expires: 0');
        header('Pragma: public');
        echo file_get_contents($path);
        unlink($path);
    }

    /**
     * @return string|null
     */
    private function getPath()
    {
        return Yii::app()->getSession()->get('CsvExport_path', null);
    }
}
