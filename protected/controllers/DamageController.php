<?php

use Components\Model\UserProfileDamage;

Yii::import('kundenakte.components.traits.*');

/**
 * Class DamageController
 */
class DamageController extends RightController implements KundenakteControllerInterface
{
    use KundenakteControllerTrait;

    public $prefix          = 'Damage';
    public $excludedActions = [
        'GetContractDataAjax',
        'SetFinish',
        'ExcelExport',
    ];

    public function actionUpdate($id)
    {
        $damage = $this->loadModel($id);

        $this->setClient($damage->client);
        $this->setTab(KundenakteTabs::SCHAEDEN);

        $request = Yii::app()->request->getParam('Damage');
        if (!empty($request)) {
            $damage->attributes = $request;
            if (empty($request['damage_done'])) {
                $damage->damage_done = 0;
            }
            $damage->parentId = 0;
        }
        if (!empty($_POST['contract_dropdown'])) {
            $damage->setMainContractData($_POST['contract_dropdown']);
        }
        if ($this->checkUpdateData($damage) && $damage->save()) {
            $this->saveContractFields($damage, Yii::app()->request->getParam('ContractFields'));
            Yii::app()->user->setFlash('success', 'Der Schaden wurde erfolgreich bearbeitet.');
            $this->redirect(Kundenakte::urlTo($damage->client_id, KundenakteTabs::SCHAEDEN));
        }
    }

    /**
     * Lädt das Damage-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des Damage-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $damage = Damage::model()->personalAccess()->findByAttributes(['id' => $id]);

        return $damage;
    }

    private function checkUpdateData($damage)
    {
        if (empty(Yii::app()->request->getParam('Damage')) || !$damage->validate()) {
            $action = [
                'name'          => 'Update',
                'parameterName' => 'id',
                'value'         => $damage->id
            ];
            $this->render('//kundenakte/vertraege/vertrag/contractForm',
                          [
                              'basicFields'    => [],
                              'optionalFields' => [],
                              'view'           => false,
                              'model'          => $damage,
                              'action'         => $action,
                              'prefix'         => $this->prefix,
                          ]);

            return false;
        }

        return true;
    }

    private function saveContractFields(&$damage, $cfs)
    {
        if (is_array($cfs)) {
            foreach ($cfs as $id => $value) {
                $model = ContractField::model()->findByPk($id);
                $model->saveValue($damage->id, $value);
            }
        }
    }

    public function actionGetContractDataAjax()
    {
        $contractId = Yii::app()->request->getParam('contract_id');
        if (!empty($contractId)) {
            $contract = Contracts::model()->personalAccess()->findByPk($contractId);
            $data     = [
                'contract_nr'          => $contract->contract_nr,
                'insurance_company_id' => $contract->company_id,
                'product'              => $contract->getSpecificProductComboId()
            ];
            echo json_encode($data);
        }
    }

    public function actionDelete($id)
    {
        $damage = $this->loadModel($id);
        if (!empty($damage)) {
            $damage->delete();
        }
    }

    public function getContractListData($damage)
    {
        return ContractDropdownData::forClient($damage->client_id)->showProductname()->getData();
    }

    public function actionAdmin()
    {
        $this->disableClientLayout();
        $damage              = new Damage();
        $damage->damage_done = 0;

        $model = UserProfileDamage::model();
        if (!empty($model->getReferencedUsers())) {
            $damage->broker = array_column($model->getAllUsers(), 'id');
        }

        if (isset($_GET['Damage'])) {
            $damage->attributes = $_GET['Damage'];
            if (!empty($_GET['Damage']['broker'])) {
                if (!is_array($_GET['Damage']['broker'])) {
                    $damage->broker = explode(',', $_GET['Damage']['broker']);
                } elseif (is_array($_GET['Damage']['broker'])) {
                    $damage->broker = $_GET['Damage']['broker'];
                }
            }
            if (!empty($_GET['Damage']['insuranceCompanies'])) {
                $damage->insuranceCompanies = $_GET['Damage']['insuranceCompanies'];
            }
            if (!empty($_GET['Damage']['product_combo_ids'])) {
                $damage->product_combo_ids = $_GET['Damage']['product_combo_ids'];
                $damage->gdv_ids           = $this->getGdvFromProducts($damage->product_combo_ids);
            }
            if (!empty($_GET['Damage']['contract_nr'])) {
                $damage->contract_nr = $_GET['Damage']['contract_nr'];
            }
            if (!empty($_GET['Damage']['damage_nr'])) {
                $damage->damage_nr = $_GET['Damage']['damage_nr'];
            }
            if (!empty($_GET['Damage']['damage_date_from'])) {
                $damage->damage_date_from = $_GET['Damage']['damage_date_from'];
            }
            if (!empty($_GET['Damage']['damage_date_until'])) {
                $damage->damage_date_until = $_GET['Damage']['damage_date_until'];
            }
            if (!empty($_GET['Damage']['clients'])) {
                if (!is_array($_GET['Damage']['clients'])) {
                    $damage->clients = explode(',', $_GET['Damage']['clients']);
                } elseif (is_array($_GET['Damage']['clients'])) {
                    $damage->clients = $_GET['Damage']['clients'];
                }
            }
            if (in_array($_GET['Damage']['damage_done'], [0, 1, 2])) {
                if ($_GET['Damage']['damage_done'] == 2) {
                    $damage->damage_done = [0, 1];
                } else {
                    $damage->damage_done = $_GET['Damage']['damage_done'];
                }
            }
        }

        $this->render('//kundenakte/schaeden/search', ['damage' => $damage]);
    }

    /**
     * Return ist ein array mit den gdv-ids zu den übergebenen Produktsparten.
     *
     * @return type
     */
    public function getGdvFromProducts($product_ids)
    {
        $gdv_ids                                     = [];
        $gdvProductAssignmentProductComboAssignments = GdvProductAssignmentProductComboAssignment::model()->findAllByAttributes([
                                                                                                                                    'product_combo_id' => $product_ids
                                                                                                                                ]);
        foreach ($gdvProductAssignmentProductComboAssignments as $gdvProductAssignmentProductComboAssignment) {
            $gdv_ids[$gdvProductAssignmentProductComboAssignment->gdv_product_id] = $gdvProductAssignmentProductComboAssignment->gdv_product_id;
        }

        return $gdv_ids;
    }

    public function actionSetFinish()
    {
        $id                  = $_POST['id'];
        $damage              = $this->loadModel($id);
        $damage->damage_done = 1;
        $damage->save();
        Yii::app()->end();
    }

    public function actionExcelExport()
    {
        $damage       = new Damage();
        $dataProvider = ExportSessionManager::getDataProvider($damage);
        $data         = $_POST['Damage_Excel'];
        $names        = array_keys(
            array_filter(
                $data,
                static function (string $value): bool {
                    return $value === '1';
                }
            )
        );
        $damage->correctResult($dataProvider);

        $table = new ExcelGenerator($dataProvider, $names, $damage);
        $table->generateTable();
        Yii::app()->end();
    }

    public function actionCreate($id)
    {
        $damage = new Damage('search');

        $client = ClientProvider::forUser(currentUser())->load($id);

        $this->setClient($client);
        $this->setTab(KundenakteTabs::SCHAEDEN);

        if (!empty($_GET['contract_id'])) {
            $contract = ContractProvider::forUser(currentUser())->load($_GET['contract_id']);
            $damage->setMainContractData($contract->id);
        }

        $contractData = Yii::app()->request->getParam('Damage');

        if (!empty($contractData)) {
            $damage->attributes     = Yii::app()->request->getParam('Damage');
            $damage->import_type_id = 5;
            $damage->damage         = 1;

            if (isset($contractData['damage_done'])) {
                $damage->damage_done = 1;
            } else {
                $damage->damage_done = 0;
            }

            if (!empty($_POST['contract_dropdown'])) {
                $damage->setMainContractData($_POST['contract_dropdown']);
            }
        }

        $damage->user_id   = Yii::app()->user->getId();
        $damage->client_id = $id;

        if ($this->checkCreateData($damage) && $damage->save()) {
            $this->saveContractFields($damage, Yii::app()->request->getParam('ContractFields'));
            $this->redirect(Kundenakte::urlTo($damage->client_id, KundenakteTabs::SCHAEDEN));
        }
    }

    private function checkCreateData($damage)
    {
        if ((empty(Yii::app()->request->getParam('Damage')) || !$damage->validate())) {
            $action = [
                'name'          => 'create',
                'parameterName' => 'id',
                'value'         => $damage->client_id
            ];
            $this->render('//kundenakte/vertraege/vertrag/contractForm',
                          [
                              'basicFields'    => [],
                              'optionalFields' => [],
                              'view'           => false,
                              'model'          => $damage,
                              'action'         => $action,
                              'prefix'         => $this->prefix,
                          ]);

            return false;
        }

        return true;
    }

    public function actionView($id)
    {
        $model = Damage::model()->findByPk($id);

        $this->setClient($model->client);
        $this->setTab(KundenakteTabs::SCHAEDEN);

        $contractFields = $model->getContractFields(true, true, false);
        $action         = [
            'name'          => 'Show',
            'parameterName' => 'id',
            'value'         => $id
        ];
        $this->render('//kundenakte/vertraege/vertrag/contractForm',
                      [
                          'basicFields'    => $contractFields['basic'],
                          'optionalFields' => $contractFields['optional'],
                          'view'           => true,
                          'model'          => $model,
                          'action'         => $action,
                          'prefix'         => $this->prefix,
                      ]);
    }
}
