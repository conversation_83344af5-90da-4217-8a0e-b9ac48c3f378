<?php

class DateTerminateContractController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     *  Diese Methode wird aufgerufen wenn ein neues DateTerminateContract-Model erstellt werden soll.
     *  Die Methode öffnet das DateTerminateContract-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if ($dateTermianteContract = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $dateTermianteContract;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das DateTerminateContract -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des DateTerminateContract -Models
     */
    private function getCreateFormularData()
    {
        $dateTerminateContract = new DateTerminateContract();

        if (!$this->validData($_POST)) {
            $this->render('create', ['dateTerminateContract' => $dateTerminateContract]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model DateTerminateContract handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['DateTerminateContract']);
    }

    /**
     * Diese Methode erstellt ein neues DateTerminateContract -Model
     *
     * @param type $data
     *          Die DateTerminateContract -Daten als Array
     *
     * @return $model
     *          Das erstellte DateTerminateContract -Exemplar
     */
    public function create($data)
    {
        $dateTerminateContract             = new DateTerminateContract();
        $dateTerminateContract->attributes = $data['DateTerminateContract'];

        if ($dateTerminateContract->save()) {
            return $dateTerminateContract;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein DateTerminateContract -Model aktualisiert werden soll.
     *  Die Methode öffnet das DateTerminateContract -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden DateTerminateContract -Models
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($model = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $model;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das DateTerminateContract-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des DateTerminateContract-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = DateTerminateContract::model()->findByPk($id);

        return $model;
    }

    /**
     *
     * Öffnet das DateTerminateContract-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden DateTerminateContract-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('update', ['model' => $model]);
        }

        $formulardata                                = $_POST;
        $formulardata['DateTerminateContract']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein DateTerminateContract-Exemplar
     *
     * @param type $data
     *          Die DateTerminateContract-Daten als Array
     *
     * @return $model
     *          Das aktualisierte DateTerminateContract-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $dateTerminateContract             = $this->loadModel($data['DateTerminateContract']['id']);
            $dateTerminateContract->attributes = $data['DateTerminateContract'];
            if ($dateTerminateContract->save()) {
                return $dateTerminateContract;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $dateTerminateContract = new DateTerminateContract('search');

        $this->render('//stammdaten/menu/ablaufendeVertraegeVerwaltung/admin', ['dateTerminateContract' => $dateTerminateContract, ]);
    }

    /**
     * Löscht das DateTerminateContract-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden DateTerminateContract-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('DateTerminateContract');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionQuickUpdate()
    {
        $data = [
            'DateTerminateContract' => [
                'id'   => Yii::app()->request->getParam('pk'),
                'days' => Yii::app()->request->getParam('value')
            ],
            'ProductCombo'          => [
                'id'   => Yii::app()->request->getParam('pk'),
                'days' => Yii::app()->request->getParam('value'),
            ]
        ];

        if ($data['ProductCombo']['days'] > 0) {
            $productComboController = new ProductComboController('productCombo');
            $productComboController->update($data);
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param DateTerminateContract $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'date-terminate-contract-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
