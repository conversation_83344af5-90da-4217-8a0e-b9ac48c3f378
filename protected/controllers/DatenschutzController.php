<?php

use Components\Access\PublicAccessibleInterface;

class DatenschutzController extends AllowAllController implements PublicAccessibleInterface
{
    public function init(): void
    {
        parent::init();

        $this->layout = '//layouts/plain';
    }

    public function actionIndex(): void
    {
        $this->render('//datenschutz/index');
    }

    public function getPublicActions(): array
    {
        return [
            'index'
        ];
    }
}
