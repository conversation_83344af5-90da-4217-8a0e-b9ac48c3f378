<?php

/** @noinspection PhpUnused */

use Auth\Services;
use Components\Authentication\OAuth\OAuthAccessible;
use Components\Pdfs\Kuendigung\DismissalContract;
use Components\Pdfs\Kuendigung\DismissalGenerator;
use Components\Pdfs\Kuendigung\DismissalRecipient;
use Components\Pdfs\Kuendigung\DismissalSender;
use Demv\JSend\JSendResponse;

class DismissalController extends AllowAllController implements OAuthAccessible
{
    public function actionGeneratePdf(): void
    {
        $contract  = $this->tryGetContract();
        $sender    = $this->tryGetSender();
        $recipient = $this->tryGetRecipient();

        JSendResponse::success(['content' => base64_encode($this->generatePdf($sender, $recipient, $contract))])->respond();
    }

    /**
     * @param string $companyId
     */
    public function actionGeneratePdfForCompany(string $companyId): void
    {
        $contract  = $this->tryGetContract();
        $recipient = DismissalRecipient::fromInsuranceCompany($this->tryGetCompany($companyId));
        $sender    = $this->tryGetSender();

        JSendResponse::success(['content' => base64_encode($this->generatePdf($sender, $recipient, $contract))])->respond();
    }

    /**
     * @param string $userId
     *
     * @throws CHttpException
     */
    public function actionGeneratePdfAsBroker(string $userId): void
    {
        $contract  = $this->tryGetContract();
        $sender    = DismissalSender::fromUser(UserProvider::forUser(currentUser())->load($userId));
        $recipient = $this->tryGetRecipient();

        JSendResponse::success(['content' => base64_encode($this->generatePdf($sender, $recipient, $contract))])->respond();
    }

    /**
     * @param string $companyId
     * @param string $userId
     *
     * @throws CHttpException
     */
    public function actionGeneratePdfForCompanyAsBroker(string $companyId, string $userId): void
    {
        $contract  = $this->tryGetContract();
        $recipient = DismissalRecipient::fromInsuranceCompany($this->tryGetCompany($companyId));
        $sender    = DismissalSender::fromUser(UserProvider::forUser(currentUser())->load($userId));

        JSendResponse::success(['content' => base64_encode($this->generatePdf($sender, $recipient, $contract))])->respond();
    }

    /**
     * @param string $companyId
     *
     * @return InsuranceCompany
     */
    private function tryGetCompany(string $companyId): InsuranceCompany
    {
        $company = InsuranceCompany::model()->findByPk($companyId);
        if ($company === null) {
            JSendResponse::error("no company with id $companyId found")->respond();
        }

        return $company;
    }

    /**
     * @return DismissalSender
     */
    private function tryGetSender(): DismissalSender
    {
        $sender = new DismissalSender();

        $sender->setName($this->tryGetParam('senderName'));
        $sender->setStreet($this->tryGetParam('senderStreet'));
        $sender->setHouseNr($this->tryGetParam('senderHouseNr'));
        $sender->setZip($this->tryGetParam('senderZip'));
        $sender->setCity($this->tryGetParam('senderCity'));

        return $sender;
    }

    /**
     * @return DismissalRecipient
     */
    private function tryGetRecipient(): DismissalRecipient
    {
        $recipient = new DismissalRecipient();

        $recipient->setName($this->tryGetParam('recipientName'));
        $recipient->setStreet($this->tryGetParam('recipientStreet'));
        $recipient->setHouseNr($this->tryGetParam('recipientHouseNr'));
        $recipient->setZip($this->tryGetParam('recipientZip'));
        $recipient->setCity($this->tryGetParam('recipientCity'));

        return $recipient;
    }

    /**
     * @return DismissalContract
     */
    private function tryGetContract(): DismissalContract
    {
        $contractNr = $this->tryGetParam('contractNr');
        $dueDate    = $this->tryGetParam('dueDate');

        return new DismissalContract($contractNr, $dueDate);
    }

    /**
     * @param string $key
     *
     * @return string
     */
    private function tryGetParam(string $key): string
    {
        $val = Yii::app()->request->getParam($key);
        if (empty($val)) {
            JSendResponse::error("param $key is required")->respond();
        }

        return $val;
    }

    /**
     * @param DismissalSender    $sender
     * @param DismissalRecipient $recipient
     * @param DismissalContract  $contract
     *
     * @return string
     */
    private function generatePdf(DismissalSender $sender, DismissalRecipient $recipient, DismissalContract $contract): string
    {
        $dismissalGen = new DismissalGenerator();
        $dismissalGen->Open();
        $dismissalGen->generate($sender, $recipient, $contract);
        $dismissalGen->Close();

        return $dismissalGen->Output('', 'S');
    }

    public static function getAllowedServices(): array
    {
        return Services::DEMV_INTERNAL_SERVICES;
    }
}
