<?php

declare(strict_types=1);

class DocumentTypeController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Displays a particular documentType.
     *
     * @param int $id the ID of the documentType to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'documentType' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das DocumentType-Exemplar mit der übergebenen ID
     *
     * @param int|string|null $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des DocumentType-Models mit der übergebenen ID falls dieses existiert, sonst null
     */
    public function loadModel($id)
    {
        return DocumentType::model()->findByPk($id);
    }

    /**
     * @param int $id
     *
     * @return DocumentTypeTag|null
     */
    public function loadTagModel(int $id): ?DocumentTypeTag
    {
        return DocumentTypeTag::model()->findByPk($id);
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues DocumentType-Model erstellt werden soll.
     *  Die Methode öffnet das DocumentType-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (!isset($data['create']) || !$this->validData($data)) {
            return;
        }

        if ($this->create($data) != null) {
            $this->redirect('admin');
        }
    }

    /**
     *  Öffnet das DocumentType -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return array|null
     *          $Data-Array des DocumentType -Models
     */
    private function getCreateFormularData(): ?array
    {
        $documentType = new DocumentType();

        if ($this->validData($_POST)) {
            return $_POST;
        }

        $this->render(
            '//stammdaten/verwaltung/dokumenttypen/create',
            ['documentType' => $documentType, 'prefix' => '']
        );

        return null;
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model DocumentType handelt
     *
     * @param array $data
     *
     * @return bool true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData(array $data): bool
    {
        return isset($data['DocumentType']) &&
               isset($data['DocumentType']['name']) &&
               $data['DocumentType']['name'] != '' &&
               (isset($data['DocumentType']['client'])
                || isset($data['DocumentType']['broker'])
                || isset($data['DocumentType']['company']));
    }

    /**
     * Diese Methode erstellt ein neues DocumentType -Model
     *
     * @param array $data
     *          Die DocumentType -Daten als Array
     *
     * @return $documentType
     *          Das erstellte DocumentType -Exemplar
     */
    public function create(array $data): ?DocumentType
    {
        if (!$this->validData($data)) {
            return null;
        }

        $documentType             = new DocumentType();
        $documentType->attributes = $data['DocumentType'];

        if ($documentType->save()) {
            return $documentType;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein DocumentType -Model aktualisiert werden soll.
     *  Die Methode öffnet das DocumentType -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden DocumentType -Models
     */
    public function actionUpdate($id)
    {
        $documentType = $this->loadModel($id);
        if ($documentType != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update']) && $this->validData($data)) {
                if ($documentType = $this->update($data) != null) {
                    $this->actionAdmin();

                    return $documentType;
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Öffnet das DocumentType-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden DocumentType-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     */
    private function getUpdateFormularData($id)
    {
        $documentType = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render(
                '//stammdaten/verwaltung/dokumenttypen/update',
                ['documentType' => $documentType, 'prefix' => '']
            );
        }

        $formulardata                       = $_POST;
        $formulardata['DocumentType']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein DocumentType-Exemplar
     *
     * @param array $data
     *          Die DocumentType-Daten als Array
     *
     * @return $documentType
     *          Das aktualisierte DocumentType-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update(array $data): ?DocumentType
    {
        if ($this->validData($data)) {
            $documentType             = $this->loadModel($data['DocumentType']['id']);
            $documentType->attributes = $data['DocumentType'];
            if ($documentType->save()) {
                return $documentType;
            }
        }

        return null;
    }

    /**
     * Manages all documentTypes.
     */
    public function actionAdmin()
    {
        $documentType = new DocumentType('search');
        $documentType->unsetAttributes(); // clear any default values

        if (isset($_GET['DocumentType'])) {
            $documentType->attributes = $_GET['DocumentType'];
        }

        $assetsPath = Yii::app()->assetManager->publish(Yii::getPathOfAlias('application.views.stammdaten.verwaltung.assets'));
        Yii::app()->clientScript->registerCssFile($assetsPath . '/document_type.css');

        $this->render('//stammdaten/verwaltung/dokumenttypen/admin', [
            'documentType' => $documentType,
        ]);
    }

    /**
     * @param int $id
     *
     * @return void
     */
    public function actionTagCreate(int $id): void
    {
        $documentType = DocumentType::model()->with(['documentTypeTags'])->findByPk($id);

        $this->render('//stammdaten/verwaltung/dokumenttypen/documentTypeTags/create', [
            'documentType' => $documentType,
        ]);
    }

    /**
     * Löscht das DocumentType-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden DocumentType-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     * @throws CDbException
     */
    public function actionDelete($id): void
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    public function actionTagAdmin(): void
    {
        $documentTypeTag = new DocumentTypeTag('search');
        $documentTypeTag->unsetAttributes(); // clear any default values

        if (isset($_GET['DocumentTypeTag'])) {
            $documentTypeTag->attributes = $_GET['DocumentTypeTag'];
        }

        $this->render('//stammdaten/verwaltung/dokumenttypen/documentTypeTags/admin', [
            'documentTypeTag' => $documentTypeTag,
        ]);
    }

    /**
     * @param int $id
     *
     * @return void
     */
    public function actionTagStore(int $id): void
    {
        if (!Yii::app()->request->isPostRequest) {
            $this->redirect(['admin']);
        }

        $tag = Yii::app()->request->getParam('tag');

        if ($tag === null || $tag === '') {
            Yii::app()->user->setFlash('error', 'Kein Schlüsselwort in der Anfrage gefunden');
            $this->redirect(['tagCreate', 'id' => $id]);
        }

        if ($this->documentTypeTagExists($tag)) {
            $documentTypeDuplicate = DocumentTypeTag::model()
                                                    ->with(['documentType'])
                                                    ->findByAttributes(['tag' => $tag]);

            Yii::app()->user->setFlash(
                'error',
                "Das Schlüsselwort \"{$tag}\" existierd schon in <strong>{$documentTypeDuplicate->documentType->name}</strong> (ID: {$documentTypeDuplicate->documentType->id})"
            );
            $this->redirect(['tagCreate', 'id' => $id]);
        }

        $documentType = $this->loadModel($id);

        if ($documentType === null) {
            Yii::app()->user->setFlash('error', 'Dokumenttyp konnte nicht gefunden werden');
            $this->redirect(['tagCreate', 'id' => $id]);
        }

        $documentTypeTag                   = new DocumentTypeTag();
        $documentTypeTag->tag              = trim($tag);
        $documentTypeTag->document_type_id = $documentType->id;

        if (!$documentTypeTag->save()) {
            Yii::app()->user->setFlash('error', 'Schlüsselwort konnte nicht gespeichert werden');
            $this->redirect(['tagCreate', 'id' => $id]);
        }

        Yii::app()->user->setFlash('success', 'Neues Schlüsselwort zugegeben');
        $this->redirect(['admin']);
    }

    /**
     * @param string $tag
     *
     * @return bool
     */
    private function documentTypeTagExists(string $tag): bool
    {
        $criteria = new CDbCriteria();
        $criteria->compare('tag', $tag);

        return DocumentTypeTag::model()->exists($criteria);
    }

    /**
     * @param int $id
     *
     * @return void
     *
     * @throws CDbException
     */
    public function actionTagDelete(int $id): void
    {
        $this->loadTagModel($id)->delete()
            ? Yii::app()->user->setFlash('success', 'Schlüsselwort entfernt')
            : Yii::app()->user->setFlash('error', 'Schlüsselwort konnte nicht entfernt werden');

        $this->redirect(['admin']);
    }

    /**
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all documentTypes.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('DocumentType');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionAjaxUpdateDocumentType()
    {
        $id                       = $_POST['id'];
        $attribute                = $_POST['attribute'];
        $value                    = $_POST['value'];
        $documenttype             = DocumentType::model()->findByPk($id);
        $documenttype->$attribute = $value == 'true' ? 1 : 0;
        $success                  = (int) $documenttype->save();
        echo json_encode(['success' => $success]);
        Yii::app()->end();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param DocumentType $documentType the documentType to be validated
     */
    protected function performAjaxValidation($documentType)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'document-type-form') {
            echo CActiveForm::validate($documentType);
            Yii::app()->end();
        }
    }
}
