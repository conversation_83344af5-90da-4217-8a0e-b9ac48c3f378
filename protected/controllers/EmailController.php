<?php

use Auth\Services;
use Business\Mail\Footer\DefaultMailFooter;
use Business\Mail\Footer\Footer;
use Components\Authentication\OAuth\OAuthAccessible;
use Components\Mail\Mailer\MailerFactory;
use Components\Mail\Mailer\MailerInterface;
use Demv\JSend\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class EmailController
 */
final class EmailController extends AllowAllController implements OAuthAccessible
{
    private const REQUIRED_MAIL_PARAMS = [
        'subject',
        'message',
        'recipient',
    ];

    public function actionSend()
    {
        $body = json_decode(Yii::app()->request->getRawBody(), true);

        $userId = $body['userId'] ?? null;

        if ($this->cannotSendMail($body)) {
            ResponseFactory::instance()->error([
                'message' => 'Die uebermittelten Parameter waren nicht valide oder vorhanden'
            ], 420)->respond();
        }

        $prioritizeSystemSmtp = $body['prioritizeSystemSmtp'] ?? false;

        $mailer          = $this->createMailer($prioritizeSystemSmtp);
        $replyto         = $body['replyto'] ?? null;
        $attachmentFiles = $body['attachmentFiles'] ?? [];
        $attachmentUrls  = $body['attachmentUrls'] ?? [];
        $cc              = $body['cc'] ?? null;
        $bcc             = $body['bcc'] ?? null;
        $message         = $body['message'] ?? '';
        $smtp            = $body['smtp'] ?? 0;
        $footer          = $body['footer'] ?? 1;
        $subject         = $body['subject'] ?? '';
        $recipient       = $body['recipient'] ?? null;

        if (!empty($replyto)) {
            $mailer->setReplyTo($replyto);
        } elseif ($smtp) {
            $mailer->setReplyTo($smtpUser->contact->email ?? currentUser()->email, '');
        }

        $message = $this->appendFooter($footer, $message, $userId);

        $mailer->setView('default' . DIRECTORY_SEPARATOR . 'empty', ['content' => $message]);
        $mailer->setSubject($subject);

        $this->setCC($cc, $mailer);
        $this->setBCC($bcc, $mailer);
        $this->addAttachments($attachmentFiles, $mailer);

        $downloadedAttachments = [];

        if (!empty($attachmentUrls)) {
            $downloadedAttachments = $this->downloadAttachments($attachmentUrls);
            $this->addAttachments($downloadedAttachments, $mailer);
        }

        if (!$mailer->send($recipient)) {
            ResponseFactory::instance()->error(
                ['message' => "An error occurred while sending a mail to $recipient with subject '$subject'."],
                Response::HTTP_INTERNAL_SERVER_ERROR
            )->respond();
        }

        if (!empty($attachmentUrls)) {
            ResponseFactory::instance()->success(['attachmentFileStatus' => $this->downloadedAttachmentStatus($downloadedAttachments)])->respond();
        }

        ResponseFactory::instance()->success([])->respond();
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    private function cannotSendMail(array $params): bool
    {
        $status = false;

        foreach (self::REQUIRED_MAIL_PARAMS as $requiredParam) {
            if (!array_key_exists($requiredParam, $params)) {
                $status = true;
            }
        }

        return $status && (!array_key_exists('userId', $params) || !array_key_exists('from', $params));
    }

    /**
     * @param int      $footer
     * @param string   $message
     * @param int|null $userId
     *
     * @return string
     */
    private function appendFooter(int $footer, string $message, int $userId = null): string
    {
        if ($footer === 1) {
            $message .= '<br /><br />Mit freundlichen Grüssen<br /><br />Ihr DEMV-Team';
            $message .= DefaultMailFooter::getContent();

            return $message;
        }

        $footer = Footer::forCurrentUser();
        if ($userId !== null) {
            $user   = User::model()->findByPk($userId);
            $footer = Footer::forUser($user);
        }

        $message .= $footer->getContent();

        return $message;
    }

    /**
     * @param array $attachments
     *
     * @return array
     */
    private function downloadAttachments(array $attachments): array
    {
        $downloadedAttachments = [];
        foreach ($attachments as $key => $attachment) {
            $downloadedAttachments[$key]['fileContent'] = @file_get_contents($attachment['fileUrl']);
            $downloadedAttachments[$key]['filename']    = $attachment['filename'];
        }

        return $downloadedAttachments;
    }

    /**
     * @param null|string     $cc
     * @param MailerInterface $mailer
     */
    private function setCC(?string $cc, MailerInterface $mailer): void
    {
        if (!empty($cc)) {
            $cc = explode(', ', urldecode($cc));
            $mailer->setCCs($cc);
        }
    }

    /**
     * @param null|string     $bcc
     * @param MailerInterface $mailer
     */
    private function setBCC(?string $bcc, MailerInterface $mailer): void
    {
        if (!empty($bcc)) {
            $bcc = explode(', ', urldecode($bcc));
            $mailer->setBCCs($bcc);
        }
    }

    /**
     * @param array           $attachmentFiles
     * @param MailerInterface $mailer
     */
    private function addAttachments(array $attachmentFiles, MailerInterface $mailer): void
    {
        if (empty($attachmentFiles)) {
            return;
        }

        foreach ($attachmentFiles as $attachmentFile) {
            if (!empty($attachmentFile['fileContent'])) {
                $mailer->setFileAttachments([$attachmentFile['filename'] => base64_decode($attachmentFile['fileContent'])]);
            }
        }
    }

    /**
     * @param array $downloadedAttachments
     *
     * @return array
     */
    private function downloadedAttachmentStatus(array $downloadedAttachments): array
    {
        $staus = [];

        foreach ($downloadedAttachments as $key => $downloadedAttachment) {
            $staus[$key]['filename']   = $downloadedAttachment['filename'];
            $staus[$key]['downloaded'] = !empty($downloadedAttachment['fileContent']);
        }

        return $staus;
    }

    /**
     * @param bool $prioritizeSystemSmtp
     *
     * @return MailerInterface
     * @throws CDbException
     */
    private function createMailer(bool $prioritizeSystemSmtp): MailerInterface
    {
        $mailFactory = MailerFactory::create();

        return $prioritizeSystemSmtp
            ? $mailFactory->useSystemSmtp()
            : $mailFactory->useMaklerOrSystemSmtp(currentUser());
    }

    public static function getAllowedServices(): array
    {
        return Services::DEMV_INTERNAL_SERVICES;
    }
}
