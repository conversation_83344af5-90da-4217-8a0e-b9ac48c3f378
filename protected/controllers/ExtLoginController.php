<?php

use Fragebogen\Services\PersonalisedUrlService;

/**
 * Class ExtLoginController
 */
class ExtLoginController extends CController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    use SideMenuRendererTrait;

    /**
     * Manages all models.
     */
    public $excludedActions = [
        'GenerateCode',
        'Login',
    ];

    /**
     * @throws CHttpException
     */
    public function actionGenerateCode()
    {
        $user             = currentUser();
        $userPermission   = \UserPermission::forUser($user);
        $hasExtLoginRight = $userPermission->hasRight(Rights::EXTERNER_LOGIN);

        if (! $hasExtLoginRight) {
            throw new CHttpException(403, 'Nicht autorisiert');
        }

        $webappBannerUrl    = Yii::app()->createAbsoluteUrl('/images/extlogin/webapp.jpg');
        $webappLoginUrl     = rtrim(env('FINANZMANAGER_URL'), '/') . '/login';
        $webappPreviewCode  = sprintf(
            '<a href="%s" target="_blank"><img src="%s" alt=""></a>',
            $webappLoginUrl,
            $webappBannerUrl
        );
        $fmBannerUrl        = Yii::app()->createAbsoluteUrl('/images/extlogin/playstore.svg');
        $fmPlaystoreUrl     = 'https://play.google.com/store/apps/details?id=de.demv.finanzmanager.app';
        $fmPreviewCode      = sprintf(
            '<a href="%s" target="_blank" rel="noopener"><img alt="Finanzmanager Playstore Badge" src="%s" style="height: 40px; width: 135px;"/></a>',
            $fmPlaystoreUrl,
            $fmBannerUrl,
        );
        $fmIOSBannerUrl     = Yii::app()->createAbsoluteUrl('/images/extlogin/appstore.svg');
        $fmIOSUrl           = 'https://apps.apple.com/app/id6648793330';
        $fmIOSPreviewCode   = sprintf(
            '<a href="%s" target="_blank"><img alt="Finanzmanager Appstore Badge" src="%s" style="height: 40px; width: 120px;"/></a>',
            $fmIOSUrl,
            $fmIOSBannerUrl,
        );
        $fmQRAndUrl         = Yii::app()->createAbsoluteUrl('/images/extlogin/qr_playstore.svg');
        $fmQRAndPreviewCode = sprintf(
            '<img alt="Finanzmanager Playstore QR-Code" src="%s" style="height: 80px; width: 80px;"/>',
            $fmQRAndUrl,
        );
        $fmQRiOSUrl         = Yii::app()->createAbsoluteUrl('/images/extlogin/qr_appstore.svg');
        $fmQRiOSPreviewCode = sprintf(
            '<img alt="Finanzmanager Appstore QR-Code" src="%s" style="height: 80px; width: 80px;"/>',
            $fmQRiOSUrl,
        );
        $pwBannerUrl        = $this->getPwBannerUrl();
        $pwLoginUrl         = Yii::app()->createAbsoluteUrl('/');
        $pwPreviewCode      = $this->getBannerCode();

        $fmPersonalisedUrls  = new PersonalisedUrlService($user->agency);
        $fmInteressentenUrls = new PersonalisedUrlService($user->agency, 'interessent');

        $this->render('//stammdaten/verwaltung/externerLogin/admin', [
            'webappBannerUrl'    => $webappBannerUrl,
            'webappLoginUrl'     => $webappLoginUrl,
            'webappPreviewCode'  => $webappPreviewCode,
            // Google Playstore Badge Finanzmanager
            'fmBannerUrl'        => $fmBannerUrl,
            'fmPlaystoreUrl'     => $fmPlaystoreUrl,
            'fmPreviewCode'      => $fmPreviewCode,
            // Badge zur Installationsanleitung für iOS Finanzmanager
            'fmIOSBannerUrl'     => $fmIOSBannerUrl,
            'fmIOSUrl'           => $fmIOSUrl,
            'fmIOSPreviewCode'   => $fmIOSPreviewCode,
            // QR-Code zur Installationsanleitung für iOS Finanzmanager
            'fmQRiOSUrl'         => $fmQRiOSUrl,
            'fmQRiOSPreviewCode' => $fmQRiOSPreviewCode,
            // QR-Code zum Google Playstore Eintrag Finanzmanager
            'fmQRAndUrl'         => $fmQRAndUrl,
            'fmQRAndPreviewCode' => $fmQRAndPreviewCode,
            // PW-Login
            'pwBannerUrl'               => $pwBannerUrl,
            'pwLoginUrl'                => $pwLoginUrl,
            'pwPreviewCode'             => $pwPreviewCode,
            'fmPersonalisedMaklerUrls'  => $fmPersonalisedUrls->getMaklerUrls(),
            'fmPersonalisedAgencyUrl'   => $fmPersonalisedUrls->getAgencyUrl(),
            'fmInteressentenMaklerUrls' => $fmInteressentenUrls->getMaklerUrlsByUserIds($userPermission->getUnderlingsIds()),
            'fmInteressentenAgencyUrl'  => $fmInteressentenUrls->getAgencyUrl(),
        ]);
    }

    public function actionLogin($bgcolor = '', $textcolor = '', $hint = '', $logo = false)
    {
        $pwBannerUrl = $this->getBannerCode();

        if (!empty($bgcolor) && !empty($textcolor) || 1) {
            $this->layout = 'extlogin';
            $this->render(
                '//stammdaten/verwaltung/externerLogin/loginTemplate',
                [
                    'code'    => $pwBannerUrl,
                    'bgcolor' => $bgcolor
                ]
            );
        }
    }

    /**
     * @return string
     */
    private function getPwBannerUrl(): string
    {
        return Yii::app()->createAbsoluteUrl('/images/extlogin/professionalworks.jpg');
    }

    /**
     * @return string
     */
    private function getBannerCode(): string
    {
        return sprintf(
            '<a href="%s" target="_blank"><img src="%s" alt=""></a>',
            Yii::app()->createAbsoluteUrl('/'),
            $this->getPwBannerUrl()
        );
    }
}
