<?php

use Apfelbox\FileDownload\FileDownload;

/**
 * Diese Klasse implementiert die create, delete und download Methode für die XYZFilesController
 * Diese Controller müssen von diesem Controller erben und die 3 Klassenvariablen überschreiben
 */
class FileController extends GlobalRightController
{
    public $modelname          = '';
    public $directoryname      = '';
    public $directoryattribute = '';

    /**
     * Diese Methode muss aufgerufen werden, wenn die Multiuploaderextension benutzt wurde, da das Files-Array sonst nicht durch
     * die create-Methode verarbeitet werden kann
     */
    public static function fixFilesArray($attributename = 'file')
    {
        $new = [];
        foreach ($_FILES as $key => $value) {
            foreach ($value as $k => $v) {
                if (!isset($_FILES[$key][$k][$attributename])) {
                    $new[$key][$k][$attributename] = $v;
                } else {
                    $new[$key][$k] = $v;
                }
            }
        }
        $_FILES = $new;
    }

    public static function purge($path)
    {
        // schau' nach, ob das ueberhaupt ein Verzeichnis ist
        if (!is_dir($path)) {
            return -1;
        }
        // oeffne das Verzeichnis
        $dir = @opendir($path);

        // Fehler?
        if (!$dir) {
            return -2;
        }

        // gehe durch das Verzeichnis
        while (($entry = @readdir($dir)) !== false) {
            // wenn der Eintrag das aktuelle Verzeichnis oder das Elternverzeichnis
            // ist, ignoriere es
            if ($entry == '.' || $entry == '..') {
                continue;
            }
            // wenn der Eintrag ein Verzeichnis ist, dann
            if (is_dir($path . '/' . $entry)) {
                // rufe mich selbst auf
                $res = self::purge($path . '/' . $entry);
                // wenn ein Fehler aufgetreten ist
                if ($res == -1) { // dies duerfte gar nicht passieren
                    @closedir($dir); // Verzeichnis schliessen

                    return -2; // normalen Fehler melden
                }
                if ($res == -2) { // Fehler?
                    @closedir($dir); // Verzeichnis schliessen

                    return -2; // Fehler weitergeben
                }
                if ($res == -3) { // nicht unterstuetzer Dateityp?
                    @closedir($dir); // Verzeichnis schliessen

                    return -3; // Fehler weitergeben
                }
                if ($res != 0) { // das duerfe auch nicht passieren...
                    @closedir($dir); // Verzeichnis schliessen

                    return -2; // Fehler zurueck
                }
            } else {
                if (is_file($path . '/' . $entry) || is_link($path . '/' . $entry)) {
                    // ansonsten loesche diese Datei / diesen Link
                    $res = @unlink($path . '/' . $entry);
                    // Fehler?
                    if (!$res) {
                        @closedir($dir); // Verzeichnis schliessen

                        return -2; // melde ihn
                    }
                } else {
                    // ein nicht unterstuetzer Dateityp
                    @closedir($dir); // Verzeichnis schliessen

                    return -3; // tut mir schrecklich leid...
                }
            }
        }

        // schliesse nun das Verzeichnis
        @closedir($dir);

        // versuche nun, das Verzeichnis zu loeschen
        $res = @rmdir($path);

        // gab's einen Fehler?
        if (!$res) {
            return -2; // melde ihn
        }

        // alles ok
        return 0;
    }

    /**
     * Erstellt ein Neue neue Datei
     *
     * @param type   $data
     * @param string $filefieldname
     *
     * @return \modelname|null
     */
    public function create($data, $filefieldname = 'file')
    {
        $modelname          = $this->modelname;
        $directoryattribute = $this->directoryattribute;
        $model              = new $modelname();
        $file               = CUploadedFile::getInstance($model, $filefieldname);

        if (!$file) {
            return null;
        }

        $model->attributes = $data[$modelname];

        $dir = Yii::getPathofAlias('uploads')
            . DIRECTORY_SEPARATOR . $this->directoryname
            . DIRECTORY_SEPARATOR . $model->$directoryattribute;

        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        $filepath    = new Filepath($dir . DIRECTORY_SEPARATOR . $file->name);
        $model->path = $filepath->getUniquePath();
        $model->name = $filepath->getUniqueName();

        if (!$model->validate() || !$file->saveAs($model->path)) {
            return null;
        }

        $model->save();

        if (!empty($data[$modelname]['comparison_calculator'])) {
            foreach ($data[$modelname]['tariff'] ?? [] as $tarifId) {
                $calculatorAssignment = new RechnerInsuranceFilesAssignment();

                $calculatorAssignment->insurance_company_file_id = $model->id;
                $calculatorAssignment->product_combo_id          = $data[$modelname]['product_combo_id'];
                $calculatorAssignment->tarifId                   = $tarifId;

                $calculatorAssignment->save(false);
            }
        }

        return $model;
    }

    /**
     * Gibt die Formatendungen zurück, die hochgeladen werden dürfen
     *
     * @param bool $uploader
     *              wenn false, werden die Endungen als Array zurückgegeben, fallse true, werden die Endungen als String zurückgegeben, der
     *              durch den FileUploader verarbeitet werden kann
     *
     * @return string|array
     */
    public static function getValidDatatypes($uploader = false)
    {
        $validDataTypes = [
            'xml',
            'jpg',
            'jpeg',
            'png',
            'gif',
            'txt',
            'bmp',
            'pdf',
            'doc',
            'docx',
            'csv',
            'ppt',
            'pptx',
            'xls',
            'wmv',
            'xlsx',
            'tif',
            'tiff',
            'rtf',
            'html',
            'zip',
            'mp3',
            'mp4',
            'wma',
            'mpg',
            'flv',
            'mpeg',
            'aiff',
            'psd',
            'odt',
            'ods',
            'odp',
            'odg',
            'odf',
            'numbers',
            'pages',
            'rar',
            'dat'
        ];

        if (!$uploader) {
            return $validDataTypes;
        }
        $return = '';
        foreach ($validDataTypes as $type) {
            empty($return) ? $return .= $type : $return .= '|' . $type;
        }

        return $return;
    }

    public function seperateNameFromFormat($name)
    {
        $exploded = explode('.', $name);

        $format = $exploded[count($exploded) - 1];
        unset($exploded[count($exploded) - 1]);

        $name = '';
        foreach ($exploded as $namepiece) {
            $name == '' ? $name = $namepiece : $name .= '.' . $namepiece;
        }

        return [
            'name'   => $name,
            'format' => $format
        ];
    }

    public function delete($id)
    {
        $modelname = $this->modelname;
        $file      = $this->loadModel($id);
        if (null !== $file) {
            $this->loadModel($id)->delete();
        }
    }

    public function actionDownload($id, $download = true)
    {
        $file = $this->loadModel($id);

        if (null === $file || !file_exists($file->path)) {
            echo 'Nicht gefunden';
        }

        $dir = $file->path;

        if (!$download) {
            return $dir;
        }

        $fileDownload = FileDownload::createFromFilePath($file->getPath());
        @$fileDownload->sendDownload($file->name, false);

        return null;
    }

    public function getDir()
    {
        return Yii::getPathofAlias('uploads') . DIRECTORY_SEPARATOR . $this->directoryname;
    }

    /**
     * @param $id
     *
     * @return FileActiveRecord
     * @throws Exception
     */
    public function loadModel($id)
    {
        throw new Exception('muss implementiert werden');
    }
}
