<?php

declare(strict_types=1);

use Carbon\Carbon;
use Courtageerfassung\Abrechnungstool\Verbunddaten\VerbunddatenFilesystemFactory;
use Courtageerfassung\Fks\Services\FksUploadService;
use Courtageerfassung\Overhead\Factories\OverheadProcessorFactory;
use Courtageerfassung\Overhead\Models\OverheadWithoutBestandFile;
use Courtageerfassung\Overhead\Repositories\OverheadWithoutBestandFileRepository;
use Courtageerfassung\Overhead\Services\RetryOverheadProcessor;
use Courtageerfassung\Service\CourtageerfassungLogger;
use Courtageerfassung\Service\FksExportGesellschaften;
use Symfony\Component\HttpFoundation\Response;

class FksController extends AdminController
{
    use ApiUtilsTrait;

    private OverheadWithoutBestandFileRepository $repository;
    private RetryOverheadProcessor               $retryOverheadProcessor;
    private FksUploadService                     $fksUploadService;
    private CourtageerfassungLogger              $logger;

    public function init()
    {
        $this->repository       = new OverheadWithoutBestandFileRepository(
            VerbunddatenFilesystemFactory::getFilesystem()
        );
        $this->fksUploadService = FksUploadService::new();
        $this->logger           = new CourtageerfassungLogger();

        $this->logger->setUserId(currentUser()->id);

        parent::init();
    }

    public function actionLostContracts()
    {
        $model = OverheadWithoutBestandFile::model();

        $model->unsetAttributes();

        $params = Yii::app()->request->getParamByAllMeans(
            'Courtageerfassung_Overhead_Models_OverheadWithoutBestandFile',
            []
        );

        if (count($params) === 0) {
            $model->status = OverheadWithoutBestandFile::OPEN;
        } else {
            $model->setAttributes($params);
        }

        $this->render('//verbunddaten/fks', [
            'model' => $model,
        ]);
    }

    public function actionDownloadFile(int $id): void
    {
        $model = $this->repository->getById($id);

        if ($model === null) {
            throw new CHttpException(404, 'Model mit der ID ' . $id . ' nicht gefunden.');
        }

        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Content-disposition: attachment; filename=' . $model->filename);
        header('Content-type: text/csv; charset=ISO-8859-1');

        echo $model->getFileContent($id);
    }

    public function actionUpdateComment()
    {
        $this->failUnlessPostMethod();

        $id      = (int) Yii::app()->request->getParam('pk');
        $comment = htmlspecialchars(Yii::app()->request->getParam('value'));

        if (!$this->repository->updateComment($id, $comment)) {
            throw new CHttpException(500, 'Kommentar konnte nicht gespeichert werden für ID: ' . $id);
        }

        return Yii::app()->end(Response::HTTP_NO_CONTENT);
    }

    public function actionUpdateStatus()
    {
        $this->failUnlessPostMethod();

        ['id' => $id, 'status' => $status] = Yii::app()->request->getRestParams();

        if (!$this->repository->updateStatus((int) $id, $status)) {
            throw new CHttpException(500, 'Status konnte nicht aktualisiert werden für ID: ' . $id);
        }

        return Yii::app()->end(Response::HTTP_NO_CONTENT);
    }

    public function actionRetryBestandsdatenExport()
    {
        $this->failUnlessPostMethod();

        $logger = $this->logger->new('actionRetryBestandsdatenExport');

        $data  = Yii::app()->request->getRestParams();
        $id    = (int) $data['id'];
        $model = $this->repository->getById($id);

        if ($model->hasLowChanceToFindNewContracts()) {
            $logger->error('Manuelle Wiederholung ist nicht möglich. Export-Zeitraum ist abgelaufen am {exportDate}', [
                'exportDate' => $model->getFinalExportDate(),
            ]);
            throw new CHttpException(400, 'Das letzte Exportdatum liegt in der Vergangenheit.');
        }

        $companyId = (int) $model->insurance_company_id;

        if (!in_array($companyId, FksExportGesellschaften::getAllGesellschaften())) {
            throw new CHttpException(500, 'Vertragsspalte nicht gefunden für Firmen ID: ' . $model->insurance_company_id);
        }

        $logger->info('Wiederhole Overhead-Export für {id} ({filename})', [
            'id'       => $model->id,
            'filename' => $model->filename,
        ]);

        $export = OverheadProcessorFactory::forRetry($logger)->manualExport($model);

        if ($export === null) {
            $logger->info('Kein Export erzeugt. Überspringe FTP-Upload ...');
            $this->respondOk(array_merge([], $model->attributes, ['manual_export' => Carbon::parse($model->manual_export)->format('d.m.Y')]));

            return;
        }

        $logger->info('Export erzeugt (id={id}). Starte FTP-Upload ...', [
            'id' => $export->id,
        ]);

        $this->fksUploadService->upload($export);

        if (!empty($export->filename)) {
            $logger->info('FTP-Upload für {filename} abgeschlossen', [
                'filename' => $export->filename,
            ]);
        } else {
            $logger->info('FTP-Upload nicht durchgeführt, da keine ZIP-Datei erstellt wurde');
        }

        $model->refresh();
        $model->manual_export = Carbon::parse($model->manual_export)->format('d.m.Y');

        $this->respondOk($model->attributes);
    }

    /**
     * @throws CHttpException
     */
    public function actionRetryBestandsdatenExportAsync(): void
    {
        $this->failUnlessPostMethod();
        $respondMethod = 'respondOk';
        $model         = null;

        $logger = $this->logger->new('actionRetryBestandsdatenExportAsync');

        $data = Yii::app()->request->getRestParams();

        if (!empty($data['id'])) {
            $model = $this->repository->getById($data['id']);
        }

        if ($model === null) {
            $status = 'Exportmodel konnte nicht gefunden werden.';
            $logger->error($status);
            $respondMethod = 'respondError';
        } else {
            $model->setAttribute('retry', true);

            if ($model->save()) {
                $status = 'Export wird bald ausgeführt.';
            } else {
                $respondMethod = 'respondError';
                $status        = 'Export konnte nicht getriggert werden.';
            }
        }

        $this->{$respondMethod}(
            [
                'status'     => $status,
                'exportdata' => $model === null ? $data : $model->getAttributes()
            ]
        );
    }
}
