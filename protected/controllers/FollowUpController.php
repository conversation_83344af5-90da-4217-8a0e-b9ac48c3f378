<?php

use Demv\JSend\JSendResponse;

class FollowUpController extends AllowAllController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    private $controllername = 'followUp';

    public function actions()
    {
        return [
            'changestatus' => [
                'class'     => 'application.components.actions.ModelAttributeEditAction',
                'modelname' => 'FollowUp',
                'attribute' => 'done',
            ],
        ];
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues FollowUp-Model erstellt werden soll.
     *  Die Methode öffnet das FollowUp-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($model = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin', ['lastsearch' => true]);

                    return $model;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das FollowUp -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des FollowUp -Models
     */
    private function getCreateFormularData()
    {
        $model = new FollowUp();

        if (!$this->validData($_POST)) {
            $this->render('//home/<USER>/wiedervorlagen/create', ['model' => $model]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model FollowUp handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) && isset($data['FollowUp']);
        //
    }

    /**
     * Diese Methode erstellt ein neues FollowUp -Model
     *
     * @param type $data
     *          Die FollowUp -Daten als Array
     *
     * @return $model
     *          Das erstellte FollowUp -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $model             = new FollowUp();
        $model->attributes = $data['FollowUp'];

        if ($model->save()) {
            return $model;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein FollowUp -Model aktualisiert werden soll.
     *  Die Methode öffnet das FollowUp -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden FollowUp -Models
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if (($model = $this->update($data)) != null) {
                        $this->redirect([$this->controllername . '/admin', 'lastsearch' => true]);

                        return $model;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das FollowUp-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return FollowUp|null
     *          Das Exemplar des FollowUp-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = FollowUp::model()->findByPk($id);

        return $model;
    }

    /**
     *
     * Öffnet das FollowUp-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden FollowUp-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('//home/<USER>/wiedervorlagen/_form', ['model' => $model, 'modal' => 'modal']);
        }

        $formulardata                   = $_POST;
        $formulardata['FollowUp']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein FollowUp-Exemplar
     *
     * @param type $data
     *          Die FollowUp-Daten als Array
     *
     * @return $model
     *          Das aktualisierte FollowUp-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $model             = $this->loadModel($data['FollowUp']['id']);
            $model->attributes = $data['FollowUp'];
            if ($model->save()) {
                return $model;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin($lastsearch = false)
    {
        $internal = $this->isInterenalReferrer();
        /** @var FollowUp $model */
        $model = new FollowUp('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['FollowUp_Search'])) {
            $model->attributes = $_GET['FollowUp_Search'];
        } else {
            if (($internal || $lastsearch) && !empty($model->getLastSearch())) {
                $model->useLastSearch('FollowUp_Search');
            } else {
                $model->done = 0;
            }
        }

        $model->setLastSearch();
        $this->render('//home/<USER>/wiedervorlagen/admin', [
            'model' => $model,
        ]);
    }

    /**
     * @param int|string $id
     *
     * @return void
     */
    public function actionDelete($id)
    {
        $followUp = $this->loadModel($id);

        if ($followUp !== null) {
            $followUp->delete();
        }

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    public function getTodaysFollowUps()
    {
        $today     = substr(ViewHelper::getDate(), 0, 5);
        $followups = FollowUp::model()->findAll(AgencyController::getUnderlingsCondition());
        $return    = [];
        foreach ($followups as $followup) {
            if (substr($followup->date, 0, 5) == $today) {
                array_push($return, $followup);
            }
        }

        return $return;
    }

    /**
     * Performs the AJAX validation.
     *
     * @param FollowUp $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'follow-up-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }

    public function actionCreateFollowUp()
    {
        $message = '';
        if (!empty($_POST)) {
            $followUp             = new FollowUp();
            $followUp->attributes = $_POST;
            if ($followUp->save()) {
                $message = 'saved';
            }
        }
        echo json_encode(['message' => $message]);
        Yii::app()->end();
    }

    public function actionReschedule()
    {
        $model = $this->loadModel(Yii::app()->request->getPost('id'));
        if ($model === null) {
            JSendResponse::error('Wiedervorlage nicht gefunden')->respond(404);
        }
        if ($model->reschedule()) {
            JSendResponse::success([])->respond();
        }
        JSendResponse::error('')->respond(400);
    }

    public function actionRescheduleAll()
    {
        $ids    = Yii::app()->request->getPost('ids');
        $date   = Yii::app()->request->getPost('date');
        $models = FollowUp::model()->findAllByPk($ids);
        if ($date === null) {
            JSendResponse::fail([])->respond(400);
        }
        foreach ($models as $model) {
            $model->rescheduleToDate($date);
        }
        JSendResponse::success([])->respond();
    }
}
