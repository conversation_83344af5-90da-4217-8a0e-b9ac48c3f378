<?php

use GuzzleHttp\Psr7\ServerRequest;
use Login\Models\Forms\LoginForm;
use Login\Services\LoginService;
use Login\Traits\NamespaceTrait;

/**
 * Class HomeController
 */
class HomeController extends AllowAllController
{
    use NamespaceTrait;

    /**
     * HomeController constructor.
     *
     * @param string $id
     * @param null   $module
     */
    public function __construct($id, $module = null)
    {
        parent::__construct($id, $module);

        $this->defaultAction = 'index';
    }

    /**
     * Declares class-based actions.
     */
    public function actions()
    {
        return [
            'show' => 'application.components.actions.Content',
        ];
    }

    /**
     * This is the default 'index' action that is invoked
     * when an action is not explicitly requested by users.
     */
    public function actionAdmin()
    {
        $this->render('index');
    }

    /**
     * Displays the expanded login page
     */
    public function actionLoginWeiterbildung($eventId = null, $videoId = null)
    {
        Yii::app()->clientScript->registerMetaTag('noindex,noarchive', 'robots');

        $model = new LoginForm();
        $this->checkLoginForm($model, $_POST);

        if (!Yii::app()->user->isGuest && !empty(Yii::app()->session['returnUrl'])) {
            $this->redirect(Yii::app()->session['returnUrl']);
        }

        if ($videoId === null && $eventId === null) {
            $this->redirect('https://demv.de/schulungen');
        }

        if (Yii::app()->request->getRequestType() === 'GET') {
            // As we change the main Domain of Professional Works, we add a check if we try to log into the old domain.
            // if so, we redirect to the new Domain.
            if (ServerRequest::fromGlobals()->getUri()->getHost() === 'crm.deutscher-maklerverbund.de') {
                $redirectUrlGetParameters = [];
                if ($eventId !== null) {
                    $redirectUrlGetParameters[] = 'eventId=' . $eventId;
                }
                if ($videoId !== null) {
                    $redirectUrlGetParameters[] = 'videoId=' . $videoId;
                }
                $this->redirect('https://mvp.professional.works/home/<USER>' . implode('&', $redirectUrlGetParameters));
            }
        }

        $target = '';

        if ($videoId !== null) {
            $target = $this->createUrl(
                '/beratungsvorsprung/video/watch',
                [
                    'id' => $videoId
                ]
            );
        }

        if ($eventId !== null) {
            $target = $this->createUrl(
                '/beratungsvorsprung/weiterbildung/register/',
                [
                    'eventId' => $eventId
                ]
            );
        }

        Yii::app()->session['returnUrl'] = $target;

        $this->layout = 'weiterbildungsLogin';
        $this->render('/home/<USER>', ['loginModel' => $model, 'eventId' => $eventId, 'videoId' => $videoId]);
    }

    /**
     * @param LoginForm $model
     * @param array     $data
     *
     * @throws CException
     */
    private function checkLoginForm(LoginForm $model, array $data): void
    {
        // collect user input data
        if (!isset($data[NamespaceTrait::getNamespaceFormName(LoginForm::class)])) {
            return;
        }
        $model->attributes = $data[NamespaceTrait::getNamespaceFormName(LoginForm::class)];

        //login not successful
        if (!$model->validate() || !$model->login()) {
            Yii::log('login failure: ' . $model->username, CLogger::LEVEL_INFO, 'application.user.auth');
            Yii::app()->user->setFlash('error', 'Der Benutzerlogin ist nicht korrekt');

            return;
        }

        /** @var SystemUser $user */
        $user = Systemuser::model()->findByPk(Yii::app()->user->getID());

        Yii::log('login', CLogger::LEVEL_INFO, 'application.user.auth');
        (new LoginService())->initialiseSystemUser($user);

        //Only Admins need Update for now
        if (Yii::app()->user->isAdmin() && $this->passwordIsInvalid($model->password)) {
            $this->redirect('/login/login/passwordUpdate');
        }

        if (currentUser()->is_2fa_active && currentUser()->is_email_validated) {
            $this->redirect('/login/login/twoFactorAuthentication');
        }

        $this->redirect('/login/login/index');
    }

    /**
     * @param string $password
     *
     * @return bool
     */
    public function passwordIsInvalid(string $password): bool
    {
        $user           = new Systemuser('validatePassword');
        $user->password = $password;

        return !$user->validate(['password']);
    }

    /**
     * @param CAction $action
     *
     * @return bool
     */
    protected function beforeAction($action)
    {
        return true;
    }
}
