<?php

declare(strict_types=1);

use Auth\Services;
use Business\Agency\Logo\AgencyLogoFactory;
use Business\Agency\Logo\AgencyLogoSizes;
use Components\Authentication\OAuth\OAuthAccessible;
use Symfony\Component\HttpFoundation\Response;

class ImageController extends AllowAllController implements OAuthAccessible
{
    /**
     * @throws CHttpException
     */
    public function actionGetAgencyImage(string $size = null, string $userId = null, bool $fallback = true)
    {
        $allowFallback = $fallback;
        $size ??= AgencyLogoSizes::HEADER;
        $agencyLogo = AgencyLogoFactory::forAgencyId($this->getAgencyId($userId));
        $sizeClass  = AgencyLogoSizes::getInstance()->getSize($size);

        if ($sizeClass !== null) {
            $agencyLogo->setSize($sizeClass);
        }

        if ($allowFallback) {
            $agencyLogo->allowFallback();
        }

        // if we have a custom cached logo, send it
        $content = Yii::app()->cache->get($agencyLogo->getCacheId());

        if ($content !== false) {
            $this->respondImage($content);
        }

        // check if we have a custom logo that is not cached yet, before sending the cached fallback logo
        $file       = $agencyLogo->getFile();
        $isFallback = $file === null;

        if ($allowFallback && $isFallback) {
            $content = Yii::app()->cache->get($agencyLogo->getFallbackCacheId());

            if ($content !== false) {
                $this->respondImage($content);
            }
        }

        $content = $agencyLogo->getContent($file);

        if ($content === '') {
            throw new CHttpException(Response::HTTP_NOT_FOUND, 'Agency image not available.');
        }

        if ($size === AgencyLogoSizes::HEADER) {
            $cacheId = $isFallback ? $agencyLogo->getFallbackCacheId() : $agencyLogo->getCacheId();
            Yii::app()->cache->set($cacheId, $content);
        }

        $this->respondImage($content);
    }

    private function respondImage(string $content): void
    {
        if (ob_get_length() > 0) {
            ob_clean();
        }

        header('Pragma: private');
        header('Cache-Control: private, max-age=18000, must-revalidate');
        header('Content-Type: image/png');
        header('Content-Length: ' . strlen($content));
        header('Content-Transfer-Encoding: binary');
        echo $content;
        Yii::app()->end();
    }

    /**
     * @throws CHttpException
     */
    private function getAgencyId(?string $userId): int
    {
        $agencyId = Yii::app()->session['agency_id'];

        if (!empty($userId)) {
            $systemuser = Systemuser::model()->personalAccess()->findByPk($userId);
            if (!empty($systemuser)) {
                $agencyId = $systemuser->agency_id;
            }
        }

        if (empty($agencyId)) {
            throw new CHttpException(Response::HTTP_INTERNAL_SERVER_ERROR, 'Agency not available.');
        }

        return (int) $agencyId;
    }

    /**
     * @param $file
     * @param $size
     *
     * @return mixed
     *
     * @throws Exception
     */
    public function getResizedImage($file, $size)
    {
        $imageHandler = $this->widget('ext.SAImageDisplayer.SAImageDisplayer', [
            'baseDir'               => dirname($file),
            'baseDirEqualsBasePath' => true,
            'isFullImagePath'       => true,
            'image'                 => $file,
            'size'                  => $size,
            //            'group' => 'agency',
            'returnNewImagePath'    => true
        ]);

        return $imageHandler->getResizedImageUrl();
    }

    public static function getAllowedServices(): array
    {
        return Services::DEMV_INTERNAL_SERVICES;
    }
}
