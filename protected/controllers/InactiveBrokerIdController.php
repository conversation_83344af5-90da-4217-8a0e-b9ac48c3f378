<?php

use Business\Vermittlernummer\BrokerIdStatus;
use Components\Log\LoggerFactory;
use Monolog\Logger;

/**
 * Created by PhpStorm.
 * User: alex
 * Date: 09.12.16
 * Time: 17:41
 */
class InactiveBrokerIdController extends AdminController
{
    const DEFAULT_PAGE_SIZE_VIEW = 10;

    public function actionIndex()
    {
        // The default scope blocks inactive broker ids, so we need to remove
        // it, which is ok since only admins are allowed use this action
        BrokerId::model()->resetScope();

        $pagesize = (int) ($_GET['pagesize'] ?? self::DEFAULT_PAGE_SIZE_VIEW);

        $brokerid = new BrokerId('search');
        $brokerid->unsetAttributes();  // clear any default values
        if (isset($_GET['BrokerId'])) {
            $brokerid->attributes = $_GET['BrokerId'];
        }

        $criteria       = $brokerid->searchCriteria();
        $criteria->with = ['insuranceCompany', 'type', 'user'];
        $criteria->compare('inactive', 1);

        $brokeridData = new CActiveDataProvider(BrokerId::class, [
            'criteria' => $criteria,
            'sort'     => [
                'defaultOrder' => 'user.lastname, user.firstname, t.id',
                'attributes'   => [
                    'user_id' => [
                        'asc'  => 't.user_id, t.id',
                        'desc' => 't.user_id DESC, t.id DESC',
                    ],
                    'Vermittler' => [
                        'asc'  => 'user.lastname, user.firstname, t.id',
                        'desc' => 'user.lastname DESC, user.firstname DESC, t.id DESC',
                    ],
                    'brokerid' => [
                        'asc'  => 'brokerid, t.id',
                        'desc' => 'brokerid DESC, t.id DESC',
                    ],
                    'type.name' => [
                        'asc'  => 'type.id, t.id',
                        'desc' => 'type.id DESC, t.id DESC',
                    ],
                    'insuranceCompany.name' => [
                        'asc'  => 'insuranceCompany.name, t.id',
                        'desc' => 'insuranceCompany.name DESC, t.id DESC',
                    ],
                ],
            ],
            'pagination' => [
                'pageSize' => $pagesize,
            ],
        ]);

        $this->render('index', [
            'brokerid'     => $brokerid,
            'brokeridData' => $brokeridData,
            'pagesize'     => $pagesize,
        ]);
    }

    public function actionActivateMain(int $id): void
    {
        // The default scope blocks inactive broker ids, so we need to remove
        // it, which is ok since only admins are allowed use this action
        if (($brokerId = BrokerId::Model()->resetScope()->findByPk($id)) === null) {
            Yii::app()->user->setFlash('error', 'Zu aktivierende Vermittlernummer wurde nicht gefunden');
            $this->redirect(['inactiveBrokerId/index']);
        }

        if ($this->hasDuplicateActiveBrokerId($brokerId)) {
            // If there's already an identical, active main number, just drop
            // the inactive one to avoid duplicates

            $this->getLogger()->info('deleting broker id {id} as duplicate', [
                'id'                   => $brokerId->id,
                'user_id'              => $brokerId->user_id,
                'insurance_company_id' => $brokerId->insurance_company_id,
                'brokerid'             => $brokerId->brokerid,
            ]);

            Yii::app()->user->setFlash('success', 'Aktivierte Vermittlernummer als Duplikat erkannt und entfernt');
            $brokerId->delete();
            $this->redirect(Yii::app()->createUrl('brokerid/admin'));
        }

        $this->activate($id, BrokerIdType::ANTRAGSNUMMER);
    }

    public function actionActivateForeign(int $id): void
    {
        $this->activate($id, BrokerIdType::FREMDNUMMER);
    }

    /**
     * Activates a broker id and sets its type to the given id
     *
     * @param int $id the broker id id
     * @param int $typeId the type id the broker id should get
     */
    private function activate(int $id, int $typeId): void
    {
        $this->getLogger()->info('activating broker id {id} with type {type}', [
            'id'   => $id,
            'type' => $typeId,
        ]);

        $updated = BrokerId::model()
                           ->getDbConnection()
                           ->createCommand()
                           ->update(BrokerId::model()->tableName(),
                               [
                                   'by_user'           => 0,
                                   'inactive'          => 0,
                                   'type_id'           => $typeId,
                                   'last_edit_user_id' => Yii::app()->user->getID(),
                                   'status'            => BrokerIdStatus::PASSIV,
                               ],
                               'id = :id',
                               [':id' => $id]
                           );
        if ($updated) {
            Yii::app()->user->setFlash('success', 'Vermittlernummer aktiviert');

            $broker = BrokerId::model()->findByPk($id);
            $this->redirect(
                Yii::app()->createUrl(
                    'brokerid/admin',
                    [
                        'BrokerId[brokerid]'              => $broker->brokerid,
                        'BrokerId[insuranceCompany_name]' => $broker->insuranceCompany->name
                    ]
                )
            );
        }

        $this->redirect(Yii::app()->createUrl('brokerid/admin'));
    }

    /**
     * Checks if there is already an active broker id that would be a duplicate of the given broker id
     *
     * @param BrokerId $brokerId The broker id for which to check for duplicates
     * @return bool true if there is a duplicate, false otherwise
     */
    private function hasDuplicateActiveBrokerId(BrokerId $brokerId): bool
    {
        $criteria = new CDbCriteria();
        $criteria->compare('user_id', $brokerId->user_id);
        $criteria->compare('insurance_company_id', $brokerId->insurance_company_id);
        $criteria->compare('brokerid', $brokerId->brokerid);

        return BrokerId::model()->count($criteria) > 0;
    }

    private function getLogger(): Logger
    {
        return LoggerFactory::new('professionalworks', 'inactive_broker_ids');
    }
}
