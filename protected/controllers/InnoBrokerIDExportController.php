<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of InnoBrokerIDExportController
 *
 * <AUTHOR>
 */
class InnoBrokerIDExportController extends AdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    public function actionAdmin()
    {
        $model = new InnoBrokerIDExport();
        if (!empty($_GET['InnoBrokerIDExport'])) {
            $model->attributes = $_GET['InnoBrokerIDExport'];
            if (array_key_exists('innoId', $_GET['InnoBrokerIDExport'])) {
                $model->innoId = $_GET['InnoBrokerIDExport']['innoId'];
            }
        }
        $dataProvider = $model->search();

        $this->render('//stammdaten/verwaltung/innoVermittlernummernExport/admin', ['dataProvider' => $dataProvider, 'innoBrokerIDExport' => $model]);
    }

    public function actionExport()
    {
        $dataProvider             = ExportSessionManager::getDataProvider(new InnoBrokerIDExport());
        $dataProvider->pagination = false;
        $data                     = $this->filterModels($dataProvider->getData(), explode(',', $_GET['InnoBrokerIDExport']['ids'] ?? ''));
        $this->saveInnoID($data[0]->user, Yii::app()->session['innoId']);
        $this->sendHeader($data[0]);
        $csvArray = $this->buildCSV($data);
        $csvArray = $this->addHeaderRow($csvArray);
        $this->sendCSV($csvArray);
    }

    public function filterModels($data, $ids)
    {
        $models = [];
        foreach ($data as $model) {
            if (in_array($model->id, $ids)) {
                $models[] = $model;
            }
        }

        return $models;
    }

    public function saveInnoID($user, $innoID)
    {
        Systemuser::model()->updateByPk($user->id, ['inno_id' => $innoID]);
    }

    /**
     *
     * @param type $model
     *              InnoBrokerIDExport model für username
     */
    public function sendHeader($model)
    {
        $user = $model->user;

        $fileName = 'Inno_Vermittlernummern_' . $user->firstname . '_' . $user->lastname . '_' . date('d.m.Y') . '.csv';
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Content-Description: File Transfer');
        header('Content-type: text/csv; charset=ISO-8859-1');
        header("Content-Disposition: attachment; filename={$fileName}");
        header('Expires: 0');
        header('Pragma: public');
    }

    public function buildCSV($data)
    {
        $csv_array = [];
        foreach ($data as $model) {
            $csv_row = [];
            foreach (InnoBrokerIDExport::printableColumns() as $attribute) {
                if ($attribute == 'innoId') {
                    $csv_row[] = $model->innoId;
                } elseif (!empty($model->$attribute)) {
                    $csv_row[] = $model->$attribute;
                } else {
                    $csv_row[] = '';
                }
            }
            $csv_array[] = $csv_row;
        }

        return $csv_array;
    }

    public function addHeaderRow($csvArray)
    {
        $headerRow       = [];
        $attributeLabels = InnoBrokerIDExport::model()->attributeLabels();
        foreach (InnoBrokerIDExport::printableColumns() as $attribute) {
            if (array_key_exists($attribute, $attributeLabels)) {
                $headerRow[] = $attributeLabels[$attribute];
            } else {
                $headerRow[] = $attribute;
            }
        }
        array_unshift($csvArray, $headerRow);

        return $csvArray;
    }

    public function sendCSV($csvArray)
    {
        $fileHandler = @fopen('php://output', 'w');
        foreach ($csvArray as $row) {
            fputcsv($fileHandler, $row, ';');
        }
        fclose($fileHandler);
    }

    public function actionGetInnoIdAjax()
    {
        $user_id = Yii::app()->request->getParam('user_id');

        if (!empty($user_id)) {
            $systemuserModel = Systemuser::model()->findByPk($user_id);

            if (null !== $systemuserModel) {
                if (!empty($systemuserModel->inno_id)) {
                    echo CHtml::encode($systemuserModel->inno_id);
                }
            }
        }
        Yii::app()->end();
    }

    public function actionGetInnoCreateButton()
    {
        $userID     = Yii::app()->request->getParam('user_id');
        $systemuser = Systemuser::model()->findByPk($userID);
        $html       = '';
        if (empty($systemuser->inno_id)) {
            $html = ViewHelper::labelFor('Makler bei Innosystems anmelden', 'InnoBrokerIDExport', 'inno_id_label', true);
            $html .= '<button class="btn btn-mini" value="reset" type="reset" onClick="createUser(' . $userID . ')">
                    <i class="icon-plus"></i>' .
                'Benutzer anlegen' . '
                </button>';
            $html .= '<div id="ajaxGifDiv" style="display: none;">';
            $html .= CHtml::image(Yii::app()->getBaseUrl() . '/images/ajax-loader.gif', 'Ajaxloader');
            $html .= '</div>';
        } else {
            $html = ViewHelper::labelFor('Makler hat bereits eine Inno-ID', 'InnoBrokerIDExport', 'inno_id_label', true);
            $html .= '<button class="btn btn-mini" value="reset" type="reset" disabled>
                    <i class="icon-plus"></i>' .
                'Benutzer anlegen' . '
                </button>';
            $html .= '<div id="ajaxGifDiv" style="display: none;">';
            $html .= CHtml::image(Yii::app()->getBaseUrl() . '/images/ajax-loader.gif', 'Ajaxloader');
            $html .= '</div>';
        }
        echo $html;
        Yii::app()->end();
    }

    /**
     * Exportiere alle Vermittlernummern von Nutzern, die eine InnoID besitzen.
     */
    public function actionExportAll()
    {
        $innoBrokerIDExport                       = new InnoBrokerIDExport();
        $innoBrokerIDExport->searchAllWithInnoIds = true;
        $dataProvider                             = $innoBrokerIDExport->search();
        $data                                     = $dataProvider->getData();
        $this->sendHeader($data[0]);
        $csvArray = $this->buildCSV($data);
        $csvArray = $this->addHeaderRow($csvArray);
        $this->sendCSV($csvArray);
    }

    /**
     * importscript für vorhandete inno-Benutzer
     */
    public function actionImportScript()
    {
//        $file = fopen(Yii::getPathofAlias('uploads') . DIRECTORY_SEPARATOR . 'untermandanten.csv',
//                'r');
        $file    = fopen(DIRECTORY_SEPARATOR . 'home' . DIRECTORY_SEPARATOR . 'christian' . DIRECTORY_SEPARATOR . 'Uploads' . DIRECTORY_SEPARATOR . 'untermandanten.csv', 'r');
        $success = [];
        $fail    = [];
        $ids     = [];
        while ($line = fgetcsv($file, 0, ';')) {
            if ($line[0] == 'Mandant') {
                continue;
            }
            $explode  = explode('_', $line[2] ?? '');
            $user_id  = $explode[1];
            $ids[]    = $user_id;
            $passwort = $line[3];
            $innoID   = $line[21];

            $systemuser = Systemuser::model()->findByPk($user_id);
            if (null !== $systemuser) {
                $existCriteria = new CDbCriteria();
                $existCriteria->compare('user_id', $systemuser->id);
                $existCriteria->compare('company_id', 182);
                $existCriteria->compare('product_combo_id', 164);
                $existCriteria->compare('type_id', 6);
                $existCriteria->compare('insurance_company_link_id', 318);

                if (!UserLinks::model()->exists($existCriteria)) {
                    $userLink                            = new UserLinks();
                    $userLink->user_id                   = $systemuser->id;
                    $userLink->company_id                = 182;
                    $userLink->product_combo_id          = 164;
                    $userLink->type_id                   = 6;
                    $userLink->insurance_company_link_id = 318;
                    $userLink->username                  = $systemuser->login . '_' . $systemuser->id;
                    $userLink->password                  = $passwort;
                    $userLink->allow_sub_broker          = 0;
                    if ($userLink->save()) {
                        $success[$user_id]['userLinkCreate'] = $userLink->attributes;
                    } else {
                        $fail[$user_id]['userLinkCreate'] = $userLink->attributes;
                    }
                } else {
                    $userLink           = UserLinks::model()->find($existCriteria);
                    $userLink->username = $systemuser->login . '_' . $systemuser->id;
                    $userLink->password = $passwort;
                    if ($userLink->save()) {
                        $success[$user_id]['userLinkUpdate'] = $userLink->attributes;
                    } else {
                        $fail[$user_id]['userLinkUpdate'] = $userLink->attributes;
                    }
                }
                Systemuser::model()->updateByPk($user_id, ['inno_id' => $innoID]);
            } else {
                $fail['systemuser nicht gefunden'] = '';
            }
        }
    }
}
