<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of InnoInsuranceCompanyAssignmentController
 *
 * <AUTHOR>
 */
class InnoInsuranceCompanyAssignmentController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    public function actionAdmin()
    {
        $dataProvider = InnoInsuranceCompanyAssignment::model()->search();
        $this->render('//stammdaten/verwaltung/innoGesellschaften/admin', [
            'dataProvider' => $dataProvider
        ]);
    }

    public function actionCreate()
    {
        $model            = new InnoInsuranceCompanyAssignment();
        $model->inno_id   = Yii::app()->request->getParam('id');
        $model->inno_name = Yii::app()->request->getParam('name');
        $model->save();
        Yii::app()->end();
    }

    public function actionDelete($id)
    {
        $model = InnoInsuranceCompanyAssignment::model()->findByPk($id);
        $model->delete();
        Yii::app()->end();
    }
}
