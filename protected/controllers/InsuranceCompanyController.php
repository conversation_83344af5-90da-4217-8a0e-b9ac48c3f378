<?php

use Business\Ansprechpartner\AnsprechpartnerSuche;

class InsuranceCompanyController extends RightController
{
    use RenderAjaxTrait;
    use RenderJsonTrait;

    public $menuGroup;

    public $excludedActions = [
        'ProductAdmin',
        'list'
    ];

    /**
     * Gibt die erste Kontaktperson zurück falls es eine gibt, sonst null
     *
     * @return SupportPerson die erste Kontaktperson zurück falls es eine gibt, sonst null
     */
    public static function getFirstContactPerson($insuranceCompany)
    {
        return AnsprechpartnerSuche::new($insuranceCompany->id)->ersterAnsprechpartner();
    }

    public function init()
    {
        //Menü fix für Gesellschaftenübersicht home / stammdaten
        if (strpos(Yii::app()->request->url, 'stammdaten') !== false || strpos(Yii::app()->request->url, 'update') !== false) {
            $this->menuGroup = MenuGroup::STAMMDATEN;
        } else {
            $this->menuGroup = MenuGroup::HOME;
        }
        parent::init();
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param integer $id the ID of the model to be loaded
     *
     * @return InsuranceCompany the loaded model
     * @throws CHttpException
     */
    public function loadModel($id)
    {
        $model = InsuranceCompany::model()->findByPk($id);

        //if ($model === null) throw new CHttpException(404, 'The requested page does not exist.');
        return $model;
    }

    public function actionAdmin()
    {
        $this->redirect(['/stammdaten/gesellschaften/company/admin']);
    }

    public function actionRelationUpdate($id)
    {
        //        dd($_POST);
        $model = InsuranceCompanyRelation::model()->findByPk($id);

        if (!empty($model) && (!empty($_POST))) {
            $model->attributes = $_POST['InsuranceCompanyRelation'];

            $model->save();
            Yii::app()->end();
        }
        $this->renderPartial('//stammdaten/gesellschaften/vertriebswege/_form', compact(['model']
        ));
    }

    public function actionRelationCreate($insurancecompanyid)
    {
        $model = new InsuranceCompanyRelation();

        if (!empty($_POST)) {
            $model->insurance_company_id = $insurancecompanyid;
            $model->attributes           = $_POST['InsuranceCompanyRelation'];

            $model->save();
            Yii::app()->end();
        }
        $this->renderPartial('//stammdaten/gesellschaften/vertriebswege/_form', compact(['model']
        ));
    }

    public function actionRelationDelete($id)
    {
        $model = InsuranceCompanyRelation::model()->findByPk($id);

        if (!empty($model)) {
            $model->delete();
            Yii::app()->end();
        }
    }

    public function actionList($q = null, $systemwide = null, $pool = null, $id = null, $limit = 50)
    {
        $criteria        = new CDbCriteria();
        $criteria->limit = $limit;
        if ($q !== null && $q !== '') {
            $criteria->compare('name', $q, true);
        }
        if ($id !== null) {
            $criteria->addInCondition('id', explode(',', $id));
        }
        if ($systemwide) {
            $criteria->compare('systemwide', 1);
        }
        if ($pool !== null) {
            $criteria->compare('pool', 1);
        }
        $models = InsuranceCompany::model()->findAll($criteria);
        $data   = [];
        foreach ($models as $model) {
            $data[] = [
                'id'   => $model->id,
                'text' => $model->name
            ];
        }
        $this->renderJson($data);
    }
}
