<?php

class InsuranceCompanyFilesController extends FileController
{
    //Brauchen wir noch für die Downloads


    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout                    = '//layouts/column2';
    public $modelname                 = 'InsuranceCompanyFiles';
    public $directoryname             = 'company';
    public $directoryattribute        = 'insurance_company_id';
    public $excludedActions           = [
        'Download'
    ];

    public function loadModel($id)
    {
        return InsuranceCompanyFiles::model()->findByPk($id);
    }
}
