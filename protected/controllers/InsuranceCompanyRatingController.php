<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of InsuranceCompanyRatingController
 *
 * <AUTHOR>
 */
class InsuranceCompanyRatingController extends AllowAllController
{
    use RenderAjaxTrait;

    /**
     * @param integer $insuranceCompanyId
     */
    public function actionRateModal($insuranceCompanyId)
    {
        $insuranceCompany = InsuranceCompany::model()->findByPk($insuranceCompanyId);
        $this->renderAjax('//home/<USER>/bewertung/rateModal', ['insuranceCompany' => $insuranceCompany]);
    }

    public function actionAjaxRate()
    {
        $deleted                      = InsuranceCompanyRating::model()->deleteAllByAttributes([
                                                                                                   'insurance_company_id' => $_POST['insurance_company_id'],
                                                                                                   'question_id'          => $_POST['question_id'],
                                                                                                   'user_id'              => Yii::app()->user->getID(),
                                                                                               ]);
        $rating                       = new InsuranceCompanyRating();
        $rating->insurance_company_id = $_POST['insurance_company_id'];
        $rating->question_id          = $_POST['question_id'];
        $rating->user_id              = Yii::app()->user->getID();
        $rating->rating               = $_POST['rating'];
        echo $rating->save();
        Yii::app()->end();
    }

    public function actionAjaxDelete()
    {
        $deleted = InsuranceCompanyRating::model()->deleteAllByAttributes([
                                                                              'insurance_company_id' => $_POST['insurance_company_id'],
                                                                              'question_id'          => $_POST['question_id'],
                                                                              'user_id'              => Yii::app()->user->getID(),
                                                                          ]);
        echo $deleted;
        Yii::app()->end();
    }

    public function actionAjaxSaveComment()
    {
        $comment                       = new InsuranceCompanyRatingComment();
        $comment->user_id              = Yii::app()->user->getId();
        $comment->insurance_company_id = $_POST['insurance_company_id'];
        $comment->type                 = $_POST['type'];
        $comment->comment              = $_POST['comment'];
        echo $comment->save();

        Yii::app()->end();
    }

    public function actionAjaxDeleteComment()
    {
        if (Yii::app()->user->isAdmin()) {
            $deleted = InsuranceCompanyRatingComment::model()->deleteAllByAttributes(['id' => $_POST['comment_id']]);
        } else {
            $deleted = InsuranceCompanyRatingComment::model()->deleteAllByAttributes(['user_id' => Yii::app()->user->getId(), 'id' => $_POST['comment_id']]);
        }
        echo $deleted;
        Yii::app()->end();
    }
}
