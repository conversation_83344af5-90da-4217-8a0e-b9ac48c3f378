<?php

class JuristicPersonController extends AllowAllController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout              = '//layouts/column2';
    private $juristicpersonname = '<PERSON><PERSON><PERSON><PERSON><PERSON>';
    private $prefix             = 'JuristicPerson';

    /**
     * Displays a particular juristicperson.
     *
     * @param integer $id the ID of the juristicperson to be displayed
     */
    public function actionView($id)
    {
        $this->render('view',
            [
                'juristicperson' => $this->loadModel($id),
            ]);
    }

    /**
     * <PERSON>ädt das JuristicPerson-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des JuristicPerson-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $juristicperson = JuristicPerson::model()->findByPk($id);

        return $juristicperson;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues JuristicPerson-Model erstellt werden soll.
     *  Die Methode öffnet das JuristicPerson-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if ($juristicperson = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $juristicperson;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das JuristicPerson -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des JuristicPerson -Models
     */
    private function getCreateFormularData()
    {
        $formulardata   = [];
        $juristicperson = new JuristicPerson();

        if (!$this->validData($_POST)) {
            $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'juristicPerson' . DIRECTORY_SEPARATOR . '_formFields.php',
                ['juristicperson' => $juristicperson, 'form' => new CActiveForm(), 'prefix' => 'Create']);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model JuristicPerson handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {

        //ToDo
        return isset($data) &&
            isset($data['JuristicPerson']) &&
            isset($data['JuristicPerson']['firstname']) &&
            $data['JuristicPerson']['firstname'] != '' &&
            isset($data['JuristicPerson']['lastname']) &&
            $data['JuristicPerson']['lastname'] != '' &&
            isset($data['JuristicPerson']['birthday']) &&
            $data['JuristicPerson']['birthday'] != ''
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues JuristicPerson -Model
     *
     * @param type $data
     *          Die JuristicPerson -Daten als Array
     *
     * @return $juristicperson
     *          Das erstellte JuristicPerson -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }
        if (!isset($data['JuristicPerson']['address_id']) || $data['JuristicPerson']['address_id'] == '') {
            $addressController = new AddressController('address');
            $address           = $addressController->create($data);

            if (null !== $address) {
                $data['JuristicPerson']['address_id'] = $address->id;
            }
        } else {
            return null;
        }

        $juristicperson                     = new JuristicPerson();
        $data['JuristicPerson']['birthday'] = UtilHelper::parseToDBDate($data['JuristicPerson']['birthday']);
        $juristicperson->attributes         = $data['JuristicPerson'];

        if ($juristicperson->save()) {
            $ajpController        = new AgencyJuristicPersonController('agencyjuristicperson');
            $ajp                  = ['AgencyJuristicPerson' => ['agency_id' => $data['AgencyJuristicPerson']['agency_id'], 'juristic_person_id' => $juristicperson->id]];
            $agencyjuristicperson = $ajpController->create($ajp);

            if (null !== $agencyjuristicperson) {
                return $juristicperson;
            }

            return null;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein JuristicPerson -Model aktualisiert werden soll.
     *  Die Methode öffnet das JuristicPerson -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden JuristicPerson -Models
     */
    public function actionUpdate($id)
    {
        $juristicperson = $this->loadModel($id);
        if ($juristicperson != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if (isset($data[$this->prefix])) {
                    $data = $data[$this->prefix];
                }
                if ($this->validData($data)) {
                    if ($juristicperson = $this->update($data) != null) {
                        //$this->actionAdmin();
                        $agencyid         = AgencyJuristicPerson::model()->findByAttributes(['juristic_person_id' => $id])->agency_id;
                        $agencyController = new AgencyController('agency');
                        $agencyController->actionUpdate($agencyid);

                        return $juristicperson;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das JuristicPerson-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden JuristicPerson-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $juristicperson = $this->loadModel($id);

        if (!isset($_POST[$this->prefix]) || !$this->validData($_POST[$this->prefix])) {
            $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'juristicPerson' . DIRECTORY_SEPARATOR . '_formFields.php',
                ['juristicperson' => $juristicperson, 'form' => new CActiveForm(), 'prefix' => 'Update']);
        }

        $formulardata                                        = $_POST;
        $formulardata[$this->prefix]['JuristicPerson']['id'] = $id;
        $formulardata[$this->prefix]['Address']['id']        = $juristicperson->address_id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein JuristicPerson-Exemplar
     *
     * @param type $data
     *          Die JuristicPerson-Daten als Array
     *
     * @return $juristicperson
     *          Das aktualisierte JuristicPerson-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $juristicperson             = $this->loadModel($data['JuristicPerson']['id']);
            $addressController          = new AddressController('address');
            $data['Address']['id']      = $juristicperson->address_id;
            $juristicperson->attributes = $data['JuristicPerson'];
            if ($addressController->update($data) != null) {
                if ($juristicperson->save()) {
                    return $juristicperson;
                }

                return null;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all juristicpersons.
     */
    public function actionAdmin()
    {
        $juristicperson = new JuristicPerson('search');
        $juristicperson->unsetAttributes();  // clear any default values
        $address = new Address();
        if (isset($_GET['JuristicPerson'])) {
            $juristicperson->attributes = $_GET['JuristicPerson'];
        }

        $this->render('admin',
            [
                'juristicperson' => $juristicperson,
                'address'        => $address,
            ]);
    }

    /**
     * Löscht das JuristicPerson-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden JuristicPerson-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
//        if (!isset($_GET['ajax']))
//                $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $relations = AgencyJuristicPerson::model()->findAllByAttributes(['juristic_person_id' => $id]);
        foreach ($relations as $value) {
            $value->delete();
        }

        return $this->loadModel($id)->delete();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param JuristicPerson $juristicperson the juristicperson to be validated
     */
//    protected function performAjaxValidation($juristicperson)
//    {
//        if (isset($_POST['ajax']) && $_POST['ajax'] === 'juristic-person-form')
//        {
//            echo CActiveForm::validate($juristicperson);
//            Yii::app()->end();
//        }
//    }

    /**
     * Lists all juristicpersons.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('JuristicPerson');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }
}
