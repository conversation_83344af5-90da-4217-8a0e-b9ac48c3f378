<?php

class LinkTypeController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout        = '//layouts/column2';
    private $linktypename = 'LinkType';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das LinkType-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des LinkType-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $linktype = LinkType::model()->findByPk($id);

        return $linktype;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues LinkType-Model erstellt werden soll.
     *  Die Methode öffnet das LinkType-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
//        print_r($data);
        if (isset($data['LinkType'])) {
            if (isset($data['create'])) {
                if ($this->validData($data)) {
                    if ($linktype = $this->create($data) != null) {
                        //echo "Das Erstellen war erfolgreich";
                        //ToDo -> View aktualiseren
                        $this->redirect('admin');

                        return $linktype;
                    }

                    return null;
                }
            }
        }
    }

    /**
     *  Öffnet das LinkType -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des LinkType -Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $linktype     = new LinkType();
        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/verwaltung/linkTyp/create', ['linktype' => $linktype]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model LinkType handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['LinkType'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues LinkType -Model
     *
     * @param type $data
     *          Die LinkType -Daten als Array
     *
     * @return $linktype
     *          Das erstellte LinkType -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $linktype             = new LinkType();
        $linktype->attributes = $data['LinkType'];
        if ($linktype->save()) {
            return $linktype;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein LinkType -Model aktualisiert werden soll.
     *  Die Methode öffnet das LinkType -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden LinkType -Models
     */
    public function actionUpdate($id)
    {
        $linktype = $this->loadModel($id);
        if ($linktype != null) {
            $data = $this->getUpdateFormularData($id);
            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($linktype = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $linktype;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das LinkType-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden LinkType-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $linktype = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/verwaltung/linkTyp/update', ['linktype' => $linktype]);
        }

        $formulardata                   = $_POST;
        $formulardata['LinkType']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein LinkType-Exemplar
     *
     * @param type $data
     *          Die LinkType-Daten als Array
     *
     * @return $linktype
     *          Das aktualisierte LinkType-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $linktype             = $this->loadModel($data['LinkType']['id']);
            $linktype->attributes = $data['LinkType'];
            if ($linktype->save()) {
                return $linktype;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $linktype = new LinkType('search');
        $linktype->unsetAttributes();  // clear any default values
        if (isset($_GET['LinkType'])) {
            $linktype->attributes = $_GET['LinkType'];
        }

        $this->render('//stammdaten/verwaltung/linkTyp/admin', [
            'linktype' => $linktype,
        ]);
    }

    /**
     * Löscht das LinkType-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden LinkType-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('LinkType');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param LinkType $linktype the model to be validated
     */
    protected function performAjaxValidation($linktype)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'link-type-form') {
            echo CActiveForm::validate($linktype);
            Yii::app()->end();
        }
    }
}
