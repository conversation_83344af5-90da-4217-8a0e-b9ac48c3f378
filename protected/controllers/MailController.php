<?php

class MailController extends AllowAllController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Diese Methode erstellt ein neues Mail -Model
     *
     * @param      $data Die Mail -Daten als Array
     * @param bool $template
     *
     * @return Mail|null    Das erstellte Mail -Exemplar
     */
    public function create($data, $template = false)
    {
        if (!$this->validData($data)) {
            return null;
        }
        $bccs = [];
        if (isset($data['Mail']['bcc'])) {
            if (false !== strpos($data['Mail']['bcc'], ';')) {
                $exploded = explode(';', $data['Mail']['bcc'] ?? '');

                foreach ($exploded as $value) {
                    $emailAddress = trim($value);
                    if (!empty($value) && filter_var($emailAddress, FILTER_VALIDATE_EMAIL)) {
                        $bccs[] = $emailAddress;
                    }
                }
            } else {
                $bccs[] = $data['Mail']['bcc'];
            }
        }
        $bccs = serialize($bccs);

        $ccs = [];
        if (isset($data['Mail']['cc'])) {
            if (false !== strpos($data['Mail']['cc'], ';')) {
                $exploded = explode(';', $data['Mail']['cc'] ?? '');
                foreach ($exploded as $value) {
                    $emailAddresses = trim($value);
                    if (!empty($value) && filter_var($emailAddresses, FILTER_VALIDATE_EMAIL)) {
                        $ccs[] = $emailAddresses;
                    }
                }
            } else {
                $ccs[] = $data['Mail']['cc'];
            }
        }
        $ccs = serialize($ccs);

        $attachments = [];
        if (isset($data['Mail']['attachments'])) {
            $attachments = $data['Mail']['attachments'];
        }
        $attachments = serialize($attachments);

        $data['Mail']['bcc']         = $bccs;
        $data['Mail']['cc']          = $ccs;
        $data['Mail']['attachments'] = $attachments;
        if (empty($data['Mail']['sender_email']) || !filter_var($data['Mail']['sender_email'], FILTER_VALIDATE_EMAIL)) {
            $data['Mail']['sender_email'] = Yii::app()->settings->get('Mail', 'systemMail')['address'];
        }

        $mail              = new Mail();
        $mail->attributes  = $data['Mail'];
        $mail->attachments = $attachments;
        if ($mail->save(!$template)) {
            return $mail;
        }

        return null;
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model Mail handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
               isset($data['Mail']) &&
               isset($data['Mail']['reciever_email']) &&
               $data['Mail']['reciever_email'] != '' &&
               isset($data['Mail']['subject']) &&
               $data['Mail']['subject'] != ''
            //                isset($data['Mail']['content']) &&
            //                $data['Mail']['content'] != ''

        ;
        //
    }

    /**
     * Diese Methode aktualisiert ein Mail-Exemplar
     *
     * @param type $data
     *          Die Mail-Daten als Array
     *
     * @return $mail
     *          Das aktualisierte Mail-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $mail             = $this->loadModel($data['Mail']['id']);
            $mail->attributes = $data['Mail'];
            if ($mail->save()) {
                return $mail;
            }

            return null;
        }

        return null;
    }

    /**
     * Lädt das Mail-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des Mail-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $mail = Mail::mail()->findByPk($id);

        return $mail;
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Mail $mail the mail to be validated
     */
    protected function performAjaxValidation($mail)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'mail-form') {
            echo CActiveForm::validate($mail);
            Yii::app()->end();
        }
    }
}
