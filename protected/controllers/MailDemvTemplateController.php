<?php

class MailDemvTemplateController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     *  Diese Methode wird aufgerufen wenn ein neues MailDemvTemplate-Model erstellt werden soll.
     *  Die Methode öffnet das MailDemvTemplate-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if (($maildemvtemplate = $this->create($data)) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $maildemvtemplate;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das MailDemvTemplate -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des MailDemvTemplate -Models
     */
    private function getCreateFormularData()
    {
        $formulardata     = [];
        $maildemvtemplate = new MailDemvTemplate();

        $_POST = ViewHelper::parseToArray($_POST);

        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/templateVerwaltung/mailVorlagen/create', ['maildemvtemplate' => $maildemvtemplate]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model MailDemvTemplate handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['MailDemvTemplate'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues MailDemvTemplate -Model
     *
     * @param type $data
     *          Die MailDemvTemplate -Daten als Array
     *
     * @return $maildemvtemplate
     *          Das erstellte MailDemvTemplate -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $data['MailDemvTemplate']['last_edit_user_id'] = Yii::app()->user->getID();
        $data['MailDemvTemplate']['last_edit_date']    = ViewHelper::getDate(true);

        if (!isset($data['MailDemvTemplate']['product_combo_id']) || $data['MailDemvTemplate']['product_combo_id'] == '') {
            $data['MailDemvTemplate']['product_combo_id'] = null;
        }

        $maildemvtemplate             = new MailDemvTemplate();
        $maildemvtemplate->attributes = $data['MailDemvTemplate'];
        if ($maildemvtemplate->save()) {
            return $maildemvtemplate;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein MailDemvTemplate -Model aktualisiert werden soll.
     *  Die Methode öffnet das MailDemvTemplate -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden MailDemvTemplate -Models
     */
    public function actionUpdate($id)
    {
        $maildemvtemplate = $this->loadModel($id);
        if ($maildemvtemplate != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($maildemvtemplate = $this->update($data) != null) {
                        $this->redirect(['mailDemvTemplate/admin']);

                        return $maildemvtemplate;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das MailDemvTemplate-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des MailDemvTemplate-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $maildemvtemplate = MailDemvTemplate::model()->findByPk($id);

        return $maildemvtemplate;
    }

    /**
     *
     * Öffnet das MailDemvTemplate-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden MailDemvTemplate-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $maildemvtemplate = $this->loadModel($id);

        $_POST = ViewHelper::parseToArray($_POST);

        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/templateVerwaltung/mailVorlagen/update', ['maildemvtemplate' => $maildemvtemplate]);
        }

        $formulardata                           = $_POST;
        $formulardata['MailDemvTemplate']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein MailDemvTemplate-Exemplar
     *
     * @param type $data
     *          Die MailDemvTemplate-Daten als Array
     *
     * @return $maildemvtemplate
     *          Das aktualisierte MailDemvTemplate-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $data['MailDemvTemplate']['last_edit_user_id'] = Yii::app()->user->getID();
            $data['MailDemvTemplate']['last_edit_date']    = ViewHelper::getDate(true);

            if (!isset($data['MailDemvTemplate']['product_combo_id']) || $data['MailDemvTemplate']['product_combo_id'] == '') {
                $data['MailDemvTemplate']['product_combo_id'] = null;
            }

            $maildemvtemplate             = $this->loadModel($data['MailDemvTemplate']['id']);
            $maildemvtemplate->attributes = $data['MailDemvTemplate'];
            if ($maildemvtemplate->save()) {
                return $maildemvtemplate;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all maildemvtemplates.
     */
    public function actionAdmin()
    {
        $maildemvtemplate = new MailDemvTemplate('search');
        $maildemvtemplate->unsetAttributes();  // clear any default values
        if (isset($_GET['MailDemvTemplate'])) {
            $maildemvtemplate->attributes = $_GET['MailDemvTemplate'];
        }

        $this->render('//stammdaten/templateVerwaltung/mailVorlagen/admin', [
            'maildemvtemplate' => $maildemvtemplate,
        ]);
    }

    /**
     * Löscht das MailDemvTemplate-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden MailDemvTemplate-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
//        if (!isset($_GET['ajax']))
//                $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param MailDemvTemplate $maildemvtemplate the maildemvtemplate to be validated
     */
    protected function performAjaxValidation($maildemvtemplate)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'mail-demv-template-form') {
            echo CActiveForm::validate($maildemvtemplate);
            Yii::app()->end();
        }
    }
}
