<?php

class MailProcedureController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout             = '//layouts/column2';
    private $mailprocedurename = 'MailProcedure';

    /**
     * Displays a particular mailprocedure.
     *
     * @param integer $id the ID of the mailprocedure to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'mailprocedure' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das MailProcedure-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des MailProcedure-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $mailprocedure = MailProcedure::model()->findByPk($id);

        return $mailprocedure;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues MailProcedure-Model erstellt werden soll.
     *  Die Methode öffnet das MailProcedure-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if ($mailprocedure = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $mailprocedure;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das MailProcedure -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des MailProcedure -Models
     */
    private function getCreateFormularData()
    {
        $formulardata  = [];
        $mailprocedure = new MailProcedure();

        if (!$this->validData($_POST)) {
            $this->renderPartial('//stammdaten/verwaltung/mailVorgangTyp/_form', ['mailprocedure' => $mailprocedure, 'form' => new CActiveForm(), 'prefix' => 'Create']);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model MailProcedure handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['MailProcedure'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues MailProcedure -Model
     *
     * @param array $data
     *          Die MailProcedure -Daten als Array
     *
     * @return $mailprocedure
     *          Das erstellte MailProcedure -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }
        if (!isset($data['MailProcedure']['support_assignment_subject_id']) || $data['MailProcedure']['support_assignment_subject_id'] == '') {
            $data['MailProcedure']['support_assignment_subject_id'] = null;
        }

        $mailprocedure             = new MailProcedure();
        $mailprocedure->attributes = $data['MailProcedure'];

        if ($mailprocedure->save()) {
            return $mailprocedure;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein MailProcedure -Model aktualisiert werden soll.
     *  Die Methode öffnet das MailProcedure -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden MailProcedure -Models
     */
    public function actionUpdate($id)
    {
        $mailprocedure = $this->loadModel($id);
        if ($mailprocedure != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($mailprocedure = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $mailprocedure;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das MailProcedure-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden MailProcedure-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $mailprocedure = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->renderPartial('//stammdaten/verwaltung/mailVorgangTyp/_form', ['mailprocedure' => $mailprocedure, 'form' => new CActiveForm(), 'prefix' => 'Update']);
        }

        $formulardata                        = $_POST;
        $formulardata['MailProcedure']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein MailProcedure-Exemplar
     *
     * @param array $data
     *          Die MailProcedure-Daten als Array
     *
     * @return $mailprocedure
     *          Das aktualisierte MailProcedure-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            if (!isset($data['MailProcedure']['support_assignment_subject_id']) || $data['MailProcedure']['support_assignment_subject_id'] == '') {
                $data['MailProcedure']['support_assignment_subject_id'] = null;
            }
            $mailprocedure             = $this->loadModel($data['MailProcedure']['id']);
            $mailprocedure->attributes = $data['MailProcedure'];
            if ($mailprocedure->save()) {
                return $mailprocedure;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all mailprocedures.
     */
    public function actionAdmin()
    {
        $mailprocedure = new MailProcedure('search');
        $mailprocedure->unsetAttributes();  // clear any default values
        if (isset($_GET['MailProcedure'])) {
            $mailprocedure->attributes = $_GET['MailProcedure'];
        }

        $this->render('//stammdaten/verwaltung/mailVorgangTyp/admin', [
            'mailprocedure' => $mailprocedure,
        ]);
    }

    /**
     * Löscht das MailProcedure-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden MailProcedure-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all mailprocedures.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('MailProcedure');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Diese Methode fügt alle MailProcedures zu den SupportAssignmentSubjects, sofern es noch nicht
     * vorhanden ist
     */
    public function actionAddProceduresToSupportAssignments()
    {
        $supportassignments = SupportAssignmentSubject::model()->findAll();
        $mailprocedures     = MailProcedure::model()->findAll();

        $supportassignmentSubjectController = new SupportAssignmentSubjectController('supportassignmentsubject');

        foreach ($mailprocedures as $procedure) {
            $contains = false;
            foreach ($supportassignments as $sas) {
                if ($sas->name == $procedure->name) {
                    $contains = true;
                    break;
                }
            }
            if (!$contains) {
                $data = ['SupportAssignmentSubject' => ['name' => $procedure->name]];
                if ($supportassignmentSubjectController->create($data)) {
                    echo 'SupportAssignmentSubject: ' . $procedure->name . ' hizugefuegt';
                    echo '<br>';
                }
            }
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param MailProcedure $mailprocedure the mailprocedure to be validated
     */
    protected function performAjaxValidation($mailprocedure)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'mail-procedure-form') {
            echo CActiveForm::validate($mailprocedure);
            Yii::app()->end();
        }
    }
}
