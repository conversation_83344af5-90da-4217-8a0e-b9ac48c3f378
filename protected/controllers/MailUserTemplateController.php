<?php

class MailUserTemplateController extends AllowAllController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     *  Diese Methode wird aufgerufen wenn ein neues MailUserTemplate-mailusertemplate erstellt werden soll.
     *  Die Methode öffnet das MailUserTemplate-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if (($mailusertemplate = $this->create($data)) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $mailusertemplate;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das MailUserTemplate -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des MailUserTemplate -mailusertemplates
     */
    private function getCreateFormularData()
    {
        $formulardata     = [];
        $mailusertemplate = new MailUserTemplate();

        if (!$this->validData($_POST)) {
            $this->renderPartial('//home/<USER>/vorgaenge/vorlagen/mail/_formFields', ['mailusertemplate' => $mailusertemplate, 'form' => new CActiveForm(), 'prefix' => 'Create']);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das mailusertemplate MailUserTemplate handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
               isset($data['MailUserTemplate']);
        //
    }

    /**
     * Diese Methode erstellt ein neues MailUserTemplate -mailusertemplate
     *
     * @param array $data
     *          Die MailUserTemplate -Daten als Array
     *
     * @return $mailusertemplate
     *          Das erstellte MailUserTemplate -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $data['Mail']['sender_email']   = Yii::app()->settings->get('Mail', 'systemMail')['address'];
        $data['Mail']['reciever_email'] = 'Template';

        $mailController = new MailController('mail');
        $mail           = $mailController->create($data, true);

        if (null === $mail) {
            return null;
        }
        $data['MailUserTemplate']['mail_id']        = $mail->id;
        $data['MailUserTemplate']['user_id']        = Yii::app()->user->getID();
        $data['MailUserTemplate']['last_edit_date'] = ViewHelper::getDate(true);
        if (!isset($data['MailUserTemplate']['description']) || $data['MailUserTemplate']['description'] == '') {
            $data['MailUserTemplate']['description'] = 'Vorlage';
        }

        if (empty($data['MailUserTemplate']['product_combo_id'])) {
            $data['MailUserTemplate']['product_combo_id'] = null;
        }

        $mailusertemplate = MailUserTemplate::model()->findByAttributes([
                                                                            'user_id'           => Yii::app()->user->getId(),
                                                                            'mail_procedure_id' => $data['MailUserTemplate']['mail_procedure_id'],
                                                                            'name'              => $data['MailUserTemplate']['name']
                                                                        ]);
        if (empty($mailusertemplate)) {
            $mailusertemplate = new MailUserTemplate();
        }
        $mailusertemplate->attributes = $data['MailUserTemplate'];

        if ($mailusertemplate->save()) {
            return $mailusertemplate;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein MailUserTemplate -mailusertemplate aktualisiert werden soll.
     *  Die Methode öffnet das MailUserTemplate -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden MailUserTemplate -mailusertemplates
     */
    public function actionUpdate($id)
    {
        $mailusertemplate = $this->loadModel($id);
        if ($mailusertemplate != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($mailusertemplate = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $mailusertemplate;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das MailUserTemplate-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden MailUserTemplate-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $mailusertemplate = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->renderPartial('//home/<USER>/vorgaenge/vorlagen/mail/_formFields', ['mailusertemplate' => $mailusertemplate, 'form' => new CActiveForm(), 'prefix' => 'Create']);
        }

        $formulardata                           = $_POST;
        $formulardata['MailUserTemplate']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein MailUserTemplate-Exemplar
     *
     * @param type $data
     *          Die MailUserTemplate-Daten als Array
     *
     * @return $mailusertemplate
     *          Das aktualisierte MailUserTemplate-Exemplar falls das mailusertemplate aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $mailusertemplate             = $this->loadModel($data['MailUserTemplate']['id']);
            $mailusertemplate->attributes = $data['MailUserTemplate'];
            if ($mailusertemplate->save()) {
                return $mailusertemplate;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all mailusertemplates.
     */
    public function actionAdmin()
    {
        $mailusertemplate = new MailUserTemplate('search');
        $mailusertemplate->unsetAttributes();  // clear any default values
        if (isset($_GET['MailUserTemplate'])) {
            $mailusertemplate->attributes = $_GET['MailUserTemplate'];
        }

        $this->render('//home/<USER>/vorgaenge/vorlagen/mail/admin', [
            'mailusertemplate' => $mailusertemplate,
        ]);
    }

    /**
     * Löscht das MailUserTemplate-mailusertemplate mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden MailUserTemplate-mailusertemplates
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        /** @var MailUserTemplate $template */
        $template = MailUserTemplate::model()->findByPk($id);
        if (Yii::app()->user->isAdmin() || $template->isMine(Yii::app()->user->id)) {
            $template->delete();
        }

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lädt das MailUserTemplate-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des MailUserTemplate-mailusertemplates mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadmailusertemplate($id)
    {
        $mailusertemplate = MailUserTemplate::model()->findByPk($id);

        return $mailusertemplate;
    }

    /**
     * Performs the AJAX validation.
     *
     * @param MailUserTemplate $mailusertemplate the mailusertemplate to be validated
     */
    protected function performAjaxValidation($mailusertemplate)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'mail-user-template-form') {
            echo CActiveForm::validate($mailusertemplate);
            Yii::app()->end();
        }
    }
}
