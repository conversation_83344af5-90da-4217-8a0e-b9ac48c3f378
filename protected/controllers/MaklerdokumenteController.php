<?php

declare(strict_types=1);

use Apfelbox\FileDownload\FileDownload;
use Auth\Services;
use Business\Client\Factory\ClientFactory;
use Business\User\UserProfileDocument\Exceptions\DuplicateFieldNameException;
use Business\User\UserProfileDocument\Exceptions\UnknownEncryptionException;
use Business\User\UserProfileDocument\Factory\CreateSignatureProcessDtoFactory;
use Business\User\UserProfileDocument\Factory\DocumentDtoFactory;
use Business\User\UserProfileDocument\PersonalizedUserProfileDocument;
use Business\User\UserProfileDocument\templates\Templates;
use Business\User\UserProfileDocument\UseCases\GetClientEinverstaendnisStatus;
use Business\User\UserProfileDocument\UseCases\RequestClientEinverstaendnis;
use Business\User\UserProfileDocument\UseCases\UploadToSign;
use Business\User\UserProfileDocument\UseCases\UploadToSignAndRequestSignatures;
use Business\User\UserProfileDocument\UserProfileDocument;
use Components\Authentication\OAuth\OAuthAccessible;
use Components\DemvSign\SignGatewayFactory;
use Components\File\Path\Specifications\IsInDirectorySpecification;
use Components\Log\LoggerFactory;
use Demv\JSend\ResponseFactory;
use Illuminate\Http\Response;
use League\Flysystem\Adapter\Local;
use League\Flysystem\Filesystem;
use Monolog\Formatter\LineFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Sign\Exceptions\TemplateException;
use Sign\UseCases\CreateTemplate;
use Sign\UseCases\DeleteTemplate;
use Sign\UseCases\LoadTemplate;
use Psr\Log\LoggerInterface;
use Sign\UseCases\CreateSignatureProcess;
use Sign\UseCases\RequestSignatures;
use function Sentry\captureException;

class MaklerdokumenteController extends GlobalRightController implements OAuthAccessible
{
    private const ERROR_MESSAGE = '<strong>Das PDF konnte nicht generiert werden.</strong><br>Bei der Umwandlung ' .
        'der Datei ist ein Formatierungsfehler aufgetreten. Versuchen Sie die originale Datei zu vereinfachen, ' .
        'in dem Sie Tabellen, große Bilder und besondere Schriftarten entfernen. Laden Sie die Datei anschließend ' .
        'erneut hoch.';

    public $excludedActions = [
        'generateBrokerLetter',
        'uploadToSign',
        'createTemplate',
    ];
    public $parentid        = 'Profile';
    public $uploadDir;

    private LoggerInterface $logger;

    /**
     *
     */
    public function init()
    {
        $this->uploadDir = Yii::getPathOfAlias('uploads.maklerauftrag') . DIRECTORY_SEPARATOR . Yii::app()->user->id;
        $this->initDir($this->uploadDir);

        $this->logger  = LoggerFactory::new('upload', 'MaklerDocController');
        $streamHandler = new StreamHandler('php://stdout', Logger::INFO);
        $streamHandler->setFormatter(new LineFormatter("[%datetime%] %channel%.%level_name%: %message%\n"));
        $this->logger->pushHandler($streamHandler);
    }

    public function initDir($dir)
    {
        if (!is_dir($dir)) {
            $dirMode = 0775;

            mkdir($dir, $dirMode, true);
            chmod($dir, $dirMode);
        }
    }

    /**
     * Creates a new model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     */
    public function actionCreate()
    {
        $documentTypeId = (int) $_POST['document_type_id'];

        if (!DocumentType::isValidForUserProfile($documentTypeId)) {
            Yii::app()->user->setFlash(
                'error',
                'Ungültiger Dokumenttyp, das Dokument (M-K) konnte nicht gespeichert werden.'
            );
        } elseif (isset($_POST['UserProfileDocuments'])) {
            $postVars = $_POST['UserProfileDocuments'];

            if (isset($postVars['file']) && isset($postVars['display_filename'])) {
                $filename    = $postVars['file'];
                $displayname = $postVars['display_filename'];

                $model                    = new UserProfileDocuments();
                $model->file              = $filename;
                $model->display_filename  = $displayname;
                $model->document_type_id  = $documentTypeId;
                if ($model->save()) {
                    Yii::app()->user->setFlash(
                        'success',
                        'Das Dokument (M-K) wurde gespeichert. Bitte überprüfen Sie Ihr Dokument über die ' .
                        '<strong>Vorschau-Option</strong>'
                    );
                } else {
                    Yii::app()->user->setFlash('error', 'Das Dokument (M-K) konnte nicht gespeichert werden.');
                }
            }
        }

        $this->redirect(['profile/update', 'active_tab' => ProfilTabs::MAKLERDOKUMENTE]);
    }

    /**
     * Deletes a particular model.
     * If deletion is successful, the browser will be redirected to the 'admin' page.
     *
     * @param int $id the ID of the model to be deleted
     */
    public function actionDelete($id)
    {
        $file = $this->loadModel($id);
        $path = $this->uploadDir . DS . $file->file;

        if ((new IsInDirectorySpecification($this->uploadDir))->isSatisfiedByFilename($path)) {
            $filesys = new Filesystem(new Local(dirname($path)));
            $filesys->delete($file->file);
        }

        if ($file->delete()) {
            Yii::app()->user->setFlash('success', 'Das Dokument (M-K) wurde gelöscht.');
        } else {
            Yii::app()->user->setFlash('success', 'Das Dokument (M-K) konnte nicht gelöscht werden.');
        }
        $this->redirect(['profile/update', 'active_tab' => ProfilTabs::MAKLERDOKUMENTE]);
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param int $id the ID of the model to be loaded
     *
     * @return UserProfileDocuments
     *          the loaded model
     * @throws CHttpException
     */
    public function loadModel($id)
    {
        /** @var UserProfileDocuments $model */
        $model = UserProfileDocuments::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $model;
    }

    /**
     * Displays a particular model preview.
     */
    public function actionPreviewPdf(int $userProfileDocumentId)
    {
        try {
            $user                = currentUser();
            $model               = UserProfileDocuments::model()->findByPk($userProfileDocumentId);
            $fileInfo            = pathinfo($model->display_filename);
            $fileBasename        = $fileInfo['filename'];
            $fileName            = $fileBasename . '.pdf';
            $userProfileDocument = new UserProfileDocument((int) $model->document_type_id, $user);
            $this->outputPdf($userProfileDocument->getContentFor(ClientFactory::getDummy()), $fileName);
        } catch (UnknownEncryptionException $e) {
            Yii::app()->user->setFlash('error', $e->getMessage());
            $this->redirect(Yii::app()->request->getUrlReferrer());
        } catch (DuplicateFieldNameException $e) {
            Yii::app()->user->setFlash('error', $e->getMessage());
            $this->redirect(Yii::app()->request->getUrlReferrer());
        } catch (Exception $e) {
            Yii::app()->user->setFlash('error', self::ERROR_MESSAGE);
            $this->redirect(Yii::app()->request->getUrlReferrer());
        }
    }

    /**
     * @param int $id
     *
     * @throws CHttpException
     * @throws Exception
     */
    public function actionDownload(int $id)
    {
        $model            = $this->loadModel($id);
        $uploadedDocument = $model->getFullFilePath();

        if (
            !file_exists($uploadedDocument)
            || !(new IsInDirectorySpecification($this->uploadDir))->isSatisfiedByFilename($uploadedDocument)
        ) {
            throw new CHttpException(404, 'Seite nicht gefunden');
        }

        @FileDownload::createFromFilePath($uploadedDocument)->sendDownload($model->display_filename);
        Yii::app()->end();
    }

    public function actionUpload()
    {
        Yii::import('ext.widgets.ajaxupload.qqFileUploader');
        $folder                     = $this->uploadDir . DS;
        $allowedExtensions          = ['pdf', 'docx'];
        $sizeLimit                  = 10 * 1024 * 1024;
        $filename                   = uuid();
        $uploader                   = new qqFileUploader(
            $allowedExtensions,
            $sizeLimit,
            basename($filename)
        );
        $result                     = $uploader->handleUpload($folder, false, '(%d)');
        $result['display_filename'] = $_GET['qqfile'] ?? 'document';
        echo json_encode($result);
    }

    /**
     * @refactoring Gehört in einen eigenen Controller
     * Erstellt den Maklerauftrag für einen Kunden
     *
     * @param int  $documentTypeId ID des DocumentType Records
     * @param int  $clientId ID des Kunden, für den der Maklerauftrag erstellt werden soll
     * @param bool $docx Maklerauftrag als DOCX
     *
     */
    public function actionGenerateBrokerLetter(int $documentTypeId, int $clientId, bool $docx = false)
    {
        $client = ClientProvider::forUser(currentUser())->load($clientId);
        if ($docx) {
            $personalUserProfileDocument = new PersonalizedUserProfileDocument($documentTypeId, $client->broker);
            $document                    = $personalUserProfileDocument->getDocument();
            if ($document === null || !$document->isFileFormat('docx')) {
                Yii::app()->user->setFlash(
                    'error',
                    'Fehler: Das Docx-Dokument konnte nicht gefunden werden.'
                );
                $this->redirect(Yii::app()->request->getUrlReferrer());
            }
            @FileDownload::createFromString($personalUserProfileDocument->getWordContentFor($client))
                ->sendDownload($document->display_filename, true);
        } else {
            try {
                $userProfileDocument = new UserProfileDocument($documentTypeId, $client->broker);
                $filename            = $userProfileDocument->getPdfDisplayFilename();
                $this->outputPdf($userProfileDocument->getContentFor($client), $filename);
            } catch (Exception $e) {
                Yii::app()->user->setFlash('error', self::ERROR_MESSAGE);
                $this->redirect(Yii::app()->request->getUrlReferrer());
            }
        }
        Yii::app()->end();
    }

    private function outputPdf(string $content, string $filename = ''): void
    {
        if (empty($filename)) {
            $filename = 'Standarddokument.pdf';
        }
        if (ob_get_level() > 0) {
            ob_clean();
        }
        header('Content-type: application/pdf');
        header(sprintf('Content-disposition: inline; filename=%s', $filename));
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        echo $content;
        Yii::app()->end();
    }

    public function actionTemplate(string $templateId, string $extension)
    {
        $template = Templates::loadFromDisk()->find($templateId);

        if ($template === null || !$template->fileExists($extension)) {
            Yii::app()->user->setFlash('error', 'Beim Herunterladen der Datei ist ein Fehler aufgetreten.');
            $this->redirect(Yii::app()->request->getUrlReferrer());
        }
        @FileDownload::createFromFilePath($template->getAbsolutePath($extension))->sendDownload($template->getFileName($extension));
    }

    /**
     * @param int $clientId
     *
     * @throws CHttpException
     */
    public function actionUploadToSign(int $clientId)
    {
        $client = ClientProvider::forUser(currentUser())->load($clientId);

        /** @var string[] $documentTypeIds */
        $documentTypeIds = Yii::app()->request->getParam('documentTypeIds');

        $uploadToSign = new UploadToSign(
            new CreateSignatureProcess(SignGatewayFactory::create()),
            new DocumentDtoFactory(),
            new CreateSignatureProcessDtoFactory()
        );
        try {
            if (!$documentTypeIds || !is_array($documentTypeIds)) {
                throw new CHttpException(Response::HTTP_BAD_REQUEST, 'documentTypeIds is missing');
            }

            $signatureProcess = $uploadToSign->handle($client, $documentTypeIds);

            if ($signatureProcess === null) {
                throw new CHttpException(Response::HTTP_INTERNAL_SERVER_ERROR, 'Signature process could not be created');
            }

            $this->redirect($signatureProcess->url_prepare);
        } catch (Exception $e) {
            captureException($e);
            $content  = $e->getMessage() . "\r\n";
            $content .= '[File: ' . $e->getFile() . ' (Line: ' . $e->getLine() . ')]' . "\r\n";
            $content .= str_repeat('-', 80) . "\r\n\r\n";
            $content .= $e->getTraceAsString();

            $this->logger->error($content);
        }

        Yii::app()->user->setFlash('error', 'Dokumente (M-K) konnten nicht hochgeladen werden');
        $this->redirect(Yii::app()->request->getUrlReferrer());
    }

    /**
     * @param int $clientId
     *
     * @throws CHttpException
     */
    public function actionRequestEinverstaendnis(int $clientId)
    {
        $client = ClientProvider::forUser(currentUser())->load($clientId);

        $signGateway = SignGatewayFactory::create();

        $requestClientEinverstaendnis = new RequestClientEinverstaendnis(
            new UploadToSignAndRequestSignatures(
                new CreateSignatureProcess($signGateway),
                new RequestSignatures($signGateway),
                new DocumentDtoFactory(),
                new CreateSignatureProcessDtoFactory()
            ),
            new GetClientEinverstaendnisStatus()
        );

        try {
            $requestClientEinverstaendnis->handle($client);
            ResponseFactory::instance()->success()->respond();
        } catch (Exception $e) {
            captureException($e);
            $content  = $e->getMessage() . "\r\n";
            $content .= '[File: ' . $e->getFile() . ' (Line: ' . $e->getLine() . ')]' . "\r\n";
            $content .= str_repeat('-', 80) . "\r\n\r\n";
            $content .= $e->getTraceAsString();

            $this->logger->error($content);

            ResponseFactory::instance()->error(['message' => $e->getMessage()])->respond();
        }
    }

    public function actionEinverstaendnisStatus(int $clientId): void
    {
        $client                         = ClientProvider::forUser(currentUser())->load($clientId);
        $getClientEinverstaendnisStatus = new GetClientEinverstaendnisStatus();

        $status = $getClientEinverstaendnisStatus->handle($client);

        ResponseFactory::instance()->success($status->jsonSerialize())->respond();
    }

    /**
     * http://professionalworks.demv.internal/maklerdokumente/createTemplate/{id}
     *
     * @param int $id Die Id des zugehörigen UserProfileDocuments
     */
    public function actionCreateTemplate(int $id)
    {
        $document = UserProfileDocuments::model()->findByPk($id);
        if ($document === null) {
            throw new CHttpException(404, 'Seite nicht gefunden');
        }

        $userProfileDocument = new UserProfileDocument((int) $document->document_type_id, currentUser());
        $content             = $userProfileDocument->getContentFor(ClientFactory::getDummy());
        $createTemplate      = new CreateTemplate(currentUser(), SignGatewayFactory::create());

        try {
            $editTemplateUrl = $createTemplate->handle(
                basename($userProfileDocument->getFullFilePath()),
                $content,
                (int) $document->documentType->id,
                'Maklerprofil'
            );

            $this->redirect($editTemplateUrl);
        } catch (TemplateException $e) {
            Yii::app()->user->setFlash('error', 'Die Vorlage konnte nicht erstellt werden.');

            // Use the "plain" layout, so the flash message doesn't get rendered here
            $this->layout = '//layouts/plain';
            $this->render('//stammdaten/sonstige/profil/maklerdokumente/open-tab-and-redirect', [
                'redirect' => Yii::app()->controller->createAbsoluteUrl(
                    'profile/update',
                    ['active_tab' => ProfilTabs::MAKLERDOKUMENTE]
                ),
            ]);
        }
    }

    /**
     * http://professionalworks.demv.internal/maklerdokumente/editTemplate/{id}
     *
     * @param int $id
     *          Die Id der zugehörigen DocumentTemplate
     */
    public function actionEditTemplate(int $id)
    {
        $documentTemplate = DocumentTemplate::model()->findByPk($id);
        $documentType     = $documentTemplate->documentType;
        if ($documentTemplate === null) {
            throw new CHttpException(404, 'Template nicht gefunden');
        }
        $userProfileDocument = new UserProfileDocument((int) $documentType->id, currentUser());
        $content             = $userProfileDocument->getContentFor(ClientFactory::getDummy());
        $loadTemplate        = new LoadTemplate(SignGatewayFactory::create());

        try {
            $editTemplateUrl = $loadTemplate->handle(
                $documentTemplate,
                $content,
                'Maklerprofil'
            );

            $this->redirect($editTemplateUrl);
        } catch (TemplateException $e) {
            Yii::app()->user->setFlash('error', 'Die Vorlage konnte nicht geladen werden.');

            // Use the "plain" layout, so the flash message doesn't get rendered here
            $this->layout = '//layouts/plain';
            $this->render('//stammdaten/sonstige/profil/maklerdokumente/open-tab-and-redirect', [
                'redirect' => Yii::app()->controller->createAbsoluteUrl(
                    'profile/update',
                    ['active_tab' => ProfilTabs::MAKLERDOKUMENTE]
                )
            ]);
        }
    }

    /**
     * http://professionalworks.demv.internal/maklerdokumente/deleteTemplate/{id}
     *
     * @param int $id
     *          Die Id der Template
     */
    public function actionDeleteTemplate(int $id)
    {
        $model = DocumentTemplate::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'Seite nicht gefunden');
        }

        $useCase = new DeleteTemplate($model);

        if ($useCase->handle()) {
            Yii::app()->user->setFlash('success', 'Die Vorlage wurde gelöscht.');
        } else {
            Yii::app()->user->setFlash('error', 'Die Vorlage konnte nicht gelöscht werden.');
        }

        $this->redirect(['profile/update', 'active_tab' => ProfilTabs::MAKLERDOKUMENTE]);
    }

    public static function getAllowedServices(): array
    {
        return Services::DEMV_INTERNAL_SERVICES;
    }
}
