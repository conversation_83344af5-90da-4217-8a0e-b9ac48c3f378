<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of MasterLoginController
 *
 * <AUTHOR>
 */
class MasterloginController extends AdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    public function actionlogin()
    {
        $params = Yii::app()->request->getParam('Masterlogin');
        if (empty($params)) {
            $this->render('//stammdaten/sonstige/masterlogin/login', []);
            Yii::app()->end();
        }
        $user_id = $params['user_id'];
        if (SuperAdmin::includesId((int) $user_id) && !Yii::app()->user->isSuperAdmin()) {
            Yii::app()->user->setFlash('error', 'Der Login als der ausgewählter Nutzer steht dir nicht zur Verfügung');
            $this->redirect(Yii::app()->createUrl($params['url'] ?? 'home'));
        }
        if ($this->login($user_id)) {
            $this->redirect(Yii::app()->createUrl($params['url'] ?? 'home'));
        }

        $this->render('//stammdaten/sonstige/masterlogin/login', ['user_id' => $user_id, 'error' => true]);
    }

    private function login($id)
    {
        $systemuser = Systemuser::model()->findByPk($id);
        if (!empty($systemuser)) {
            $this->logUsage($id);
            Yii::app()->getModule('login')->forceUserLogin($systemuser);
            Yii::app()->user->setIsMasterLogin(true);

            return true;
        }

        return false;
    }

    private function logUsage($id)
    {
        $masterloginUsage                = new MasterloginUsage('create');
        $masterloginUsage->login_user_id = $id;
        $masterloginUsage->save();
    }
}
