<?php

declare(strict_types=1);

use Components\Access\PublicAccessibleInterface;
use Components\Authentication\BasicAuth\BasicAuthAccessible;

class MetricsController extends Controller implements PublicAccessibleInterface, BasicAuthAccessible
{
    public function init()
    {
        parent::init();

        $this->defaultAction = 'metrics';
    }

    public function actionMetrics()
    {
        Yii::app()->metrics->renderMetrics();
    }

    public function getPublicActions(): array
    {
        return ['metrics'];
    }
}
