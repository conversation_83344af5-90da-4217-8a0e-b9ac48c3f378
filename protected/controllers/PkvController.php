<?php

class PkvController extends Controller
{
    public $menuGroup = MenuGroup::PKV;

    public function actions()
    {
        // return external action classes, e.g.:
        return [
            'show'           => 'application.components.actions.Content',
            'anfrage'        => ['class' => 'application.components.actions.Request', 'menuId' => Menu::PKV],
            'angebot'        => ['class' => 'application.components.actions.Request', 'menuId' => Menu::PKV, 'offer' => true],
        ];
    }

    public function actionIndex()
    {
        $this->render('index');
    }
}
