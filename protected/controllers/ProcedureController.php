<?php

class ProcedureController extends AllowAllController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout             = '//layouts/column2';
    public $modelname          = 'ProcedureFiles';
    public $directoryname      = 'procedure';
    public $directoryattribute = 'procedure_id';

    private $procedurename  = 'Procedure';
    private $controllername = 'procedure';

    public static function getProcedureNumber($userid, $prefix = '', $contextModel = null, $contextAttr = null)
    {
        if (!empty($prefix)) {
            $prefix .= '-';
        }
        $systemuser = Systemuser::model()->findByPk($userid);
        $userpart   = '';
        if (null !== $systemuser) {
            if ($systemuser->assignment_agency == 1) {
                $userpart = $systemuser->agency->name;
            } else {
                $userpart = $systemuser->company->name;
            }
            if (strlen($userpart ?? '') > 3) {
                $userpart = substr($userpart, 0, 3);
            }
            if ($userpart != '') {
                $userpart .= '-';
            }
        }
        $random = 0;
        for ($i = 0; $i < 6; $i++) {
            $n      = rand(0, 9);
            $random = $random * 10 + $n;
        }
        $name = $prefix . $userpart . $random;
        if (!empty($contextModel) && !empty($contextAttr)) {
            do {
                $random = 0;
                for ($i = 0; $i < 6; $i++) {
                    $n      = rand(0, 9);
                    $random = $random * 10 + $n;
                }
                $name = $prefix . $userpart . $random;
            } while (null !== $contextModel::model()->findByAttributes([$contextAttr => $name]));
        }

        return $name;
    }

    /**
     * Erstellt eine neue Procedure. Der Name wird aus den angegebenen Parametern und einer Zufallszahl generiert
     * Der generierte Procedure->name ist immer eindeutig
     *
     * @param integer $userid
     * @param integer $clientid *
     * @param integer $insurancecompanyid
     * @param integer $productcomboid
     *
     * @return \Procedure
     */
    public static function generateNewProcedure($userid, $clientid = null, $insurancecompanyid = null, $productcomboid = null)
    {
        return ProcedureGenerator::generateNew($userid);
    }

    public static function getProcedureHTMLEditorSettings($allowFontsettings = true)
    {
        $fontsettings = $allowFontsettings ? 'fontselect fontsizeselect' : '';
        $settings     = [
            'menubar'      => '"tools table format view edit"',
            'plugins'      => '"advlist autolink lists image link charmap print hr anchor pagebreak",
                "searchreplace wordcount visualblocks visualchars code fullscreen",
                "insertdatetime media nonbreaking save table directionality",
                "emoticons template paste moxiemanager textcolor"',
            'toolbar1'     => '"print preview undo redo | underline bold italic forecolor  backcolor | alignleft aligncenter alignright alignjustify | link |bullist numlist outdent indent | styleselect formatselect ' . $fontsettings . '"',
            'image_advtab' => 'true',
        ];

        return $settings;
        //        return false;
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $procedureFilesController = new ProcedureFilesController('procedurefiles');
        $procedureFilesController->deleteProcedureFiles($id);
        $this->loadModel($id)->delete();
    }

    /**
     * Lädt das Procedure-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des Procedure-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $procedure = Procedure::model()->findByPk($id);

        return $procedure;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues Procedure-Model erstellt werden soll.
     *  Die Methode öffnet das Procedure-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($procedure = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $procedure;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das Procedure -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des Procedure -Models
     */
    private function getCreateFormularData()
    {
        if (!$this->validData($_POST)) {
            $this->render('//home/<USER>/vorgaenge/vorgang/formContainer');
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model Procedure handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
               isset($data['Procedure']);
        //
    }

    /**
     * Diese Methode erstellt ein neues Procedure -Model
     *
     * @param type $data
     *          Die Procedure -Daten als Array
     *
     * @return $procedure
     *          Das erstellte Procedure -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $procedure             = new Procedure();
        $procedure->attributes = $data['Procedure'];

        if ($procedure->save()) {
            return $procedure;
        }

        return null;
    }

    public function actionTemplateAdmin()
    {
        $this->render('//home/<USER>/vorgaenge/vorgang/templateAdmin');
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein Procedure -Model aktualisiert werden soll.
     *  Die Methode öffnet das Procedure -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden Procedure -Models
     */
    public function actionUpdate($id)
    {
        $procedure = $this->loadModel($id);
        if ($procedure != null) {
            Yii::app()->session['vorgaenge.list.lastDetailOpened'] = $id;
            $data                                                  = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($procedure = $this->update($data) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $procedure;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das Procedure-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden Procedure-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $procedure = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('//home/<USER>/vorgaenge/vorgang/formUpdate', ['procedure' => $procedure]);
        }

        $formulardata                    = $_POST;
        $formulardata['Procedure']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein Procedure-Exemplar
     *
     * @param type $data
     *          Die Procedure-Daten als Array
     *
     * @return $procedure
     *          Das aktualisierte Procedure-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $procedure             = $this->loadModel($data['Procedure']['id']);
            $procedure->attributes = $data['Procedure'];
            if ($procedure->save()) {
                return $procedure;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all procedures.
     */
    public function actionAdmin($useLastSearch = false, $contract_id = null)
    {
        $selected = Yii::app()->session->get('vorgaenge.list.lastDetailOpened', null);

        $procedure = new Procedure('search');
        $procedure->unsetAttributes();  // clear any default values
        if ($useLastSearch) {
            if (method_exists($procedure, 'useLastSearch')) {
                $procedure->useLastSearch();
            }
        } else {
            if (isset($_GET['Procedure'])) {
                $procedure->attributes = Yii::app()->request->getParam('Procedure');
            } elseif (null !== $contract_id) {
                $procedure->loadContractSearch($contract_id);
            } else {
                $procedure->status = 'open';
            }
        }

        $this->render('//home/<USER>/vorgaenge/vorgang/admin', [
            'procedure' => $procedure,
            'selected'  => $selected
        ]);
    }

    /**
     * Löscht das Procedure-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden Procedure-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        $this->redirect($_POST['returnUrl'] ?? ['procedure/admin']);
    }

    public function collectGabadge($ids)
    {
        if (is_array($ids)) {
            $systemusers = Systemuser::model()->findAllByPk($ids);
        } else {
            $systemuser    = Systemuser::model()->findByPk($ids);
            $systemusers[] = $systemuser;
        }

        $time = time();
        foreach ($systemusers as $systemuser) {
            foreach ($systemuser->procedures as $procedure) {
                //Wenn der Vorgang älter als 2 Stunden ist und Leer
                if ($procedure->Empty) {
                    $this->delete($procedure->id);
                }
            }
        }
    }

    public function actionCollectGarbadge($id)
    {
        set_time_limit(0);
        $procedures = Procedure::model()->findAllByAttributes(['user_id' => $id]);
        foreach ($procedures as $proc) {
            if ($proc->getEmpty()) {
                $this->delete($proc->id);
            }
        }
    }

    public function actionAjaxFinishProcedures()
    {
        $ids        = $_POST['ids'];
        $procedures = Procedure::model()->findAllByPk($ids);

        if (empty($procedures)) {
            echo 0;
            Yii::app()->end();
        }

        foreach ($procedures as $procedure) {
            $command = Procedure::model()->dbConnection->createCommand();
            $command->update('user_mail', ['mail_status_id' => MailStatus::DONE], 'procedure_id = ' . $procedure->id);
            $res      = $command->execute();
            $command2 = Procedure::model()->dbConnection->createCommand();
            $res2     = $command2->update('user_writing', ['writing_status_id' => WritingStatus::DONE], 'procedure_id = ' . $procedure->id);
            $command2->execute();
            NotificationCenter::getInstance()->updateNotification(new UserMail());
        }

        echo 1;
        Yii::app()->end();
    }

    public function actionAjaxReopenProcedure()
    {
        $id        = $_POST['id'];
        $procedure = Procedure::model()->findByPk($id);
        if (!empty($procedure)) {
            $command = Procedure::model()->dbConnection->createCommand();
            $command->update('user_mail', ['mail_status_id' => MailStatus::OPEN], 'procedure_id = ' . $id);
            $res      = $command->execute();
            $command2 = Procedure::model()->dbConnection->createCommand();
            $res2     = $command2->update('user_writing', ['writing_status_id' => WritingStatus::OPEN], 'procedure_id = ' . $id);
            $command2->execute();
            NotificationCenter::getInstance()->updateNotification(new UserMail());
            echo 1;
        } else {
            echo 0;
        }
        Yii::app()->end();
    }

    public function actionAjaxGetOriginalContent()
    {
        $procedure_id = Yii::app()->request->getPost('id');
        $content      = '<div class="alert alert-error">Der Inhalt konnte nicht geladen werden.</div>';
        $infos        = [];
        if (!empty($procedure_id)) {
            //Darf der Vorgang gesehen werden?
            $procedure = Procedure::model()->findByPk($procedure_id);
            if (!empty($procedure)) {
                //Erste Mail
                $user_mail = UserMail::model()->findByAttributes(['procedure_id' => $procedure_id], ['order' => 'id']);
                //Erster Brief
                $criteria = new CDbCriteria();
                $criteria->compare('procedure_id', $procedure_id);
                $criteria->order = 'id';
                if (!empty($user_mail->send_date)) {
                    $date = UtilHelper::parseToDBDate($user_mail->send_date);
                    $criteria->addCondition("last_edit_date <'$date'");
                }
                $user_writing = UserWriting::model()->find($criteria);
                $subject      = '';
                if (!empty($user_writing)) {
                    $object    = $user_writing;
                    $content   = $user_writing->content;
                    $status    = $user_writing->writingStatus->name;
                    $procedure = $user_writing->writingProcedure->name;
                    $receiver  = $user_writing->getrecieverAddress()->Address;
                } else {
                    if (!empty($user_mail)) {
                        $content   = '';
                        $send_time = $user_mail->getSendTime();
                        if (!empty($send_time)) {
                            $send_info = $user_mail->getSendTime();
                        } else {
                            $send_info = UtilHelper::parseToDEDate($user_mail->last_edit_date);
                        }
                        $content .= $user_mail->mail->getContent();

                        $status    = $user_mail->mailStatus->name;
                        $procedure = $user_mail->mailProcedure->name;
                        $receiver  = $user_mail->mail->reciever_email;
                        $object    = $user_mail;
                        $subject   = $user_mail->mail->subject;
                    }
                }
                if (!empty($object)) {
                    $infos                   = [];
                    $infos['Verfasst von']   = @$object->user->Fullname;
                    $infos['Gesendet am']    = !empty($send_info) ? $send_info : null;
                    $infos['Vorgang']        = $procedure;
                    $infos['Status']         = $status;
                    $infos['An']             = $receiver;
                    $infos['Gesellschaft']   = @$object->insuranceCompany->name;
                    $infos['Kunde']          = @$object->client->Fullname;
                    $infos['Kommentar']      = @$object->comment;
                    $infos['Sparte']         = @$object->productCombo->name;
                    $infos['Vertragsnummer'] = @$object->contract->contract_nr;
                }
            }
        }
        echo '<table class="items table table-striped table-bordered table-condensed table-hover">';
        foreach ($infos as $key => $value) {
            if (!empty($value)) {
                echo '<tr>';
                echo '<td>' . $key . ':</td>';
                echo '<td>' . $value . '</td>';
                echo '</tr>';
            }
        }
        echo '</table>';
        if (!empty($subject)) {
            echo '<b>' . $subject . '</b><br /><br />';
        }
        echo $content;
        Yii::app()->end();
    }

    public function actionCreateDamage()
    {
        $data     = Yii::app()->request->getPost('Damage');
        $usermail = null;
        $success  = false;
        if (!empty($data)) {
            $usermail = UserMail::model()->findByPk($data['user_mail_id']);
            if (!empty($usermail)) {
                $contract = $usermail->contract;
                if (!empty($contract)) {
                    $damage_nr = !empty($data['damage_nr']) ? $data['damage_nr'] : null;
                    $success   = $contract->createDamage($damage_nr);
                }
            }
        }
        if ($success) {
            Yii::app()->user->setFlash('success', 'Der Schaden wurde erfolgreich angelegt.');
        } else {
            Yii::app()->user->setFlash('error', 'Es konnte kein Schaden angelegt werden.');
        }
        if (!empty($usermail->id)) {
            $this->redirect(['usermail/update', 'id' => $usermail->id]);
        } else {
            $this->redirect(['procedure/admin', 'useLastSearch' => 1]);
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Procedure $procedure the procedure to be validated
     */
    protected function performAjaxValidation($procedure)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'procedure-form') {
            echo CActiveForm::validate($procedure);
            Yii::app()->end();
        }
    }

    //Redirect zu neuen Vorgängen
    public function actionVorgaenge2()
    {
        $this->redirect(env('VORGAENGE_URL'));
    }
}
