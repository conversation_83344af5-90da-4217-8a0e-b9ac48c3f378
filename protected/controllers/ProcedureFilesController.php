<?php

class ProcedureFilesController extends FileController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout              = '//layouts/column2';
    public $modelname           = 'ProcedureFiles';
    public $directoryname       = 'procedure';
    public $directoryattribute  = 'procedure_id';
    public $parentid            = 'Procedure';
    public $excludedActions     = [
        'AfterUpload',
    ];
    private $procedurefilesname = 'ProcedureFiles';
    private $controllername     = 'procedureFiles';

    public function actionAfterUpload()
    {
        $this->render('afterUpload',
            []
        );
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues ProcedureFiles-Model erstellt werden soll.
     *  Die Methode öffnet das ProcedureFiles-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate($id)
    {
        $data = $this->getCreateFormularData($id);
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if (($procedurefiles = $this->create($data)) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
//                    $this->redirect('admin');
                    Yii::app()->user->setFlash('success', 'Die Datei wurde erfolgreich hochgeladen');

                    return $procedurefiles;
                }
                Yii::app()->user->setFlash('alert', 'Die Datei konnte nicht hochgeladen werden');

                return null;
                $this->redirect('afterUpload');
            }
        }
    }

    /**
     *  Öffnet das ProcedureFiles -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des ProcedureFiles -Models
     */
    private function getCreateFormularData($procedureid)
    {
        $formulardata   = [];
        $procedurefiles = new ProcedureFiles();

        if (!$this->validData($_POST)) {
//            $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'procedureFiles' . DIRECTORY_SEPARATOR . '_form.php',
//                    array('procedurefiles' => $procedurefiles, 'procedureid'    => $procedureid));
            $this->render('_form',
                ['procedurefiles' => $procedurefiles, 'procedureid' => $procedureid]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model ProcedureFiles handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['ProcedureFiles'])
        ;
//
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein ProcedureFiles -Model aktualisiert werden soll.
     *  Die Methode öffnet das ProcedureFiles -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden ProcedureFiles -Models
     */
    public function actionUpdate($id)
    {
        $procedurefiles = $this->loadModel($id);
        if ($procedurefiles != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if (($procedurefiles = $this->update($data)) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $procedurefiles;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das ProcedureFiles-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des ProcedureFiles-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $procedurefiles = ProcedureFiles::model()->findByPk($id);

        return $procedurefiles;
    }

    /**
     *
     * Öffnet das ProcedureFiles-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden ProcedureFiles-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $procedurefiles = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('update', ['procedurefiles' => $procedurefiles]);
        }

        $formulardata                         = $_POST;
        $formulardata['ProcedureFiles']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein ProcedureFiles-Exemplar
     *
     * @param type $data
     *          Die ProcedureFiles-Daten als Array
     *
     * @return $procedurefiles
     *          Das aktualisierte ProcedureFiles-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $procedurefiles             = $this->loadModel($data['ProcedureFiles']['id']);
            $procedurefiles->attributes = $data['ProcedureFiles'];
            if ($procedurefiles->save()) {
                return $procedurefiles;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all procedurefiless.
     */
    public function actionAdmin()
    {
        $procedurefiles = new ProcedureFiles('search');
        $procedurefiles->unsetAttributes();  // clear any default values
        if (isset($_GET['ProcedureFiles'])) {
            $procedurefiles->attributes = $_GET['ProcedureFiles'];
        }

        $this->render('admin', [
            'procedurefiles' => $procedurefiles,
        ]);
    }

    /**
     * Löscht das ProcedureFiles-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden ProcedureFiles-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    public function deleteProcedureFiles($procedureid)
    {
        $procedurefiles = ProcedureFiles::model()->findAllByAttributes(['procedure_id' => $procedureid]);
        foreach ($procedurefiles as $procedurefile) {
            $this->delete($procedurefile->id);
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ProcedureFiles $procedurefiles the procedurefiles to be validated
     */
    protected function performAjaxValidation($procedurefiles)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'procedure-files-form') {
            echo CActiveForm::validate($procedurefiles);
            Yii::app()->end();
        }
    }
}
