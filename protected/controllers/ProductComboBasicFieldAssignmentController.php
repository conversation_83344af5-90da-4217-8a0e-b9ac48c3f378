<?php

class ProductComboBasicFieldAssignmentController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    private $prefix = 'ProductComboBasicFieldAssignment';

    /**
     *  Diese Methode wird aufgerufen wenn ein neues ProductComboBasicFieldAssignment-Model erstellt werden soll.
     *  Die Methode öffnet das ProductComboBasicFieldAssignment-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if ($productComboBasicFieldAssignment = $this->create($data) != null) {
                    $this->redirect('addAdmin');
                    $this->actionAddAdmin($data['ProductComboBasicFieldAssignment']['product_combo_id']);
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    return $productComboBasicFieldAssignment;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das ProductComboBasicFieldAssignment -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des ProductComboBasicFieldAssignment -Models
     */
    private function getCreateFormularData()
    {
        $formulardata                     = [];
        $productComboBasicFieldAssignment = new ProductComboBasicFieldAssignment();
        if (!$this->validData($_POST)) {
            $product_combos                   = $this->getAllProductsAsKeyValue();
            $productComboBasicFieldAssignment = new ProductComboBasicFieldAssignment('search');
            $output                           = $this->renderPartial('//stammdaten/vertragsdaten/grundfelderHinzufuegen/_formFields',
                                                                     [
                                                                         'productComboBasicFieldAssignment' => $productComboBasicFieldAssignment,
                                                                         'product_combos'                   => $product_combos,
                                                                         'prefix'                           => $this->prefix
                                                                     ], true);
            Yii::app()->clientScript->renderBodyEnd($output);
            echo $output;
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model ProductComboBasicFieldAssignment handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
               isset($data['ProductComboBasicFieldAssignment']);
        //
    }

    /**
     * returns a array of key value pairs like CHtml::ListData (need for Dropdownlists)
     *
     * @return
     *         form: array([0] => name1, [1] => name2...) etc.
     */
    public function getAllProductsAsKeyValue()
    {
        $criteria = new CDbCriteria();

        return CHtml::listData(ProductCombo::model()->findAll($criteria,
                                                              ['order' => 'name DESC']), 'id', 'name');
    }

    /**
     * Diese Methode erstellt ein neues ProductComboBasicFieldAssignment -Model
     *
     * @param type $data
     *          Die ProductComboBasicFieldAssignment -Daten als Array
     *
     * @return $model
     *          Das erstellte ProductComboBasicFieldAssignment -Exemplar
     */
    public function create($data)
    {
        $contractFieldController = new ContractFieldController('ContractFieldController');
        $contractField           = $contractFieldController->create($data);

        if ($contractField != null) {
            $productComboBasicFieldAssignment                = new ProductComboBasicFieldAssignment();
            $productComboBasicFieldAssignment->product_combo = $data['ProductComboBasicFieldAssignment']['product_combo_id'];
            $productComboBasicFieldAssignment->field_id      = $contractField->id;

            $contractSort = new ContractSort();
            $contractSort->platziereFeldWeitNachOben($contractField, (int) $data['ProductComboBasicFieldAssignment']['product_combo_id']);

            if ($productComboBasicFieldAssignment->save()) {
                $productComboBasicFieldAssignment->sort = $productComboBasicFieldAssignment->id;
                $productComboBasicFieldAssignment->save();
            } else {
                return null;
            }
        }
    }

    public function actionAddAdmin($productComboID = null)
    {
        $productComboBasicFieldAssignment = new ProductComboBasicFieldAssignment();

        $product_combo_id = $_GET['ProductComboBasicFieldAssignment']['product_combo_id'] ?? null;
        $this->render('//stammdaten/vertragsdaten/grundfelderHinzufuegen/addAdmin',
                      [
                          'product_combos'                   => $this->getAllProductsAsKeyValue(),
                          'prefix'                           => $this->prefix,
                          'product_combo_id'                 => $productComboID == null ? $product_combo_id : $productComboID,
                          'productComboBasicFieldAssignment' => $productComboBasicFieldAssignment
                      ]);
    }

    /**
     * Löscht das ProductComboBasicFieldAssignment-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden ProductComboBasicFieldAssignment-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['addAdmin']);
        }
    }

    /**
     * Lädt das ProductComboBasicFieldAssignment-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des ProductComboBasicFieldAssignment-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = ProductComboBasicFieldAssignment::model()->findByPk($id);

        return $model;
    }

    /*
     * adminview zum hinzufügen neuer Grunfelder
     */

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Delete funktion für selbst erstellte Assignments
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function actionAddDelete($id)
    {
        $model                   = $this->loadModel($id);
        $contractFieldController = new ContractFieldController('ContractFieldController');
        $contractFieldController->delete($model->field_id);
        $model->delete();
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('ProductComboBasicFieldAssignment');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $product_combos = $this->getAllProductsAsKeyValue();

        $productComboBasicFieldAssignment = new ProductComboBasicFieldAssignment('search');
        $productComboBasicFieldAssignment->unsetAttributes();
        if (isset($_GET['ProductComboBasicFieldAssignment'])) {
            $productComboBasicFieldAssignment->attributes = $_GET['ProductComboBasicFieldAssignment'];
        }

        $this->render('//stammdaten/vertragsdaten/grundfelderZuordnen/admin',
                      [
                          'productComboBasicFieldAssignment' => $productComboBasicFieldAssignment,
                          'product_combos'                   => $product_combos,
                          'prefix'                           => $this->prefix,
                      ]);
    }

    public function actionOrderAdmin()
    {
        $productComboBasicFieldAssignment = new ProductComboBasicFieldAssignment();
        $product_combo_id                 = 0;
        if (isset($_POST['product_combo_id'])) {
            $product_combo_id = $_POST['product_combo_id'];
        }

        $this->render('//stammdaten/vertragsdaten/darstellungsreihenfolge/orderAdmin',
                      ['product_combo_id' => $product_combo_id, 'productComboBasicFieldAssignment' => $productComboBasicFieldAssignment]);
    }

    public function actionUpdateBasicFieldAssignment()
    {
        $field_id         = (int) Yii::app()->request->getParam('field_id');
        $product_combo_id = (int) Yii::app()->request->getParam('product_combo_id');
        if (!empty($product_combo_id) && !empty($field_id)) {
            $productModel    = ProductCombo::model()->findByPk($product_combo_id);
            $productModels   = $productModel->getChildren();
            $productModels[] = $productModel;
            if (!empty($productModel)) {
                $mainCriteria = new CDbCriteria();
                $mainCriteria->compare('field_id', $field_id);
                $mainCriteria->compare('product_combo', $productModel->id);
                $delete = ProductComboBasicFieldAssignment::model()->exists($mainCriteria);
            }

            foreach ($productModels as $model) {
                $criteria = new CDbCriteria();
                $criteria->compare('field_id', $field_id);
                $criteria->compare('product_combo', $model->id);
                if (!$delete) {
                    $cfIDs = [];
                    foreach ($model->assignedGDVProducts(true) as $gdvModel) {
                        $cfIDs = array_merge($cfIDs, $gdvModel->contractFields());
                    }
                }

                if ($delete && ProductComboBasicFieldAssignment::model()->exists($criteria)) {
                    $existingModels = ProductComboBasicFieldAssignment::model()->findAll($criteria);
                    foreach ($existingModels as $toDeleteModel) {
                        $toDeleteModel->delete();
                    }
                } elseif (!$delete && !ProductComboBasicFieldAssignment::model()->exists($criteria) && in_array($field_id,
                                                                                                                $cfIDs)
                ) {
                    $toSaveModel                = new ProductComboBasicFieldAssignment('search');
                    $toSaveModel->field_id      = $field_id;
                    $toSaveModel->product_combo = $model->id;
                    if ($toSaveModel->save()) {
                        $toSaveModel->sort = $toSaveModel->id;
                        $toSaveModel->save();
                    }
                }
            }
        }
    }

    public function actions()
    {
        return [
            'OrderContractFields' => [
                'class' => 'ext.yiisortablemodel.actions.AjaxSortingAction',
            ],
        ];
    }

    /**
     * Dragged == kleinerer Eintrag
     */
    public function actionOrderContractFields()
    {
        $dragdown = true;

        $draggedField  = ProductComboBasicFieldAssignment::model()->findByPk($_POST['dragged_item_id']);
        $replacedField = ProductComboBasicFieldAssignment::model()->findByPk($_POST['replacement_item_id']);

        $draggedSortID  = $draggedField->sort;
        $replacedSortID = $replacedField->sort;
        //wenn von unten nach oben gezogen wird
        if ($draggedSortID > $replacedSortID) {
            $dragdown       = false;
            $replacedSortID = $draggedField->sort;
            $draggedSortID  = $replacedField->sort;
        }
        $product_combo_id = $draggedField->product_combo;
        $contracts        = ProductComboBasicFieldAssignment::model()->findAllByAttributes([
                                                                                               'sort' => range($draggedSortID,
                                                                                                               $replacedSortID)
                                                                                           ]);

        foreach ($contracts as $value) {
            if (count($contracts) != 2) {
                if ($value->sort == $draggedSortID && $dragdown == true) {
                    $value->sort = $replacedSortID;
                } elseif ($value->sort == $replacedSortID && $dragdown == false) {
                    $value->sort = $draggedSortID;
                } elseif ($dragdown == true && $value->sort != $draggedSortID) {
                    $value->sort--;
                } elseif ($dragdown == false && $value->sort != $replacedSortID) {
                    $value->sort++;
                }
            } else {
                if ($value->sort == $draggedSortID) {
                    $value->sort = $replacedSortID;
                } elseif ($value->sort == $replacedSortID) {
                    $value->sort = $draggedSortID;
                }
            }

            $value->save();
        }
    }

    public function createMultiple($data, $onlyDownwards)
    {
        $criteria                             = new CDbCriteria();
        $criteria->with                       = ['contractField'];
        $productComboBasicFieldAssignments    = ProductComboBasicFieldAssignment::model()->with('contractField')->findAll();
        $productComboBasicFieldAssignmentList = [];

        foreach ($productComboBasicFieldAssignments as $productComboBasicFieldAssignment) {
            $contractField = $productComboBasicFieldAssignment->contractField;
            if (!empty($contractField) && $contractField->gdv_product == 0) {
                $productComboBasicFieldAssignmentList[] = strtolower(trim($contractField->fieldtitle));
            }
        }
        $productComboListdata = [];
        if (!$onlyDownwards) {
            $productComboListdata = $this->getAllProductsAsKeyValue();
        } else {
            $productComboId       = $data['ProductComboBasicFieldAssignment']['product_combo_id'];
            $productCombo         = ProductCombo::model()->findByPk($productComboId);
            $productComboListdata = CHtml::listData($productCombo->getChildren(), 'id', 'name');
        }
        foreach ($productComboListdata as $productCombo => $value) {
            $contractFieldController = new ContractFieldController('ContractFieldController');
            $contractField           = $contractFieldController->create($data);

            if (!in_array(strtolower(trim($data['ProductComboBasicFieldAssignment']['fieldtitle'])),
                          $productComboBasicFieldAssignmentList)
            ) {
                if ($contractField != null) {
                    $productComboBasicFieldAssignment                = new ProductComboBasicFieldAssignment();
                    $productComboBasicFieldAssignment->product_combo = $productCombo;
                    $productComboBasicFieldAssignment->field_id      = $contractField->id;

                    if ($productComboBasicFieldAssignment->save()) {
                        $productComboBasicFieldAssignment->sort = $productComboBasicFieldAssignment->id;
                        $productComboBasicFieldAssignment->save();
                    }
                }
            }
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ProductComboBasicFieldAssignment $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'product-combo-basic-field-assignment-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }

    /**
     * Fügt neue BasicFields hinzu (Stammdaten->Grundfelderhinzufügen)
     */
    public function actionCreateBasicField(): void
    {
        $data                                                         = [];
        $data['ProductComboBasicFieldAssignment']['product_combo_id'] = $_POST['product_combo_id'];
        $data['ProductComboBasicFieldAssignment']['fieldtitle']       = $_POST['fieldtitle'];
        $data['ProductComboBasicFieldAssignment']['gdv_product']      = $_POST['gdv_product'];
        $createType                                                   = $_POST['createType'];

        if ($createType == 'choosen') {
            $this->create($data);
        } elseif ($createType == 'all') {
            $this->createMultiple($data, false);
        } elseif ($createType == 'downwards') {
            $this->createMultiple($data, true);
        }

        Yii::app()->end();
    }
}
