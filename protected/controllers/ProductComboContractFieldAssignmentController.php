<?php

class ProductComboContractFieldAssignmentController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    public static $product_combo = 0;
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    public $prefix          = 'ProductComboContractFieldAssignment';
    private $controllername = 'productComboContractFieldAssignment';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das ProductComboContractFieldAssignment-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des ProductComboContractFieldAssignment-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = ProductComboContractFieldAssignment::model()->findByPk($id);

        return $model;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues ProductComboContractFieldAssignment-Model erstellt werden soll.
     *  Die Methode öffnet das ProductComboContractFieldAssignment-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($model = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $model;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das ProductComboContractFieldAssignment -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des ProductComboContractFieldAssignment -Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $model        = new ProductComboContractFieldAssignment();

        if (!$this->validData($_POST)) {
            $this->render('create', ['model' => $model]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model ProductComboContractFieldAssignment handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['ProductComboContractFieldAssignment'])
        ;
    }

    /**
     * Diese Methode erstellt ein neues ProductComboContractFieldAssignment -Model
     *
     * @param type $data
     *          Die ProductComboContractFieldAssignment -Daten als Array
     *
     * @return $model
     *          Das erstellte ProductComboContractFieldAssignment -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $model             = new ProductComboContractFieldAssignment();
        $model->attributes = $data['ProductComboContractFieldAssignment'];

        if ($model->save()) {
            return $model;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein ProductComboContractFieldAssignment -Model aktualisiert werden soll.
     *  Die Methode öffnet das ProductComboContractFieldAssignment -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden ProductComboContractFieldAssignment -Models
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);

        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($model = $this->update($data) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $model;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das ProductComboContractFieldAssignment-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden ProductComboContractFieldAssignment-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('update', ['model' => $model]);
        }

        $formulardata                                              = $_POST;
        $formulardata['ProductComboContractFieldAssignment']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein ProductComboContractFieldAssignment-Exemplar
     *
     * @param type $data
     *          Die ProductComboContractFieldAssignment-Daten als Array
     *
     * @return $model
     *          Das aktualisierte ProductComboContractFieldAssignment-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $model             = $this->loadModel($data['ProductComboContractFieldAssignment']['id']);
            $model->attributes = $data['ProductComboContractFieldAssignment'];
            if ($model->save()) {
                return $model;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $productComboContractFieldAssignment = new ProductComboContractFieldAssignment();
        $product_combo_id                    = 0;
        if (isset($_POST['product_combo_id'])) {
            $product_combo_id = $_POST['product_combo_id'];
        }

        $product_combos = $this->getAllGdvProductProductComboAssignment();

        $dataProvider = $productComboContractFieldAssignment->search($product_combo_id);

        $this->render('//stammdaten/vertragsdaten/felderZuordnen/admin',
            [
                'productComboContractFieldAssignment' => $productComboContractFieldAssignment,
                'prefix'                              => $this->prefix,
                'product_combos'                      => $product_combos,
                'product_combo_id'                    => $product_combo_id,
                'dataProvider'                        => $dataProvider
            ]);
    }

    public function getAllGdvProductProductComboAssignment()
    {
        $criteria        = new CDbCriteria();
        $criteria->group = 'name ASC';
        foreach (GdvProductAssignmentProductComboAssignment::model()->findAll() as $gdvProductAssignmentProductComboAssignment) {
            $criteria->addCondition('id = ' . $gdvProductAssignmentProductComboAssignment->product_combo_id,
                'OR');
        }

        return CHtml::listData(ProductCombo::model()->findAll($criteria,
            ['order' => 'name ASC']), 'id', 'name');
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete()
    {
        return $this->delete();
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('ProductComboContractFieldAssignment');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * returns a array of key value pairs (need for Dropdownlists)
     *
     * @return
     *         form: array([0] => name1, [1] => name2...) etc.
     */
    public function getAllProductsAsKeyValue()
    {
        $productCombos = ProductCombo::model()->findAll(['order' => 'name']);
        $datas         = [];
        foreach ($productCombos as $productCombo) {
            $datas[$productCombo->id] = $productCombo->name;
        }

        return $datas;
    }

    public function UpdateAssignment($data)
    {
        $productComboContractFieldAssignment                      = new ProductComboContractFieldAssignment();
        $productComboContractFieldAssignment->product_combo_id    = $data['product_combo_id'];
        $productComboContractFieldAssignment->field_id            = $data['field_id'];
        $productComboContractFieldAssignment->assignment_field_id = $data['assignment_field_id'];

        $criteria = new CDbCriteria();
        $criteria->addCondition('product_combo_id = :product_combo_id');
        $criteria->addCondition('field_id = :field_id');
        $criteria->params = [
            ':field_id'         => (int) $data['field_id'],
            ':product_combo_id' => (int) $data['product_combo_id']
        ];

        $existProductComboContractFieldAssignment = ProductComboContractFieldAssignment::model()->find($criteria);
        if (count($existProductComboContractFieldAssignment) <= 0) {
            $productComboContractFieldAssignment->save();
        } else {
            $existProductComboContractFieldAssignment->delete();
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ProductComboContractFieldAssignment $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'product-combo-contract-field-assignment-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
