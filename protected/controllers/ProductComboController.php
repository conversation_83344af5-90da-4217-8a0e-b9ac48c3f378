<?php

class ProductComboController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Gibt eine Array der Combos zurück, die für die DropdownMenüs benötigt werden
     *
     * @return $combodata
     *          Combodaten als Array der Form array(combo->id => getFullname(combo))
     */
    public static function getComboData($attributes = null)
    {
        if ($attributes == null) {
            $combos = ProductCombo::model()->findAll(['order' => 'name']);
        } else {
            $combos = ProductCombo::model()->findAllByAttributes($attributes, ['order' => 'name']);
        }
        $combodata = CHtml::listData($combos, 'id', 'name');

        return $combodata;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $productCombo = new ProductCombo();
        $this->render('//stammdaten/vertragsdaten/nettoBruttoZuordnung/admin',
                      [
                          'productCombo' => $productCombo,
                      ]);
    }

    /**
     * Updatet den net_gros Wert des ProductCombos der für die Verträge benötigtwerden.
     *
     * @param type $product_combo_id
     *
     * @return boolean
     */
    public function UpdateNetGrossVal($product_combo_id)
    {
        $productCombo = $this->loadModel($product_combo_id);
        if ($productCombo->net_gross == 1) {
            $productCombo->net_gross = 0;
        } elseif ($productCombo->net_gross == 0) {
            $productCombo->net_gross = 1;
        }

        if ($productCombo->save()) {
            return true;
        }

        return false;
    }

    /**
     * Updatet den net_gros Wert des ProductCombos der für die Verträge benötigtwerden.
     *
     * @param type $product_combo_id
     *
     * @return boolean
     */
    public function UpdateNetGrossValCourtage($product_combo_id)
    {
        $productCombo = $this->loadModel($product_combo_id);
        if ($productCombo->net_gross_courtage == 1) {
            $productCombo->net_gross_courtage = 0;
        } elseif ($productCombo->net_gross_courtage == 0) {
            $productCombo->net_gross_courtage = 1;
        }

        if ($productCombo->save()) {
            return true;
        }

        return false;
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ProductCombo $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'product-combos-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }

    /**
     * @param $id
     *
     * @return ProductCombo
     * @throws CHttpException
     */
    private function loadModel($id): ProductCombo
    {
        $model = ProductCombo::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'Die Sparte existiert nicht');
        }

        return $model;
    }
}
