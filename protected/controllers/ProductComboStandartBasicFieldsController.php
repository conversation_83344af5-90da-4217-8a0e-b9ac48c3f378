<?php

class ProductComboStandartBasicFieldsController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout    = '//layouts/column2';

    public function actionAdmin()
    {
        $productComboStandartBasicField = new ProductComboStandartBasicFields();
        $this->render('//stammdaten/vertragsdaten/standartfelderZuordnen/admin',
            [
                'productComboStandartBasicField' => $productComboStandartBasicField
            ]);
    }

    public function updateValue($data)
    {
        $attribute = $data['ProductComboStandartBasicField']['attributeName'];
        $model     = $this->loadModel($data['ProductComboStandartBasicField']['id']);
        if (empty($model)) {
            $model = new ProductComboStandartBasicFields();
        }
        if ($model->$attribute == 0) {
            $model->$attribute = 1;
        } else {
            if ($model->$attribute == 1) {
                $model->$attribute = 0;
            }
        }
        $model->attributes = $data['ProductComboStandartBasicField'];

        $model->save();
    }

    /**
     * Lädt das ProductComboStandartBasicFields-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des ProductComboStandartBasicFields-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = ProductComboStandartBasicFields::model()->findByPk($id);

        return $model;
    }

    public function getAllProductsAsKeyValue()
    {
        $criteria = new CDbCriteria();
        foreach (GdvProductAssignmentProductComboAssignment::model()->findAll() as $gdvProductAssignmentProductComboAssignment) {
            $criteria->addCondition('id = ' . $gdvProductAssignmentProductComboAssignment->product_combo_id,
                'OR');
        }

        return CHtml::listData(ProductCombo::model()->findAll($criteria,
            ['order' => 'name DESC']), 'id', 'name');
    }

    public function actionGenerate()
    {
        $created = 0;
        foreach (ProductCombo::model()->findAll() as $product) {
            $model                   = new ProductComboStandartBasicFields();
            $model->product_combo_id = $product->id;

            $criteria = new CDbCriteria();
            $criteria->compare('product_combo_id', $product->id);
            if (!$model->exists($criteria)) {
                $model->save();
                $created++;
            }
        }
        echo $created;
        exit();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ProductComboStandartBasicFields $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'product-combo-standart-basic-fields-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
