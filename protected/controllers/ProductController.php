<?php

class ProductController extends AdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'product' => $this->loadModel($id),
        ]);
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param integer $id the ID of the model to be loaded
     *
     * @return ProductsLevel1 the loaded model
     * @throws CHttpException
     */
    public function loadModel($id)
    {
        $product = Product::model()->findByPk($id);

        return $product;
    }

    /**
     *  Diese Methode wird aufgerufen wenn eine neues Product erstellt werden soll.
     *  Die Methode öffnet das Product-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();

        if ($this->validData($data)) {
            if ($this->create($data) != null) {
            }
        }
    }

    /**
     *  Öffnet das Product-Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des Product-Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $product      = new Product();

        if (!$this->validData($_POST)) {
            $this->render('create', ['product' => $product]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein Data-Array für die Product handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        return isset($data) &&
            isset($data['Product']);
//                isset($data['Product']['name']) &&
//                $data['Product']['name'] != '');
    }

    /**
     * Diese Methode erstellt ein neues Model eines Products.
     *
     * @param type $data
     *          Die Product-Daten als Array
     *
     * @return $product
     *          Das erstellte Product-Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $product             = new Product();
        $product->attributes = $data['Product'];

        if ($product->save()) {
            $productComboController = new ProductComboController('productcombo');

            if ($data['Product']['level'] == 1) {
                $data['ProductCombo']['level1_id'] = $product->id;
            } else {
                if ($data['Product']['level'] == 2) {
                    $data['ProductCombo']['level2_id'] = $product->id;
                } else {
                    $data['ProductCombo']['level3_id'] = $product->id;
                }
            }

            //add Combo to combotable
            $productCombo = $productComboController->create($data);
            if ($productCombo != null) {
                try {
                    $productComboStandartBasicField                   = new ProductComboStandartBasicFields();
                    $productComboStandartBasicField->product_combo_id = $productCombo->id;
                    $productComboStandartBasicField->save();
                } catch (Exception $e) {
                }
            }
            if ($productCombo != null) {
                return $product;
            }
            $this->delete($product->id);

            return null;
        }

        return null;
    }

    /**
     * Löscht ein Product aus der DB
     *
     * @param type $id
     *          ID des Products das gelöscht werden soll
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Löscht das Product mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden Products
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $success = $this->delete($id);

        return $success;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein Product aktualisiert werden soll.
     *  Die Methode öffnet das Product-Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID des zu aktualisierenden Product
     */
    public function actionUpdate($id)
    {
        $product = $this->loadModel($id);
        if ($product != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($product = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $product;
                    }

                    return null;
                }
            }
        } else {
            $this->actionAdmin();

            return null;
        }
    }

    /**
     *
     * Öffnet das Product-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden Product-Exemplares
     *
     * @return $product
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $formulardata = [];

        $product = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('update', ['product' => $product]);
        }

        $formulardata                  = $_POST;
        $formulardata['Product']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein Product-Exemplar
     *
     * @param type $data
     *          Die Product-Daten als Array
     *
     * @return $product
     *          Das aktualisierte Product-Exemplar falls das Product aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $product = $this->loadModel($data['Product']['id']);
            if ($this->validData($data)) {
                $product->attributes = $data['Product'];
                if ($product->save()) {
                    return $product;
                }

                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $product = new Product('search');
        $product->unsetAttributes();  // clear any default values
        if (isset($_GET['Product'])) {
            $product->attributes = $_GET['Product'];
        }

        $this->render('//stammdaten/verwaltung/sparten/produkte/admin', [
            'product' => $product,
        ]);
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('Product');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionAdminview()
    {
        $product = new Product('search');
        $product->unsetAttributes();  // clear any default values
        if (isset($_GET['Product'])) {
            $product->attributes = $_GET['Product'];
        }

        $this->render('//stammdaten/verwaltung/sparten/produkte/adminview', [
            'product' => $product,
        ]);
    }

    /**
     * Erstellt eine Produktsparte auf dem ersten Level
     *
     */
    public function actionCreateLevel1()
    {
        $data                     = [];
        $data['Product']          = [];
        $data['Product']['level'] = 1;
        $data['Product']['name']  = $_POST['Level1'];
        $this->create($data);
        $this->redirect('adminview');
    }

    /**
     * Erstellt eine Vertragssparte auf dem zweiten Level
     *
     */
    public function actionCreateLevel2()
    {
        $data                     = [];
        $data['Product']          = [];
        $data['Product']['level'] = 2;
        if (isset($_POST['priority'])) {
            if ($_POST['priority'] == 1) {
                $data['Product']['priority'] = $_POST['Level1id'];
            } elseif ($_POST['priority'] == 0) {
                $data['Product']['priority'] = null;
            }
        }
        if ($_POST['Level2'] != '' && $_POST['Level2dd'] == '') {
            $data['Product']['name']           = $_POST['Level2'];
            $data['ProductCombo']['level1_id'] = $_POST['Level1id'];
            $this->create($data);
            $this->redirect('adminview');
        } elseif ($_POST['Level2dd'] != '' && $_POST['Level2'] == '') {
            $data['Product']['name']           = $_POST['Level2dd'];
            $data['ProductCombo']['level1_id'] = $_POST['Level1id'];
            $data['ProductCombo']['id']        = $_POST['Level2dd'];
            $this->createCombo($data);
            $this->redirect('adminview');
        }
    }

    /**
     * Erstellt die Kombination der Level
     *
     * @param type $data
     *
     * @return null
     */
    public function createCombo($data)
    {
        $product                = $data['ProductCombo']['id'];
        $productComboController = new ProductComboController('productcombo');

        if ($data['Product']['level'] == 1) {
            $data['ProductCombo']['level1_id'] = $product;
        } else {
            if ($data['Product']['level'] == 2) {
                $data['ProductCombo']['level2_id'] = $product;
            } else {
                if ($data['Product']['level'] == 3) {
                    $data['ProductCombo']['level3_id'] = $product;
                }
            }
        }

        $productCombo = $productComboController->create($data);

        if ($productCombo != null) {
            return $product;
        }
        $this->delete($product->id);

        return null;
    }

    /**
     * Erstellt eine Untersparte auf dem dritten Level
     *
     */
    public function actionCreateLevel3()
    {
//        echo '<pre>';
//        print_r($_POST);
//        echo '</pre>';
//        exit();

        $data = [];
        $data = $_POST;
        if ($data['Level3'] == '' && $data['Level3dd'] == '') {
            $product           = $this->loadModel($data['Level2id']);
            $product->priority = $data['Level1id'];
            $product->save();
            $this->redirect('adminview');
        } elseif ($_POST['Level3'] != '' || $_POST['Level3dd'] != '') {
            $data['Product']['level'] = 3;
            if (isset($data['priority'])) {
                if ($_POST['priority'] == 1) {
                    $data['Product']['priority'] = $_POST['Level2id'];
                } elseif ($_POST['priority'] == 0) {
                    $data['Product']['priority'] = null;
                }
            }
            if ($_POST['Level3'] != '' && $_POST['Level3dd'] == '') {
                $data['Product']['name']           = $_POST['Level3'];
                $data['ProductCombo']['level1_id'] = $_POST['Level1id'];
                $data['ProductCombo']['level2_id'] = $_POST['Level2id'];
                $this->create($data);
                $this->redirect('adminview');
            } elseif ($_POST['Level3dd'] != '' && $_POST['Level3'] == '') {
                $data['Product']['name']           = $_POST['Level3dd'];
                $data['ProductCombo']['level1_id'] = $_POST['Level1id'];
                $data['ProductCombo']['level2_id'] = $_POST['Level2id'];
                $data['ProductCombo']['id']        = $_POST['Level3dd'];
                $this->createCombo($data);
                $this->redirect('adminview');
            }
        } else {
            echo 'Fehler beim erstellen!! Beide Felder waren ausgefüllt!';
        }
//        $this->redirect('admin');
    }

    /**
     * Updated die Priorität von Level 3
     */
    public function actionUpdateLevel3()
    {
        $data              = $_POST;
        $product           = $this->loadModel($data['Level3id']);
        $product->priority = $data['Level2id'];
        $product->save();
        $this->redirect('adminview');
    }

    /**
     * Performs the AJAX validation.
     *
     * @param ProductsLevel1 $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'products-level1-form') {
            echo CActiveForm::validate($product);
            Yii::app()->end();
        }
    }
}
