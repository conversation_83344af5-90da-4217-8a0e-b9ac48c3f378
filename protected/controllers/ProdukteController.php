<?php

class ProdukteController extends Controller
{
    public function __construct($id, $module = null)
    {
        parent::__construct($id, $module);
    }

    public function actions()
    {

        //initialize with default action
        $returnAction = [
            'anzeigen' => 'application.components.actions.Content',
        ];

//        if (!is_null(Yii::app()->request->getParam('produkt', null))
//                && !is_null(Yii::app()->request->getParam('link', null)){
//
//                }
        //return external action classes, e.g.:
        return $returnAction;
    }

    public function actionIndex()
    {
        $this->render('index');
    }
}
