<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

use Demv\JSend\ResponseFactory;
use profil\services\MaklerNotificationSettingsService;

/**
 * Description of ProfileController
 *
 * <AUTHOR>
 */
class ProfileController extends GlobalRightController
{
    protected $registeredValidation = true;

    public $menuGroup = MenuGroup::STAMMDATEN;

    const ACTIVE_TAB_SMTP               = 1;
    const ACTIVE_TAB_VERGLEICHSRECHNER  = 2;
    const ACTIVE_TAB_MAKLERDOKUMENTE    = 3;
    const ACTIVE_TAB_UNTERSCHRIFT       = 4;
    const ACTIVE_TAB_SIGNATUR           = 5;
    const ACTIVE_TAB_TERMIANTECONTRACTS = 6;

    const MAX_SIGNATURE_LENGTH = 500000;

    private $active_tab;

    /**
     * @param null   $id
     * @param string $active_tab
     *
     * @throws CException
     */
    public function actionUpdate($id = null, $tab = null, $active_tab = ProfilTabs::SMTP): void
    {
        //Wir nehmen erstmal immer den Eingeloggten User
        $id = Yii::app()->user->id;

        $active_tab       = $tab !== null ? $tab : $active_tab;
        $this->active_tab = is_numeric($active_tab) ? ProfilTabs::byId((int) $active_tab) : $active_tab;
        $this->active_tab = ProfilTabs::exists($this->active_tab) ? $this->active_tab : ProfilTabs::DEFAULT;

        //SMTP Einstellungen
        //Rechner Einstellungen
        $calculatorDataProvider = ProfileCalculator::model()->search();
        //Maklerdokumente
        $brokerDocuments          = new UserProfileDocuments();
        $brokerDocuments->user_id = $id;
        //Ablaufende Verträge
        $productCombo          = new ProductCombo();
        ProductCombo::$profile = true;

        //Unterschrift
        $signature = UserSignature::model()->getLoggedInUserSignature();
        //Signatur
        $footer    = MailFooter::model()->getLoggedInUserFooter(true, false);
        $agency_id = Yii::app()->user->getAgencyId();
        $agency    = Agency::model()->findByPk($agency_id);
        $tabs      = [];
        $tabs[]    = [
            'label'       => 'E-Mail-Einstellungen',
            'linkOptions' => [
                'data-source' => $this->createUrl('/profil/usermailsettings/update'),
                'class'       => 'ajax-tab',
                'id'          => 'tablink-' . ProfilTabs::SMTP
            ],
            'active'      => $this->active_tab === ProfilTabs::SMTP,
            'key'         => ProfilTabs::SMTP,
        ];
        $tabs[]    = [
            'label'       => 'Briefeinstellungen',
            'linkOptions' => [
                'data-source' => $this->createUrl('/serienbrief/bulkletter/editProfile'),
                'class'       => 'ajax-tab',
                'id'          => 'tablink-' . ProfilTabs::BRIEFEINSTELLUNGEN
            ],
            'active'      => $this->active_tab === ProfilTabs::BRIEFEINSTELLUNGEN,
            'key'         => ProfilTabs::BRIEFEINSTELLUNGEN,
        ];
        $tabs[]    = [
            'label'   => 'Dokumente (M-K)',
            'content' => $this->renderPartial(
                '//stammdaten/sonstige/profil/maklerdokumente/index',
                ['dataProvider' => $brokerDocuments->search()],
                true
            ),
            'active'  => $this->active_tab === ProfilTabs::MAKLERDOKUMENTE,
            'key'     => ProfilTabs::MAKLERDOKUMENTE,
            'visible' => Yii::app()->user->hasRight(Rights::DOKUMENTE_M_K)
        ];
        $tabs[]    = [
            'label'   => 'Unterschrift',
            'content' => $this->renderPartial(
                '//stammdaten/sonstige/profil/unterschrift/create',
                ['signature' => $signature],
                true
            ),
            'active'  => $this->active_tab === ProfilTabs::UNTERSCHRIFT,
            'key'     => ProfilTabs::UNTERSCHRIFT,
        ];
        $tabs[]    = [
            'label'   => 'Signatur',
            'content' => $this->renderPartial(
                '//stammdaten/sonstige/profil/signatur/create',
                ['footer' => $footer],
                true
            ),
            'active'  => $this->active_tab === ProfilTabs::SIGNATUR,
            'key'     => ProfilTabs::SIGNATUR,
        ];
        $tabs[]    = [
            'label'   => 'Ablaufende Verträge',
            'content' => $this->renderPartial(
                '//stammdaten/sonstige/profil/ablaufendeVertraege/tabContainerTerminateContracts',
                ['tabs' => $this->getTerminateContractTabs()],
                true
            ),
            'active'  => $this->active_tab === ProfilTabs::ABLAUFENDE_VERTRAEGE,
            'key'     => ProfilTabs::ABLAUFENDE_VERTRAEGE,
        ];
        $tabs[]    = [
            'label'       => 'Schadeneinstellungen',
            'linkOptions' => [
                'data-source' => $this->createUrl('/profil/damagesettings/update'),
                'class'       => 'ajax-tab',
                'id'          => 'tablink-' . ProfilTabs::SCHADEN_EINSTELLUNGEN
            ],
            'active'      => $this->active_tab === ProfilTabs::SCHADEN_EINSTELLUNGEN,
            'key'         => ProfilTabs::SCHADEN_EINSTELLUNGEN,
        ];
        $tabs[]    = [
            'label'   => 'Finanzmanager',
            'content' => fn () => $this->renderPartial(
                '//stammdaten/sonstige/profil/kundenlogin/index',
                [
                    'view' => WebAppView::new(currentUser(), $_GET)
                ],
                true
            ),
            'active'  => $this->active_tab === ProfilTabs::KUNDENLOGIN,
            'static'  => true,
            'key'     => ProfilTabs::KUNDENLOGIN,
        ];

        $tabs[]    = [
            'label'   => 'Benachrichtigungen',
            'content' => fn () => $this->renderPartial(
                '//stammdaten/sonstige/profil/benachrichtigungen/index',
                [
                    'view' => WebAppView::new(currentUser(), $_GET)
                ],
                true
            ),
            'active'  => $this->active_tab === ProfilTabs::BENACHRICHTIGUNGEN,
            'static'  => true,
            'key'     => ProfilTabs::BENACHRICHTIGUNGEN,
        ];

        if ($this->active_tab === ProfilTabs::KUNDENLOGIN) {
            $this->layout                       = '//layouts/main_sidebar';
            Yii::app()->clientScript->scriptMap = [
                YII_DEBUG ? 'bootstrap.css' : 'bootstrap.min.css' => false,
                'bootstrap-editable.css'                          => false,
            ];
            Yii::app()->clientScript->registerCssFile(Yii::app()->baseUrl . '/css/demv.css');
            Yii::app()->clientScript->registerCssFile(webpackAsset('css/global.css'));
        }

        $this->render('//stammdaten/sonstige/profil/tabContainer', ['tabs' => $tabs]);
    }

    private function getTerminateContractTabs()
    {
        $productCombo          = new ProductCombo();
        ProductCombo::$profile = true;
        $tabs                  = [];
        //terminateContractSettings
        $tabs[] = [
            'label'   => 'Allgemeine Einstellungen',
            'content' => $this->renderPartial(
                '//stammdaten/sonstige/profil/ablaufendeVertraege/terminateContractSettings',
                ['dataProvider' => $productCombo->search()],
                true
            ),
            'active'  => $this->active_tab != ProfilTabs::ABLAUFENDE_VERTRAEGE
        ];
        $tabs[] = [
            'label'   => 'Vordefinierte Suche',
            'content' => $this->renderPartial(
                '//stammdaten/sonstige/profil/ablaufendeVertraege/terminateContractSearchSettings',
                ['formularFields' => ContractsController::getProfileFields(UserProfileSearch::ablaufendeVertraege)],
                true
            ),
            'active'  => $this->active_tab === ProfilTabs::ABLAUFENDE_VERTRAEGE
        ];

        return $tabs;
    }

    /**
     *  Für Vergleichsrechner Profil´. Wird nur per Ajax aufgerufen.
     */
    public function actionDeleteProfileCalculator($id): void
    {
        $model = ProfileCalculator::model()->findByPk($id);
        $model->delete();
    }

    public function actionCreateMailFooter(): void
    {
        $footer             = MailFooter::model()->getLoggedInUserFooter(true, false);
        $params             = Yii::app()->request->getPost('MailFooter');
        $params['user_id']  = Yii::app()->user->getId();

        // Prevent saving too large base64 images that are cut off at MAX_SIGNATURE_LENGTH
        if (strlen($params['text'] ?? '') > (self::MAX_SIGNATURE_LENGTH - 1)) {
            Yii::app()->user->setFlash('error', 'Der Inhalt Ihrer Signatur ist zu lang.');
            $this->redirect(['profile/update', 'active_tab' => ProfilTabs::SIGNATUR]);
        }

        $footer->attributes = $params;
        if ($footer->save()) {
            Yii::app()->user->setFlash('success', 'Ihre Signatur wurde erfolgreich geändert.');
        } else {
            Yii::app()->user->setFlash('error', 'Ihre Signatur konnte nicht geändert werden.');
        }
        $this->redirect(['profile/update', 'active_tab' => ProfilTabs::SIGNATUR]);
    }

    /**
     * Für ablaufende Verträge Profil. Wird nur per Ajax aufgerufen.
     */
    public function actionChangeProductComboDays(): void
    {
        $id   = Yii::app()->request->getParam('pk');
        $days = Yii::app()->request->getParam('value');

        $productComboModel    = ProductCombo::model()->findByPk($id);
        $productComboChildIds = [$id];
        if ($productComboModel->getLevel() == 1) {
            $productComboChildIds = array_merge(
                $productComboChildIds,
                $productComboModel->getChildren(true),
            );
        }

        $userId = Yii::app()->user->getId();
        foreach ($productComboChildIds as $id) {
            $model    = new UserProfileTerminateContracts();
            $criteria = new CDbCriteria();
            $criteria->compare('user_id', $userId);
            $criteria->compare('product_combo_id', $id);
            if (UserProfileTerminateContracts::model()->exists($criteria)) {
                $model = UserProfileTerminateContracts::model()->find($criteria);
            }
            $model->days             = $days;
            $model->user_id          = $userId;
            $model->product_combo_id = $id;
            $model->save();
        }
        Yii::app()->end();
    }

    /**
     * Für ablaufende Verträge Profil. Wird nur per Ajax aufgerufen.
     */
    public function actionChangeShow(): void
    {
        $id       = Yii::app()->request->getParam('id');
        $show     = Yii::app()->request->getParam('show');
        $criteria = new CDbCriteria();
        $criteria->compare('user_id', Yii::app()->user->getId());
        $criteria->compare('product_combo_id', $id);
        $model = new UserProfileTerminateContracts();
        if (UserProfileTerminateContracts::model()->exists($criteria)) {
            $model = UserProfileTerminateContracts::model()->find($criteria);
        }
        if ($show == 1) {
            $model->show = 0;
        } else {
            $model->show = 1;
        }
        $model->user_id          = Yii::app()->user->getId();
        $model->product_combo_id = $id;
        $model->save();
        Yii::app()->end();
    }

    public function actionSaveProfileFields($model, $function, $redirect, $saveAsModel = true): void
    {
        if ($saveAsModel) {
            $toSaveModel = new $model();
            foreach ($_REQUEST as $key => $value) {
                if (array_key_exists($key, $toSaveModel->attributes)) {
                    $toSaveModel->$key = $value;
                }
            }
            UserProfileSearch::model()->saveSearch($toSaveModel, $function, null);
        } else {
            UserProfileSearch::model()->saveSearch(new $model(), $function, $_REQUEST);
        }
        $this->redirect([$redirect]);
    }

    /**
     * @throws CDbException
     */
    public function actionUpdateMaklerNotifications()
    {
        $request = json_decode(request()->getRawBody(), true) ?? [];

        (new MaklerNotificationSettingsService())->updateMaklerNotifications($request);

        ResponseFactory::instance()->success()->respond();
    }
}
