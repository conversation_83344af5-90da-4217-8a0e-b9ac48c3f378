<?php

use Apfelbox\FileDownload\FileDownload;
use Business\Courtageumzug\Document\CourtageumzugDocument;
use Components\Api\Response\ContentType;
use Components\Api\Response\Response;
use Components\Api\Response\StatusCode;
use Components\File\Path\Specifications\FilesSpecification;
use Components\Filesystem\FilesystemFactory;
use League\Flysystem\FileNotFoundException;

/**
 * Class PublicUploadController
 */
final class PublicUploadController extends AllowAllController
{
    /**
     * @param string $file
     *
     * @throws CHttpException
     * @throws FileNotFoundException
     */
    public function actionIndex(string $file)
    {
        $this->streamPublicUploadFileIfPossible($file);
    }

    /**
     * @param string $file
     *
     * @throws CHttpException
     * @throws FileNotFoundException
     */
    public function actionCooperation(string $file)
    {
        $this->streamPublicUploadFileIfPossible('Kooperation' . DS . $file);
    }

    /**
     * @param string $file
     *
     * @throws CHttpException
     * @throws FileNotFoundException
     */
    public function actionNewsletter(string $file)
    {
        $this->streamPublicUploadFileIfPossible('Newsletter' . DS . $file);
    }

    /**
     * @param string $file
     *
     * @throws CHttpException
     */
    public function actionFiles(string $file)
    {
        $pathToFile = Yii::getPathOfAlias('download-files') . DS . $file;

        $specificaton = new FilesSpecification();
        if ($specificaton->isSatisfiedByFilename($file)) {
            @FileDownload::createFromFilePath($pathToFile)->sendDownload(basename($file));
            Yii::app()->end();
        }

        throw new CHttpException(404);
    }

    /**
     * @param string $path
     *
     * @throws CHttpException|FileNotFoundException
     */
    private function streamPublicUploadFileIfPossible(string $path): void
    {
        $filesystem = (new FilesystemFactory())->publicUpload();

        if (!$filesystem->has($path)) {
            throw new CHttpException(404);
        }

        $contentResource = $filesystem->readStream($path);

        if ($contentResource === false) {
            throw new CHttpException(404);
        }

        $mimetype = $filesystem->getMimetype($path);

        if ($mimetype !== false && stripos($mimetype, 'image/') === 0) {
            (new Response(StatusCode::createOf(StatusCode::OK)))
                ->setContentType(ContentType::createOf($mimetype))
                ->setBody($contentResource)
                ->allowCaching()
                ->send();
        } else {
            @(new FileDownload($contentResource))
                ->sendDownload(basename($path), false);
        }

        Yii::app()->end();
    }

    public function actionCourtageumzug()
    {
        $document = new CourtageumzugDocument();
        @FileDownload::createFromString($document->getPlainContent())->sendDownload('Courtageumzug.pdf');
    }

    public function actionFondsFinanzCooperationDocumentDownload(): void
    {
        @FileDownload::createFromFilePath(
            __DIR__ . '/../modules/Fondsfinanz/resources/auftragsverhaeltnis_datenuebermittlung_demv.pdf'
        )->sendDownload('Auftragsverhältnis Datenübermittlung DEMV.pdf');
    }
}
