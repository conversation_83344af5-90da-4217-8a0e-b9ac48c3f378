<?php

use Auth\Services;
use Business\Ansprechpartner\AnsprechpartnerSuche;
use Business\Mail\Attachments\AttachmentSources;
use Business\Mail\Footer\DefaultMailFooter;
use Business\Mail\Footer\Footer;
use Business\Vermittlernummer\BrokerIdStatus;
use Business\Vorgaenge\Korrespondenz;
use Components\Api\Message\MessageStatus;
use Components\Api\Response\ContentType;
use Components\Api\Response\Response;
use Components\Api\Response\Rest\RestfulInterface;
use Components\Api\Response\Rest\RestResponseTrait;
use Components\Api\Response\StatusCode;
use Components\Authentication\OAuth\OAuthAccessible;
use Components\Client\Providers\ClientfileProvider;
use Components\Mail\Mailer\MailerFactory;
use Components\Pdfs\Kuendigung\DismissalContract;
use Components\Pdfs\Kuendigung\DismissalGenerator;
use Components\Pdfs\Kuendigung\DismissalRecipient;
use Components\Pdfs\Kuendigung\DismissalSender;
use Components\Vermittlernummern\BrokerIdProvider;
use Demv\JSend\JSendResponse;
use mikehaertl\pdftk\Pdf;

/**
 * Der Rest Controller verarbeitet alle Requests über die Rest-API.<br />
 * Requests werden gestellt wie in folgendem Beispiel: <br />
 * <br />
 * Requests haben einen Status der abgefragt werden kann. <br />
 * STATUS_SUCCESS = 1 <br />
 * STATUS_FAILED = 0
 *
 * <code>
 * <?php<br />
 *   $ch = curl_init({url} . 'BrokerId?uid=' . $uId . "&gid=" . $gId . "&secret=" . {secret});<br />
 *   curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);<br />
 *   $result = curl_exec($ch);<br />
 *   curl_close($ch);<br />
 *   $result = json_decode($result);<br />
 * ?><br />
 * </code>
 */
class RestController extends AllowAllController implements RestfulInterface, OAuthAccessible
{
    use RestResponseTrait;

    /**
     * @param int    $status
     * @param string $body
     * @param string $contentType
     */
    public function _sendResponse($status = 200, $body = '', $contentType = 'text/html')
    {
        $response = new Response(StatusCode::createOf($status));
        $response->setBody($body)
                 ->setContentType(ContentType::createOf($contentType))
                 ->send();
        Yii::app()->end();
    }

    /**
     * Gibt einen Nutzer zurück
     */
    public function actionUser()
    {
        $this->log(
            ' User angefragt',
            $_GET,
            'application.rest',
            'actionUser'
        );

        $model = User::model()->findByPk($_GET['id']);

        if ($model === null || $model->agency_id != currentUser()->agency_id) {
            $this->_sendResponse(404, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'No User found',
                                                    ]));
        }

        if (!empty($model->olddata)) {
            $model->olddata = '';
        }
        $model->password = '';
        $model->sid      = '';

        $response                    = [];
        $response['user']            = $model;
        $response['contact']         = $model->contact;
        $response['address']         = $model->address;
        $response['agency']          = $model->agency;
        $response['agency_contact']  = $model->agency->contact;
        $response['agency_address']  = $model->agency->address;
        $response['salutation']      = $model->salutation;
        $response['emailSalutation'] = $model->getFullSalutation(true);
        $settings                    = $model->getSmtpSettings();
        $response['hasSmtp']         = $settings !== null && $settings->isActive() ? 1 : 0;

        $this->log(
            'User angefragt Response',
            $response,
            'application.rest',
            'actionUser'
        );

        $this->_sendResponse(200, CJSON::encode($response));
    }

    /**
     * Liefert das Objekt Agency_files zurück, welches das Logo der Firma abbildet <br />
     *
     * @return void
     */
    public function actionAgencyLogo()
    {
        //Antwort dieser action ist derzeit noch sehr inkonsistent...
        $userid     = currentUser()->id;
        $systemuser = Systemuser::model()->findByPk($userid);
        $response   = [
            'status'  => MessageStatus::FAILED,
            'message' => '',
            'id'      => 0,
        ];

        if (empty($systemuser)) {
            $response['message'] = 'Der User konnte nicht gefunden werden.';
            $this->_sendResponse(200, CJSON::encode($response));
        }

        $agencyLogo = AgencyFiles::model()->findByAttributes([
                                                                 'agency_id'        => $systemuser->agency_id,
                                                                 'document_type_id' => DocumentType::LOGO,
                                                             ]);
        if (empty($agencyLogo)) {
            $response['message'] = 'Für die Firma des Users konnte kein Logo gefunden werden';
            $this->_sendResponse(200, CJSON::encode($response));
        }

        /** @var SAImageDisplayer $handler */
        $handler = $this->widget('ext.SAImageDisplayer.SAImageDisplayer', [
            'baseDir'               => dirname($agencyLogo->path),
            'baseDirEqualsBasePath' => true,
            'isFullImagePath'       => true,
            'image'                 => $agencyLogo->path,
            'size'                  => 'calculator',
            'displayImage'          => false,
            'returnNewImagePath'    => true,
        ]);

        $agencyLogo->path = $handler->getResizedImageUrl();

        $this->_sendResponse(200, CJSON::encode($agencyLogo->attributes));
    }

    /**
     * Liefert die Vermittlernummer eines Vermittlers bei einer Gesellschaft zurück <br />
     *
     * @return void
     */
    public function actionBrokerId()
    {
        $gId = (int) $_GET['gid']; // Die Id der Gesellschaft
        $uId = (int) $_GET['uid']; // Die Id des Nutzers

        $this->log(
            PHP_EOL . ' Vermittlernummer angefragt mit ',
            compact('gId', 'uId'),
            'application.rest',
            'actionBrokerId'
        );
        $user = UserProvider::forUser(currentUser())->load($uId);
        if (!empty($user)) {
            $result = BrokerIdProvider::find($user)->one($gId);
        }
        if (empty($result)) {
            $this->_sendResponse(200, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'No BrokerId found',
                                                    ]));
        }

        // Damit weiterhin ein Objekt übergeben wird, wird hier ein neues erzeugt
        $brokerid                       = new BrokerId();
        $brokerid->user_id              = $uId;
        $brokerid->insurance_company_id = $gId;
        $brokerid->type                 = BrokerIdType::ANTRAGSNUMMER;
        $brokerid->status               = BrokerIdStatus::AKTIV;
        $brokerid->brokerid             = $result;

        $this->_sendResponse(200, CJSON::encode($brokerid)); // Hier wirden ein Objekt zurueck gegeben
    }

    /**
     * @return void
     * @throws CHttpException
     */
    public function actionBrokerIdJsend(): void
    {
        $gId = (int) $_GET['gid']; // Die Id der Gesellschaft
        $uId = (int) $_GET['uid']; // Die Id des Nutzers

        $this->log(
            PHP_EOL . ' Vermittlernummer angefragt mit ',
            compact('gId', 'uId'),
            'application.rest',
            'actionBrokerIdJsend'
        );

        $user = UserProvider::forUser(currentUser())->load($uId);
        if (!empty($user)) {
            $result = BrokerIdProvider::find($user)->distributionBrokerId($gId);
        }
        if (empty($result)) {
            JSendResponse::fail(['message' => 'No BrokerId found'])->respond();
        }

        JSendResponse::success(['vermittlernummer' => $result])->respond();
    }

    /**
     * Diese Funktion übergibt die Profie, die der übergebene Benutzer
     * sehen darf.
     *
     * POST: user_id es muss die user_id im feld 'user_id' übergeben werden.
     */
    public function actionGetProfiles()
    {
        $user_id       = (int) Yii::app()->request->getParam('user_id', '');
        $calculator_id = (int) Yii::app()->request->getParam('calculator_id', '');
        $global        = Yii::app()->request->getParam('global', '');

        $criteria = new CDbCriteria();

        if (!Yii::app()->user->getPermissions()->canSeeUser($user_id)) {
            throw new CHttpException(403);
        }

        if (!empty($user_id) && !empty($calculator_id)) {
            $userPermissions = UserPermission::forUser(User::model()->findByPk($user_id));
            $criteria->addInCondition('user_id', $userPermissions->getUnderlingsIds());
            $criteria->compare('product_combo_id', $calculator_id);
            $criteria->addCondition('global is null');
        } else {
            $this->_sendResponse(200,
                CJSON::encode([
                                  'status'  => MessageStatus::FAILED,
                                  'message' => 'User-Id oder calculator-ID leer',
                              ]));
        }
        if (!empty($global) || Yii::app()->user->isAdmin()) {
            $criteria->addCondition('global = 1 AND product_combo_id = :pc_id', 'OR');
            $criteria->params[':pc_id'] = $calculator_id;
            $criteria->order            = 'global, name';
        }
        $models = ProfileCalculator::model()->findAll($criteria);

        if (null !== $models) {
            $this->_sendResponse(200,
                CJSON::encode([
                                  'status'   => MessageStatus::SUCCESS,
                                  'message'  => 'Rückgabewert: Profileattribute',
                                  'profiles' => $models,
                              ]));
        }
    }

    /**
     * Liefert die Kontaktperson einer Gesellschaft zu einem Bestimmten Fachgebiet und Sparte zurück.<br />
     *
     * @return void
     */
    public function actionContactPersonId()
    {
        Yii::log('actionContactPersonId: ' . print_r($_GET, true), CLogger::LEVEL_INFO, 'application.rest');
        //Wert aus der URL ziehen
        $productcomboid     = $_GET['pcId']; //ProductComboldId
        $insurancecompanyid = $_GET['icId']; //GesellschaftId
        $subjectid          = $_GET['sId']; //FachbereichId
        $userId             = $_GET['uId']; //UserID

        $systemUser = UserProvider::forUser(currentUser())->load($userId); //User hollen
        $zip        = $systemUser->address !== null ? $systemUser->address->zip : null;

        $model = AnsprechpartnerSuche::new($insurancecompanyid, $systemUser)
                                     ->forProductComboId($productcomboid)
                                     ->forSubject($subjectid)
                                     ->forZipcode($zip)
                                     ->one();

        if (null === $model) {
            $this->_sendResponse(404, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'No ContactPerson found',
                                                    ]));
        } else {
            $response['contactPerson'] = $model;
            $response['address']       = $model->address;
            $response['contact']       = $model->contact;

            $this->_sendResponse(200, CJSON::encode($response));
        }
    }

    /**
     * Versendet Mails über den DemvMailer. <br />
     * Anhänge müssen Dateien der Digitalen Kundenakte sein.
     *
     * @return void
     */
    public function actionEMail()
    {
        $this->log(
            'Parameter übermittelt',
            $_POST,
            'application.rest',
            'actionEMail'
        );
        $subject    = Yii::app()->request->getParam('subject');
        $message    = Yii::app()->request->getParam('message');
        $replyto    = Yii::app()->request->getParam('replyto');
        $smtp       = Yii::app()->request->getParam('use_smtp', 0);
        $smtpUserId = Yii::app()->request->getParam('smtp_user_id', Yii::app()->user->getId());

        $footer = Yii::app()->request->getParam('footer', 1);

        if ($footer == 1) {
            $message .= '<br /><br />Mit freundlichen Grüssen<br /><br />Ihr DEMV-Team';
            $message .= DefaultMailFooter::getContent();
        } else {
            if ($footer == 2) {
                $message .= Footer::forCurrentUser()->getContent();
            }
        }

        $smtpUser = UserProvider::forUser(currentUser())->load($smtpUserId);
        $mailer   = $smtp ? MailerFactory::create()->useMaklerOrSystemSmtp($smtpUser) : MailerFactory::create()->useSystemSmtp();

        if (!empty($replyto)) {
            $mailer->setReplyTo($replyto);
        } elseif ($smtp) {
            $mailer->setReplyTo($smtpUser->contact->email ?? $smtpUser->email, '');
        }

        $contentAttachments = Yii::app()->request->getPost('contentAttachments');

        if (!empty($contentAttachments)) {
            $contentAttachments = json_decode($contentAttachments, true);
            foreach ($contentAttachments as $attachment) {
                //set internally adds Attachments
                $mailer->setFileAttachments([$attachment['name'] => base64_decode($attachment['content'])]);
            }
        }

        $cc  = Yii::app()->request->getParam('cc', null);
        $bcc = Yii::app()->request->getParam('bcc', null);
        if (!empty($cc)) {
            $cc = explode(', ', urldecode($cc));
            $mailer->setCCs($cc);
        }
        if (!empty($bcc)) {
            $bcc = explode(', ', urldecode($bcc));
            $mailer->setBCCs($bcc);
        }

        $mailer->setSubject(urldecode($subject));
        $view = 'default' . DIRECTORY_SEPARATOR . 'empty';
        //Inhalt muss durch die View geladen werden, damit das Design das gleiche ist wie die des Hauptsystems
        $mailer->setView($view, ['content' => urldecode($message)]);

        if (!$mailer->send(Yii::app()->request->getParam('recipient'))) {
            $this->_sendResponse(404, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'Mail not sent',
                                                    ]));
        } else {
            $this->_sendResponse(200, CJSON::encode([
                                                        'status'  => MessageStatus::SUCCESS,
                                                        'message' => 'Mail sent',
                                                    ]));
        }
    }

    /**
     * Legt einen Vorgang an
     */
    public function actionProcedure()
    {
        Yii::log('actionProcedure: ' . print_r($_POST, true), CLogger::LEVEL_INFO, 'application.rest');

        $data = $_POST;
        UserProvider::forUser(currentUser())->load($data['UserMail']['user_id']);
        if (!empty($data['UserMail']['mail_procedure_id']) && $data['UserMail']['mail_procedure_id'] == MailProcedure::VERMITTLERNUMMER_FEHLT) {
            $data['Mail']['content'] = 'In einer separaten E-Mail erhalten Sie die Kooperationsunterlagen und den Antrag zu diesem Vorgang.<br />{maklersignatur}';
        }
        $data['UserMail']['send_date'] = date('Y-m-d');
        $id                            = null;
        $url                           = '';

        $attachments = $this->parseAttachments($data['Mail']['attachments'] ?? []);
        unset($data['Mail']['attachments']);

        $korrespodenz = Korrespondenz::mail()
            ->fuerUser(currentUser())
            ->ausUserMailData($data)
            ->mitAnhaengen($attachments)
            ->alsBereitsGesendet(true)
            ->anlegen();

        if ($korrespodenz !== null) {
            $id  = $korrespodenz->getId();
            $url = $korrespodenz->link;
        }
        if (!empty($id)) {
            Yii::log('actionProcedure: Angelegt!', CLogger::LEVEL_INFO, 'application.rest');
            $this->_sendResponse(200, CJSON::encode([
                                                        'status'  => MessageStatus::SUCCESS,
                                                        'message' => $id,
                                                        'url'     => $url
                                                    ]));
        } else {
            $this->_sendResponse(200, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'Mail not sent',
                                                    ]));
        }
    }

    /**
     * POST-Parameter: user_id, client_file_id, company_id
     */
    public function actionAntragGestellt()
    {
        Yii::log('actionAntragGestellt: ' . print_r($_POST, true), CLogger::LEVEL_INFO, 'application.rest');

        $userId       = (int) Yii::app()->request->getPost('user_id', 0);
        $clientFileId = (int) Yii::app()->request->getPost('client_file_id', 0);

        UserProvider::forUser(currentUser())->load($userId);

        if (empty($clientFileId) || empty($userId)) {
            return;
        }

        /** @var ClientFiles $clientFile */
        $clientFile = ClientFiles::model()->findByPk($clientFileId);

        if (!empty($clientFile)) {
            $companyId = (int) Yii::app()->request->getPost('company_id', $clientFile->insurance_company_id);
            $userFile  = new UserFiles();

            if (!BrokerIdService::new()->mainBrokerIdExists($userId, $companyId)) {
                $userFile->saveFromClientFile($clientFile, $userId, DocumentType::ERSTANTRAG, $companyId);
            }
        }
    }

    private function parseAttachments(array $attachments): array
    {
        $sources = new AttachmentSources();
        $return  = array_map(static function (string $attachment) use ($sources) {
            if (strpos($attachment, 'client') !== false) {
                return ClientfileProvider::forUser(currentUser())
                    ->find()
                    ->findByPk(str_replace('client_', '', $attachment));
            }

            $attachment = $sources->getAttachment($attachment);
            if ($attachment !== null) {
                return $attachment;
            }

            return null;
        }, $attachments);

        return array_filter($return);
    }

    /**
     * Legt die Courtage (Courtagemodul) an
     */
    public function actionUserCourtage()
    {
        Yii::log('actionUserCourtage' . print_r($_POST, true), CLogger::LEVEL_INFO, 'application.rest');

        /*
         * Form
         * array (
         * 'UserCourtage' => array(
         *                          key => value,
         *                          key => value,
         *                          )
         *      )
         *
         * Benötigte Daten im Array
         *
         * user_id = ID des Maklers, für den die Courtage erfasst werden soll
         * insurance_company_id = ID der Gesellschaft
         * client_id = ID des Kunden
         * product_combo_id = ID der ProduktKombo aus unserem System
         * courtage_amount = Courtagepflichtiger Beitrag (Runtergerechnet auf die Zahlweise also kein fester Monats- oder Jahresbeitrag)
         * start_date = Beginndatum des Vertrages
         * end_date = Ablaufdatum des Vertrages
         * payment_type = Zahlweise der Courtageerfassung (ContractPaymentTypes)
         * commision = %-Wert der Abschlussprovision
         * commission_type_id = Erstellungsgrund
         *
         *
         * Optionale Werte
         *
         * user_mail_id = ID der UserMail, die zu dem Antrag gehört
         * courtage_dynamic = Dynamik der Courtage
         * trailer_commission = %-Wert der Bestandsprovision
         * dynamic = Dynamik der Berechnungsgrundlagen
         * rating_amount = Bewertungssumme der Courtageerfassung
         * max = Maximierung der CA
         * storno_reserve = %-Satz der Stornoreserve
         * factor = Faktor der CA
         * responseability_period = Haftungszeitraum in Monaten
         * bonus = Bonusangabe in % oder €
         * bonus_percent = 1, wenn bonus ein %-Wert ist, 0 wenn bonus ein €-Wert ist
         * trust_damage_insurance_height = Angabe zur VSV
         * trust_damage_insurance_percent = 1, wenn trust_damage_insurance_height ein %-Wert ist, 0 wenn trust_damage_insurance_height ein €-Wert ist
         * contract_id = ID des zugeordneten Vertrages
         *
         *
         */
        /**
         * BEISPIEL
         * $data = [
         * 'UserCourtage' => [
         * 'user_id' => 799,
         * 'insurance_company_id' => 128,
         * 'client_id' => 55376,
         * 'product_combo_id' => 171,
         * 'courtage_amount' => 1814.**********,
         * 'start_date' => "27.02.2015",
         * 'end_date' => "2016-02-26",
         * 'payment_type' => 1
         * ]
         * ];
         */
        $data = $_POST['UserCourtage'];
        UserProvider::forUser(currentUser())->load($data['user_id']);
        $rename = [
            'courtage_amount'               => 'contribution',
            'trust_damage_insurance_height' => 'trust_damage_insurance',
        ];
        foreach ($rename as $old => $new) {
            if (isset($data[$old])) {
                $data[$new] = $data[$old];
                unset($data[$old]);
            }
        }

        $transaction = CourtageData::model()->getDbConnection()->beginTransaction();
        try {
            $courtageData             = new CourtageData('create');
            $courtageData->attributes = $data;
            $courtageData->autoSetCommissionPaymentType();

            $courtage             = new Courtage('create');
            $courtage->attributes = $data;

            $tarif = TarifController::getTarif((int) $data['user_id'], $data['insurance_company_id'], $data['product_combo_id']);

            if (!empty($tarif)) {
                $courtage->setTarifData($tarif);
            }

            if ($courtageData->addCourtage($courtage)) {
                $courtage->createSuperiorCourtages();
                if ($courtageData->save()) {
                    $transaction->commit();
                }
            }
        } catch (Exception $e) {
            $transaction->rollback();
        }
    }

    /**
     * erstellt ein Kündigungsschreiben
     */
    public function actionDismissal()
    {
        $type               = Yii::app()->request->getParam('type');
        $clientId           = Yii::app()->request->getParam('client_id');
        $brokerId           = Yii::app()->request->getParam('broker_id');
        $contractNr         = Yii::app()->request->getParam('contract_nr');
        $dismissalDate      = Yii::app()->request->getParam('dismissal_date');
        $productId          = Yii::app()->request->getParam('product_id');
        $insuranceCompanyId = Yii::app()->request->getParam('insurance_company_id');

        $user             = UserProvider::forUser(currentUser())->load($brokerId);
        $client           = Client::model()->findByPk($clientId);
        $insuranceCompany = InsuranceCompany::model()->findByPk($insuranceCompanyId);

        if (empty($client)) {
            $this->_sendResponse(200, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'Client existiert nicht!',
                                                    ]));
        }
        if (empty($user)) {
            $this->_sendResponse(200, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'User existiert nicht!',
                                                    ]));
        }

        if (empty($insuranceCompany)) {
            $this->_sendResponse(200, CJSON::encode([
                                                        'status'  => MessageStatus::FAILED,
                                                        'message' => 'Gesellschaft existiert nicht!',
                                                    ]));
        }

        $contract = new Contracts();

        $contract->company_id  = $insuranceCompanyId;
        $contract->contract_nr = $contractNr;
        $contract->due_date    = $dismissalDate;
        $contract->client      = $client;
        $contract->user_id     = $brokerId;

        $dokumentName = $type === 'client' ? 'Kundenkuendigung' : 'Maklerkuendigung';
        $name         = sprintf('%s_%s_%s.pdf', $dokumentName, date('d-m-Y'), time());

        $pdf = new DismissalGenerator();
        $pdf->Open();
        $sender = $type === 'client'
            ? DismissalSender::fromClient($client)
            : DismissalSender::fromUser($user, $client->getFullname());
        $pdf->generate(
            $sender,
            DismissalRecipient::fromInsuranceCompany($contract->insurance_company),
            DismissalContract::fromContract($contract)
        );
        $pdf->Close();
        $content = $pdf->Output('', 'S');

        if ($client->getFilesystem()->write($name, $content)) {
            $clientFile                       = new ClientFiles();
            $clientFile->client_id            = $client->id;
            $clientFile->document_type_id     = DocumentType::KUENDIGUNG;
            $clientFile->insurance_company_id = $insuranceCompanyId;
            $clientFile->product_combo_id     = $productId;
            $clientFile->name                 = $name;
            $clientFile->path                 = $clientFile->getPath();
            $clientFile->setContentHashFromString($content);

            if (!$clientFile->save()) {
                $this->_sendResponse(
                    200,
                    CJSON::encode(
                        [
                            'status'  => MessageStatus::FAILED,
                            'message' => 'File wurde gespeichert. Der Eintrag in der DB konnte NICHT erstellt werden!',
                        ]
                    )
                );
            }
            $this->_sendResponse(
                200,
                CJSON::encode(
                    [
                        'status'       => MessageStatus::SUCCESS,
                        'message'      => 'Kuendigungsschreiben erfolgreich erstellt und gespeichert!',
                        'clientFileID' => $clientFile->id,
                        'path'         => $client->id . DIRECTORY_SEPARATOR . $name,
                    ]
                )
            );
        }
        $this->_sendResponse(
            200,
            CJSON::encode(
                [
                    'status'  => MessageStatus::FAILED,
                    'message' => 'Kuendigungsschreiben konnte trotz existierender Gesellschaft, Client und User nicht erstellt und gespeichert werden!',
                ]
            )
        );
    }

    /**
     * Befüllt ein Sepamandat
     */
    public function actionCreateSEPA()
    {
        $filename = 'SEPA_' . date('d.m.Y') . '.pdf';
        $path     = Yii::getPathOfAlias('uploads.system');

        $data = [
            '0'  => Yii::app()->request->getParam('0', ''),
            '1'  => Yii::app()->request->getParam('1', 'X'),
            '2'  => Yii::app()->request->getParam('2', ''),
            '3'  => Yii::app()->request->getParam('3', ''),
            '4'  => Yii::app()->request->getParam('4', ''),
            '5'  => Yii::app()->request->getParam('5', ''),
            '6'  => Yii::app()->request->getParam('6', ''),
            '7'  => Yii::app()->request->getParam('7', ''),
            '8'  => Yii::app()->request->getParam('8', ''),
            '9'  => Yii::app()->request->getParam('9', ''),
            '10' => Yii::app()->request->getParam('10', ''),
        ];

        $pdf = new Pdf(Yii::getPathOfAlias('uploads.system') . '/SEPA-Mandat.pdf');
        $pdf->fillForm($data)->flatten();
        $pdf->send($filename);
    }

    public static function getAllowedServices(): array
    {
        return Services::DEMV_INTERNAL_SERVICES;
    }
}
