<?php

class RightsController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Gibt zurück, ob der eingeloggte User das Rechte hat die übergebene Action an dem bestimmten Controller aufzurufen
     *
     * @param type $controller
     *              Eine Instanz des Controllers
     * @param type $action
     *              Der Name der Action
     *
     * @return boolean
     */
    public static function hasRight($controller, $action)
    {
        if ($controller instanceof Controller) {
            if ($controller instanceof AllowAllController) {
                return true;
            }
            if ($controller instanceof GlobalRightController || $controller instanceof AdminController) {
                $accessRules = $controller->accessRules();

                return $accessRules[0][0] == 'allow';
            }
            if ($controller instanceof RightController) {
                $accessRules = $controller->accessRules();

                if ($accessRules[0][0] == 'allow' && !isset($accessRules[0]['actions']) && !isset($accessRules[1])) {
                    return true;
                }
                if (!isset($accessRules[0]['actions'])) {
                    return false;
                }
                $search_array = array_map('strtolower', $accessRules[0]['actions']);

                return in_array(strtolower($action), $search_array);
            }

            return true;
        }

        return false;
    }

    public static function hasSpecificRight($user_id, $right_id)
    {
        if (Yii::app()->user->isAdmin()) {
            return true;
        }

        if ($user_id === null) {
            return false;
        }

        $command = 'select count(id) from user_rights ur where ur.user_id = ' . $user_id . ' and ur.right = ' . $right_id;
        $result  = Yii::app()->db->createCommand($command)->queryRow();

        return !empty($result['count(id)']);
    }

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('//stammdaten/rechteManagement/rechte/view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param integer the ID of the model to be loaded
     */
    public function loadModel($id)
    {
        $model = Rights::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $model;
    }

    /**
     * Creates a new model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     */
    public function actionCreate()
    {
        $model = new Rights();

        // Uncomment the following line if AJAX validation is needed
        // $this->performAjaxValidation($model);

        if (isset($_POST['Rights'])) {
            $model->attributes = $_POST['Rights'];
            if ($model->save()) {
                $this->redirect(['admin']);
            }
        }

        $this->render('//stammdaten/rechteManagement/rechte/create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates a particular model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id the ID of the model to be updated
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        // Uncomment the following line if AJAX validation is needed
        // $this->performAjaxValidation($model);

        if (isset($_POST['Rights'])) {
            $model->attributes = $_POST['Rights'];
            if ($model->save()) {
                $this->redirect(['admin']);
            }
        }

        $this->render('//stammdaten/rechteManagement/rechte/update', [
            'model' => $model,
        ]);
    }

    /**
     * Updates a particular model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id the ID of the model to be updated
     */
    public function actionUpdateDesc($id)
    {
        $model = $this->loadModel($id);
        // Uncomment the following line if AJAX validation is needed
        // $this->performAjaxValidation($model);

        if (isset($_POST['Rights'])) {
            $model->attributes = $_POST['Rights'];
            if ($model->save()) {
                $this->redirect(['admin']);
            }
        }

        $this->render('//stammdaten/rechteManagement/rechte/updateDesc', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes a particular model.
     * If deletion is successful, the browser will be redirected to the 'admin' page.
     *
     * @param integer $id the ID of the model to be deleted
     */
    public function actionDelete($id)
    {
        if (Yii::app()->request->isPostRequest) {
            // we only allow deletion via POST request
            $this->loadModel($id)->delete();

            // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
            if (!isset($_GET['ajax'])) {
                $this->redirect($_POST['returnUrl'] ?? ['admin']);
            }
        } else {
            throw new CHttpException(400, 'Invalid request. Please do not repeat this request again.');
        }
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('Rights');
        $this->render('//stammdaten/rechteManagement/rechte/index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new Rights('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['Rights'])) {
            $model->attributes = $_GET['Rights'];
        }

        $this->render('//stammdaten/rechteManagement/rechte/admin', [
            'model' => $model,
        ]);
    }

    public function actionGetActions()
    {
        $controller = $_POST['controller'];
        $actions    = [];

        $object = new $controller('id');
        if ($object instanceof GlobalRightController) {
            array_push($actions, 'actionAllowAll');
        } else {
            if ($object instanceof RightController) {
                $methods = get_class_methods($controller);
                foreach ($methods as $method) {
                    if (!in_array(str_replace('action', '', $method), $object->excludedActions)) {
                        if (preg_match('/^action+\w{2,}/', $method)) {
                            array_push($actions, $method);
                        }
                    }
                }
            }
        }

        $translate = [
            'Create'   => 'Erstellen',
            'Update'   => 'Bearbeiten',
            'Delete'   => 'Löschen',
            'AllowAll' => 'Alles',
            'Admin'    => 'Übersicht'
        ];

        foreach ($actions as $action) {
            $name                             = str_replace('action', '', $action);
            isset($translate[$name]) ? $value = $translate[$name] : $value = $name;
            echo CHtml::tag('option', ['value' => "$action"], $value, 'foo', true);
        }
        Yii::app()->end();
    }

    public function buildRulesArray()
    {
        $controllers = $this->getController();
        $rules       = [];
        $rights      = Rights::model()->findAll();
        $userRights  = UserRights::model()->findAllByAttributes(['user_id' => Yii::app()->user->getId()]);
        foreach ($controllers as $controllername) {
            $controller = new $controllername(str_replace('Controller', '', $controllername));
            $actions    = $this->getActions($controller);
            if ($controller instanceof GlobalRightController) {
                $parentController = $controller;
                while (!empty($parentController->parentid)) {
                    $parentname       = $parentController->parentid . 'Controller';
                    $parentController = new $parentname(strtolower($parentController->parentid));
                }
                $right = $this->getRight($rights, get_class($parentController), 'AllowAll');
                if ($this->hasUserRight($userRights, $right)) {
                    $rules[$controllername][] = 'allowAll';
                }
                if (!empty($controller->excludedActions)) {
                    foreach ($controller->excludedActions as $action) {
                        $rules[$controllername][] = $action;
                    }
                }
            } else {
                if ($controller instanceof RightController) {
                    foreach ($actions as $action) {
                        $right = $this->getRight($rights, $controllername, $action);
                        if ($this->hasUserRight($userRights, $right)) {
                            $rules[$controllername][] = $action;
                        }
                    }
                }
            }
        }

        return $rules;
    }

    private function getController()
    {
        $declaredClasses = get_declared_classes();
        $controllers     = [];
        foreach (glob(Yii::getPathOfAlias('application.controllers') . '/*Controller.php') as $controller) {
            $class = basename($controller, '.php');
            if (!in_array($class, $declaredClasses)) {
                Yii::import('application.controllers.' . $class, true);
            }
            $controllers[] = $class;
        }

        return $controllers;
    }

    private function getActions($controller)
    {
        $actions = [];
        foreach (get_class_methods($controller) as $key => $method) {
            if (strpos($method, 'action') !== false) {
                $action = str_replace('action', '', $method);
                if (!empty($action)) {
                    $actions[] = $action;
                }
            }
        }

        return $actions;
    }

    public function getRight($rights, $controllername, $action)
    {
        $model  = null;
        $return = [];
        if (substr($action, 0, 6) != 'action') {
            $action = 'action' . $action;
        }

        foreach ($rights as $right) {
            if (strtolower($right->controller ?? '') == strtolower($controllername ?? '') && strtolower($right->action ?? '') == strtolower($action ?? '')) {
                $return[] = $right;
            }
        }
        if (empty($return)) {
            return null;
        }
        foreach ($return as $model) {
            $model = $model->parentright;
            while (!empty($model)) {
                $return[] = $model;
                $model    = $model->parentright;
            }
        }

        return count($return) == 1 ? $return[0] : $return;
    }

    public function hasUserRight($userrights, $right)
    {
        if (!empty($right)) {
            if (!is_array($right)) {
                $right = [$right];
            }
            $ids = [];
            foreach ($right as $r) {
                $ids[] = $r->id;
            }
            foreach ($userrights as $ur) {
                if (in_array($ur->right, $ids)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Performs the AJAX validation.
     *
     * @param CModel the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'rights-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
