<?php

use Anfragen\Data\ClientDataAccess;
use Anfragen\Data\MailDataPreparation;
use Anfragen\Mail\AnfragenEmpfaenger;
use Business\Ansprechpartner\Filter\NationalwideFilter;
use Business\Ansprechpartner\Filter\PoolFilter;
use Business\Ansprechpartner\Filter\ProductFilter;
use Business\Ansprechpartner\Filter\StateFilter;
use Business\Ansprechpartner\Filter\ZipAndStateFilter;
use Business\Ansprechpartner\Filter\ZipFilter;
use Business\Ansprechpartner\Provider\SupportPersonsQuery;
use Business\Ansprechpartner\SupportPersonContainer;
use Business\Company\Kooperation\SystemweiteGesellschaften;
use Business\Mail\Attachments\PathAttachment;
use Business\Vorgaenge\Korrespondenz;
use Business\Vorgaenge\Vorgangsgruppe;
use Components\Client\Providers\ClientfileProvider;
use Components\Mail\Mailer\MailerFactory;
use Components\Vermittlernummern\BrokerIdProvider;
use Demv\VorgaengeSdk\Support\Enum\VorgangsTyp;
use League\Flysystem\FileNotFoundException;

/**
 * Class RiskCheckController
 */
class RiskCheckController extends AllowAllController
{
    public static $menuassignment = [
        Menu::VORSORGE        => 'Vorsorge',
        Menu::PKV             => 'PKV',
        Menu::SACH_PRIVAT     => 'Sachversicherung',
        Menu::SACH_GEWERBE    => 'Sachversicherung',
        Menu::BAUFINANZIERUNG => 'Baufinanzierung',
    ];

    private static $productcombos = [
        //Vorsorge
        Menu::VORSORGE    => [
            135 => 'Berufsunfähigkeitsversicherung',
            136 => 'Risikolebensversicherung',
            239 => 'Pflegeversicherung',
            137 => 'Dread Disease',
            250 => 'Erwerbsunfähigkeitsversicherung',
            138 => 'Grundfähigkeitsversicherung',
        ],
        //PKV
        Menu::PKV         => [
            156 => 'Private Krankenvollversicherung',
            157 => 'Private Krankenzusatzversicherung',
            159 => 'Krankentagegeld',
            160 => 'Pflegetagegeld',
        ],
        //Privat-Sach
        Menu::SACH_PRIVAT => [
            171 => 'KFZ',
            169 => 'Rechtsschutz',
            196 => 'Sonstiges',
        ],
    ];

    /**
     * RiskCheckController constructor.
     *
     * @param string $id
     * @param null   $module
     */
    public function __construct($id, $module = null)
    {
        parent::__construct($id, $module);

        /**
         *
         * MENU FIXES
         *
         */
        if (strpos(Yii::app()->request->url, 'vorsorge')) {
            $this->menuGroup = MenuGroup::VORSORGE;
        }
        if (strpos(Yii::app()->request->url, 'pkv')) {
            $this->menuGroup = MenuGroup::PKV;
        }
        if (strpos(Yii::app()->request->url, 'sach-privat')) {
            $this->menuGroup = MenuGroup::SACHVERSICHERUNG_PRIVAT;
        }
        if (strpos(Yii::app()->request->url, 'sach-gewerbe')) {
            $this->menuGroup = MenuGroup::SACHVERSICHERUNG_GEWERBE;
        }
        if (strpos(Yii::app()->request->url, 'baufinanzierung')) {
            $this->menuGroup = MenuGroup::BAUFINANZIERUNG;
        }
    }

    /**
     * @param string $produkt
     * @param bool   $offer
     * @param null   $user_mail_id
     * @param null   $client_id
     */
    public function actionCreate($produkt, $offer = false, $user_mail_id = null, $client_id = null): void
    {
        if (!isset(self::$menuassignment[$produkt])) {
            Yii::app()->user->setFlash('error', 'Es wurde keine Sparte angegeben');
            $this->redirect('/home');
        }
        $sector                = self::$menuassignment[$produkt];
        $productMenuAssignment = MenuProductAssignment::Model()->findByAttributes(['menu_id' => $produkt]);
        $productcomboid        = $productMenuAssignment->product_combo_id;

        $riskCheck = new RiskCheck();
        if (!empty($user_mail_id)) {
            $user_mail = UserMail::model()->findByPk($user_mail_id);
            if (!empty($user_mail)) {
                $riskCheck = $user_mail->getRiskCheck();
            }
        }
        if (!empty($client_id)) {
            $riskCheck->client_id = $client_id;
        }

        $riskCheck->attributes = @$_GET['RiskCheck'];
        $riskCheck->produkt    = $produkt;
        $riskCheck->sector     = strtolower($sector);
        if (empty($riskCheck->product_combo_id)) {
            $riskCheck->product_combo_id = $productcomboid;
        }
        $riskCheck->setOffer($sector);
        $riskCheck->offer = $riskCheck->offer || $offer;
        $riskCheck->unserializeData();
        $data = $this->getCreateFormularData($riskCheck) ?? [];

        if (array_key_exists('create', $data) && isset($data['create'])) {
            $url = $this->create($sector, $data);
            if (null === $url) {
                Yii::app()->user->setFlash('error', 'Es konnte keine Anfrage gesendet werden');
                $this->redirect(['riskCheck/create', 'produkt' => $produkt, 'offer' => $riskCheck->offer]);
            } else {
                Yii::app()->user->setFlash('success', 'Ihre Anfrage wurde erfolgreich gesendet');
                $this->redirect($url);
            }
        }
    }

    /**
     * @param string $sector
     * @param array  $data
     *
     * @return null|string
     */
    private function create($sector, $data): ?string
    {
        $user = currentUser();

        $client = new Client();
        if (!empty($data['Client']['id'])) {
            $client = ClientProvider::forUser($user)->load($data['Client']['id']);
        }

        /** @var ProductCombo $productCombo */
        $productCombo = ProductCombo::model()->findByPk($data['Contract']['product_combo_id']);

        $clientDataAccess = new ClientDataAccess($user, $client);
        $clientDataAccess->setAttributes($data);

        if (isset($data['RiskCheck']['save_changes']) && $data['RiskCheck']['save_changes'] == 1) {
            $clientDataAccess->save();
        }

        $preparation = new MailDataPreparation();
        $preparation->initData($client, $data);

        $comment = $data['RiskCheck']['comment'];

        $subject   = '';
        $riskCheck = true;
        if ($sector == 'Vorsorge' || $sector == 'PKV') {
            $subject = 'Risikovoranfrage von ';
        } else {
            if ($sector == 'Sachversicherung') {
                $riskCheck = false;
                $subject   = 'Angebotsanfrage von ';
            }
        }
        $subject .= $user->Fullname;

        if (empty($data['Companies']) && empty($data['RiskCheck']['toDemv'])) {
            return null;
        }

        $attachments     = [];
        $attachmentPaths = [];
        $procedure       = ProcedureController::generateNewProcedure($user->id);
        $path            = Yii::app()->basePath . DS . 'views' . DS . 'mail' . DS . 'template' . DS . 'riskCheck.php';
        $content         = $this->renderInternal($path,
            [
                'anonymise'      => $data['RiskCheck']['anonymise_data'] ?? 0,
                'comment'        => $comment,
                'clientFields'   => $preparation->getClientData(),
                'contractfields' => $preparation->getContractData(),
            ], true);

        FileController::fixFilesArray();
        foreach ($_FILES['ProcedureFiles']['name']['file'] ?? [] as $key => $value) {
            $file = ProcedureFileUpload::upload($procedure, CUploadedFile::getInstance(new ProcedureFiles(), 'file[' . $key . ']'));
            if (!empty($file)) {
                $data['ClientFiles']['attachments'][] = $this->copyFileToClient($client, $file);
            }
        }

        $data['ClientFiles']['attachments'] = array_filter($data['ClientFiles']['attachments'] ?? [], function ($value) {
            return !empty($value);
        });

        foreach ($data['ClientFiles']['attachments'] ?? [] as $id) {
            /** @var ClientFiles $file */
            $file = ClientfileProvider::forUser(currentUser())->find()->findByAttributes(['id' => $id, 'client_id' => $client->id]);
            if ($file !== null) {
                $attachments[]     = $file;
                $attachmentPaths[] = $file->getPath();
            }
        }
        foreach ($data['RiskCheck'] as $attr => $path) {
            if (strpos($attr, 'attachment') !== false) {
                if ($this->checkAttachmentAccess($path)) {
                    $attachments[]     = new PathAttachment(basename($path), $path);
                    $attachmentPaths[] = $path;
                }
            }
        }
        $riskCheckData                 = new RiskCheckData();
        $riskCheckData->contract_data  = serialize($preparation->getContractData());
        $riskCheckData->client_data    = serialize($preparation->getClientData());
        $riskCheckData->anonymise_data = $data['RiskCheck']['anonymise_data'] ?? 0;
        if (empty($data['Configuration'])) {
            $data['Configuration'] = [];
        }
        $riskCheckData->configuration_data = serialize($data['Configuration']);
        $riskCheckData->comment            = $comment;
        $riskCheckData->user_id            = Yii::app()->user->getId();
        $riskCheckData->save();

        $vorgangData = RiskCheckVorgangData::new()
            ->setProductCombo($productCombo)
            ->setUser($user)
            ->setClient($client)
            ->setAttachments($attachments)
            ->setAttachmentPaths($attachmentPaths)
            ->setRiskCheck($riskCheck)
            ->setSubject($subject)
            ->setContent($content)
            ->setData($data);

        return $this->createVorgangV2($vorgangData);
    }

    private function createVorgangV2(RiskCheckVorgangData $vorgangData): ?string
    {
        //Mail an DEMV
        $clientId = $vorgangData->getClient()->id;
        if (!empty($vorgangData->getData()['RiskCheck']['toDemv'])) {
            $korrespondenz = Korrespondenz::mail();
            $korrespondenz->an((new AnfragenEmpfaenger())->getMailaddress($vorgangData->getProductCombo()));
            $korrespondenz->mitBetreff('Angebotsanfrage');
            $korrespondenz->mitInhalt($vorgangData->getContent());
            $korrespondenz->mitBccs([$vorgangData->getUser()->getMailAddress()]);
            $korrespondenz->mitAnhaengen($vorgangData->getAttachments());

            if ($clientId !== null) {
                $korrespondenz->fuerKunde((int) $clientId);
            }

            $korrespondenz->inSparte((int) $vorgangData->getProductCombo()->id);
            $korrespondenz->fuerUser($vorgangData->getUser());
            $korrespondenz->fuerVorgang($vorgangData->isRiskCheck() ? VorgangsTyp::risikovoranfrage() : VorgangsTyp::anfrageAngebot());

            return $korrespondenz->anlegen()->getLink() ?? null;
        }

        $gruppe = new Vorgangsgruppe();

        if ($clientId !== null) {
            $gruppe->fuerKunde((int) $clientId);
        }

        $gruppe->inSparte((int) $vorgangData->getProductCombo()->id);
        $gruppe->fuerUser($vorgangData->getUser());
        $gruppe->fuerVorgang($vorgangData->isRiskCheck() ? VorgangsTyp::risikovoranfrage() : VorgangsTyp::anfrageAngebot());
        $brokerIdProvider   = BrokerIdProvider::find($vorgangData->getUser());
        $insuranceCompanies = InsuranceCompany::model()->findAllByPk(array_keys($vorgangData->getData()['Companies']));
        foreach ($vorgangData->getData()['Companies'] as $company_id => $receiverId) {
            $supportPerson = SupportPerson::model()->findByPk($receiverId);
            $receiverEmail = $supportPerson->contact->email ?? null;
            if ($receiverEmail === null || $receiverEmail === '') {
                if (Yii::app()->isLive()) {
                    \Sentry\captureMessage(
                        "Risikovoranfrage - Empfänger nicht gesetzt (wurde übersprungen) - (company_id =$company_id)",
                        \Sentry\Severity::warning()
                    );
                }
                continue;
            }
            $brokerid      = $brokerIdProvider->withFallback()->distributionBrokerId($company_id);
            $subjectSuffix = empty($brokerid) ? '' : ', ' . $brokerid;
            $newsubject    = $vorgangData->getSubject() . $subjectSuffix;

            $bcc = $vorgangData->getUser()->getMailAddress();

            $korrespondenz = Korrespondenz::mail();
            $korrespondenz->an($receiverEmail);
            $korrespondenz->mitBetreff($newsubject);
            $korrespondenz->mitInhalt($this->getContent(InsuranceCompany::model()->findByPk($company_id), $vorgangData->getContent(), $insuranceCompanies));
            $korrespondenz->mitBccs([$bcc]);

            $korrespondenz->mitAnhaengen($vorgangData->getAttachments());

            if ($clientId !== null) {
                $korrespondenz->fuerKunde((int) $clientId);
            }

            $korrespondenz->fuerGesellschaft((int) $company_id);
            $korrespondenz->inSparte((int) $vorgangData->getProductCombo()->id);
            $korrespondenz->fuerUser($vorgangData->getUser());
            $korrespondenz->fuerVorgang($vorgangData->isRiskCheck() ? VorgangsTyp::risikovoranfrage() : VorgangsTyp::anfrageAngebot());
            $gruppe->untervorgangHinzufuegen($korrespondenz->getKorrespondenzvorgang());
        }
        if ($gruppe->hatUntervorgaenge()) {
            return $gruppe->speichernUndSenden()->link ?? null;
        }

        return null;
    }

    /**
     * @param InsuranceCompany   $company
     * @param string             $content
     * @param InsuranceCompany[] $insuranceCompanies
     *
     * @return string
     */
    private function getContent(InsuranceCompany $company, string $content, array $insuranceCompanies): string
    {
        if (!$company->isPool()) {
            return $content;
        }

        $companyNames = array_filter(
            array_map(fn (InsuranceCompany $company) => $company->isPool() ? null : $company->name, $insuranceCompanies)
        );

        $prefix = $this->renderPartial('pool_content', ['companyNames' => $companyNames], true);

        return $prefix . $content;
    }

    /**
     * @param Client         $client
     * @param ProcedureFiles $file
     *
     * @return int|null
     * @throws FileNotFoundException
     */
    private function copyFileToClient(Client $client, ProcedureFiles $file): ?int
    {
        $content = $file->getContent();

        $name = $client->getFilesystem()->writeUnique($file->name, $content);
        if ($name === null) {
            return null;
        }
        $clientFile                   = new ClientFiles();
        $clientFile->client_id        = $client->id;
        $clientFile->upload_date      = date('Y-m-d');
        $clientFile->name             = $name;
        $clientFile->document_type_id = DocumentType::SONSTIGE;
        $clientFile->setContentHashFromString($content);

        if (!$clientFile->save()) {
            return null;
        }

        return (int) $clientFile->id;
    }

    /**
     * @param string $path
     *
     * @return bool
     */
    private function checkAttachmentAccess($path): bool
    {
        if (!empty($path)) {
            $exploded = explode(DIRECTORY_SEPARATOR, $path);
            //Vorgangsdokumente
            $pos = array_search('procedure', $exploded);
            if (!empty($pos)) {
                $model = 'Procedure';
                $id    = $exploded[$pos + 1];
            } else {
                $pos   = array_search('client', $exploded);
                $model = 'Client';
                $id    = $exploded[$pos + 1];
            }
            if (!empty($pos) && !empty($model)) {
                $found = $model::model()->findByPk($id);

                return !empty($found);
            }
        }

        return false;
    }

    /**
     * @param Procedure  $procedure
     * @param Systemuser $systemuser
     * @param int        $clientid
     * @param int        $insurancecompanyid
     * @param Mail       $mail
     * @param bool       $riskcheck
     * @param null       $productcomboid
     * @param null       $riskCheckData
     *
     * @return null|UserMail
     */
    private function createUserMail($procedure, $systemuser, $clientid, $insurancecompanyid, $mail, $riskcheck, $productcomboid = null, $riskCheckData = null)
    {
        $data                               = [];
        $data['UserMail']['procedure_id']   = $procedure->id;
        $data['UserMail']['user_id']        = $systemuser->id;
        $data['UserMail']['mail_id']        = $mail->id;
        $data['UserMail']['mail_status_id'] = MailStatus::OPEN;
        if ($riskcheck) {
            $data['UserMail']['mail_procedure_id'] = MailProcedure::RISKCHECK;
        } else {
            $data['UserMail']['mail_procedure_id'] = MailProcedure::ENQUIRY;
        }
        $data['UserMail']['insurance_company_id'] = $insurancecompanyid;
        $data['UserMail']['client_id']            = $clientid;
        $data['UserMail']['product_combo_id']     = $productcomboid;

        $userMailController = new UserMailController('usermail');
        $usermail           = $userMailController->create($data);
        if (null !== $usermail) {
            $usermail->send_date = ViewHelper::getDate(true);
            $usermail->send_time = ViewHelper::getTimeStamp();
            if (!empty($riskCheckData)) {
                $usermail->risk_check_data_id = $riskCheckData->id;
            }
            $usermail->save(false);

            $this->sendMail($usermail, $mail);

            return $usermail;
        }
    }

    /**
     * @param UserMail $userMail
     * @param Mail     $mail
     *
     * @return bool
     */
    private function sendMail(UserMail $userMail, Mail $mail): bool
    {
        $mailer = MailerFactory::create()->useMaklerOrSystemSmtp($userMail->user);
        $mailer->setView('default' . DS . 'empty', ['content' => $mail->getContent()]);
        $mailer->setCCs($mail->getCCs());
        $mailer->setBCCs($mail->getBCCs());
        $mailer->setSubject($mail->subject);
        $mailer->setAttachments($mail->getAttachments());
        $mailer->setReplyTo($mail->sender_email);

        return $mail->send($mailer);
    }

    /**
     * @param RiskCheck $riskCheck
     *
     * @return ?array
     */
    private function getCreateFormularData($riskCheck)
    {
        if (!isset($_POST['create'])) {
            $dropdowndata = null;
            if (isset(self::$productcombos[$riskCheck->produkt])) {
                $dropdowndata = self::$productcombos[$riskCheck->produkt];
            }
            //            $procedureid = ProcedureController::generateNewProcedure(Yii::app()->user->getID())->id;
            $procedureid             = null;
            $riskCheck->procedure_id = $procedureid;
            $this->render('form', ['riskCheck' => $riskCheck, 'dropdowndata' => $dropdowndata, 'branchenData' => $this->getBranchenData()]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     *
     * Gibt ein Array mit allen InsuranceCompanies zurück, welche
     * einen Betreuer für die übergebene SupportSubjectID haben
     *
     * @param      $supportsubjectid
     * @param      $productcomboid
     * @param null $zip
     * @param null $stateid
     * @param bool $offer
     *
     * @return array
     */
    public static function getInsuranceCompanies($supportsubjectid, $productcomboid, $zip = null, $stateid = null, $offer = false)
    {
        $PCs          = [];
        $productCombo = ProductCombo::model()->findByPk($productcomboid);

        if ($productcomboid === null || $productCombo === null) {
            return [];
        }

        while ($productCombo->getLevel() !== 1) {
            $PCs[]        = $productCombo->id;
            $productCombo = $productCombo->getParentProductCombo();
        }
        $PCs[]                     = $productCombo->id;
        $productCombo              = ProductCombo::model()->findByAttributes(['level1_id' => $productCombo->level1_id, 'level2_id' => null]);
        $criteria                  = new CDbCriteria();
        $brokerIdProvider          = new BrokerIdProvider(currentUser());
        $systemweiteGesellschaften = new SystemweiteGesellschaften(currentUser(), $brokerIdProvider, $productCombo->id);

        $supportPersonIds = SupportPersonsQuery::forAgency((int) currentUser()->agency_id)
            ->whereCompanyIdsIn($systemweiteGesellschaften->getCompanyDataList()->getCompaniesIds())
            ->whereSubjectIdIs((int) $supportsubjectid)
            ->queryIds();
        $criteria->addInCondition('id', $supportPersonIds);
        $supportPersons = SupportPerson::model()->findAll($criteria);

        $container = new SupportPersonContainer($supportPersons);
        if (!empty($zip) && !empty($stateid)) {
            $container->applyFilter(new ZipAndStateFilter($zip, (int) $stateid));
        } elseif (!empty($stateid)) {
            $container->applyFilter(new StateFilter((int) $stateid));
        } elseif (!empty($zip)) {
            $container->applyFilter(new ZipFilter($zip));
        } else {
            $container->applyFilter(new NationalwideFilter());
        }
        $container->applyFilter(ProductFilter::multiple($PCs));
        $poolFilter = PoolFilter::filterByBrokerIds($brokerIdProvider);
        $poolFilter->ensureNotEmpty();

        $containers = $container->splitByCompany();
        foreach ($containers as $companyId => $container) {
            if ($brokerIdProvider->exists($companyId)) {
                $container->applyFilter(PoolFilter::removeAll());
            } else {
                $container->applyFilter($poolFilter);
            }
        }

        $mergedContainer = new SupportPersonContainer([]);
        foreach ($containers as $container) {
            $mergedContainer->mergeWith($container);
        }
        $supportPersons = $mergedContainer->getAll();

        $insuranceCompanyies = CHtml::listData($systemweiteGesellschaften->getCompanies(), 'id', 'name');
        $companies           = [];
        /** @var SupportPerson[] $supportPersons */
        foreach ($supportPersons as $supportPerson) {
            if (null !== $supportPerson && isset($insuranceCompanyies[$supportPerson->insuranceCompanyData->insurance_company_id])) {
                $companies[$supportPerson->insuranceCompany->id] = [
                    'lowercasename'      => strtolower($insuranceCompanyies[$supportPerson->insuranceCompanyData->insurance_company_id]),
                    'name'               => $insuranceCompanyies[$supportPerson->insuranceCompanyData->insurance_company_id],
                    'contactpersonemail' => $supportPerson->contact->email,
                    'contactpersonid'    => $supportPerson->id,
                ];
            }
        }
        asort($companies);

        return $companies;
    }

    public static function getSupportSubjectId($offer)
    {
        return $offer ? SupportAssignmentSubject::ENQUIRY : SupportAssignmentSubject::RISKCHECK;
    }

    public function actionGetAdditionalFields(): void
    {
        $productCombo  = Yii::app()->request->getParam('produkt');
        $contract_data = Yii::app()->request->getParam('contract_data');
        if (UtilHelper::is_serialized($contract_data)) {
            $contract_data = unserialize($contract_data);
        } else {
            $contract_data = [];
        }
        $riskCheck                = new RiskCheck();
        $riskCheck->contract_data = $contract_data;

        //PKV->Private Krankezusatzversicherung
        if ($productCombo == ProductCombo::PKV) {
            echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'riskCheck' . DIRECTORY_SEPARATOR . 'contract_additional_krankenvoll.php',
                ['riskCheck' => $riskCheck]);
        }
        if ($productCombo == ProductCombo::PKZUSATZ) {
            echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'riskCheck' . DIRECTORY_SEPARATOR . 'contract_additional_krankenzusatz.php',
                ['riskCheck' => $riskCheck]);
        } elseif ($productCombo == ProductCombo::KRANKENTAGEGELD) {
            echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'riskCheck' . DIRECTORY_SEPARATOR . 'contract_additional_krankentagegeld.php',
                ['riskCheck' => $riskCheck]);
        } elseif ($productCombo == ProductCombo::PFLEGETAGEGELD) {
            echo $this->renderInternal(Yii::app()->basePath . DIRECTORY_SEPARATOR . 'views' . DIRECTORY_SEPARATOR . 'riskCheck' . DIRECTORY_SEPARATOR . 'contract_additional_pflegetagegeld.php',
                ['riskCheck' => $riskCheck]);
        }

        Yii::app()->end();
    }

    public function actionGetRiskCheckInsuranceCompanies(): void
    {
        $sector         = $_POST['sector'];
        $productcomboid = $_POST['productcomboid'];
        $offer          = Yii::app()->request->getPost('offer', false);

        echo $this->renderPartial('insuranceCompanies', ['offer' => $offer, 'sector' => $sector, 'productcomboid' => $productcomboid]);
        Yii::app()->end();
    }

    /**
     * @return array
     * @throws CException
     */
    private function getBranchenData(): array
    {
        $data = Yii::app()->db->createCommand()
            ->select('b.id, b.product_combo_id, b.name, group_concat(concat(i.id, "---", i.name) separator "///") as companies')
            ->from('risk_check_branche b')
            ->join('risk_check_branche_insurance_company bi', 'b.id = bi.branche_id')
            ->join('insurance_company i', 'i.id = bi.insurance_company_id')
            ->group('b.id')
            ->queryAll();

        foreach ($data as &$datum) {
            $datum['companies'] = explode('///', $datum['companies'] ?? '');
            foreach ($datum['companies'] as &$company) {
                $idName  = explode('---', $company ?? '');
                $company = [
                    'id'   => $idName[0],
                    'name' => $idName[1],
                ];
            }
        }

        return $data;
    }
}
