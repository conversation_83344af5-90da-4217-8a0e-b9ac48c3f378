<?php

use Apfelbox\FileDownload\FileDownload;
use Business\Agency\Siegel\DemvSiegel;

class SiegelController extends Controller
{
    private const PATH           = 'images/siegel/DEMV_Siegel_%s.svg';
    private const PNG_SIZE       = 2048;
    private const TYPE_VERT      = 'Vertikal';
    private const TYPE_HORIZ     = 'Horizontal';
    private const FONT_SIZE      = '120px';
    public const  MAX_CHARS_NAME = 27;

    /**
     * @param int $userId
     *
     * @throws CHttpException
     * @throws ImagickException
     * @throws CException
     */
    public function actionDownloadHorizontal(int $userId)
    {
        $user = UserProvider::forUser(currentUser())->load($userId);
        if (!Yii::app()->user->isAdmin() && !DemvSiegel::availableFor($user)) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }
        $xdoc = new DomDocument();
        $xdoc->Load(sprintf(self::PATH, self::TYPE_HORIZ));
        $splitname = explode("\n", wordwrap(currentUser()->agency->name, self::MAX_CHARS_NAME));
        $nameRow1  = !isset($splitname[1]) ? '' : ($splitname[0] ?? '');
        $nameRow2  = $splitname[1] ?? $splitname[0] ?? '';
        $nameRow3  = $splitname[2] ?? '';
        $this->replaceName($xdoc->getElementsByTagName('tspan'), $nameRow1, $nameRow2, $nameRow3);
        $pngStr = $this->toPNG($xdoc->saveXML());
        @FileDownload::createFromString($pngStr)->sendDownload('DEMV-Siegel-' . self::TYPE_HORIZ . '.png');
    }

    /**
     * @param int $userId
     *
     * @throws CHttpException
     * @throws ImagickException
     * @throws CException
     */
    public function actionDownloadVertical(int $userId)
    {
        $user = UserProvider::forUser(currentUser())->load($userId);
        if (currentUser()->user_role != UserRole::ADMIN && !DemvSiegel::availableFor($user)) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }
        $xdoc = new DomDocument();
        $xdoc->Load(sprintf(self::PATH, self::TYPE_VERT));
        $splitname = explode("\n", wordwrap(currentUser()->agency->name, self::MAX_CHARS_NAME));
        $nameRow1  = !isset($splitname[1]) ? '' : ($splitname[0] ?? '');
        $nameRow2  = $splitname[1] ?? $splitname[0] ?? '';
        $nameRow3  = $splitname[2] ?? '';
        $this->replaceName($xdoc->getElementsByTagName('tspan'), $nameRow1, $nameRow2, $nameRow3);
        $pngStr = $this->toPNG($xdoc->saveXML());
        @FileDownload::createFromString($pngStr)->sendDownload('DEMV-Siegel-' . self::TYPE_VERT . '.png');
    }

    /**
     * @throws CHttpException
     * @throws ImagickException
     */
    public function actionDownloadCustomName()
    {
        if (!isset($_POST['type'])
            || empty($_POST['name'])
            || (currentUser()->user_role != UserRole::ADMIN && !DemvSiegel::availableFor(currentUser()))) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }
        if ($_POST['type'] === 'horizontal') {
            $this->downloadHorizontalCustomName($_POST['name']['row1'], $_POST['name']['row2'], $_POST['name']['row3']);
        } else {
            $this->downloadVerticalCustomName($_POST['name']['row1'], $_POST['name']['row2'], $_POST['name']['row3']);
        }
    }

    /**
     * @param string $nameRow1
     * @param string $nameRow2
     * @param string $nameRow3
     *
     * @throws ImagickException
     */
    private function downloadHorizontalCustomName(string $nameRow1, string $nameRow2, string $nameRow3)
    {
        $xdoc = new DomDocument();
        $xdoc->Load(sprintf(self::PATH, self::TYPE_HORIZ));
        $this->replaceName($xdoc->getElementsByTagName('tspan'), $nameRow1, $nameRow2, $nameRow3);
        $pngStr = $this->toPNG($xdoc->saveXML());
        @FileDownload::createFromString($pngStr)->sendDownload('DEMV-Siegel-' . self::TYPE_HORIZ . '.png');
    }

    /**
     * @param string $nameRow1
     * @param string $nameRow2
     * @param string $nameRow3
     *
     * @throws ImagickException
     */
    private function downloadVerticalCustomName(string $nameRow1, string $nameRow2, string $nameRow3)
    {
        $xdoc = new DomDocument();
        $xdoc->Load(sprintf(self::PATH, self::TYPE_VERT));
        $this->replaceName($xdoc->getElementsByTagName('tspan'), $nameRow1, $nameRow2, $nameRow3);
        $pngStr = $this->toPNG($xdoc->saveXML());
        @FileDownload::createFromString($pngStr)->sendDownload('DEMV-Siegel-' . self::TYPE_VERT . '.png');
    }

    /**
     * @param string $svgStr
     *
     * @return string
     * @throws ImagickException
     */
    private function toPNG(string $svgStr): string
    {
        $imagick = new Imagick();
        $imagick->readImageBlob($svgStr);
        $imagick->setImageFormat('png32');
        $imagick->adaptiveResizeImage(self::PNG_SIZE, self::PNG_SIZE, true);
        $imagick->setImageCompression(Imagick::COMPRESSION_LZW);
        $imagick->setImageCompressionQuality(100);
        $pngStr = $imagick->getImageBlob();
        $imagick->clear();
        $imagick->destroy();

        return $pngStr;
    }

    private function replaceName(DOMNodeList $domElements, string $nameRow1, string $nameRow2, string $nameRow3)
    {
        $domElements[0]->textContent = $nameRow1 ?? '';
        $domElements[1]->textContent = $nameRow2 ?? '';
        $domElements[2]->textContent = $nameRow3 ?? '';
        foreach ($domElements as $domElement) {
            /* @var DOMElement $domElement */
            $domElement->setAttribute('font-size', self::FONT_SIZE);
        }
    }
}
