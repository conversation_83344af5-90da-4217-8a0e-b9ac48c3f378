<?php

use Components\Access\PublicAccessibleInterface;
use Sign\UseCases\UpdateTemplate;
use Demv\JSend\ResponseFactory;

class SignTemplateController extends Controller implements PublicAccessibleInterface
{
    use RenderJsonTrait;

    public function getPublicActions(): array
    {
        return [
            'updateTemplate',
        ];
    }

    /**
     * http://professionalworks.demv.internal/signtemplate/updateTemplate
     */
    public function actionUpdateTemplate()
    {
        $data = json_decode(Yii::app()->request->getRawBody(), true);

        if (!isset($data['uuid']) || null === $data['uuid']) {
            ResponseFactory::instance()->error(
                ['message' => 'No sign identifier (uuid) found in request body.'],
                400
            )->respond();
        }

        $model = DocumentTemplate::findBySignId($data['uuid']);

        if ($model === null) {
            ResponseFactory::instance()->error(
                ['message' => 'The requested page does not exist.'],
                404
            )->respond();
        }

        $useCase = new UpdateTemplate(
            $model,
            json_encode($data['template']),
        );

        if ($useCase->handle()) {
            ResponseFactory::instance()->success()->respond();
        }

        ResponseFactory::instance()->error(
            ['message' => 'Something went wrong, the template could not be updated.'],
            500
        )->respond();
    }
}
