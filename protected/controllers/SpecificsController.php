<?php

class SpecificsController extends Controller
{
    private const DELIMETER = ';';
    private const FILENAME  = 'gesellschaften-besonderheiten.csv';

    public function actionExport()
    {
        $criteria                  = new CDbCriteria();
        $criteria->join            = 'join insurance_company_data icd on t.insurance_company_id = icd.id ';
        $criteria->join .= 'join insurance_company ic on icd.insurance_company_id = ic.id';
        $criteria->condition       = 'icd.general = 1 AND ic.systemwide = 1';
        $criteria->order           = 'ic.name';
        $insuranceCompanySpecifics = InsuranceCompanySpecifics::model()->findAll($criteria);

        $file       = fopen('php://memory', 'w');
        $colHeaders = InsuranceCompanySpecifics::model()->attributeLabels();
        unset($colHeaders['id']);
        $demvInfoHeaders = [];
        if (Yii::app()->user->isAdmin()) {
            $demvInfoHeaders = $this->getDemvInfoHeaders();
            $colHeaders      = array_merge($colHeaders, $demvInfoHeaders);
        }
        fputcsv($file, $colHeaders, self::DELIMETER);

        foreach ($insuranceCompanySpecifics as $insuranceCompanySpecific) {
            $rowData = $this->getRow($insuranceCompanySpecific, $demvInfoHeaders);
            fputcsv($file, $rowData, self::DELIMETER);
        }

        fseek($file, 0);
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . self::FILENAME . '";');
        fpassthru($file);
    }

    private function getDemvInfoHeaders(): array
    {
        /** @var InsuranceCompanyDemvInfo[] $infos */
        $infos   = InsuranceCompanyDemvInfo::model()->findAll();
        $headers = [];
        foreach ($infos as $item) {
            $headers = array_merge($headers, array_keys($item->getContent()));
        }

        return $headers;
    }

    private function getRow(InsuranceCompanySpecifics $insuranceCompanySpecific, array $demvInfoHeaders): array
    {
        $data = [
            $insuranceCompanySpecific->insuranceCompany->name ?? '',
            format($insuranceCompanySpecific->application_transmission_email, 'ternary'),
            format($insuranceCompanySpecific->application_transmission_fax, 'ternary'),
            format($insuranceCompanySpecific->application_transmission_post, 'ternary'),
            $insuranceCompanySpecific->settlement_interval,
            $insuranceCompanySpecific->payment_cycle,
            $insuranceCompanySpecific->payment_start,
            $insuranceCompanySpecific->storno_reserve,
            $insuranceCompanySpecific->trust_damage_insurance_height,
            $insuranceCompanySpecific->trust_damage_insurance_refund,
            format($insuranceCompanySpecific->pro_rata_payment_possible, 'ternary'),
            format($insuranceCompanySpecific->settlement_subbroker_possible, 'ternary'),
            $insuranceCompanySpecific->hierarchical_level,
            $insuranceCompanySpecific->courtage_partition,
            $insuranceCompanySpecific->other_matters,
            format($insuranceCompanySpecific->gdv, 'ternary'),
            format($insuranceCompanySpecific->settlement_help, 'ternary'),
            format($insuranceCompanySpecific->verzicht_erstantrag_bu, 'ternary'),
            format($insuranceCompanySpecific->broker_id_migration_possible, 'ternary'),
            $insuranceCompanySpecific->verzicht_auf_erstantrag ?? '',
            $insuranceCompanySpecific->payoff_count,
            format($insuranceCompanySpecific->payoff_minimum_amount, 'ternary'),
            format($insuranceCompanySpecific->maklerbetreuer_bleibt_bei_cu, 'ternary'),
            $insuranceCompanySpecific->regional_support_info,
            format($insuranceCompanySpecific->zusammenarbeit_mehrfachagenten, 'ternary'),
            $insuranceCompanySpecific->vnr_bleibt_bei_courtageumzug,
        ];
        foreach (InsuranceCompanySpecifics::getAdminAttributes() as $adminAttribute => $type) {
            $data[$adminAttribute] = $type === 'bool' ?
                format($insuranceCompanySpecific->{$adminAttribute}, 'ternary') :
                $insuranceCompanySpecific->{$adminAttribute};
        }
        if (Yii::app()->user->isAdmin()) {
            $demvInfo = InsuranceCompanyDemvInfo::getForCompany((int) $insuranceCompanySpecific->insurance_company_id);
            foreach ($demvInfoHeaders as $demvInfoHeader) {
                $data[$demvInfoHeader] = array_key_exists($demvInfoHeader, $demvInfo->getContent()) ?
                    $demvInfo->getContent()[$demvInfoHeader] :
                    null;
            }
        }

        return $data;
    }
}
