<?php

class StartPageContentController extends GlobalRightController
{
    private const THUMB_MAX_HEIGHT = 300;
    private const THUMB_MAX_WIDTH  = 600;

    public $access_right_id = Rights::STARTSEITE_VERWALTEN;
    public $menuGroup       = MenuGroup::STAMMDATEN;
    public $excludedActions = [
        'GetImage',
    ];

    /**
     * @return array action filters
     */
    public function filters()
    {
        return [
            'accessControl', // perform access control for CRUD operations
            'postOnly + delete', // we only allow deletion via POST request
        ];
    }

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Creates a new model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     */
    public function actionCreate($type)
    {
        if ($type == StartPageContent::HOME_CONTENT) {
            $model = new HomeContent();
        } elseif ($type == StartPageContent::HOME_NEWS) {
            $model = new HomeNews();
        } elseif ($type == StartPageContent::HOME_TIPP) {
            $model = new HomeTipp();
        }
        $data = Yii::app()->request->getPost(get_class($model));
        if (!empty($data)) {
            $model->attributes = $data;
            $file              = CUploadedFile::getInstance($model, 'file');
            if (!empty($file)) {
                $model->mimeType = $file->type;
            }
            if ($model->validate() && $model->save()) {
                if (!empty($file)) {
                    $model->uploadFile($file);
                }
                $this->redirect(['admin']);
            }
        }
        $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates a particular model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id the ID of the model to be updated
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if (!empty($model) && isset($_POST[get_class($model)])) {
            $fileName = '';
            if (empty($_POST[get_class($model)]['file'])) {
                $fileName = $model->file;
            }
            $model->attributes = $_POST[get_class($model)];
            if (empty($_POST[get_class($model)]['file'])) {
                $model->file = $fileName;
            }
            $file = CUploadedFile::getInstance($model, 'file');
            if (!empty($file)) {
                $model->mimeType = $file->type;
            }
            if ($model->validate() && $model->save()) {
                if (!empty($file)) {
                    $model->uploadFile($file);
                }
                Yii::app()->user->setFlash('success', 'Inhalt bearbeitet');
                $this->redirect(['update', 'id' => $model->id]);
            }
        }
        $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes a particular model.
     * If deletion is successful, the browser will be redirected to the 'admin' page.
     *
     * @param integer $id the ID of the model to be deleted
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('StartPageContent');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new StartPageContent('search');
        if (!Yii::app()->user->isAdmin()) {
            $model->agency_id = Yii::app()->user->getAgencyId();
        }
        $this->render('admin', [
            'model' => $model,
        ]);
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param integer $id the ID of the model to be loaded
     *
     * @return StartPageContent the loaded model
     * @throws CHttpException
     */
    public function loadModel($id)
    {
        $provider = new StartpageContentProvider();
        $model    = $provider->loadByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $model;
    }

    /**
     * Performs the AJAX validation.
     *
     * @param StartPageContent $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'start-page-content-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }

    public function actionUpdateActivation()
    {
        $id = Yii::app()->getRequest()->getPost('id');

        $model = StartPageContent::model()->findByPk($id);
        $model->updateActivation();
        Yii::app()->end();
    }

    public function actionDownload($id)
    {
        $model = $this->loadModel($id);
        $this->downloadImage($model);
    }

    private function downloadImage(StartPageContent $model, $isThumb = false)
    {
        if ($model->fileExists()) {
            if (ob_get_length() > 0) {
                ob_clean();
            }

            $content = $isThumb ? $this->getImageContentThumb($model) : $model->getImageContent();

            header('Pragma: private');
            header('Cache-Control: private, max-age=18000, must-revalidate');
            header('Content-Type: image/png');
            header('Content-Length: ' . strlen($content ?? ''));
            header('Content-Transfer-Encoding: binary');
            echo $content;
        }
        Yii::app()->end();
    }

    public function actionGetImage()
    {
        $id      = Yii::app()->request->getParam('id');
        $isThumb = Yii::app()->request->getParam('thumb') ?? false;

        $model = $this->loadModel($id);
        $this->downloadImage($model, $isThumb);
    }

    /**
     * @param StartPageContent $model
     *
     * @return string
     */
    private function getImageContentThumb(StartPageContent $model): string
    {
        [$orgWidth, $orgHeight] = $model->getImageSize();

        $width  = $orgWidth;
        $height = $orgHeight;

        if ($height > self::THUMB_MAX_HEIGHT) {
            $width  = (int) ((self::THUMB_MAX_HEIGHT / $height) * $width);
            $height = self::THUMB_MAX_HEIGHT;
        }

        if ($width > self::THUMB_MAX_WIDTH) {
            $height = (int) ((self::THUMB_MAX_WIDTH / $width) * $height);
            $width  = self::THUMB_MAX_WIDTH;
        }

        $source = imagecreatefromstring($model->getImageContent());
        $thumb  = imagecreatetruecolor($width, $height);
        imagecopyresized($thumb, $source, 0, 0, 0, 0, $width, $height, $orgWidth, $orgHeight);
        ob_start();
        imagejpeg($thumb);

        return ob_get_clean();
    }
}
