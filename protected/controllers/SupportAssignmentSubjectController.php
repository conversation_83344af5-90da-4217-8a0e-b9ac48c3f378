<?php

class SupportAssignmentSubjectController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout      = '//layouts/column2';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das SupportAssignmentSubject-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des SupportAssignmentSubject-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $subject = SupportAssignmentSubject::model()->findByPk($id);

        return $subject;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues SupportAssignmentSubject-Model erstellt werden soll.
     *  Die Methode öffnet das SupportAssignmentSubject-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['SupportAssignmentSubject'])) {
            if (isset($data['create'])) {
                if ($this->validData($data)) {
                    if ($subject = $this->create($data) != null) {
                        //echo "Das Erstellen war erfolgreich";
                        //ToDo -> View aktualiseren
                        $this->redirect('admin');

                        return $subject;
                    }

                    return null;
                }
            }
        }
    }

    /**
     *  Öffnet das SupportAssignmentSubject -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des SupportAssignmentSubject -Models
     */
    private function getCreateFormularData()
    {
        $subject      = new SupportAssignmentSubject();
        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/verwaltung/fachbereiche/create', ['subject' => $subject]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model SupportAssignmentSubject handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['SupportAssignmentSubject'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues SupportAssignmentSubject -Model
     *
     * @param type $data
     *          Die SupportAssignmentSubject -Daten als Array
     *
     * @return $subject
     *          Das erstellte SupportAssignmentSubject -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $subject             = new SupportAssignmentSubject();
        $subject->attributes = $data['SupportAssignmentSubject'];

        if ($subject->save()) {
            return $subject;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein SupportAssignmentSubject -Model aktualisiert werden soll.
     *  Die Methode öffnet das SupportAssignmentSubject -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden SupportAssignmentSubject -Models
     */
    public function actionUpdate($id)
    {
        $subject = $this->loadModel($id);
        if ($subject != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($subject = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $subject;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das SupportAssignmentSubject-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden SupportAssignmentSubject-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $subject = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/verwaltung/fachbereiche/update', ['subject' => $subject]);
        }

        $formulardata                                   = $_POST;
        $formulardata['SupportAssignmentSubject']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein SupportAssignmentSubject-Exemplar
     *
     * @param type $data
     *          Die SupportAssignmentSubject-Daten als Array
     *
     * @return $subject
     *          Das aktualisierte SupportAssignmentSubject-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $subject             = $this->loadModel($data['SupportAssignmentSubject']['id']);
            $subject->attributes = $data['SupportAssignmentSubject'];
            if ($subject->save()) {
                return $subject;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $subject = new SupportAssignmentSubject('search');
        $subject->unsetAttributes();  // clear any default values
        if (isset($_GET['SupportAssignmentSubject'])) {
            $subject->attributes = $_GET['SupportAssignmentSubject'];
        }

        $this->render('//stammdaten/verwaltung/fachbereiche/admin', [
            'subject' => $subject,
        ]);
    }

    /**
     * Löscht das SupportAssignmentSubject-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden SupportAssignmentSubject-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('SupportAssignmentSubject');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param SupportAssignmentSubject $subject the model to be validated
     */
    protected function performAjaxValidation($subject)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'support-assignment-subject-form') {
            echo CActiveForm::validate($subject);
            Yii::app()->end();
        }
    }
}
