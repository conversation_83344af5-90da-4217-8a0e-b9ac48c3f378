<?php

class SupportTypeController extends AdminController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout           = '//layouts/column2';
    private $supporttypename = 'SupportType';

    /**
     * Displays a particular supporttype.
     *
     * @param integer $id the ID of the supporttype to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'supporttype' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das SupportType-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des SupportType-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $supporttype = SupportType::model()->findByPk($id);

        return $supporttype;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues SupportType-Model erstellt werden soll.
     *  Die Methode öffnet das SupportType-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if ($supporttype = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $supporttype;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das SupportType -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des SupportType -Models
     */
    private function getCreateFormularData()
    {
        $formulardata = [];
        $supporttype  = new SupportType();

        if (!$this->validData($_POST)) {
            $this->render('create', ['supporttype' => $supporttype]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model SupportType handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['SupportType'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues SupportType -Model
     *
     * @param type $data
     *          Die SupportType -Daten als Array
     *
     * @return $supporttype
     *          Das erstellte SupportType -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $supporttype             = new SupportType();
        $supporttype->attributes = $data['SupportType'];

        if ($supporttype->save()) {
            return $supporttype;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein SupportType -Model aktualisiert werden soll.
     *  Die Methode öffnet das SupportType -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden SupportType -Models
     */
    public function actionUpdate($id)
    {
        $supporttype = $this->loadModel($id);
        if ($supporttype != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($supporttype = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $supporttype;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das SupportType-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden SupportType-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $supporttype = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('update', ['supporttype' => $supporttype]);
        }

        $formulardata                      = $_POST;
        $formulardata['SupportType']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein SupportType-Exemplar
     *
     * @param type $data
     *          Die SupportType-Daten als Array
     *
     * @return $supporttype
     *          Das aktualisierte SupportType-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $supporttype             = $this->loadModel($data['SupportType']['id']);
            $supporttype->attributes = $data['SupportType'];
            if ($supporttype->save()) {
                return $supporttype;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all supporttypes.
     */
    public function actionAdmin()
    {
        $supporttype = new SupportType('search');
        $supporttype->unsetAttributes();  // clear any default values
        if (isset($_GET['SupportType'])) {
            $supporttype->attributes = $_GET['SupportType'];
        }

        $this->render('admin', [
            'supporttype' => $supporttype,
        ]);
    }

    /**
     * Löscht das SupportType-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden SupportType-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all supporttypes.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('SupportType');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param SupportType $supporttype the supporttype to be validated
     */
    protected function performAjaxValidation($supporttype)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'support-type-form') {
            echo CActiveForm::validate($supporttype);
            Yii::app()->end();
        }
    }
}
