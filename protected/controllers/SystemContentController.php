<?php

class SystemContentController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     * Displays a particular model.
     *
     * @param integer $id the ID of the model to be displayed
     */
    public function actionView($id)
    {
        $this->render('//stammdaten/menu/contentVerwaltung/view', [
            'model' => $this->loadModel($id),
        ]);
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param integer the ID of the model to be loaded
     */
    public function loadModel($id)
    {
        $model = SystemContent::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $model;
    }

    /**
     * Creates a new model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     */
    public function actionCreate()
    {
        $model = new SystemContent();

        // Uncomment the following line if AJAX validation is needed
        // $this->performAjaxValidation($model);

        if (isset($_POST['SystemContent'])) {
            $model->attributes = $_POST['SystemContent'];
            if ($model->save()) {
                $this->redirect(['//stammdaten/content']);
            }
        }

        $this->render('//stammdaten/menu/contentVerwaltung/create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates a particular model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id the ID of the model to be updated
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);

        // Uncomment the following line if AJAX validation is needed
        // $this->performAjaxValidation($model);

        if (isset($_POST['SystemContent'])) {
            $model->attributes = $_POST['SystemContent'];

            if ($model->save()) {
                Yii::app()->user->setFlash('success', 'Der Inhalt wurde erfolgreich geändert!');
                $this->redirect(['update', 'id' => $model->id]);
            }
        }

        $this->render('//stammdaten/menu/contentVerwaltung/update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes a particular model.
     * If deletion is successful, the browser will be redirected to the 'admin' page.
     *
     * @param integer $id the ID of the model to be deleted
     */
    public function actionDelete($id)
    {
        if (Yii::app()->request->isPostRequest) {
            // we only allow deletion via POST request
            $this->loadModel($id)->delete();

            // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
            if (!isset($_GET['ajax'])) {
                $this->redirect($_POST['returnUrl'] ?? ['admin']);
            }
        } else {
            throw new CHttpException(400, 'Invalid request. Please do not repeat this request again.');
        }
    }

    /**
     * Lists all models.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('SystemContent');
        $this->render('//stammdaten/menu/contentVerwaltung/index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new SystemContent('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['SystemContent'])) {
            $model->attributes = $_GET['SystemContent'];
        }

        $this->render('//stammdaten/menu/contentVerwaltung/admin', [
            'model' => $model,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param CModel the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'system-content-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
