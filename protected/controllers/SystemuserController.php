<?php

class SystemuserController extends RightController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    public $excludedActions = [
        'AutocompleteFullname',
        'InsuranceCompanySelect',
        'FromTable',
        'actionCreatePW',
        'GetActiveRights',
        'GenerateExcel',
        'GeneratePDF',
        'AjaxQuery',
        'sendPassword',
        //**start**added for Partnerbogen
        'Partnerbogen',
        'addperson',
        'addbankaccount',
        'addjobhistory',
        'deletejobhistory',
        'deleteperson',
        'deletebankaccount',
        'RedirectPartnerbogen',
        'Beitrittserklaerung'
        //**end** added for Partnerbogen
    ];

    private $prefix = 'Systemuser';
    private $rights;

    public function __construct($id, $module = null)
    {
        parent::__construct($id, $module);
    }

    public static function staticRandomPassword()
    {
        $user = new User();
        $user->setRandomPassword();

        return $user->password;
    }

    public function actions()
    {
        return [
            'addbankaccount'    => [
                'class'     => 'application.components.actions.XTabularInputAction',
                'modelName' => 'BankAccount',
                'viewName'  => '//stammdaten/systemuser/tabular/_bankaccount',
            ],
            'addjobhistory'     => [
                'class'     => 'partnerbogen.components.XTabularInputAction',
                'modelName' => 'UserJobHistory',
                'viewName'  => '//stammdaten/systemuser/tabular/_jobhistory'
            ],
            'deletejobhistory'  => [
                'class'     => 'application.components.actions.ModelDeletionAction',
                'modelname' => 'UserJobHistory',
            ],
            'deletebankaccount' => [
                'class'     => 'application.components.actions.ModelDeletionAction',
                'modelname' => 'BankAccount',
            ],
        ];
    }

    /**
     * Eine Funktion um eine Autovervollständigung auszuführen
     *
     * @return array action filters
     */
    public function actionAutocompleteFullname()
    {
        $res = [];

        if (isset($_GET['term'])) {
            $qtxt    = 'SELECT DISTINCT lastname FROM user WHERE lastname LIKE :lastname AND user_status <> ' . UserStatus::CANCELED . ' AND user_status <> ' . UserStatus::PASSIVE . ' AND ' . AgencyController::getUnderlingsCondition('id') . ' AND deleted = 0';
            $command = Yii::app()->db->createCommand($qtxt);
            $command->bindValue(':lastname', '%' . $_GET['term'] . '%', PDO::PARAM_STR);
            $res = $command->queryColumn();
        }
        echo CJSON::encode($res);
        Yii::app()->end();
    }

    public function actionAjaxQuery(string $q): void
    {
        $criteria = new CDbCriteria([
            'with'  => ['userRole', 'userStatus'],
            'order' => 't.lastname, t.firstname, t.id',
        ]);

        echo CJSON::encode(array_map(static fn (User $user) => [
            'id'        => $user->id,
            'text'      => $user->getIdentityString(currentUser()->user_role),
            'firstname' => $user->firstname,
            'birthday'  => $user->birthday,
            'lastname'  => $user->lastname,
        ], User::model()->personalAccess()->quickSearch($q, User::QUICKSEARCH_FULLNAME, $criteria)));

        Yii::app()->end();
    }

    public function actionInsuranceCompanySelect()
    {
        $this->render('insuranceCompanySelect', []);
    }

    /**
     * Wird beim erstellen eines Users aufgerufen. Zunächst werden die Formulardaten geholt
     * und anschließend in der user-Tabelle erstellt.
     */
    public function actionCreate()
    {
        $this->redirect(Yii::app()->createAbsoluteUrl('/stammdaten/user/user/create'));
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein Systemuser aktualisiert werden soll.
     *  Die Methode öffnet das Systemuser-Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID des zu aktualisierenden Systemuser
     * @param $activetab
     *         gibt den Namen des Tabs an, der angezeigt werden soll
     * @param $sendDataUpdate
     *         gibt an, ob eine Datenaktuallisierung gesendet werden soll (true/false)
     */
    public function actionUpdate($id, $activetab = '')
    {
        $this->redirect(Yii::app()->createAbsoluteUrl('/stammdaten/user/user/update', compact('id', 'activetab')));
    }

    public function callBackTitlePage(PDFgenerator $pdf)
    {
        $pdf->SetXY(70, 70);
        $pdf->Cell(20, 5, 'CALLBACKFUNCTION');
    }

    /**
     * Manages all models.
     */
    public function actionAdmin($lastSearch = false)
    {
        $systemuser = new Systemuser('search');
        $systemuser->with('contact');
        $systemuser->setLastSearch($lastSearch);
        $systemuser->unsetAttributes();  // clear any default values
        if (!$lastSearch) {
            if (isset($_GET['searchByLetter']) && isset($_GET['searchByLetter']['clicked'])) {
                if ($_GET['searchByLetter']['clicked'] === 'true') {
                    $systemuser->attributes = ['fullname' => $_GET['searchByLetter']['letter']];
                }
                if ($_GET['searchByLetter']['clicked'] === 'false' && isset($_GET['Systemuser'])) {
                    $systemuser->attributes = $_GET['Systemuser'];
                }
            } elseif (isset($_GET['Systemuser'])) {
                $systemuser->attributes = $_GET['Systemuser'];
            } else {
                if (currentUser()->user_status == UserStatus::ACTIVE) {
                    $systemuser->user_status = UserStatus::ACTIVE;
                }
            }
            Yii::app()->session['lastSystemuserSearch'] = $systemuser;
        } elseif ($lastSearch) {
            if (isset(Yii::app()->session['lastSystemuserSearch'])) {
                $systemuser = Yii::app()->session['lastSystemuserSearch'];
            }
        }

        $this->render('admin', [
            'systemuser' => $systemuser,
        ]);
    }

    /**
     * Generiert ein neues Passwort aus dem angegebenem $alphabet
     *
     * @return type
     */
    public function randomPassword()
    {
        $alphabet    = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        $pass        = []; //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < 8; $i++) {
            $n      = rand(0, $alphaLength);
            $pass[] = $alphabet[$n];
        }

        return implode($pass); //turn the array into a string
    }

    public function actionBeitrittserklaerung($id)
    {
        $systemuser = Systemuser::model()->personalAccess()->findByPk($id);
        if (!empty($systemuser)) {
            $beitritt         = new Beitrittserklaerung();
            $path             = $beitritt->generate($systemuser);
            $beitrittFormular = UserFiles::model()->findByAttributes(['user_id' => $systemuser->id, 'document_type_id' => DocumentType::BEITRITTSERKLAERUNG_FORMULAR],
                ['order' => 'id desc']);
            if (!empty($beitrittFormular)) {
                $this->redirect(['stammdaten/user/userFiles/download', 'id' => $beitrittFormular->id]);
            }
        }
        $this->redirect(['systemuser/admin']);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param Systemuser $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'systemuser-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }

    /**
     * Lädt das Model mit der übergebenen ID falls dieses geladen werden konnte
     *
     * @param int $id
     *          ID des Models das geladen werden soll
     *
     * @return User|null
     *          Model des Systemusers falls dieses geladen werden konnte, sonst NULL
     */
    public function loadModel(int $id)
    {
        $systemuser = User::model()->findByPk($id);

        return $systemuser;
    }

    //-----------------------------------------------------------------------------------------------------------------------
    //-----------------------------------------  Datenaktualisierung   -----------------------------------------------
    //-----------------------------------------------------------------------------------------------------------------------

    /**
     * Diese Aktion Sendet eine Datenaktualiserung an alle Definierten Empfänger
     *
     * @param int $id
     *              $id des Systemuser, desssn Datenaktualiserung gesendet werden soll
     */
    public function actionSendDataUpdate(int $id)
    {
        $systemuser = $this->loadModel($id);
        $data       = $_POST;
        $newData    = $this->buildDataArray($systemuser);
        $oldData    = $this->getOldData($systemuser);
        if (!isset($data['sendDataUpdate']) || $data['sendDataUpdate'] == 0) {
            $this->render('sendDataUpdate', ['systemuser' => $systemuser, 'prefix' => $this->prefix]);
        } else {
            $changeddata = [];
            foreach ($data as $key => $value) {
                if (strpos($key, 'send_') !== false) {
                    $exploded = explode('_', $key);
                    $type     = $exploded[1];
                    $field    = $exploded[2];

                    $changeddata[$type][$field]['Neu'] = $newData[$type][$field];
                    $changeddata[$type][$field]['Alt'] = '';
                    if (isset($oldData[$type][$field])) {
                        $changeddata[$type][$field]['Alt'] = $oldData[$type][$field];
                    }
                }
            }

            $remark = '';
            if (isset($data['remark'])) {
                $remark = $data['remark'];
            }
            $attachments = [];
            if (!empty($data['attachments'])) {
                foreach ($data['attachments'] as $id) {
                    foreach ($systemuser->userFiles as $files) {
                        if ($files->id == $id) {
                            $attachments[] = $files->getPath();
                        }
                    }
                }
            }

            if (isset($data['sendData-grid_c2'])) {
                $brokerids = BrokerId::model()->findAllByPk($data['sendData-grid_c2']);
                foreach ($brokerids as $brokerid) {
                    try {
                        UserDataUpdateMail::forUser($systemuser)
                                          ->forBrokerId($brokerid)
                                          ->forChangedData($changeddata)
                                          ->withRemark($remark ?? '')
                                          ->withAttachments($attachments ?? [])
                                          ->send();
                    } catch (Exception $e) {
                    }
                }
                Yii::app()->user->setFlash('success', 'Datenaktualisierung gesendet');
            }
            $this->saveOldData($systemuser);

            $this->redirect(['systemuser/update', 'id' => $systemuser->id]);
        }
    }

    /**
     * Gibt das Old-Data-Array des Systemusers zurück
     *
     * @param User $systemuser
     *              Systemuser, dessen olddata zurückgegeben werden soll
     *
     * @return array|false Old-Data Array
     */
    public function getOldData($systemuser)
    {
        $data = $systemuser->olddata;

        return is_string($data) ? unserialize($data) : false;
    }

    /**
     * Diese Methode vergleicht zwei Felder des Data-Arrays für den Vergleich von alten und neuen Daten
     *
     * @param type $systemuser
     *              Systemuser, dessen Data-Arrays verglichen werden sollen
     * @param type $type
     *              Überschrift der DatenGruppe (Siehe buildDataArray)
     * @param type $field
     *              Name des Feldes das verglichen werden soll (siehe buildDataArray)
     *
     * @return string
     *              True falls die beiden Felder gleich sind, sonst false.
     *
     */
    public function compareDataField($newData, $oldData, $type, $field)
    {
        if (empty($newData[$type][$field])) {
            return false;
        }
        if (isset($newData[$type][$field]) && isset($oldData[$type][$field])) {
            return $newData[$type][$field] != $oldData[$type][$field];
        }

        return false;
    }

    /**
     * Speichert die aktuellen Werte als oldData
     *
     * @param type $systemuser
     *              Systemuser, dess Werte gespeichert werden sollen
     */
    public function saveOldData($systemuser)
    {
        $data                = $this->buildDataArray($systemuser);
        $data                = serialize($data);
        $systemuser->olddata = $data;
        $systemuser->save(false);
    }

    /**
     * Definiert alle Daten die für eine Datenaktualiserungsnachricht relevant sind
     *
     * @param User $systemuser
     *              Systemuser
     *
     * @return array Data-Array mit den momentanen Werten
     */
    public function buildDataArray($systemuser)
    {
        $address                                      = Address::model()->findByPk($systemuser->address_id);
        $data['Grunddaten']['Straße']                 = $address->street;
        $data['Grunddaten']['Hausnummer']             = $address->nr;
        $data['Grunddaten']['Postleitzahl']           = $address->zip;
        $data['Grunddaten']['Stadt']                  = $address->city;
        $data['Grunddaten']['IHK-Registrierung-§34d'] = $systemuser->ihk_reg_nr;
        $data['Grunddaten']['IHK-Registrierung-§34f'] = $systemuser->ihk_reg_nr2;
        $data['Grunddaten']['Handelsregisternummer']  = @$systemuser->agency->reg_nr;
        $data['Grunddaten']['Steuernummer']           = $systemuser->taxnumber;

        $bankaccountassignment = BankAccountAssignmentUser::model()->findByAttributes(['user_id' => $systemuser->id]);
        if (null !== $bankaccountassignment) {
            $bankaccount                            = BankAccount::model()->findByPk($bankaccountassignment->bank_account_id);
            $data['Bankverbindung']['Kontoinhaber'] = $bankaccount->depositor;
            $data['Bankverbindung']['Kontonummer']  = $bankaccount->account_number;
            $data['Bankverbindung']['Bankleitzahl'] = $bankaccount->bank_code;
            $data['Bankverbindung']['Bank']         = $bankaccount->bank_name;
            $data['Bankverbindung']['IBAN-Code']    = $bankaccount->iban_code;
            $data['Bankverbindung']['SWIFT-Code']   = $bankaccount->swift_code;
        }

        $contact                          = Contact::model()->findByPk($systemuser->contact_id);
        $data['Kontakt']['Telefon-Büro']  = $contact->phone_business;
        $data['Kontakt']['Telefon-Mobil'] = $contact->phone_mobile;
        $data['Kontakt']['Fax']           = $contact->fax;
        $data['Kontakt']['E-Mail']        = $contact->email;
        $data['Kontakt']['Homepage']      = $contact->homepage;

        return $data;
    }

    //-----------------------------------------------------------------------------------------------------------------------
    //-----------------------------------------  Partnerbogen   -----------------------------------------------
    //-----------------------------------------------------------------------------------------------------------------------

    public function getPartnerBogenUserData($systemuser)
    {
        $data                 = [];
        $partnerbogenUserData = PartnerbogenUserData::model()->findByAttributes(['user_id' => $systemuser->id]);
        if (!empty($partnerbogenUserData)) {
            $userData                 = $partnerbogenUserData->data;
            $partnerbogen             = new Partnerbogen();
            $partnerbogen->attributes = $userData['partnerbogen'];
            $partnerbogen->remark     = $userData['partnerbogen']['remark'] ?? '';
            $data['negativeintrag']   = $partnerbogenUserData->getNegativeintrag();
            $data['numPartner']       = count($data['negativeintrag']);
            $data['partnerbogen']     = $partnerbogen;
            $data['tabulardata']      = $userData['tabulardata'];
            $data['allgemein']        = $partnerbogenUserData->getAllgemein();
            $data['jobHistory']       = $partnerbogenUserData->getJobHistory($data['numPartner']);
        } else {
            $partnerbogenUserData = new PartnerbogenUserData();
            $partnerbogenUserData->setDefaultData($systemuser->id);
            $data = $partnerbogenUserData->data;
        }

        return $data;
    }

    public function partnerbogenExists($systemuser)
    {
        $partnerbogenUserData = PartnerbogenUserData::model()->findByAttributes(['user_id' => $systemuser->id]);
        if (null === $partnerbogenUserData) {
            return false;
        }

        return true;
    }

    public function isVisiblePartnerbogen($attribut, $partnerID, $negativeintraege)
    {
        return isset($negativeintraege[$partnerID][$attribut]) &&
               $negativeintraege[$partnerID][$attribut . '_num'] == 1 ? 'display:block;' :
            'display:none;';
    }

    public function actionGetAddPartnerHtml()
    {
        $counter = Yii::app()->request->getParam('partnerCounter');
        $view    = Yii::app()->request->getParam('view');
        $index   = Yii::app()->request->getParam('index');
        if (empty($index)) {
            $index = 1;
        }
        $tabulardata['jobhistory'] = [0 => new UserJobHistory()];
        $html                      = $this->renderPartial($view,
            [
                'systemuser'     => new Systemuser(),
                'partnerCounter' => $counter,
                'tabulardata'    => $tabulardata,
                'form'           => new CActiveForm(),
                'index'          => $index,
                'agency'         => new Agency()
            ], false, false);
        Yii::app()->clientScript->renderBodyEnd($html);
        echo $html;
        Yii::app()->end();
    }

    public function actionRedirectPartnerbogen()
    {
        $this->redirect('/partnerbogen/form/index');
    }

    public function actionPartnerbogen($id)
    {
        $partnerbogenUserData = PartnerbogenUserData::model()->findByAttributes(['user_id' => $id]);
        if (!empty($partnerbogenUserData)) {
            $userData = $partnerbogenUserData->data;
        } else {
            $partnerbogenUserData          = new PartnerbogenUserData();
            $partnerbogenUserData->user_id = $id;
            $userData                      = $partnerbogenUserData->data;
            $userData['tabulardata']       = [];
        }
        if (!empty($_POST['allgemein'])) {
            $userData['allgemein'] = $_POST['allgemein'];
        }
        if (!empty($_POST['Nagativeintrag'])) {
            $userData['negativeintrag'] = $_POST['Nagativeintrag'];
        }
        if (isset($_POST['remark'])) {
            $userData['partnerbogen']['remark'] = $_POST['remark'];
        }
        if (!empty($_POST['Partnerbogen']['auszahlung'])) {
            $userData['partnerbogen']['auszahlung'] = $_POST['Partnerbogen']['auszahlung'];
        } else {
            $userData['partnerbogen']['auszahlung'] = '';
        }

        if (empty($userData['partnerbogen'])) {
            $userData['partnerbogen'] = [];
        }

        $historyJob = [];
        $counter    = 0;
        if (!empty($_POST['JobHistory'])) {
            foreach ($_POST['JobHistory'] as $jobHistory) {
                foreach ($jobHistory as $row) {
                    $jobHistoryModel             = new UserJobHistory();
                    $jobHistoryModel->attributes = $row;
                    $historyJob[$counter][]      = $jobHistoryModel;
                }
                $counter++;
            }
        }
        if (!empty($historyJob)) {
            $userData['jobHistory'] = $historyJob;
        }
        $partnerbogenUserData->data = $userData;
        if ($partnerbogenUserData->save()) {
            Yii::app()->user->setFlash('success', 'Systemnutzer aktualisiert');
        }
        $this->redirect(['update', 'id' => $id, 'activetab' => 'Partnerbogen']);
    }

    public function batchTabular($modelname, $start = false)
    {
        $models = [];
        if (isset($_POST[$modelname])) {
            foreach ($_POST[$modelname] as $i => $model) {
                if (is_int($i)) {
                    $new             = new $modelname();
                    $new->attributes = $_POST[$modelname][$i];
                    $new->validate();
                    $models[] = $new;
                }
            }
        } elseif ($start) {
            $models[] = new $modelname();
        }

        return $models;
    }

    public function actionAddToSuperadmin()
    {
        if (!Yii::app()->user->isAdmin()) {
            throw new AccessDeniedException();
        }
        if (!YII_DEBUG) {
            Yii::app()->user->setFlash('error', 'Du kannst dich nur lokal als Admin zum Superadmin freischalten.');
        } else {
            SuperAdmin::add((int) currentUser()->id);
            Yii::app()->user->setFlash('success', 'Du wurdest als Superadmin freischalten.');
        }
        if (Yii::app()->request->getUrlReferrer() !== $this->createAbsoluteUrl('addToSuperadmin')) {
            $this->redirect(Yii::app()->request->getUrlReferrer());
        }
        $this->redirect('/home');
    }

    protected function redirectToWithError(array $url, string $error)
    {
        Yii::app()->user->setFlash(
            'error',
            $error
        );

        $this->redirect($url);
    }
}
