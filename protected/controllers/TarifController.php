<?php

use Business\Tarif\Provider\TarifProvider;

class TarifController extends GlobalRightController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout           = '//layouts/column2';
    public $excludedActions  = [
        'GetLevel2',
        'GetLevel3',
    ];

    private $prefix = 'Tarif';

    public static function getTarif(int $user_id, $insurance_company_id, $product_combo_id)
    {
        $productCombo  = ProductCombo::model()->findByPk($product_combo_id);
        $user          = User::model()->findByPk($user_id);
        $agency_id     = !empty($user) ? $user->agency_id : null;
        $companyDataId = InsuranceCompanyData::model()->getDataId($insurance_company_id, false, $agency_id);
        if (!empty($productCombo) && !empty($user) && !empty($companyDataId)) {
            $tarifProvider = new TarifProvider((int) $insurance_company_id, $user);
            $criteria      = new CDbCriteria();
            $criteria->addInCondition('product_combo_id', $productCombo->getParentIds());
            $criteria->order = 'productCombo.level4_id DESC, productCombo.level3_id DESC,productCombo.level2_id DESC,productCombo.level1_id, t.sort_order';
            $criteria->with  = ['productCombo'];
            $tariffs         = $tarifProvider->all($criteria);
            $tarifController = new self('tarif');
            $tariffs         = $tarifController->getUserTariffs($tariffs, $insurance_company_id, $product_combo_id, $user_id);
            $tariffs         = array_values($tariffs);
        }

        return !empty($tariffs) ? $tariffs[0] : null;
    }

    /**
     * Bearbeitet eine Menge von Tarifen und gibt diere zurück.
     * Die Werte der Tarife werden in Abhängigkeit des Users und dessen "Parents" bearbeitet
     *
     * @param array        $tariffs
     *              Array von Tarifen
     * @param int          $companyId
     *              ID der Gesellschaftf, für die die Tarife bearbeitet werden sollen
     * @param ProductCombo $productCombo
     *              Die Product-Kombo, für die die Tarife bearbeitet werden sollen
     *
     * @return array<Tarif>
     *              Die überarbeiteten Tarife als Array.
     *              Diese Tarife können nicht gespeichert werden, da deren Werte verfälscht wurden
     */
    public static function getUserTariffs($tariffs, $companyId, $productCombo, $user_id = null)
    {
        if (empty($user_id)) {
            $user_id = Yii::app()->user->getId();
        }
        $commissionArray   = [];
        $generalCommission = [
            'commission'         => 1.00,
            'trailer_commission' => 1.00,
            'lap_commission'     => 1.00,
        ];
        if (!empty($productCombo) && $productCombo instanceof ProductCombo) {
            $comboChilds = $productCombo->getChildren();
        } else {
            $comboChilds = ProductCombo::model()->findAll();
        }
        $comboids = [];
        foreach ($comboChilds as $c) {
            $comboids[] = $c->id;
        }
        $searchForCommission = false;

        $stop = false;
        while (!$stop && !empty($user_id)) {
            $general = null;
            $general = Commission::model()->findByAttributes(['user_id' => $user_id, 'company_id' => $companyId, 'general' => 1]);
            if (empty($general)) {
                $general = Commission::model()->findByAttributes(['user_id' => $user_id, 'company_id' => null, 'general' => 1]);
            }
            if (!empty($general)) {
                if (empty($generalCommission)) {
                    $generalCommission['commission']         = 1.00 * $general->commission;
                    $generalCommission['trailer_commission'] = 1.00 * $general->trailer_commission;
                    $generalCommission['lap_commission']     = 1.00 * $general->lap_commission;
                } else {
                    $generalCommission['commission']         = 1.00 * $generalCommission['commission'] * ($general->commission / 100.00);
                    $generalCommission['trailer_commission'] = 1.00 * $generalCommission['trailer_commission'] * ($general->trailer_commission / 100.00);
                    $generalCommission['lap_commission']     = 1.00 * $generalCommission['lap_commission'] * ($general->lap_commission / 100.00);
                }
                $user_id = null;
                $stop    = true;
            } else {
                $criteria = new CDbCriteria();
                $criteria->compare('user_id', $user_id);
                $criteria->addCondition('general = 0 or general is null');
                $criteria->addInCondition('product_combo_id', $comboids);
                $criteria2 = clone $criteria;
                $criteria->addCondition('company_id is null');
                $commissions = Commission::model()->findAll($criteria);

                $criteria2->compare('company_id', $companyId);
                $companyCommissions = Commission::model()->findAll($criteria2);

                if (!empty($commissions) || !empty($companyCommissions)) {
                    $commissionArray = [];
                    foreach ($commissions as $com) {
                        $commissionArray[$com->product_combo_id] = [
                            'commission'         => $com->commission,
                            'promille'           => $com->promille,
                            'trailer_commission' => $com->trailer_commission,
                            'lap_commission'     => $com->lap_commission
                        ];
                    }
                    foreach ($companyCommissions as $com) {
                        $commissionArray[$com->product_combo_id] = [
                            'commission'         => $com->commission,
                            'promille'           => $com->promille,
                            'trailer_commission' => $com->trailer_commission,
                            'lap_commission'     => $com->lap_commission,
                        ];
                    }
                    $searchForCommission = true;
                }
                $stop = true;
            }
        }
        foreach ($tariffs as $tarif) {
            $pc   = $tarif->product_combo_id;
            $stop = false;
            if ($searchForCommission) {
                while (!$stop) {
                    if (isset($commissionArray[$pc])) {
                        $userCom = $commissionArray[$pc];

                        $tarif->commission = 1.00 * $userCom['commission'] * $generalCommission['commission'];

                        $tarif->commission_promille = $userCom['promille'];
                        $tarif->trailer_commission  = 1.00 * $userCom['trailer_commission'] * $generalCommission['trailer_commission'];
                        $tarif->lap                 = 1.00 * $userCom['lap_commission'] * $generalCommission['lap_commission'];
                        if (!empty($tarif->dynamic)) {
                            $tarif->dynamic          = $userCom['commission'];
                            $tarif->dynamic_promille = $userCom['promille'];
                        }
                        $tarif->disable_save = 1;
                        $stop                = true;
                    } else {
                        $productCombo = ProductCombo::model()->findByPk($pc);
                        if (in_array($productCombo->getLevel(), [2, 3, 4])) {
                            $pc = $productCombo->getParentProductCombo()->id;
                        } else {
                            $tarif->commission         = 0;
                            $tarif->trailer_commission = 0;
                            $tarif->lap                = 0;
                            $tarif->dynamic            = 0;
                            $tarif->disable_save       = 1;
                            $stop                      = true;
                        }
                    }
                }
            } else {
                $tarif->commission         = 1.00 * (float) $tarif->commission * (float) $generalCommission['commission'];
                $tarif->trailer_commission = 1.00 * (float) $tarif->trailer_commission * (float) $generalCommission['trailer_commission'];
                $tarif->lap                = 1.00 * (float) $tarif->lap * (float) $generalCommission['lap_commission'];
                if (!empty($tarif->dynamic)) {
                    $tarif->dynamic = 1.00 * (float) $tarif->dynamic * (float) $generalCommission['commission'];
                }
                $tarif->disable_save = 1;
                $stop                = true;
            }
        }

        return $tariffs;
    }

    /**
     * Gibt den Inhalt der Level2 Textbox vom Tarif in Abhängigkeit von Textbox Level1
     */
    public function actionGetLevel2()
    {
        $names         = [];
        $names[-1]     = '';
        $level1id      = $_POST['level2'];
        $allProducts   = Product::model()->findAll();
        $productCombos = ProductCombo::model()->findAllByAttributes(['level3_id' => null, 'level1_id' => $level1id]);
        foreach ($productCombos as $combo) {
            if ($combo->level2_id != '') {
                foreach ($allProducts as $product) {
                    if ($combo->level2_id == $product->id) {
                        $names[$product->id] = $product->name;
                    }
                }
            }
        }
        print_r($names);
        foreach ($names as $id => $name) {
            echo CHtml::tag('option', ['value' => $id], CHtml::encode($name), true);
        }
    }

    /**
     * Gibt den Inhalt der Level3 Textbox vom Tarif in Abhängigkeit von Textbox Level2
     */
    public function actionGetLevel3()
    {
        $names         = [];
        $names[-1]     = '';
        $level2id      = $_POST['level3'];
        $allProducts   = Product::model()->findAll();
        $productCombos = ProductCombo::model()->findAllByAttributes(['level2_id' => $level2id]);
        foreach ($productCombos as $combo) {
            if ($combo->level3_id != '') {
                foreach ($allProducts as $product) {
                    if ($combo->level3_id == $product->id) {
                        $names[$product->id] = $product->name;
                    }
                }
            }
        }
        print_r($names);
        foreach ($names as $id => $name) {
            echo CHtml::tag('option', ['value' => $id], CHtml::encode($name), true);
        }
    }

    /**
     * Lädt das Tarif-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des Tarif-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $tarif = Tarif::model()->findByPk($id);

        return $tarif;
    }
}
