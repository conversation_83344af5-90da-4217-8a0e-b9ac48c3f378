<?php

class TodoController extends Controller
{
    use ApiUtilsTrait;

    public function actions()
    {
        return [
            'create' => [
                'class'            => 'application.components.actions.AjaxSaveModelAction',
                'modelname'        => 'Todo',
                'echo_attr'        => 'id',
                'default_response' => false
            ],
            'delete' => [
                'class'     => 'application.components.actions.ModelDeletionAction',
                'modelname' => 'Todo',
            ],
        ];
    }

    public function actionToggleStatus()
    {
        $id    = Yii::app()->request->getPost('id');
        $model = Todo::model()->own()->findByPk($id);
        if ($model === null) {
            echo 0;
            Yii::app()->end();
        }
        if (empty($model->done_datetime)) {
            $model->done_datetime = date('Y-m-d H:i:s');
        } else {
            $model->done_datetime = new CDbExpression('NULL');
        }
        echo $model->save() ? 1 : 0;
        Yii::app()->end();
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionList()
    {
        $this->respondOk(array_map(
            static fn ($todo) => [
                'id'   => (int) $todo->id,
                'text' => $todo->todo,
                'done' => isset($todo->done_datetime),
            ],
            Todo::model()->own()->findAll()),
        );
    }
}
