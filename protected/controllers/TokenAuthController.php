<?php

/**
 * Created by PhpStorm.
 * User: alex
 * Date: 08.04.15
 * Time: 16:14
 */
class TokenAuthController extends CController
{
    public function actionAuth($token)
    {
        $this->checkAccess();
        $model   = Token::model()->getToken($token);
        $success = !empty($model) ? 1 : 0;
        $this->logRequest(Yii::app()->request->getUserHostAddress(), $token, $success);
        if (!empty($model)) {
            $systemuser  = Systemuser::model()->findByPk($model->user_id);
            $model->used = 1;
            $model->save();
            if (!empty($systemuser) && Yii::app()->user->isGuest) {
                Yii::app()->getModule('login')->forceUserLogin($systemuser);

                $this->redirect([$model->getLocation()]);
            } elseif (!empty($systemuser) && $systemuser->id == Yii::app()->user->getId()) {
                $this->redirect([$model->getLocation()]);
            } else {
                Yii::app()->user->setFlash('error', 'Sie sind mit einem anderen <PERSON>er eingeloggt.');
                $this->redirect(['/home']);
            }
        } else {
            $expired = Token::model()->findByAttributes(['token' => $token]);
            if (!empty($expired)) {
                Yii::app()->user->setFlash('error', '<strong>Link abgelaufen</strong><br/> Der angefragte Login-Link ist nicht mehr verfügbar.');
            } else {
                Yii::app()->user->setFlash('error', 'Sie konnten nicht eingeloggt werden.');
            }
            $this->redirect(['login/login/index']);
        }
    }

    private function checkAccess()
    {
        if (TokenRequest::isBanned(Yii::app()->request->getUserHostAddress())) {
            $this->redirect('tokenauth/accessdenied');

            return false;
        }

        return true;
    }

    private function logRequest($ip, $token, $success)
    {
        $log                  = new TokenRequest();
        $log->ip              = $ip;
        $log->requested_token = $token;
        $log->success         = $success;
        $log->save();
    }

    public function actionAccessDenied()
    {
        $this->renderPartial('accessDenied');
    }
}
