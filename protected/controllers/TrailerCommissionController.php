<?php

class TrailerCommissionController extends GlobalRightController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout   = '//layouts/column2';
    public $parentid = 'Commission';
    private $prefix  = 'TrailerCommission';

    /**
     *  Diese Methode wird aufgerufen wenn ein neues TrailerCommission-Model erstellt werden soll.
     *  Die Methode öffnet das TrailerCommission-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate($post)
    {
        $systemuserID = Yii::app()->session['procedure_commission_exception']['systemuser'];
        $data         = ViewHelper::parseToArray($post);
        $this->createExceptions($data, $systemuserID);
    }

    public function createExceptions($data, $userId)
    {
        foreach ((TrailerCommission::model()->findAllByAttributes(['user_id' => $userId])) as $value) {
            foreach ($data['insuranceCompany_select'] as $key => $companyID) {
                if ($value->company_id == $companyID) {
                    $this->delete($value->id);
                }
            }
        }

        $newDataArray    = [];
        $commissionArray = $data['TrailerCommission'];
        $companies       = $data['insuranceCompany_select'];

        foreach ($companies as $companieKey => $companieID) {
            foreach ($commissionArray as $key => $value) {
                if (isset($value['trailerCommission_level1'])) {
                    $commission     = $value['trailerCommission_level1'];
                    $newDataArray[] = ['user_id' => $userId, 'product_combo_id' => $value['id'], 'commission' => $commission, 'company_id' => $companieID];
                } elseif ((isset($value['trailerCommission_level2']))) {
                    $commission     = $value['trailerCommission_level2'];
                    $newDataArray[] = ['user_id' => $userId, 'product_combo_id' => $value['id'], 'commission' => $commission, 'company_id' => $companieID];
                } elseif (isset($value['trailerCommission_level3'])) {
                    $commission     = $value['trailerCommission_level3'];
                    $newDataArray[] = ['user_id' => $userId, 'product_combo_id' => $value['id'], 'commission' => $commission, 'company_id' => $companieID];
                } elseif ($key == 'TrailerCommissionMain') {
                    $newDataArray[] = ['user_id' => $userId, 'product_combo_id' => null, 'commission' => $value['Commission'], 'company_id' => $companieID];
                }
            }
        }

        foreach ($newDataArray as $key1 => $value1) {
            if ($value1['commission'] != '') {
                $this->createSingle($value1);
            }
        }

        $procedure       = Yii::app()->session['procedure_commission_exception'];
        $companie_select = $procedure['insuranceCompany_select'];

        //!Start! update Yii::app()->session['insuranceCompany_select'] for next Eselect (unset selected companies)
        foreach ($companie_select as $companieID => $companyName) {
            foreach ($data['insuranceCompany_select'] as $exceptionName => $exceptionID) {
                if ($companieID == $exceptionID) {
                    unset($companie_select[$companieID]);
                }
            }
        }
        $procedure['insuranceCompany_select']                 = $companie_select;
        Yii::app()->session['procedure_commission_exception'] = $procedure;
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $this->loadModel($id)->delete();
    }

    /**
     * Lädt das TrailerCommission-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des TrailerCommission-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = TrailerCommission::model()->findByPk($id);

        return $model;
    }

    /**
     * Diese Methode erstellt ein TrailerCommissions-Eintrag
     *
     * @param type $data
     *          Die TrailerCommission -Daten als Array
     *
     * @return $commission
     *          Das erstellte TrailerCommission -Exemplar
     */
    public function createSingle($data)
    {
        $trailerCommission = new TrailerCommission();
        $trailerCommission->unsetAttributes();
        $trailerCommission->attributes = $data;
        if ($trailerCommission->save()) {
            return $trailerCommission;
        }
    }

    /**
     * Diese Methode erstellt ein neues TrailerCommission -Model
     *
     * @param type $data
     *          Die TrailerCommission -Daten als Array
     *
     * @return $model
     *          Das erstellte TrailerCommission -Exemplar
     */
    public function create($data, $userId)
    {
        foreach ((TrailerCommission::model()->findAllByAttributes(['user_id' => $userId])) as $value) {
            if ($value->company_id == null) {
                $this->delete($value->id);
            }
        }

        if (!$this->validData($data)) {
            return null;
        }
        $newDataArray           = [];
        $trailerCommissionArray = $data;

        foreach ($trailerCommissionArray as $key => $value) {
            if ((isset($value['trailerCommission_level1']))) {
                $trailerCommission = $value['trailerCommission_level1'];
                $newDataArray[]    = ['user_id' => $userId, 'product_combo_id' => $value['id'], 'commission' => $trailerCommission];
            } elseif (isset($value['trailerCommission_level2'])) {
                $trailerCommission = $value['trailerCommission_level2'];
                $newDataArray[]    = ['user_id' => $userId, 'product_combo_id' => $value['id'], 'commission' => $trailerCommission];
            } elseif (isset($value['trailerCommission_level3'])) {
                $trailerCommission = $value['trailerCommission_level3'];
                $newDataArray[]    = ['user_id' => $userId, 'product_combo_id' => $value['id'], 'commission' => $trailerCommission];
            } elseif ($key == 'TrailerCommissionMain') {
                $newDataArray[] = ['user_id' => $userId, 'product_combo_id' => null, 'commission' => $value['Commission']];
            }
        }

        foreach ($newDataArray as $key1 => $value1) {
            if ($value1['commission'] != '') {
                $this->createSingle($value1);
            }
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model TrailerCommission handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        return isset($data);
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein TrailerCommission -Model aktualisiert werden soll.
     *  Die Methode öffnet das TrailerCommission -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden TrailerCommission -Models
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($model = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $model;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das TrailerCommission-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden TrailerCommission-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('update', ['model' => $model]);
        }

        $formulardata                            = $_POST;
        $formulardata['TrailerCommission']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein TrailerCommission-Exemplar
     *
     * @param type $data
     *          Die TrailerCommission-Daten als Array
     *
     * @return $model
     *          Das aktualisierte TrailerCommission-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $model             = $this->loadModel($data['TrailerCommission']['id']);
            $model->attributes = $data['TrailerCommission'];
            if ($model->save()) {
                return $model;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new TrailerCommission('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['TrailerCommission'])) {
            $model->attributes = $_GET['TrailerCommission'];
        }

        $this->render('admin', [
            'model' => $model,
        ]);
    }

    /**
     * Löscht das TrailerCommission-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden TrailerCommission-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
        }
    }

    public function deleteUpdate($company_id, $user_id)
    {
        foreach ((TrailerCommission::model()->findAllByAttributes(['user_id' => $user_id])) as $value) {
            if ($value->company_id == $company_id) {
                $this->loadModel($value->id)->delete();
            }
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param TrailerCommission $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'trailer-commission-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
