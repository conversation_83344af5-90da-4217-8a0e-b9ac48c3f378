<?php

class UserCourtageSettlementController extends GlobalRightController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout         = '//layouts/column2';

    /**
     * Löscht das UserCourtageSettlement-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden UserCourtageSettlement-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lädt das UserCourtageSettlement-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des UserCourtageSettlement-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $userCourtageSettlement = UserCourtageSettlement::model()->findByPk($id);

        return $userCourtageSettlement;
    }

    public function actionCreate()
    {
        $data = Yii::app()->request->getPost('Settlement');
        if (!empty($data)) {
            $settlement             = new UserCourtageSettlement();
            $settlement->user_id    = Yii::app()->user->getId();
            $settlement->attributes = $data;
            $settlement->setAgencyId();
            $settlement->check_timeframe = !empty($data['check_timeperiod']);
            $settlement->setIds($data['payment_ids']);
            $settlement->sorting = $data['sorting'] ?? null;
            try {
                $pdf = $settlement->getPDF();
                if (!empty($pdf)) {
                    $settlement->save();
                    $name = $settlement->getName(true);
                    $pdf->output($name, 'I');
                } else {
                    Yii::app()->user->setFlash('error', 'Die Abrechnung konnte nicht erstellt werden.');
                    $this->redirect(['courtage/admin']);
                }
            } catch (Exception $e) {
                Yii::app()->user->setFlash('error', 'Die Abrechnung konnte nicht erstellt werden.');
                $this->redirect(['courtage/admin']);
            }
        } else {
            Yii::app()->user->setFlash('error', 'Bitte wählen Sie die Zahlungen für die Abrechnung aus');
            $this->redirect(['courtage/admin']);
        }
    }

    public function actionPDF($id)
    {
        $settlement = $this->loadModel($id);
        $pdf        = $settlement->pdf;

        $name = $settlement->getName(true);
        //PDF ausgeben
        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $name . '"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        echo $pdf;
    }

    /**
     * Manages all userCourtageSettlements.
     */
    public function actionAdmin()
    {
        $userCourtageSettlement = new UserCourtageSettlement('search');
        $userCourtageSettlement->unsetAttributes();  // clear any default values
        if (isset($_GET['UserCourtageSettlement'])) {
            $userCourtageSettlement->attributes = $_GET['UserCourtageSettlement'];
        }

        $this->render('admin', [
            'userCourtageSettlement' => $userCourtageSettlement,
        ]);
    }

    public function actionAjaxCheckPolicy()
    {
        if (Yii::app()->request->isAjaxRequest) {
            $data                   = Yii::app()->request->getParam('Settlement');
            $settlement             = new UserCourtageSettlement();
            $settlement->attributes = $data;
            $settlement->setIds($data['payment_ids']);
            echo $settlement->checkPolicy();
        } else {
            throw new CHttpException('403', 'Forbidden access.');
        }
        Yii::app()->end();
    }

    public function actionAjaxSettlementExists()
    {
        if (Yii::app()->request->isAjaxRequest) {
            $data                   = Yii::app()->request->getPost('Settlement');
            $settlement             = new UserCourtageSettlement();
            $settlement->attributes = $data;
            echo empty($settlement->alreadyExists()) ? 0 : 1;
        } else {
            throw new CHttpException('403', 'Forbidden access.');
        }
        Yii::app()->end();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param UserCourtageSettlement $userCourtageSettlement the userCourtageSettlement to be validated
     */
    protected function performAjaxValidation($userCourtageSettlement)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'user-courtage-settlement-form') {
            echo CActiveForm::validate($userCourtageSettlement);
            Yii::app()->end();
        }
    }
}
