<?php

use Apfelbox\FileDownload\FileDownload;
use Business\Courtageanfragen\Mails\CourtageRequestMailFactory;
use Business\Mail\Attachments\AttachmentSources;
use Business\Mail\Footer\Footer;
use Business\Vorgaenge\Korrespondenz;
use Components\Client\Providers\ClientfileProvider;

class UserMailController extends AllowAllController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    public static function userMailExists($mail_procedure_id, $user_id, $insurance_company_id = null, $client_id = null)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('t.mail_procedure_id', $mail_procedure_id);
        $criteria->compare('t.user_id', $user_id);
        $criteria->compare('t.insurance_company_id', $insurance_company_id);
        $criteria->compare('t.client_id', $client_id);
        $userMail = UserMail::model()->find($criteria);

        return !empty($userMail) ? $userMail : false;
    }

    /**
     * Diese Methode erstellt ein neues UserMail -Model
     *
     * @param ?array $data Die UserMail -Daten als Array
     *
     * @return UserMail|null    Das erstellte UserMail -Exemplar
     * @throws CDbException
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        FileController::fixFilesArray();

        $userId                                = $data['user_id'] ?? Yii::app()->user->getID();
        $data['UserMail']['mail_status_id']    = MailStatus::OPEN;
        $data['UserMail']['last_edit_date']    = ViewHelper::getDate(true);
        $data['UserMail']['last_edit_user_id'] = $userId;

        if (!isset($data['UserMail']['insurance_company_id']) || $data['UserMail']['insurance_company_id'] == '') {
            $data['UserMail']['insurance_company_id'] = null;
        }
        if (isset($data['UserMail']['client_id']) && $data['UserMail']['client_id'] == '') {
            unset($data['UserMail']['client_id']);
        }
        if (isset($data['UserMail']['contract_id']) && $data['UserMail']['contract_id'] == '') {
            unset($data['UserMail']['contract_id']);
        }
        if (isset($data['UserMail']['product_combo_id']) && $data['UserMail']['product_combo_id'] == '') {
            unset($data['UserMail']['product_combo_id']);
        }
        unset($data['UserMail']['id']);

        $usermail              = new UserMail();
        $usermail->new_created = true;
        $usermail->attributes  = $data['UserMail'];

        if (!isset($data['UserMail']['procedure_id']) || $data['UserMail']['procedure_id'] == '') {
            $procedure = ProcedureController::generateNewProcedure(
                $usermail->user_id,
                $usermail->client_id,
                $usermail->insurance_company_id,
                $usermail->product_combo_id
            );
            if (null === $procedure) {
                return null;
            }
            $usermail->procedure_id = $procedure->id;
        } else {
            $procedure = Procedure::model()->findByPk($data['UserMail']['procedure_id']);
        }

        if (!isset($data['UserMail']['mail_id'])) {
            $tags = new Tags();
            $tags->forUserId($usermail->user_id);
            $tags->forCompanyId($usermail->insurance_company_id);
            $tags->forProductComboId($usermail->product_combo_id);
            if (!empty($usermail->client_id)) {
                $tags->forClientId((int) $usermail->client_id);
            }
            $tags->forContractId($usermail->contract_id);
            $tags->allowCustomSalutation();

            $mailprocedure = $usermail->mailProcedure;
            if (!empty($mailprocedure)) {
                $tags->forSubjectId($mailprocedure->support_assignment_subject_id);
            }

            $data['Mail']['content']      = $tags->replaceAvailable($data['Mail']['content']);
            $data['Mail']['subject']      = $tags->replaceAvailable($data['Mail']['subject']);
            $data['Mail']['sender_email'] = $usermail->user->getMailAddress();

            $attachments = [];
            if (!empty($data['Mail']['attachments'])) {
                foreach ($data['Mail']['attachments'] as $key => $value) {
                    $file = null;
                    if (empty($data['Mail']['attachments_absolute_path'])) {
                        if (strpos($value, '$DS$') !== false) {
                            $value = str_replace(['$DS$', '$SPACE$'], [DIRECTORY_SEPARATOR, ' '], $value);
                            $path  = Yii::getPathofAlias('uploads') . DIRECTORY_SEPARATOR . ltrim($value, '/');
                        } elseif (strpos($value, 'user') !== false) {
                            $file = UserFiles::model()->findByPk(str_replace('user_', '', $value));
                        } elseif (strpos($value, 'client') !== false) {
                            $file =
                                ClientfileProvider::forUser(User::model()->findByPk($userId))
                                                  ->find()
                                                  ->findByPk(str_replace('client_', '', $value));
                        } elseif (strpos($value, 'procedure') !== false) {
                            $file = ProcedureFiles::model()->findByPk(str_replace('procedure_', '', $value));
                        }
                        if (!empty($file)) {
                            $path = $file->path;
                        }
                    } else {
                        $path = $value;
                    }

                    // Wenn eine Anlage vorhanden sein soll, diese aber nicht gefunden wird, wird die E-Mail nicht verschickt
                    if (empty($path)) {
                        return null;
                    }

                    $attachments[] = $path;
                }
            }

            if (!empty($data['UserMail']['vollmacht_senden'])) {
                $vollmacht = ClientFiles::findByDocumentTypesForClient(
                    $usermail->client_id,
                    [DocumentType::MAKLERVOLLMACHT, DocumentType::MAKLERAUFTRAG]
                );

                if ($vollmacht !== null) {
                    $attachments[] = $vollmacht->getPath();
                }
            }

            foreach ($_FILES['ProcedureFiles']['name']['file'] ?? [] as $key => $attrname) {
                $uploaded = ProcedureFileUpload::upload($procedure, CUploadedFile::getInstance(new ProcedureFiles(), 'file[' . $key . ']'));
                if ($uploaded !== null) {
                    array_push($attachments, $uploaded->getPath());
                }
            }

            $data['Mail']['attachments'] = $attachments;

            $mail = (new MailController('mail'))->create($data);
            if (null === $mail) {
                return null;
            }
            $usermail->mail_id = $mail->id;
        }
        if (empty($data['UserMail']['notification_date'])) {
            $usermail->setNotificationDate();
        }
        if ($usermail->save()) {
            return $usermail;
        }
        if (null !== $procedure) {
            ProcedureFiles::model()->deleteAllByAttributes(['procedure_id' => $procedure->id]);
            $procedure->delete();
        }

        return null;
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model UserMail handelt
     *
     * @param $data
     *
     * @return bool true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data): bool
    {
        return isset($data, $data['UserMail']);
    }

    /**
     * Diese Methode verschickt eine Usermail
     *
     * @param int  $id ID der Usermail, die verschickt werden soll
     * @param bool $flashmessage
     *
     * @return bool
     */
    public function sendUserMail($id, $flashmessage = true): bool
    {
        $send     = false;
        $usermail = UserMail::model()->findByPk($id);
        if (null !== $usermail) {
            $send = $usermail->send();
        }

        if ($flashmessage && $send) {
            Yii::app()->user->setFlash('success', 'Ihre E-Mail wurde verschickt.');
        } else {
            if ($flashmessage && !$send) {
                Yii::app()->user->setFlash('error', 'Ihre E-Mail konnte nicht verschickt werden.
                Eine mögliche Ursache ist, dass die Anhänge zu groß sind. Bitte komprimieren Sie die Anhänge und versenden Sie die E-Mail in diesem Fall über Ihr eigenes E-Mailprogramm erneut.');
            }
        }

        return $send;
    }

    public function actionCreateByParam(int $procedureId, int $companyId, int $clientId = null): void
    {
        $data = [
            'user_id'              => currentUser()->id,
            'client_id'            => $clientId,
            'mail_procedure_id'    => $procedureId,
            'insurance_company_id' => $companyId,
        ];

        $this->redirect(Korrespondenz::mail()->ausUserMailData($data)->getUrl());
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues UserMail-Model erstellt werden soll.
     *  Die Methode öffnet das UserMail-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     * @param bool  $send
     * @param null  $procedureid
     * @param null  $id
     * @param array $usermaildata
     * @param null  $user_writing_id
     *
     * @return UserMail|null
     */
    public function actionCreate($send = false, $procedureid = null, $id = null, $usermaildata = [], $user_writing_id = null)
    {
        if (UtilHelper::is_serialized($usermaildata)) {
            @$usermaildata = unserialize($usermaildata);
        }

        $this->redirect(Korrespondenz::mail()->ausUserMailData(is_array($usermaildata) ? $usermaildata : [])->getUrl());
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein UserMail -Model aktualisiert werden soll.
     *  Die Methode öffnet das UserMail -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden UserMail -Models
     */
    public function actionUpdate($id, $new_created = false)
    {
        $usermail = $this->loadModel($id);
        if ($usermail != null) {
            $data = $this->getUpdateFormularData($id, $new_created);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if (($usermail = $this->update($data)) != null) {
                        $this->redirect(['userMail/update', 'id' => $usermail->id]);

                        return $usermail;
                    }
                }
            }
        } else {
            Yii::app()->user->setFlash('error', 'Der Vorgang ist nicht mehr vorhanden');
            $this->redirect('/procedure/admin');
        }

        return null;
    }

    /**
     * Lädt das UserMail-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des UserMail-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $usermail = UserMail::model()->findByPk($id);

        return $usermail;
    }

    /**
     *
     * Öffnet das UserMail-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden UserMail-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id, $new_created = false)
    {
        $usermail              = $this->loadModel($id);
        $usermail->new_created = $new_created;
        if (!$this->validData($_POST)) {
            $this->render('//home/<USER>/vorgaenge/mail/update', ['usermail' => $usermail]);
        }

        $formulardata                   = $_POST;
        $formulardata['UserMail']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein UserMail-Exemplar
     *
     * @param type $data
     *          Die UserMail-Daten als Array
     *
     * @return $usermail
     *          Das aktualisierte UserMail-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $usermail                    = $this->loadModel($data['UserMail']['id']);
            $usermail->last_edit_date    = ViewHelper::getDate(true);
            $usermail->last_edit_user_id = Yii::app()->user->getID();
            $usermail->attributes        = $data['UserMail'];
            if ($usermail->save()) {
                return $usermail;
            }
        }

        return null;
    }

    /**
     * Löscht das UserMail-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden UserMail-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        $this->redirect($_POST['returnUrl'] ?? ['procedure/admin']);
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $usermail = $this->loadModel($id);

        return $usermail->delete();
    }

    /**
     * Setzt den Status eines Vorganges auf erledigt
     *
     * @param type $id
     *          ID der UserMail, dess Status auf erledigt gesetzt werden soll
     */
    public function actionDone($id)
    {
        $usermail = UserMail::model()->findByPk($id);
        if (null !== $usermail) {
            $usermail->mail_status_id = MailStatus::DONE;
            $usermail->save();
            foreach ($usermail->userMails as $value) {
                $value->mail_status_id = MailStatus::DONE;
                $value->save();
            }
        }
        if (!Yii::app()->request->isAjaxRequest) {
            $this->redirect(['update', 'id' => $id]);
        }
    }

    /**
     * Versendet eine bereits existierende Usermail nochmals
     *
     * @param type $id
     *              ID der UserMail, die nochmals verschickt werden soll
     */
    public function actionResendUserMail($id, $subject = '', $contentaddition = '', $comment = 'Ursprüngliche E-Mail erneut versendet')
    {
        $usermail                                = UserMail::model()->findByPk($id);
        $data['UserMail']['user_id']             = $usermail->user_id;
        $data['UserMail']['mail_id']             = $usermail->mail_id;
        $data['UserMail']['mail_procedure_id']   = $usermail->mail_procedure_id;
        $data['UserMail']['product_combo_id']    = $usermail->product_combo_id;
        $data['UserMail']['client_id']           = $usermail->client_id;
        $data['UserMail']['comment']             = $comment;
        $data['UserMail']['parent_user_mail_id'] = $usermail->id;
        $data['UserMail']['procedure_id']        = $usermail->procedure_id;
        $newusermail                             = $this->create($data);
        if ($newusermail) {
            $this->sendUserMail($newusermail->id);
        }
        $this->redirect(['userMail/update', 'id' => $usermail->id]);
    }

    /**
     * Erinnert an eine UserMail
     *
     * @param type $id
     *          ID der UserMail, an die erinnert werden soll
     */
    public function actionRemind($id)
    {
        /**
         * @var UserMail $usermail
         */
        $usermail = UserMail::model()->findByPk($id);
        if (!empty($usermail)) {
            $remindStatus = [
                MailStatus::OPEN,
                MailStatus::REMINDER1,
                MailStatus::REMINDER2,
                MailStatus::ADMONITION1,
            ];
            if (in_array($usermail->mail_status_id, $remindStatus)) {
                if ($usermail->connectedToCourtageRequest()) {
                    $courtageRequest = CourtageRequest::model()->findByAttributes(['user_id' => $usermail->user_id, 'insurance_company_id' => $usermail->insurance_company_id]);
                    if (!empty($courtageRequest)) {
                        if ($courtageRequest->isOpen()) {
                            CourtageRequestMailFactory::new($courtageRequest)
                                                      ->kooperationsUnterlagenAnGesellschaft()
                                                      ->markAsReminder()
                                                      ->send();
                        }
                    } else {
                        Yii::app()->user->setFlash('error', 'Es besteht noch keine Courtageanfrage zu dieser Gesellschaft');
                        $this->redirect(['userMail/update', 'id' => $usermail->id]);

                        return 0;
                    }
                }

                if (!empty($contactPerson)) {
                    $salutation = $contactPerson->getFullSalutation();
                } elseif ($usermail->recipientIsClient()) {
                    $salutation = $usermail->client->getFullSalutation(1, true);
                } else {
                    $salutation = 'Sehr geehrte Damen und Herren';
                }

                $contentaddition = '<p>' . $salutation . ',<br /><br />';
                $content         = (new ReminderText())->getContent($usermail);

                $contentaddition .= '<p>' . $content->getContentForUsermail($usermail) . '</p>';
                $contentaddition .= Footer::forCurrentUser()->getContent();

                $subjectaddittion = $content->getSubjectAddtion();
                $comment          = $content->getInfo();

                $usermail->mail_status_id = MailStatus::getNextAfterReminder($usermail);
                $usermail->setNotificationDate();
                $usermail->save();
                Yii::app()->user->setFlash('success', 'Es wurde an den Vorgang erinnert');
                $this->remind($usermail, $subjectaddittion, $contentaddition, $comment);
            } else {
                if ($usermail->mail_status_id == MailStatus::ADMONITION2) {
                    Yii::app()->user->setFlash('error',
                        'Sie haben an den Vorgang bereits zweimal erinnert und zweimal gemahnt.
                    Bitte wenden Sie sich an die nächst höhere, geeignete Instanz. <br />Gehen Sie dazu auf unserer Plattform unter
                    "Gesellschaften" und dann auf "Ansprechpartner". ');
                } else {
                    Yii::app()->user->setFlash('error', 'Auf Grund des Status kann nicht an diesen Vorgang erinnert werden.');
                }
            }
        }
        $this->redirect(['userMail/update', 'id' => $usermail->id]);
    }

    /**
     * @param UserMail $usermail
     * @param string   $subjectaddittion
     * @param string   $contentaddition
     * @param string   $comment
     * @param null     $reciever_email
     */
    private function remind($usermail, $subjectaddittion = '', $contentaddition = '', $comment = '', $reciever_email = null)
    {
        $data['UserMail']['user_id']             = $usermail->user_id;
        $data['UserMail']['mail_procedure_id']   = $usermail->mail_procedure_id;
        $data['UserMail']['product_combo_id']    = $usermail->product_combo_id;
        $data['UserMail']['client_id']           = $usermail->client_id;
        $data['UserMail']['comment']             = $comment;
        $data['UserMail']['parent_user_mail_id'] = $usermail->id;
        $data['UserMail']['procedure_id']        = $usermail->procedure_id;
        $mail                                    = new Mail();
        $mail->attributes                        = $usermail->mail->attributes;
        $mail->attachments                       = $usermail->mail->attachments;
        $mail->id                                = null;
        $mail->content                           = $usermail->mail->getContent();
        $mail->subject                           = $subjectaddittion . $mail->subject;
        $childs                                  = $usermail->userMails;
        if (!empty($childs)) {
            $child = array_pop($childs);
        } else {
            $child = $usermail;
        }
        $sendTime = $child->getSendTime();
        $info     = '<b>Urspünglich an:</b> ' . $usermail->mail->reciever_email . '<br />';
        if (!empty($sendTime)) {
            $info .= '<b>Am ' . $sendTime . ' schrieb ' . $child->user->Fullname . ':<br /><br /></b>';
        } else {
            $info .= '<b>' . $child->user->Fullname . ' schrieb:<br /><br /></b>';
        }
        $mail->content = $contentaddition . '<br /><hr><br />' . $info . $child->mail->getContent();
        if (!empty($reciever_email)) {
            $mail->reciever_email = $reciever_email;
        }
        $mail->save();
        $data['UserMail']['mail_id']    = $mail->id;
        $newusermail                    = $this->create($data, true);
        $newusermail->last_edit_user_id = Yii::app()->user->getID();
        $newusermail->last_edit_date    = ViewHelper::getDate(true);
        $newusermail->save();
        $this->sendUserMail($newusermail->id, false);
        $this->redirect(['userMail/update', 'id' => $usermail->id]);
    }

    /**
     * @param $id
     * @param $name
     *
     * @throws CHttpException
     */
    public function actionOpenAttachment($id, $name)
    {
        $usermail = UserMail::model()->findByPk($id);
        if (empty($usermail)) {
            throw new CHttpException(404, 'Mail nicht gefunden');
        }
        $mail = $usermail->mail;
        $data = $mail->getAttachmentData();

        $sources = new AttachmentSources();
        foreach ($data as $d) {
            if ($d['name'] == $name) {
                $attachment = $sources->getAttachment($d['link']);
                if ($attachment !== null) {
                    @FileDownload::createFromString($attachment->getContent())->sendDownload($attachment->getFileName(), false);
                }
            }
        }
        throw new CHttpException(404, 'Datei nicht gefunden');
    }

    /**
     * Performs the AJAX validation.
     *
     * @param UserMail $usermail the usermail to be validated
     */
    protected function performAjaxValidation($usermail)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'user-mail-form') {
            echo CActiveForm::validate($usermail);
            Yii::app()->end();
        }
    }
}
