<?php

class UserProductFocusController extends GlobalRightController
{

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    /**
     *  Diese Methode wird aufgerufen wenn ein neues UserProductFocus-Model erstellt werden soll.
     *  Die Methode öffnet das UserProductFocus-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['yt0'])) {
            if ($this->validData($data)) {
                if ($model = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $model;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das UserProductFocus -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des UserProductFocus -Models
     */
    private function getCreateFormularData()
    {
        $model = new UserProductFocus();

        if (!$this->validData($_POST)) {
            $this->render('create', ['model' => $model]);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model UserProductFocus handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['UserProductFocus'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues UserProductFocus -Model
     *
     * @param type $data
     *          Die UserProductFocus -Daten als Array
     *
     * @return $model
     *          Das erstellte UserProductFocus -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $model             = new UserProductFocus();
        $model->attributes = $data['UserProductFocus'];

        if ($model->save()) {
            return $model;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein UserProductFocus -Model aktualisiert werden soll.
     *  Die Methode öffnet das UserProductFocus -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden UserProductFocus -Models
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);
        if ($model != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['yt0'])) {
                if ($this->validData($data)) {
                    if ($model = $this->update($data) != null) {
                        $this->actionAdmin();

                        return $model;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das UserProductFocus-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des UserProductFocus-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $model = UserProductFocus::model()->findByPk($id);

        return $model;
    }

    /**
     *
     * Öffnet das UserProductFocus-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden UserProductFocus-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $model = $this->loadModel($id);


        if (!$this->validData($_POST)) {
            $this->render('update', ['model' => $model]);
        }

        $formulardata                           = $_POST;
        $formulardata['UserProductFocus']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein UserProductFocus-Exemplar
     *
     * @param type $data
     *          Die UserProductFocus-Daten als Array
     *
     * @return $model
     *          Das aktualisierte UserProductFocus-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $model             = $this->loadModel($data['UserProductFocus']['id']);
            $model->attributes = $data['UserProductFocus'];
            if ($model->save()) {
                return $model;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new UserProductFocus('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['UserProductFocus'])) {
            $model->attributes = $_GET['UserProductFocus'];
        }

        $this->render('admin', [
            'model' => $model,
        ]);
    }

    /**
     * Löscht das UserProductFocus-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden UserProductFocus-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Setzt den UserProductFocus für einen User, abhängig vom $_POST-Array
     *
     * @param type $userID
     *          User-ID des Useers
     */
    public function actionUpdateFocus($userID)
    {
        $data = $_POST;
        $this->updateFocus($userID, $data);


        $_POST = [];
        Yii::app()->user->setFlash('success', 'Schwerpunkte wurden festgelegt');
        $this->redirect(['systemuser/update/?id=' . $userID . '&activetab=Schwerpunkt']);
    }

    /**
     * Setzt den UserProductFocus für einen User
     *
     * @param type $userID
     *          User-ID des Useers
     */
    private function updateFocus($userID, $data)
    {
        $this->deleteAll($userID);
        foreach ($data as $key => $value) {
            if (strpos($key, 'UserProductFocus') !== false && isset($value['UserProductFocus'])) {
                $value['UserProductFocus']['user_id']          = $userID;
                $value['UserProductFocus']['product_combo_id'] = substr($key, 16);
                $this->create($value);
            }
        }
    }

    /**
     * Löscht alle Einträge mit der übergebenen User-ID
     *
     * @param type $userID
     *              User-ID des User, dessen ProductsFocus-Einträge gelöscht werden sollen
     */
    private function deleteAll($userID)
    {
        UserProductFocus::model()->deleteAllByAttributes(['user_id' => $userID]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param UserProductFocus $model the model to be validated
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'user-product-focus-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
