<?php

class UserRoleRightsController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     *             using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout = '//layouts/column2';

    public static function setStandardRightsForUser($systemuser)
    {
        UserRights::model()->deleteAllByAttributes(['user_id' => $systemuser->id]);
        $roleRights = UserRoleRights::model()->findAllByAttributes(['user_role_id' => $systemuser->user_role, 'agency_id' => $systemuser->agency_id]);
        if (empty($roleRights)) {
            $roleRights = UserRoleRights::model()->findAllByAttributes(['user_role_id' => $systemuser->user_role, 'agency_id' => null]);
        }
        foreach ($roleRights as $rr) {
            $userright          = new UserRights();
            $userright->user_id = $systemuser->id;
            $userright->right   = $rr->right_id;
            $userright->save();
        }
    }

    /**
     * Creates a new model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     */
    public function actionCreate()
    {
        $model = new UserRoleRights();
        if (!empty($_POST['UserRoleRights']) && !empty($_POST['UserRoleRights']['user_role_id'])) {
            $rights     = empty($_POST['rights_id']) ? [] : filter_var_array($_POST['rights_id'], FILTER_SANITIZE_NUMBER_INT);
            $userRoleID = (int) filter_var($_POST['UserRoleRights']['user_role_id'], FILTER_SANITIZE_NUMBER_INT);
            $agencyID   = (int) filter_var($_POST['UserRoleRights']['agency_id'], FILTER_SANITIZE_NUMBER_INT);
            $cmd        = $this->distinguishQueryByAgency('DELETE FROM user_role_rights', $userRoleID, $agencyID);
            $cmd->execute();

            foreach ($rights as $key => $value) {
                $model               = new UserRoleRights();
                $model->user_role_id = $userRoleID;
                if ($agencyID > 0) {
                    $model->agency_id = $agencyID;
                }
                $model->right_id = $value;
                $model->save();
            }

            if (isset($_POST['update_users']) && $_POST['update_users'] == 1) {
                $cmd = $this->distinguishQueryByAgency(
                    'DELETE FROM user_rights USING user_rights INNER JOIN user ON user.id = user_rights.user_id',
                    $userRoleID,
                    $agencyID
                );
                $cmd->execute();
                $cmd = $this->distinguishQueryByAgency('SELECT id FROM user', $userRoleID, $agencyID);

                $result1 = $cmd->queryAll();
                $cmd     = $this->distinguishQueryByAgency('SELECT right_id FROM user_role_rights', $userRoleID, $agencyID);
                $result2 = $cmd->queryAll();

                $values = [];
                foreach ($result1 as $r1) {
                    foreach ($result2 as $r2) {
                        $values[] = '(null,' . $r1['id'] . ',' . $r2['right_id'] . ')';
                    }
                }

                $valstring = implode(',', $values);

                $cmd = Yii::app()->db->createCommand('INSERT INTO user_rights VALUES :values');
                $cmd->bindValue(':value', $valstring);
                $insert = $cmd->execute();

                if ($insert) {
                    Yii::app()->user->setFlash('success', 'Die Rechte der User, mit der entsprechenden Rolle wurden erfolgreich aktualisiert');
                } else {
                    Yii::app()->user->setFlash('alert', 'Bei der Aktualiserung ist ein Fehler aufgetreten');
                }
            }

            $this->redirect(['create', 'id' => $model->id]);
        }

        $this->render('//stammdaten/rechteManagement/rechteRollen/create', [
            'model' => $model,
        ]);
    }

    /**
     * Entscheidet ob der Query mit Spezialisierung auf eine Agency ausgeführt wird
     * oder nicht
     *
     * @param string $query      Der Query aus dem ein CDbCommand generiert wird
     * @param int    $userRoleId Die id der userRole, nach der in der where Klausel
     *                           gefiltert wird
     * @param int    $agencyId   Die id der agency, nach der in der where Klausel
     *                           gefiltert wird. Bei null/0 wird kein where Filter an den Query gehängt
     *
     * @return CDbCommand Der resultierende Command
     */
    private function distinguishQueryByAgency(string $query, int $userRoleId, int $agencyId): CDbCommand
    {
        return $agencyId <= 0
            ? $this->queryWithUserRole($query, $userRoleId)
            : $this->queryWithUserRoleAndAgency($query, $userRoleId, $agencyId);
    }

    /**
     * Kombiniert einen gegeben Query mit einer where Klausel für die userRoleId
     * und agencyId
     *
     * @param string $query      Der Query aus dem ein CDbCommand generiert wird
     * @param int    $userRoleId Die id der userRole, nach der in der where Klausel
     *                           gefiltert wird
     * @param int    $agencyId   die id der agency, nach der in der where Klausel
     *                           gefiltert wird
     *
     * @return CDbCommand Der resultierende Command
     */
    private function queryWithUserRoleAndAgency(string $query, int $userRoleId, int $agencyId): CDbCommand
    {
        $query .= ' WHERE user_role_id = :user_role_id AND agency_id = :agency_id';

        $cmd = Yii::app()->db->createCommand($query);
        $cmd->bindValues([':user_role_id' => $userRoleId, ':agency_id' => $agencyId]);

        return $cmd;
    }

    /**
     * Kombiniert einen gegeben Query mit einer where Klausel für die userRoleId
     *
     * @param string $query      Der Query aus dem ein CDbCommand generiert wird
     * @param int    $userRoleId Die id der userRole, nach der in der where Klausel
     *                           gefiltert wird
     *
     * @return CDbCommand Der resultierende Command
     */
    private function queryWithUserRole(string $query, int $userRoleId): CDbCommand
    {
        $query .= ' WHERE user_role_id = :user_role_id AND agency_id IS NULL';

        $cmd = Yii::app()->db->createCommand($query);
        $cmd->bindValue(':user_role_id', $userRoleId);

        return $cmd;
    }

    /**
     * Updates a particular model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param int $id the ID of the model to be updated
     */
    public function actionUpdate($id)
    {
        $model = $this->loadModel($id);

        // Uncomment the following line if AJAX validation is needed
        // $this->performAjaxValidation($model);

        if (isset($_POST['UserRoleRights'])) {
            $model->attributes = $_POST['UserRoleRights'];
            if ($model->save()) {
                $this->redirect(['view', 'id' => $model->id]);
            }
        }

        $this->render('//stammdaten/rechteManagement/rechteRollen/update', [
            'model' => $model,
        ]);
    }

    /**
     * Returns the data model based on the primary key given in the GET variable.
     * If the data model is not found, an HTTP exception will be raised.
     *
     * @param       int the ID of the model to be loaded
     * @param mixed $id
     */
    public function loadModel($id)
    {
        $model = UserRoleRights::model()->findByPk($id);
        if ($model === null) {
            throw new CHttpException(404, 'The requested page does not exist.');
        }

        return $model;
    }

    /**
     * Deletes a particular model.
     * If deletion is successful, the browser will be redirected to the 'admin' page.
     *
     * @param int $id the ID of the model to be deleted
     */
    public function actionDelete($id)
    {
        if (Yii::app()->request->isPostRequest) {
            // we only allow deletion via POST request
            $this->loadModel($id)->delete();

            // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
            if (!isset($_GET['ajax'])) {
                $this->redirect($_POST['returnUrl'] ?? ['admin']);
            }
        } else {
            throw new CHttpException(400, 'Invalid request. Please do not repeat this request again.');
        }
    }

    /**
     * Manages all models.
     */
    public function actionAdmin()
    {
        $model = new UserRoleRights('search');
        $model->unsetAttributes();  // clear any default values
        if (isset($_GET['UserRoleRights'])) {
            $model->attributes = $_GET['UserRoleRights'];
        }

        $this->render('//stammdaten/rechteManagement/rechteRollen/admin', [
            'model' => $model,
        ]);
    }

    public function actionGetActiveRights()
    {
        $userRoleID        = (int) filter_var($_POST['userRoleID'], FILTER_SANITIZE_NUMBER_INT);
        $agencyID          = (int) filter_var($_POST['agencyID'], FILTER_SANITIZE_NUMBER_INT);
        $model             = new UserRoleRights();

        $activeRights      = $model->findAllByAttributes(['user_role_id' => $userRoleID, 'agency_id' => null]);
        $flagDefaultValues = false;
        if ($agencyID > 0) {
            $activeRights = $model->findAllByAttributes(['user_role_id' => $userRoleID, 'agency_id' => $agencyID]);
            if (empty($activeRights)) {
                $activeRights      = $model->findAllByAttributes(['user_role_id' => $userRoleID, 'agency_id' => null]);
                $flagDefaultValues = true;
            }
        }

        $res = '';
        foreach ($activeRights as $key => $value) {
            $res .= $value->right_id . ',';
        }
        $res = substr($res, 0, strlen($res) - 1);

        echo json_encode(['flagDefaultValues' => $flagDefaultValues, 'res' => $res]);

        Yii::app()->end();
    }

    public function actionAddRightForUsers($user_role_id, $right_id)
    {
        set_time_limit(0);
        $userRoleRight               = new UserRoleRights();
        $userRoleRight->user_role_id = $user_role_id;
        $userRoleRight->right_id     = $right_id;
        $userRoleRight->save();
        $query   = 'select id from user where user_role = ' . $user_role_id;
        $result1 = Yii::app()->db->createCommand($query)->queryAll();
        $counter = 0;
        foreach ($result1 as $r1) {
            $userRight = UserRights::model()->findByAttributes(['user_id' => $r1['id'], 'right' => $right_id]);
            if (empty($userRight)) {
                $userRight          = new UserRights();
                $userRight->user_id = $r1['id'];
                $userRight->right   = $right_id;
                $userRight->save();
                $counter++;
            }
        }
        echo '<pre>';
        print_r($counter . ' Rechte gesetzt');
        echo '</pre>';
    }

    /**
     * Performs the AJAX validation.
     *
     * @param       CModel the model to be validated
     * @param mixed $model
     */
    protected function performAjaxValidation($model)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'user-role-rights-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }
    }
}
