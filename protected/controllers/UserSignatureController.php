<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

Yii::import('stammdaten.modules.user.controllers.UserFilesController');

/**
 * Description of UserSignatureController
 *
 * <AUTHOR>
 */
class UserSignatureController extends GlobalRightController
{
    public $parentid = 'Profile';

    public function actionCreate()
    {
        $newRecord = false;
        $signature = new UserSignature();
        $params    = Yii::app()->request->getPost('UserSignature');
        //Datei ermitteln
        if (!empty($params['user_files_id'])) {
            $file = UserFiles::model()->findByPk($params['user_files_id']);
        } else {
            $newRecord                                  = true;
            $filesdata                                  = [];
            $filesdata['UserFiles']['document_type_id'] = UserSignature::model()->getDocumentTypeId();
            $filesdata['UserFiles']['user_id']          = Yii::app()->user->getId();
            $userFilesController                        = new UserFilesController('userfiles');
            $file                                       = $userFilesController->create($filesdata);
        }
        while (true) {
            if (empty($params['accept'])) {
                Yii::app()->user->setFlash('error', 'Sie müssen Ihre Zustimmung per Auswahl der Checkbox bestätigen.');
                break;
            }
            if (empty($file)) {
                Yii::app()->user->setFlash('error', 'Es konnte keine Datei gefunden werden. Datei zu groß? (max. 4 MB)');
                break;
            }
            if (get_class($file) != 'UserFiles' || preg_match('/jpg|jpeg|png$/', $file->name) === 0 || !$file->isImage()) {
                Yii::app()->user->setFlash('error', 'Es sind nur folgende Datei-Formate gestattet: jpeg, jpg, png');
                if ($newRecord) {
                    $file->delete();
                }
                break;
            }
            $file->resizeImage(400, 0, 90);
            $signature->user_id       = Yii::app()->user->getId();
            $signature->user_files_id = $file->id;
            if ($signature->save()) {
                Yii::app()->user->setFlash('success', 'Ihre Unterschrift wurde erfolgreich im System hinterlegt.');
            } else {
                Yii::app()->user->setFlash('error', 'Ihre Unterschrift konnte nicht im System hinterlegt werden.');
            }
            break;
        }

        $this->redirect(['profile/update', 'active_tab' => ProfilTabs::UNTERSCHRIFT]);
    }

    public function actionDelete()
    {
        $delete_file = Yii::app()->request->getPost('delete_file', true);
        $signature   = UserSignature::model()->getLoggedInUserSignature(false);
        if (!empty($signature)) {
            $file = $signature->getUserFile();
            if ($signature->delete()) {
                if (!empty($file) && $delete_file) {
                    $file->delete();
                }
                Yii::app()->user->setFlash('success', 'Ihre Unterschrift wurde aus dem System entfernt.');
            } else {
                Yii::app()->user->setFlash('error', 'Ihre Unterschrift konnte nicht entfernt werden.');
            }
        }
        $this->redirect(['profile/update', 'active_tab' => ProfilTabs::UNTERSCHRIFT]);
    }
}
