<?php

use Business\Vorgaenge\Korrespondenz;

class UserWritingController extends AllowAllController
{
    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout           = '//layouts/column2';
    public $excludedActions  = [
        'ShowPreviewPDF',
    ];
    private $userwritingname = 'UserWriting';
    private $controllername  = 'userWriting';

    /**
     *  Diese Methode wird aufgerufen wenn ein neues UserWriting-Model erstellt werden soll.
     *  Die Methode öffnet das UserWriting-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate($print = false, $procedureid = null, $userwritingdata = '')
    {
        if (UtilHelper::is_serialized($userwritingdata)) {
            $userwritingdata = unserialize($userwritingdata);
        }

        $this->redirect(Korrespondenz::brief()->ausUserMailData(is_array($userwritingdata) ? $userwritingdata : [])->getUrl());
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model UserWriting handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
               isset($data['UserWriting']);
        //
    }

    /**
     * Diese Methode gibt den Namen des UserWriting-Models zurück
     *
     * @return string
     *              Name des UserWriting-Models
     */
    /**
     *  Diese Methode wird aufgerufen wenn ein UserWriting -Model aktualisiert werden soll.
     *  Die Methode öffnet das UserWriting -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden UserWriting -Models
     */
    public function actionUpdate($id, $open = false, $new_created = false)
    {
        $userwriting = $this->loadModel($id);
        if ($userwriting != null) {
            $userwriting->new_created = $new_created;
            $data                     = $this->getUpdateFormularData($id, $open, $new_created);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if (($userwriting = $this->update($data)) != null) {
                        $this->redirect(['userWriting/update/', 'id' => $userwriting->id]);

                        return $userwriting;
                    }
                }
            }
        } else {
            Yii::app()->user->setFlash('error', 'Der Vorgang ist nicht mehr vorhanden');
            $this->redirect('/procedure/admin');
        }

        return null;
    }

    /**
     * Lädt das UserWriting-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des UserWriting-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $userwriting = UserWriting::model()->findByPk($id);

        return $userwriting;
    }

    /**
     *
     * Öffnet das UserWriting-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden UserWriting-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id, $open, $new_created = false)
    {
        $userwriting              = $this->loadModel($id);
        $userwriting->new_created = $new_created;

        if (!$this->validData($_POST)) {
            $this->render('//home/<USER>/vorgaenge/brief/update', ['userwriting' => $userwriting, 'open' => $open]);
        }

        $formulardata                      = $_POST;
        $formulardata['UserWriting']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein UserWriting-Exemplar
     *
     * @param type $data
     *          Die UserWriting-Daten als Array
     *
     * @return $userwriting
     *          Das aktualisierte UserWriting-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $userwriting                    = $this->loadModel($data['UserWriting']['id']);
            $userwriting->comment           = $_POST['UserWriting']['comment'];
            $userwriting->last_edit_date    = ViewHelper::getDate(true);
            $userwriting->last_edit_user_id = Yii::app()->user->getID();
            $userwriting->attributes        = $data['UserWriting'];
            if ($userwriting->save()) {
                return $userwriting;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all userwritings.
     */
    public function actionAdmin()
    {
        $userwriting = new UserWriting('search');
        $userwriting->unsetAttributes();  // clear any default values
        if (isset($_GET['UserWriting'])) {
            $userwriting->attributes = $_GET['UserWriting'];
        }

        $this->render('//home/<USER>/vorgaenge/brief/admin', [
            'userwriting' => $userwriting,
        ]);
    }

    /**
     * Löscht das UserWriting-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden UserWriting-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        //        if (!isset($_GET['ajax']))
        $this->redirect($_POST['returnUrl'] ?? ['procedure/admin/?activetab = Briefe']);
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $userwriting = $this->loadModel($id);

        return $userwriting->delete();
    }

    /**
     * Setzt den Status eines Vorganges auf erledigt
     *
     * @param type $id
     *          ID der UserWriting, dess Status auf erledigt gesetzt werden soll
     */
    public function actionDone($id)
    {
        $userwriting = UserWriting::model()->findByPk($id);
        if (null !== $userwriting) {
            $userwriting->writing_status_id = WritingStatus::$done;
            $userwriting->save();
        }
        if (!Yii::app()->request->isAjaxRequest) {
            $this->redirect(['update', 'id' => $id]);
        }
    }

    public function actionOpenPDF($id)
    {
        $userwriting = UserWriting::model()->findByPk($id);
        if (null !== $userwriting) {
            $userwriting->generatePDF();
        }
    }

    /**
     * Öffnet ein PDF eines UserWritings als Preview
     */
    public function actionShowPreviewPDF()
    {
        $userWriting             = new UserWriting();
        $userWriting->attributes = $_POST['UserWriting'];
        $address                 = new Address();
        $address->attributes     = $_POST['UserWriting']['Address'];
        $userWriting->setReceiverAddress($address);

        $tags = new Tags();
        $tags->forUserId((int) $userWriting->user_id);
        $tags->forCompanyId($userWriting->insurance_company_id);
        $tags->forProductComboId($userWriting->product_combo_id);
        if (!empty($userWriting->client_id)) {
            $tags->forClientId((int) $userWriting->client_id);
        }
        $tags->forContractId($userWriting->contract_id);
        $tags->forWriting();
        $tags->allowCustomSalutation();

        $userWriting->content = $tags->replaceAvailable($userWriting->content);
        $userWriting->generatePDF();
    }

    /**
     * Performs the AJAX validation.
     *
     * @param UserWriting $userwriting the userwriting to be validated
     */
    protected function performAjaxValidation($userwriting)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'user-writing-form') {
            echo CActiveForm::validate($userwriting);
            Yii::app()->end();
        }
    }
}
