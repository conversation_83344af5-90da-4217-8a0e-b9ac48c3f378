<?php

use Courtageerfassung\Abrechnungstool\Verbunddaten\VerbunddatenSplitterFactory;
use Courtageerfassung\Service\File as FileService;
use Courtageerfassung\Service\Verbunddaten;

class VerbunddatenController extends GlobalRightController
{
    public function actionManuellerImport()
    {
        $companyId       = (int) app()->request->getParam('company');
        $successesCount  = 0;
        $splitterFactory = VerbunddatenSplitterFactory::new();

        $requestFiles = FileService::getRequestFiles();

        foreach ($requestFiles as $file) {
            if ($splitterFactory->hasRoutine($companyId)) {
                $splitter       = $splitterFactory->getRoutine($companyId);
                $successesCount += (int) $splitter->split($file['name'] ?? '', (string) file_get_contents($file['tmp_name']));
            }
        }

        if (count($requestFiles)) {
            Verbunddaten::zuordnen();
        }

        if (count($requestFiles) > 0) {
            $message = $successesCount === count($requestFiles) ? 'Alle Dateien wurden erfolgreich hochgeladen' :
                $successesCount . ' von ' . count($requestFiles) . ' Dateien wurden erfolgreich hochgeladen';

            $message .= ' und alle gefunden Vermittler wurden entsprechend den Vermittlernummern zugeordnet.';

            $hint = 'Hinweis: Der Upload der Daten ins das Abrechnungstool erfolgt erst mit dem nächtlichen Job.';
        }

        $this->render('//verbunddaten/manuellerImport', [
            'message' => $message ?? null,
            'hint'    => $hint ?? null,
        ]);
    }
}
