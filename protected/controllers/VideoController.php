<?php

use Components\Videos\VideosFactory;

/**
 * Class VideoController
 */
class VideoController extends GlobalRightController
{
    /**
     * @param int $video
     */
    public function actionIndex($video = null)
    {
        $videosFactory = VideosFactory::create();
        $videoId       = $video;
        $videosData    = [];
        $videos        = $videosFactory->getAll();
        $activeVideo   = null;
        foreach (array_values($videos) as $index => $video) {
            if ($video->getId() === (int) $videoId || ($index === 0 && $videoId === null)) {
                $activeVideo = $video;
                VideoLog::logVideo($video);
            }
            if (empty($videosData[$video->getGroup()])) {
                $videosData[$video->getGroup()]['title'] = CHtml::link($video->getGroup(), ['index', 'video' => $video->getId()]);
            }
            $title                                                     = '<span title="' . $video->getName() . ' (Video ' . ($index + 1) . ')">' . $video->getName() . '</span>';
            $videosData[$video->getGroup()]['videos'][$video->getId()] = CHtml::link($title, ['index', 'video' => $video->getId()]);
        }

        if ($activeVideo === null) {
            $vs          = array_values($videos);
            $activeVideo = array_pop($vs);
            Yii::app()->user->setFlash('error', 'Das angegebene Video existiert nicht.');
        }

        $this->render('index', ['videosData' => $videosData, 'activeVideo' => $activeVideo]);
    }
}
