<?php

class WritingProcedureController extends SuperAdminController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout                = '//layouts/column2';
    private $writingprocedurename = 'WritingProcedure';
    private $controllername       = 'writingProcedure';

    /**
     * Displays a particular writingprocedure.
     *
     * @param integer $id the ID of the writingprocedure to be displayed
     */
    public function actionView($id)
    {
        $this->render('view', [
            'writingprocedure' => $this->loadModel($id),
        ]);
    }

    /**
     * Lädt das WritingProcedure-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return $address
     *          Das Exemplar des WritingProcedure-Models mit der übergebenen ID falls dieses existiert, sonst null
     *
     */
    public function loadModel($id)
    {
        $writingprocedure = WritingProcedure::model()->findByPk($id);

        return $writingprocedure;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues WritingProcedure-Model erstellt werden soll.
     *  Die Methode öffnet das WritingProcedure-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate()
    {
        $data = $this->getCreateFormularData();
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($writingprocedure = $this->create($data) != null) {
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $writingprocedure;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das WritingProcedure -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des WritingProcedure -Models
     */
    private function getCreateFormularData()
    {
        $writingprocedure = new WritingProcedure();

        if (!$this->validData($_POST)) {
            $this->renderPartial('//stammdaten/verwaltung/briefVorgangTyp/_form', ['writingprocedure' => $writingprocedure, 'form' => new CActiveForm(), 'prefix' => 'Create']);
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model WritingProcedure handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
            isset($data['WritingProcedure'])
        ;
//
    }

    /**
     * Diese Methode erstellt ein neues WritingProcedure -Model
     *
     * @param type $data
     *          Die WritingProcedure -Daten als Array
     *
     * @return $writingprocedure
     *          Das erstellte WritingProcedure -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $writingprocedure             = new WritingProcedure();
        $writingprocedure->attributes = $data['WritingProcedure'];

        if ($writingprocedure->save()) {
            return $writingprocedure;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein WritingProcedure -Model aktualisiert werden soll.
     *  Die Methode öffnet das WritingProcedure -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden WritingProcedure -Models
     */
    public function actionUpdate($id)
    {
        $writingprocedure = $this->loadModel($id);

        if ($writingprocedure != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($writingprocedure = $this->update($data) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $writingprocedure;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     *
     * Öffnet das WritingProcedure-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden WritingProcedure-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $writingprocedure = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->renderPartial('//stammdaten/verwaltung/briefVorgangTyp/_form', ['writingprocedure' => $writingprocedure, 'form' => new CActiveForm(), 'prefix' => 'Update']);
        }

        $formulardata                           = $_POST;
        $formulardata['WritingProcedure']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein WritingProcedure-Exemplar
     *
     * @param type $data
     *          Die WritingProcedure-Daten als Array
     *
     * @return $writingprocedure
     *          Das aktualisierte WritingProcedure-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $writingprocedure             = $this->loadModel($data['WritingProcedure']['id']);
            $writingprocedure->attributes = $data['WritingProcedure'];
            if ($writingprocedure->save()) {
                return $writingprocedure;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all writingprocedures.
     */
    public function actionAdmin()
    {
        $writingprocedure = new WritingProcedure('search');
        $writingprocedure->unsetAttributes();  // clear any default values
        if (isset($_GET['WritingProcedure'])) {
            $writingprocedure->attributes = $_GET['WritingProcedure'];
        }

        $this->render('//stammdaten/verwaltung/briefVorgangTyp/admin', [
            'writingprocedure' => $writingprocedure,
        ]);
    }

    /**
     * Löscht das WritingProcedure-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden WritingProcedure-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->loadModel($id)->delete();

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        return $this->loadModel($id)->delete();
    }

    /**
     * Lists all writingprocedures.
     */
    public function actionIndex()
    {
        $dataProvider = new CActiveDataProvider('WritingProcedure');
        $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Performs the AJAX validation.
     *
     * @param WritingProcedure $writingprocedure the writingprocedure to be validated
     */
    protected function performAjaxValidation($writingprocedure)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'writing-procedure-form') {
            echo CActiveForm::validate($writingprocedure);
            Yii::app()->end();
        }
    }
}
