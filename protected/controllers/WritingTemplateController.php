<?php

class WritingTemplateController extends AllowAllController
{
    public $menuGroup = MenuGroup::STAMMDATEN;

    /**
     * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $layout          = '//layouts/column2';
    private $controllername = 'writingTemplate';

    public function accessRules()
    {
        $rules = [];
        if (!Yii::app()->user->isSuperAdmin()) {
            $rules[] = [
                'deny', // deny all users
                'actions' => ['admin'],
                'users'   => ['*'],
            ];
        }

        $rulesNew = parent::accessRules();
        $rules[]  = $rulesNew[0];

        return $rules;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein neues WritingTemplate-Model erstellt werden soll.
     *  Die Methode öffnet das WritingTemplate-Create-Formular, ließt die Eingaben aus,
     *  lässt ein neues Exemplar erstellen und aktualisiert die View
     *
     */
    public function actionCreate($modal = false)
    {
        $data = $this->getCreateFormularData($modal);
        if (isset($data['create'])) {
            if ($this->validData($data)) {
                if ($writingtemplate = $this->create($data) != null) {
                    //echo "Das Erstellen war erfolgreich";
                    //ToDo -> View aktualiseren
                    $this->redirect('admin');

                    return $writingtemplate;
                }

                return null;
            }
        }
    }

    /**
     *  Öffnet das WritingTemplate -Create-Formular, ließt das $_POST-Array aus und gibt die Werte
     *  als $data-Array zurück
     *
     * @return type
     *          $Data-Array des WritingTemplate -Models
     */
    private function getCreateFormularData($modal)
    {
        $formulardata    = [];
        $writingtemplate = new WritingTemplate();

        if (!$this->validData($_POST)) {
            if (!$modal) {
                $this->render('//stammdaten/templateVerwaltung/briefVorlagen/create', ['writingtemplate' => $writingtemplate]);
            } else {
                $this->renderPartial('//stammdaten/templateVerwaltung/briefVorlagen/_formFieldsModal',
                                     ['writingtemplate' => $writingtemplate, 'form' => new CActiveForm(), 'prefix' => 'Create']);
            }
        } else {
            $formulardata = $_POST;

            return $formulardata;
        }
    }

    /**
     * Diese Methode gibt zurück ob es sich um ein gültiges Data-Array für das Model WritingTemplate handelt
     *
     * @param type $data
     *              $Data-Array
     *
     * @return type
     *              true falls es sich im ein Data-Array handelt, sonst false
     */
    private function validData($data)
    {
        //ToDo
        return isset($data) &&
               isset($data['WritingTemplate']);
        //
    }

    /**
     * Diese Methode erstellt ein neues WritingTemplate -Model
     *
     * @param type $data
     *          Die WritingTemplate -Daten als Array
     *
     * @return $writingtemplate
     *          Das erstellte WritingTemplate -Exemplar
     */
    public function create($data)
    {
        if (!$this->validData($data)) {
            return null;
        }

        $data['WritingTemplate']['last_edit_user_id'] = Yii::app()->user->getID();
        $data['WritingTemplate']['last_edit_date']    = ViewHelper::getDate(true);

        if (empty($data['WritingTemplate']['product_combo_id'])) {
            $data['WritingTemplate']['product_combo_id'] = null;
        }

        $writingtemplate = WritingTemplate::model()->findByAttributes([
                                                                          'user_id'              => Yii::app()->user->getId(),
                                                                          'writing_procedure_id' => $data['WritingTemplate']['writing_procedure_id'],
                                                                          'name'                 => $data['WritingTemplate']['name']
                                                                      ]);
        if (empty($writingtemplate)) {
            $writingtemplate = new MailUserTemplate();
        }

        $writingtemplate             = new WritingTemplate('assign');
        $writingtemplate->attributes = $data['WritingTemplate'];

        $writingtemplate->content = $data['WritingTemplate']['content'];

        if (!Yii::app()->user->isAdmin() && empty($writingtemplate->user_id)) {
            return null;
        }
        if ($writingtemplate->save()) {
            return $writingtemplate;
        }

        return null;
    }

    /**
     *  Diese Methode wird aufgerufen wenn ein WritingTemplate -Model aktualisiert werden soll.
     *  Die Methode öffnet das WritingTemplate -Update-Formular, ließt die Eingaben aus,
     *  lässt das Exemplar Aktualisieren und aktualisiert die View
     *
     * @param $id
     *         Die ID der zu aktualisierenden WritingTemplate -Models
     */
    public function actionUpdate($id)
    {
        $writingtemplate = $this->loadModel($id);
        if ($writingtemplate != null) {
            $data = $this->getUpdateFormularData($id);

            if (isset($data['update'])) {
                if ($this->validData($data)) {
                    if ($writingtemplate = $this->update($data) != null) {
                        $this->redirect([$this->controllername . '/admin']);

                        return $writingtemplate;
                    }
                }
            }
        } else {
            $this->actionAdmin();
        }

        return null;
    }

    /**
     * Lädt das WritingTemplate-Exemplar mit der übergebenen ID
     *
     * @param type $id
     *          ID des Exemplares, dass geladen werden soll
     *
     * @return WritingTemplate
     *
     */
    public function loadModel($id)
    {
        $writingtemplate = WritingTemplate::model()->findByPk($id);

        return $writingtemplate;
    }

    /**
     *
     * Öffnet das WritingTemplate-Update-Formular, ließt das $_POST-Array aus und gibt die Werte
     * als Data-Array zurück
     *
     * @param type $id
     *          die ID des zu aktualisierenden WritingTemplate-Exemplares
     *
     * @return $data
     *          Das aktualierte Data-Array
     *
     */
    private function getUpdateFormularData($id)
    {
        $writingtemplate = $this->loadModel($id);

        if (!$this->validData($_POST)) {
            $this->render('//stammdaten/templateVerwaltung/briefVorlagen/update', ['writingtemplate' => $writingtemplate]);
        }

        $formulardata                          = $_POST;
        $formulardata['WritingTemplate']['id'] = $id;

        return $formulardata;
    }

    /**
     * Diese Methode aktualisiert ein WritingTemplate-Exemplar
     *
     * @param type $data
     *          Die WritingTemplate-Daten als Array
     *
     * @return $writingtemplate
     *          Das aktualisierte WritingTemplate-Exemplar falls das Model aktualisiert werden konnte, sonst null
     */
    public function update($data)
    {
        if ($this->validData($data)) {
            $data['WritingTemplate']['last_edit_user_id'] = Yii::app()->user->getID();
            $data['WritingTemplate']['last_edit_date']    = ViewHelper::getDate(true);

            if (!isset($data['WritingTemplate']['product_combo_id']) || $data['WritingTemplate']['product_combo_id'] == '') {
                $data['WritingTemplate']['product_combo_id'] = null;
            }

            $writingtemplate             = $this->loadModel($data['WritingTemplate']['id']);
            $writingtemplate->attributes = $data['WritingTemplate'];
            if ($writingtemplate->save()) {
                return $writingtemplate;
            }

            return null;
        }

        return null;
    }

    /**
     * Manages all writingtemplates.
     */
    public function actionAdmin()
    {
        $writingtemplate = new WritingTemplate('search');
        $writingtemplate->unsetAttributes();  // clear any default values
        if (isset($_GET['WritingTemplate'])) {
            $writingtemplate->attributes = $_GET['WritingTemplate'];
        }

        $this->render('//stammdaten/templateVerwaltung/briefVorlagen/admin', [
            'writingtemplate' => $writingtemplate,
        ]);
    }

    /**
     * Löscht das WritingTemplate-Model mit der übergebenen ID, falls dieses existiert
     *
     * @param type $id
     *          $ID des zu löschenden WritingTemplate-Models
     *
     * @return type true || false
     *          true falls das Löschen erfolgreich war, sonst false
     */
    public function actionDelete($id)
    {
        $this->delete($id);

        // if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
        if (!isset($_GET['ajax'])) {
            $this->redirect($_POST['returnUrl'] ?? ['admin']);
        }
    }

    /**
     *
     * @param type $id
     *
     * @return true/false
     *          true falls das löschen erfolgreich war, sonst false
     */
    public function delete($id)
    {
        $model = $this->loadModel($id);
        if (Yii::app()->user->isAdmin() || $model->isMine(Yii::app()->user->id)) {
            $model->delete();
        }
    }

    /**
     * Performs the AJAX validation.
     *
     * @param WritingTemplate $writingtemplate the writingtemplate to be validated
     */
    protected function performAjaxValidation($writingtemplate)
    {
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'writing-demv-template-form') {
            echo CActiveForm::validate($writingtemplate);
            Yii::app()->end();
        }
    }
}
