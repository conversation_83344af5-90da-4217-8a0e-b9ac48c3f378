<?php

/**
 * Class m180202_135648_fulltext_indices
 */
class m180202_135648_fulltext_indices_client extends CDbMigration
{
    private const TABLE = 'client';

    public function up()
    {
        $stmt = sprintf(
            'ALTER TABLE %s ADD FULLTEXT INDEX %1$s_fulltext (%s, %s, %s)',
            self::TABLE,
            'firstname',
            'lastname',
            'internal_clientnumber'
        );
        $this->execute($stmt);
    }

    public function down()
    {
        $this->execute(
            sprintf('ALTER TABLE listings DROP INDEX %s_fulltext;', self::TABLE)
        );

        return true;
    }
}
