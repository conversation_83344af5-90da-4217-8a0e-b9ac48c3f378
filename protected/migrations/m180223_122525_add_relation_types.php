<?php

use Demv\Werte\Person\Beziehung\<PERSON><PERSON><PERSON>lich\ExArbeitgeber;
use Demv\Werte\Person\Beziehung\Beruflich\ExArbeitnehmer;

class m180223_122525_add_relation_types extends CDbMigration
{
    const TABLE = 'client_relation_type';

    private const ARBEITNEHMER = ExArbeitnehmer::ID;
    private const ARBEITGEBER  = ExArbeitgeber::ID;

    public function up()
    {
        $this->insert(self::TABLE, [
            'id'          => self::ARBEITGEBER,
            'name'        => 'Ex-Arbeitgeber',
            'category_id' => ClientRelationType::CATEGORY_WORK
        ]);

        $this->insert(self::TABLE, [
            'id'                  => self::ARBEITNEHMER,
            'name'                => 'Ex-Arbeitnehmer',
            'related_relation_id' => self::ARBEITGEBER,
            'category_id'         => ClientRelationType::CATEGORY_WORK
        ]);

        $this->update(self::TABLE, ['related_relation_id' => self::ARBEITNEHMER], 'id = ' . self::ARBEITGEBER);
    }

    public function down()
    {
        $this->delete(self::TABLE, 'id = ' . self::ARBEITNEHMER);
        $this->delete(self::TABLE, 'id = ' . self::ARBEITGEBER);

        return true;
    }
}
