<?php

class m180420_095748_user_doubletten extends CDbMigration
{
    const TABLE  = 'user';
    const COLUMN = 'duplicate_parent_id';

    const FK_NAME = 'duplicate_user_fk';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned');

        $this->addForeignKey(self::FK_NAME, self::TABLE, self::COLUMN, self::TABLE, 'id',
                             'SET NULL', 'SET NULL');
    }

    public function down()
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
