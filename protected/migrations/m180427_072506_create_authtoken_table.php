<?php

/**
 * Class m180427_072506_create_authtoken_table
 */
class m180427_072506_create_authtoken_table extends CreateTableMigration
{
    public $tableName = 'authtoken';

    /**
     * @return array
     */
    public function getColumns(): array
    {
        return [
            'id'              => 'pk',
            'name'            => 'string not null',
            'token'           => 'string not null',
            'user_id'         => 'integer unsigned not null',
            'disabled'        => 'boolean default 0',
            'create_datetime' => 'datetime'
        ];
    }
}
