<?php

class m180503_071027_alter_authtoken_column extends CDbMigration
{
    private const TABLE  = 'authtoken';
    private const COLUMN = 'token';

    public function up()
    {
        $this->truncateTable(self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);
        $this->addColumn(self::TABLE, self::COLUMN, 'VARCHAR(1024) not null');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);
        $this->addColumn(self::TABLE, self::COLUMN, 'string not null');

        return true;
    }
}
