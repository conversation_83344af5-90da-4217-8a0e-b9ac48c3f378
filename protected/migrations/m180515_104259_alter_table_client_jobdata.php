<?php

class m180515_104259_alter_table_client_jobdata extends CDbMigration
{
    const TABLE = 'client_jobdata';

    public function up()
    {
        $this->addColumn(self::TABLE, 'rentenalter', 'int');
        $this->addColumn(self::TABLE, 'rentenbedarf', 'string');
        $this->addColumn(self::TABLE, 'inflation', 'string');
        $this->addColumn(self::TABLE, 'gesetzliche_rente', 'string');

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, 'rentenalter');
        $this->dropColumn(self::TABLE, 'rentenbedarf');
        $this->dropColumn(self::TABLE, 'inflation');
        $this->dropColumn(self::TABLE, 'gesetzliche_rente');

        return true;
    }
}
