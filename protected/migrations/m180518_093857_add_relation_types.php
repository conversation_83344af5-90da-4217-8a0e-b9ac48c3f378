<?php

use Demv\Werte\Person\Beziehung\<PERSON><PERSON><PERSON>lich\Geschaeftspartner;
use Demv\Werte\Person\Beziehung\Beruflich\Hauptgeschaeftsstelle;
use Demv\Werte\Person\Beziehung\Beruflich\Zweigstelle;

class m180518_093857_add_relation_types extends CDbMigration
{
    const TABLE = 'client_relation_type';

    private const HAUPTGESCHAEFTSSTELLE = Hauptgeschaeftsstelle::ID;
    private const ZWEIGSTELLE           = Zweigstelle::ID;
    private const GESCHAEFTSPARTNER     = Geschaeftspartner::ID;

    public function up()
    {
        $this->insert(self::TABLE, [
            'id'          => self::HAUPTGESCHAEFTSSTELLE,
            'name'        => (new Hauptgeschaeftsstelle())->getName(),
            'category_id' => ClientRelationType::CATEGORY_WORK
        ]);

        $this->insert(self::TABLE, [
            'id'                  => self::ZWEIGSTELLE,
            'name'                => (new Zweigstelle())->getName(),
            'related_relation_id' => self::HAUPTGESCHAEFTSSTELLE,
            'category_id'         => ClientRelationType::CATEGORY_WORK
        ]);

        $this->insert(self::TABLE, [
            'id'                  => self::GESCHAEFTSPARTNER,
            'name'                => (new Geschaeftspartner())->getName(),
            'related_relation_id' => self::GESCHAEFTSPARTNER,
            'category_id'         => ClientRelationType::CATEGORY_WORK
        ]);

        $this->update(self::TABLE, ['related_relation_id' => self::ZWEIGSTELLE], 'id = ' . self::HAUPTGESCHAEFTSSTELLE);
    }

    public function down()
    {
        $this->delete(self::TABLE, 'id = ' . self::HAUPTGESCHAEFTSSTELLE);
        $this->delete(self::TABLE, 'id = ' . self::ZWEIGSTELLE);
        $this->delete(self::TABLE, 'id = ' . self::GESCHAEFTSPARTNER);

        return true;
    }
}
