<?php

class m180525_085058_remove_menu_item extends CDbMigration
{
    const TABLE = 'system_menu_items';
    const URL   = '/sach-gewerbe/gewerbegeschaeftohnehaftung';

    public function up()
    {
        $this->delete(self::TABLE, 'url = "' . self::URL . '"');
    }

    public function down()
    {
        $sql = "INSERT INTO system_menu_items (menu_id, title, active, regex, icon, url, target, exclude_testuser, admin_only, right_id, sort, exclude_insurancecompanies, rights, roles, excluded_roles) VALUES (34, 'Gewerbegeschäft ohne Haftung', 1, '', 'fa fa-thumbs-up', '/sach-gewerbe/gewerbegeschaeftohnehaftung', '_top', 0, 0, null, 15, null, null, '1,2,3,4,5,6,7,8,9', null);";
        $this->execute($sql);

        return true;
    }
}
