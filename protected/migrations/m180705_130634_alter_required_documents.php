<?php

class m180705_130634_alter_required_documents extends CDbMigration
{
    private const TABLE = 'insurance_company_required_document_type';

    public function up()
    {
        $this->addColumn(self::TABLE, 'on_request', 'boolean DEFAULT 0');
        $this->addColumn(self::TABLE, 'on_rearrangement', 'boolean DEFAULT 0');
        $this->update(self::TABLE, ['on_request' => 1], 'id > 0');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, 'on_request');
        $this->dropColumn(self::TABLE, 'on_rearrangement');

        return true;
    }
}
