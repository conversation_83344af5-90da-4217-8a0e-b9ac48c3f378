<?php

class m180803_132937_profile_kundenlogin_dokumenttypen extends CDbMigration
{
    private const TABLE      = 'user_profile_clientlogin_documenttypes';
    const         FK_NAME    = 'user_profile_documenttypes_user';
    const         COLUMN     = 'user_id';
    const         USER_TABLE = 'user';

    public function up()
    {
        $this->createTable(self::TABLE, [
            'id'                 => 'pk',
            'user_id'            => 'int unsigned NOT NULL unique',
            'documenttypes'      => 'string',
            'last_edit_datetime' => 'datetime'
        ]);

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            self::COLUMN,
            self::USER_TABLE,
            'id'
        );
    }

    public function down()
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE);
        $this->dropTable(self::TABLE);

        return true;
    }
}
