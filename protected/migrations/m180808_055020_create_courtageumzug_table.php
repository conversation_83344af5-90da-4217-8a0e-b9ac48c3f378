<?php

class m180808_055020_create_courtageumzug_table extends CreateTableMigration
{
    public $tableName = 'courtageumzug';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'user_id'              => 'integer unsigned not null',
            'created_by_user_id'   => 'integer unsigned',
            'requested_company_id' => 'integer unsigned not null',
            'actual_company_id'    => 'integer unsigned',
            'brokerids'            => 'tinytext',
            'demv_conditions'      => 'bool',
            'remark'               => 'text',
            'create_datetime'      => 'datetime',
            'cancel_datetime'      => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
        $this->addOwnFk('create_user', 'created_by_user_id', 'user', 'SET NULL', 'SET NULL');
        $this->addOwnFk('requested_company', 'requested_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
        $this->addOwnFk('actual_company', 'actual_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
