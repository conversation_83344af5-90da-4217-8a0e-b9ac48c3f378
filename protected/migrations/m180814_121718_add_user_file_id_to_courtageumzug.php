<?php

class m180814_121718_add_user_file_id_to_courtageumzug extends CDbMigration
{
    private const TABLE  = 'courtageumzug';
    private const COLUMN = 'user_file_id';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int');
        $this->addForeignKey('fk_courtageumzug_user_file',
                             self::TABLE,
                             self::COLUMN,
                             'user_files',
                             'id',
                             'SET NULL',
                             'SET NULL');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
