<?php

class m180816_131122_update_contracts_free_text_field_column extends CDbMigration
{
    private const TABLE  = 'contracts';
    private const COLUMN = 'free_text_field';

    public function up()
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(2048)');
    }

    public function down()
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(1024)');

        return true;
    }
}
