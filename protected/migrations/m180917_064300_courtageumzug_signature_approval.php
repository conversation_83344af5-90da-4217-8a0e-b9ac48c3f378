<?php

class m180917_064300_courtageumzug_signature_approval extends CreateTableMigration
{
    public $tableName = 'courtageumzug_signature_approval';

    /**
     * @return array
     */
    public function getColumns(): array
    {
        return [
            'id'         => 'pk',
            'user_id'    => 'int unsigned not null',
            'created_at' => 'datetime',
            'created_by' => 'int unsigned not null',
            'approved'   => 'boolean DEFAULT "0"'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
        $this->addOwnFk('approved_by_user', 'created_by', 'user', 'CASCADE', 'CASCADE');
    }
}
