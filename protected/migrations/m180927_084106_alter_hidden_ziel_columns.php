<?php

class m180927_084106_alter_hidden_ziel_columns extends CDbMigration
{
    private const TABLE   = 'hidden_ziel';
    private const COLUMNS = [
        'bedarf_id',
        'ziel_id',
    ];

    public function up()
    {
        foreach (self::COLUMNS as $column) {
            $this->alterColumn(self::TABLE, $column, 'VARCHAR(128) not null');
        }
    }

    public function down()
    {
        foreach (self::COLUMNS as $column) {
            $this->alterColumn(self::TABLE, $column, 'VARCHAR(30) not null');
        }

        return true;
    }
}
