<?php

use Kundenakte\Bedarfsanalyse\Models\BedarfHandlungstext;

class m181026_084708_handlungstext extends CDbMigration
{
    private $table   = 'bedarf_handlungstext';
    private $title   = '<PERSON>ar<PERSON> besprochen, <PERSON>ust<PERSON> nicht gewünscht';
    private $comment = 'Wir haben den Bedarf zu der Sparte mit Ihnen besprochen, doch Sie wünschen zurzeit keinen Versicherungsschutz.\n' .
                       'Wunschgemäß berücksichtigen wir diese Sparte in künftigen Beratungen nicht mehr.\n' .
                       'Wir weisen Sie ausdrücklich darauf hin, dass Sie im Schadenfall keinen Versicherungsschutz haben.\n' .
                       'Bitte informieren Sie uns explizit, sofern diese Sparte wieder Gegenstand der Beratung werden soll.';

    public function up()
    {
        $existing = BedarfHandlungstext::model()->findByAttributes(['title' => $this->title]);
        if ($existing === null) {
            $this->insert($this->table, [
                'title'   => $this->title,
                'comment' => $this->comment
            ]);
        }
    }

    public function down()
    {
        BedarfHandlungstext::model()->deleteAllByAttributes(['title' => $this->title]);
    }
}
