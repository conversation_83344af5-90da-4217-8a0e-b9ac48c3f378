<?php

class m181026_114606_menu_sach_gewerbe extends CDbMigration
{
    private const TABLE = 'system_menu_items';

    public function up()
    {
        $this->update(
            self::TABLE,
            [
                'menu_id' => 34,
                'title'   => '1:1-Umdeckung Einzelvertrag',
                'url'     => '/sach-gewerbe/umdeckungeinzelvertrag',
                'regex'   => '/sach-gewerbe\/umdeckungeinzelvertrag/',
                'sort'    => 40,
            ],
            'url like "/sach-gewerbe/spezialversicherungen#rahmenvertrag-1-1"'
        );

        $this->insert(self::TABLE, [
            'menu_id'                    => 34,
            'active'                     => true,
            'title'                      => '1:1-Umdeckung Gesamtbestand',
            'url'                        => '/sach-gewerbe/umdeckunggesamtbestand',
            'regex'                      => '/sach-gewerbe\/umdeckunggesamtbestand/',
            'target'                     => '_top',
            'exclude_testuser'           => false,
            'admin_only'                 => false,
            'right_id'                   => null,
            'sort'                       => 30,
            'exclude_insurancecompanies' => false,
            'roles'                      => null,
            'rights'                     => '',
            'excluded_roles'             => '',
        ]);

        $this->insert(self::TABLE, [
            'menu_id'                    => 23,
            'active'                     => true,
            'title'                      => '1:1-Umdeckung Gesamtbestand',
            'url'                        => '/sach-privat/umdeckunggesamtbestand',
            'regex'                      => '/sach-privat\/umdeckunggesamtbestand/',
            'target'                     => '_top',
            'exclude_testuser'           => false,
            'admin_only'                 => false,
            'right_id'                   => null,
            'sort'                       => 30,
            'exclude_insurancecompanies' => false,
            'roles'                      => null,
            'rights'                     => '',
            'excluded_roles'             => '',
        ]);
    }

    public function down()
    {
        $this->update(
            self::TABLE,
            [
                'menu_id' => 35,
                'title'   => 'Rahmenvertrag 1:1',
                'url'     => '/sach-gewerbe/spezialversicherungen#rahmenvertrag-1-1',
                'regex'   => '/sach-gewerbe\/rahmenvertraggewerbe/',
                'sort'    => 1,
            ],
            'url like "/sach-gewerbe/umdeckungeinzelvertrag"'
        );

        $this->delete(self::TABLE, 'url = "/sach-gewerbe/umdeckunggesamtbestand"');

        $this->delete(self::TABLE, 'url = "/sach-privat/umdeckunggesamtbestand"');

        return true;
    }
}
