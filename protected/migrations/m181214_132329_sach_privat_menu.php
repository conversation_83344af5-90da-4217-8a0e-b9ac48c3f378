<?php

class m181214_132329_sach_privat_menu extends CDbMigration
{
    const TABLE     = 'system_menu_items';
    const URL_JAGD  = '/sach-privat/spezialversicherungen#versicherung-jagdversicherungen';
    const URL_FORST = '/sach-privat/spezialversicherungen#versicherung-forstversicherung';

    public function up()
    {
        $this->insert(self::TABLE, [
            'menu_id'                    => 32,
            'title'                      => 'Jagdversicherungen',
            'active'                     => 1,
            'regex'                      => '/sach-privat\\/140/',
            'icon'                       => '',
            'url'                        => self::URL_JAGD,
            'target'                     => '_top',
            'exclude_testuser'           => 1,
            'admin_only'                 => 0,
            'sort'                       => 40,
            'exclude_insurancecompanies' => 0,
            'rights'                     => '',
            'excluded_roles'             => '',
        ]);

        $this->insert(self::TABLE, [
            'menu_id'                    => 32,
            'title'                      => 'Forstversicherung',
            'active'                     => 1,
            'regex'                      => '/sach-privat\\/141/',
            'icon'                       => '',
            'url'                        => self::URL_FORST,
            'target'                     => '_top',
            'exclude_testuser'           => 1,
            'admin_only'                 => 0,
            'sort'                       => 45,
            'exclude_insurancecompanies' => 0,
            'rights'                     => '',
            'excluded_roles'             => '',
        ]);
    }

    public function down()
    {
        $this->delete(self::TABLE, 'url = "' . self::URL_JAGD . '"');
        $this->delete(self::TABLE, 'url = "' . self::URL_FORST . '"');

        return true;
    }
}
