<?php

use Components\Authentication\DbAuthtoken;

class m190107_095950_add_authtoken_hash extends CDbMigration
{
    private const TABLE  = 'authtoken';
    private const COLUMN = 'hash';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'string');
        $entries = DbAuthtoken::model()->findAll();
        /** @var DbAuthtoken $entry */
        foreach ($entries as $entry) {
            $entry->renewHash();
            $entry->save();
        }
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
