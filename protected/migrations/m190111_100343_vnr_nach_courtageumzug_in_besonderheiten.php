<?php

class m190111_100343_vnr_nach_courtageumzug_in_besonderheiten extends CDbMigration
{
    private const TABLE  = 'insurance_company_specifics';
    private const COLUMN = 'vnr_bleibt_bei_courtageumzug';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'VARCHAR(1024) not null');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);
    }
}
