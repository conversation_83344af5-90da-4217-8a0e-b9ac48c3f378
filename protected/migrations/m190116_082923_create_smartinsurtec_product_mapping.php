<?php

class m190116_082923_create_smartinsurtec_product_mapping extends CreateTableMigration
{
    public $tableName = 'smartinsurtec_product_mapping';

    public function getColumns()
    {
        return [
            'id'               => 'pk',
            'product_combo_id' => 'int unsigned not null',
            'gdv_id'           => 'string',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('product_combo', 'product_combo_id', 'product_combo', 'CASCADE', 'CASCADE');
    }
}
