<?php

use League\Csv\Reader;

class m190116_090908_import_smartinsurtec_mapping extends CDbMigration
{
    private $file = __DIR__ . DS . 'files' . DS . 'Sparten.csv';

    public function up()
    {
        $csv = Reader::createFromPath($this->file, 'r');
        $csv->setDelimiter(';');
        $recordList = iterator_to_array($csv->getRecords());
        $all        = array_column($recordList, 1, 0);

        foreach (ProductCombo::model()->findAll() as $item) {
            if (!empty($all[$item->name])) {
                $this->insert('smartinsurtec_product_mapping', [
                    'product_combo_id' => $item->id,
                    'gdv_id'           => $all[$item->name]
                ]);
            }
        }
    }

    public function down()
    {
        $this->truncateTable('smartinsurtec_product_mapping');
    }
}
