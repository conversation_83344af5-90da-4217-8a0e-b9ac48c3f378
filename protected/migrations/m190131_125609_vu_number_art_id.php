<?php

class m190131_125609_vu_number_art_id extends CDbMigration
{
    const VU_NUMBERS = [
        ['ref' => '9374', 'name' => 'AXA Life Europe', 'kind' => VuNumber::BAFIN],
        ['ref' => '0030', 'name' => 'AXA VERSICHERUNG AG', 'kind' => VuNumber::FICTION],
        ['ref' => '0919', 'name' => 'COMMERZ Real Inv.mbH', 'kind' => VuNumber::FICTION],
        ['ref' => '0923', 'name' => 'EUROPEAN BANK FOR FUND SERVICEBohnebuck', 'kind' => VuNumber::FICTION],
        ['ref' => '1146', 'name' => 'Deutsche Beamten Versicherung', 'kind' => VuNumber::BIPRO],
        ['ref' => '2262', 'name' => 'winsecura Pensionskasse Aktiengesellschaft', 'kind' => VuNumber::BIPRO],
        ['ref' => '5311', 'name' => 'Deutsche Beamten Versicherung', 'kind' => VuNumber::BIPRO],
        ['ref' => '5582', 'name' => 'DAEV ALLGEMEINE VERS. AG', 'kind' => VuNumber::FICTION],
    ];

    const TABLE = 'vu_number';

    public function up()
    {
        try {
            $this->addColumn(self::TABLE, 'kind', 'int');
        } catch (Exception $e) {
        }

        Yii::app()->cache->flush();

        /** @var VuNumber $vunumber */
        foreach (VuNumber::model()->findAll() as $vunumber) {
            $vunumber->kind = VuNumber::BAFIN;
            $vunumber->save();
        }

        foreach (self::VU_NUMBERS as $attributes) {
            $vunumber       = new VuNumber();
            $vunumber->kind = (int) $attributes['kind'];
            $vunumber->ref  = $attributes['ref'];
            $vunumber->name = $attributes['name'];
            $vunumber->save();
        }
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, 'kind');

        foreach (self::VU_NUMBERS as $attributes) {
            $vunumber = VuNumber::model()->findByAttributes(['ref' => $attributes['ref']]);
            if ($vunumber !== null) {
                $vunumber->delete();
            }
        }

        return true;
    }
}
