<?php

class m190205_134359_gdv2bipro_axa_vu_number_insurance_company_link extends CDbMigration
{
    private const LINK = [
        ['ref' => '9374', 'id' => 63], // Axa Lebensversicherung AG
        ['ref' => '0030', 'id' => 868], // AXA Versicherungen AG
        ['ref' => '0919', 'id' => 519], // CFB Commerz Real Fonds Beteiligungsgesellschaft mbH
        ['ref' => '0923', 'id' => 188], // eBase
        ['ref' => '1146', 'id' => 72], // DBV Deutsche Beamtenversicherung
        ['ref' => '2262', 'id' => 63], // Axa Lebensversicherung AG
        ['ref' => '5582', 'id' => 229], // Deutsche Ärzteversicherung AG
    ];

    public function up()
    {
        /** @var VuNumber $vu */
        foreach (self::LINK as $values) {
            $vu = VuNumber::model()->findByAttributes(['ref' => $values['ref']]);
            if ($vu === null) {
                continue;
            }
            $vu->insurance_company_id = $values['id'];
            $vu->save();
        }
    }

    public function down()
    {
        foreach (self::LINK as $values) {
            /** @var VuNumber $vu */
            $vu = VuNumber::model()->findByAttributes(['ref' => $values['ref']]);
            if ($vu === null) {
                continue;
            }
            $vu->insurance_company_id = null;
            $vu->save();
        }

        return true;
    }
}
