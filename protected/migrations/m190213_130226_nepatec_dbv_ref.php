<?php

class m190213_130226_nepatec_dbv_ref extends CDbMigration
{
    private const LINK = [
        ['ref' => '5311', 'id' => 72], // DBV Deutsche Beamtenversicherung
    ];

    public function up()
    {
        foreach (self::LINK as $values) {
            /** @var VuNumber $vu */
            $vu                       = VuNumber::model()->findByAttributes(['ref' => $values['ref']]);
            $vu->insurance_company_id = $values['id'];
            $vu->save();
        }
    }

    public function down()
    {
        foreach (self::LINK as $values) {
            /** @var VuNumber $vu */
            $vu                       = VuNumber::model()->findByAttributes(['ref' => $values['ref']]);
            $vu->insurance_company_id = null;
            $vu->save();
        }

        return true;
    }
}
