<?php

class m190218_135155_vertragsservice_import_backup extends CDbMigration
{
    const TABLE   = 'vertragsservice_import_backup';
    const FK_NAME = 'fk_contract_id';
    const COLUMN  = 'contract_id';

    public function up()
    {
        $this->createTable(self::TABLE, ['id' => 'pk', 'data' => 'longtext NOT NULL', 'created' => 'datetime NOT NULL', self::COLUMN => 'int unsigned NOT NULL']);
        $this->addForeignKey(self::FK_NAME, self::TABLE, self::COLUMN, 'contracts', 'id',
                             'CASCADE', 'CASCADE');
    }

    public function down()
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
