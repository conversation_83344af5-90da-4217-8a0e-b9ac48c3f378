<?php

class m190523_101333_hauptbet<PERSON>uer extends CreateTableMigration
{
    public $tableName = 'hauptbetreuer';

    public function getColumns()
    {
        return [
            'id'         => 'pk',
            'user_id'    => 'int unsigned not null',
            'adviser_id' => 'int unsigned not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
        $this->addOwnFk('adviser', 'adviser_id', 'user', 'CASCADE', 'CASCADE');
    }
}
