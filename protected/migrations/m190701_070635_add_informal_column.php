<?php

class m190701_070635_add_informal_column extends CDbMigration
{
    private const TABLE_USER = 'mail_user_template';
    private const TABLE_DEMV = 'mail_demv_template';
    private const COLUMN     = 'informal_content';
    private const SUBJECT    = 'informal_subject';

    public function up(): void
    {
        $this->addColumn(self::TABLE_USER, self::COLUMN, 'text');
        $this->addColumn(self::TABLE_USER, self::SUBJECT, 'string');
        $this->addColumn(self::TABLE_DEMV, self::COLUMN, 'text');
        $this->addColumn(self::TABLE_DEMV, self::SUBJECT, 'string');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE_USER, self::COLUMN);
        $this->dropColumn(self::TABLE_USER, self::SUBJECT);
        $this->dropColumn(self::TABLE_DEMV, self::COLUMN);
        $this->dropColumn(self::TABLE_DEMV, self::SUBJECT);

        return true;
    }
}
