<?php

class m190813_140503_create_cooperation_selections_table extends CDbMigration
{
    private const TABLE                     = 'user_cooperation_selections';
    private const FK_NAME_USER              = 'fk_cooperation_user';
    private const FK_NAME_INSURANCE_COMPANY = 'fk_cooperation_insurance_company';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                   => 'pk',
                'user_id'              => 'int unsigned not null',
                'insurance_company_id' => 'int unsigned not null',
                'is_rearrangement'     => 'bit not null',
                'create_timestamp'     => 'datetime',
            ]
        );

        $this->addForeignKey(self::FK_NAME_USER, self::TABLE, 'user_id', 'user', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey(
            self::FK_NAME_INSURANCE_COMPANY,
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down()
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
