<?php

class m190819_120412_create_table_risk_check_branche extends CDbMigration
{
    private const TABLE = 'risk_check_branche';

    private const FK_NAME_PRODUCTCOMBO = 'fk_product_combo';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'               => 'pk',
                'name'             => 'varchar(255)',
                'product_combo_id' => 'int unsigned not null',
            ]
        );

        $this->addForeignKey(
            self::FK_NAME_PRODUCTCOMBO,
            self::TABLE,
            'product_combo_id',
            'product_combo',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down()
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
