<?php

class m190819_120416_create_table_risk_check_branche_insurance_company extends CDbMigration
{
    private const TABLE = 'risk_check_branche_insurance_company';

    private const FK_NAME_BRANCHE           = 'fk_branche';
    private const FK_NAME_INSURANCE_COMPANY = 'fk_insurance_company';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                   => 'pk',
                'branche_id'           => 'int not null',
                'insurance_company_id' => 'int unsigned not null',
            ]
        );

        $this->addForeignKey(
            self::FK_NAME_BRANCHE,
            self::TABLE,
            'branche_id',
            'risk_check_branche',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            self::FK_NAME_INSURANCE_COMPANY,
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down()
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
