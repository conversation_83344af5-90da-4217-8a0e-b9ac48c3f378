<?php

class m190819_133032_add_column_contract_nr_print_to_contracts extends CDbMigration
{
    private const TABLE  = 'contracts';
    private const COLUMN = 'contract_nr_print';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'varchar(60) after contract_nr');
        $this->createIndex('contracts_contract_nr_print_index', self::TABLE, self::COLUMN);
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
