<?php

class m190819_144551_remove_columns_key_Level_X extends CDbMigration
{
    private const COLUMNS = [
        'keyLevel1',
        'keyLevel2',
        'keyLevel3',
        'keyLevel4',
    ];

    private const TABLE = 'contracts';

    public function up()
    {
        foreach (self::COLUMNS as $column) {
            $table = Yii::app()->db->schema->getTable(self::TABLE);
            if (array_key_exists($column, $table->columns)) {
                $this->dropColumn(self::TABLE, $column);
            }
        }
    }

    public function down()
    {
        foreach (self::COLUMNS as $column) {
            $this->addColumn(self::TABLE, $column, 'varchar(100)');
        }

        return true;
    }
}
