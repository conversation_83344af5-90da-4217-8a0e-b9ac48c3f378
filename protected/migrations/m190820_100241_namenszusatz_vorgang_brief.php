<?php

class m190820_100241_namenszusatz_vorgang_brief extends CDbMigration
{
    private const TABLE  = 'user_writing';
    private const COLUMN = 'receiver_name_affix';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'VARCHAR(128) AFTER reciever_name');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
