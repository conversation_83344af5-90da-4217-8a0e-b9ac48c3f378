<?php

use League\Csv\Reader;

class m190827_074834_import_risk_check_suggestions extends CDbMigration
{
    private const FILE_PATH = __DIR__ . DS . 'files';
    private const FILE_NAME = 'risk-check-suggestions.csv';
    private const DELIMITER = ';';
    private const ENCLOSURE = '"';

    private const TABLE_NAME_BRANCHE         = 'risk_check_branche';
    private const TABLE_NAME_BRANCHE_COMPANY = 'risk_check_branche_insurance_company';

    public function up()
    {
        $reader = $this->initReader();

        $productComboId = '';

        foreach ($reader->getRecords() as [$newProductComboId, $brancheName, $companyIdsStr]) {
            if (!empty($newProductComboId)) {
                $productComboId = $newProductComboId;
            }

            if (!is_numeric($productComboId) || empty($companyIdsStr)) {
                continue;
            }

            if (empty($brancheName)) {
                $brancheName = 'Allgemein';
            }

            $brancheId = $this->createBranche($brancheName, $productComboId);
            $this->createBrancheCompanyRelations($brancheId, $companyIdsStr);
        }
    }

    /**
     * @return Reader static
     */
    private function initReader(): Reader
    {
        $reader = Reader::createFromPath(self::FILE_PATH . DS . self::FILE_NAME, 'r');
        $reader->setDelimiter(self::DELIMITER);
        $reader->setEnclosure(self::ENCLOSURE);

        return $reader;
    }

    /**
     * @param string $brancheName
     * @param int    $productComboId
     *
     * @return int
     */
    private function createBranche(string $brancheName, int $productComboId): int
    {
        try {
            Yii::app()->db->createCommand()->insert(self::TABLE_NAME_BRANCHE, [
                'name'             => $brancheName,
                'product_combo_id' => $productComboId,
            ]);
        } catch (Exception $e) {
        }

        return Yii::app()->db->getLastInsertID();
    }

    /**
     * @param int $brancheId
     * @param     $companyIdsStr
     */
    private function createBrancheCompanyRelations(int $brancheId, $companyIdsStr): void
    {
        $companyIds = explode(',', trim(str_replace(' ', '', $companyIdsStr), ','));

        foreach ($companyIds as $companyId) {
            try {
                Yii::app()->db->createCommand()->insert(self::TABLE_NAME_BRANCHE_COMPANY, [
                    'branche_id'           => $brancheId,
                    'insurance_company_id' => $companyId,
                ]);
            } catch (Exception $e) {
            }
        }
    }

    public function down()
    {
        $this->delete(self::TABLE_NAME_BRANCHE, 'id is not null');
    }
}
