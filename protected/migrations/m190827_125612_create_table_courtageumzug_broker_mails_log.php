<?php

class m190827_125612_create_table_courtageumzug_broker_mails_log extends CreateTableMigration
{
    public $tableName = 'courtageumzug_broker_mails_log';

    public function getColumns()
    {
        return [
            'id'              => 'pk',
            'user_id'         => 'integer unsigned not null',
            'mail'            => 'text',
            'sent'            => 'bool',
            'create_datetime' => 'datetime',
            'json_companies'  => 'text',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
