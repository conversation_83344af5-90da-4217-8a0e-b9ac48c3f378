<?php

class m190902_120746_insert_documenttype_auftragsverarbeitungsvereinbarung extends CDbMigration
{
    private const TABLE        = 'document_type';
    private const DOC_ID       = 525;
    private const INSERT_VALUE = [
        'id'      => self::DOC_ID,
        'name'    => 'Auftragsverarbeitung',
        'client'  => 0,
        'broker'  => 1,
        'company' => 1,
        'agency'  => 0,
    ];

    public function up()
    {
        $doc = DocumentType::model()->findAllByAttributes(['id' => DocumentType::AUFTRAGSVERARBEITUNG]);

        if (empty($doc)) {
            $this->insert(self::TABLE, self::INSERT_VALUE);
        }
    }

    public function down()
    {
        return true;
    }
}
