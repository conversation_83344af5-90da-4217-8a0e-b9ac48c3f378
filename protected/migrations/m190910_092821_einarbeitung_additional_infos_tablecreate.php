<?php

class m190910_092821_einarbeitung_additional_infos_tablecreate extends CreateTableMigration
{
    public $tableName = 'einarbeitung_additional_infos';

    /**
     * @return array
     */
    public function getColumns(): array
    {
        return [
            'id'                    => 'pk',
            'user_id'               => 'int(11) unsigned',
            'origin'                => 'string not null',
            'has_potential'         => 'bit not null',
            'has_potential_info'    => 'text',
            'has_recommended_demv'  => 'bit not null',
            'is_large_agency'       => 'bit not null',
            'is_large_agency_info'  => 'string',
            'is_fully_supplied'     => 'bit not null',
            'no_rearrangement_info' => 'string',
            'peculiarity'           => 'string',
        ];
    }

    public function addForeignKeys()
    {
        $this->addForeignKey(
            'einarbeitung_additional_infos_user_fk',
            'einarbeitung_additional_infos',
            'user_id',
            'user',
            'id',
            'cascade',
            'cascade'
        );
    }
}
