<?php

class m190910_121514_create_einarbeitung_additional_infos_brokersoftware extends CDbMigration
{
    private const TABLE                  = 'einarbeitung_additional_infos_brokersoftware';
    private const FK_SBS_ADDITIONAL_INFO = 'fk_sbs_additional_infos';
    private const FK_SBB_BROKERSOFTWARE  = 'fk_sbb_brokersoftware';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                               => 'pk',
                'einarbeitung_additional_infos_id' => 'int not null',
                'einarbeitung_brokersoftware_id'   => 'int not null',
            ]
        );

        $this->addForeignKey(
            self::FK_SBS_ADDITIONAL_INFO,
            self::TABLE,
            'einarbeitung_additional_infos_id',
            'einarbeitung_additional_infos',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            self::FK_<PERSON>BB_BROKERSOFTWARE,
            self::TABLE,
            'einarbeitung_brokersoftware_id',
            'einarbeitung_brokersoftware',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down()
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
