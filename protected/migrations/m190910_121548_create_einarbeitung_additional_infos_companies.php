<?php

class m190910_121548_create_einarbeitung_additional_infos_companies extends CDbMigration
{
    private const TABLE                  = 'einarbeitung_additional_infos_companies';
    private const FK_SPS_ADDITIONAL_INFO = 'fk_sps_additional_infos';
    private const FK_SPP_POOL            = 'fk_spp_pool';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                               => 'pk',
                'einarbeitung_additional_infos_id' => 'int not null',
                'insurance_company_id'             => 'int(11) unsigned',
                'type'                             => 'int(11) unsigned',
            ]
        );

        $this->addForeignKey(
            self::FK_SPS_ADDITIONAL_INFO,
            self::TABLE,
            'einarbeitung_additional_infos_id',
            'einarbeitung_additional_infos',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            self::FK_SPP_POOL,
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down()
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
