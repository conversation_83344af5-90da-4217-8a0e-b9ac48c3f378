<?php

class m190919_073736_drop_beratungsvorsprung_event extends CDbMigration
{
    public function up()
    {
        $this->dropTable('beratungsdoku_event_product_combos');
        $this->dropForeignKey('fk_beratungsdoku_textmodule_event', 'beratungsdoku_textmodule');
        $this->dropTable('beratungsdoku_event');
    }

    public function down()
    {
        $this->execute('CREATE TABLE `beratungsdoku_event` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` text,
  `sort` int(11) unsigned DEFAULT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `global` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8');

        $this->addForeignKey('fk_beratungsdoku_textmodule_event', 'beratungsdoku_textmodule', 'event_id', 'beratungsdoku_event', 'id', 'SET NULL', 'SET NULL');

        $this->execute('CREATE TABLE `beratungsdoku_event_product_combos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `product_combo_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_beratungsdoku_event_product_combos_event` (`event_id`),
  KEY `fk_beratungsdoku_event_product_combos_product_combo` (`product_combo_id`),
  CONSTRAINT `fk_beratungsdoku_event_product_combos_event` FOREIGN KEY (`event_id`) REFERENCES `beratungsdoku_event` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_beratungsdoku_event_product_combos_product_combo` FOREIGN KEY (`product_combo_id`) REFERENCES `product_combo` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8');

        return true;
    }
}
