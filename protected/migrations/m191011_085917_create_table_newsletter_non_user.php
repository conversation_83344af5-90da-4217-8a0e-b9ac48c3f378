<?php

class m191011_085917_create_table_newsletter_non_user extends CDbMigration
{
    private const TABLE              = 'newsletter_non_user';
    private const FK_NAME_SALUTATION = 'fk_salutation_newsletter_non_user';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'              => 'pk',
                'email'           => 'varchar(100) not null',
                'firstname'       => 'varchar(120)',
                'lastname'        => 'varchar(120)',
                'salutation_id'   => 'int(10) unsigned',
                'blacklisted'     => 'boolean default false',
                'create_datetime' => 'datetime not null'
            ]
        );

        $this->addForeignKey(
            self::FK_NAME_SALUTATION,
            self::TABLE,
            'salutation_id',
            'salutation',
            'id'
        );
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
