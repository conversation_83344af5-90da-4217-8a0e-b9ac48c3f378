<?php

use League\Csv\Reader;

class m191015_122538_import_einarbeitung_brokersoftwares extends CDbMigration
{
    private const FILE_PATH = __DIR__ . DS . 'files';
    private const FILE_NAME = 'brokersoftwares.csv';
    private const DELIMITER = ';';
    private const ENCLOSURE = '"';

    private const TABLE_NAME = 'einarbeitung_brokersoftware';

    public function up()
    {
        $reader = $this->initReader();

        foreach ($reader->getRecords() as [$name]) {
            if (empty($name) | !is_string($name)) {
                continue;
            }

            $this->createBrokersoftware($name);
        }
    }

    /**
     * @return Reader static
     */
    private function initReader(): Reader
    {
        $reader = Reader::createFromPath(self::FILE_PATH . DS . self::FILE_NAME, 'r');
        $reader->setDelimiter(self::DELIMITER);
        $reader->setEnclosure(self::ENCLOSURE);

        return $reader;
    }

    /**
     * @param string $brancheName
     * @param int    $productComboId
     *
     * @return int
     */
    private function createBrokersoftware(string $name): int
    {
        try {
            Yii::app()->db->createCommand()->insert(self::TABLE_NAME, [
                'name'             => $name,
            ]);
        } catch (Exception $e) {
        }

        return Yii::app()->db->getLastInsertID();
    }

    public function down()
    {
        $this->delete(self::TABLE_NAME, 'id is not null');
    }
}
