<?php

class m191126_160334_courtageumzuege_menu_item extends CDbMigration
{
    private $title = 'Courtageumzüge';

    public function up()
    {
        $this->down();

        $this->insert('system_menu_items', [
            'menu_id'                    => 12,
            'title'                      => $this->title,
            'active'                     => 1,
            'icon'                       => 'fa fa-anchor',
            'url'                        => '/courtageumzug/umzug/overview',
            'target'                     => '_top',
            'exclude_testuser'           => 0,
            'admin_only'                 => 0,
            'sort'                       => 25,
            'exclude_insurancecompanies' => 1,
            'roles'                      => '1,2,3,4,5,6,7,8,9'
        ]);
    }

    public function down()
    {
        $this->delete('system_menu_items', 'title = "' . $this->title . '"');
    }
}
