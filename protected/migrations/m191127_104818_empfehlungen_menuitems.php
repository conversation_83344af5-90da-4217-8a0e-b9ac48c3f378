<?php

class m191127_104818_empfehlungen_menuitems extends CDbMigration
{
    private $titleBroschueren      = 'Broschüren';
    private $titleVorteilsprogramm = 'Vorteilsprogramm';

    public function up()
    {
        $this->down();

        $this->insert('system_menu_items', [
            'menu_id'                    => 8,
            'title'                      => $this->titleBroschueren,
            'active'                     => 1,
            'icon'                       => 'fa fa-anchor',
            'url'                        => '/empfehlungen/broschueren',
            'target'                     => '_top',
            'exclude_testuser'           => 1,
            'admin_only'                 => 0,
            'sort'                       => 310,
            'exclude_insurancecompanies' => 0,
            'roles'                      => '1,2,3,4,5,6,7,8,9'
        ]);

        $this->insert('system_menu_items', [
            'menu_id'                    => 8,
            'title'                      => $this->titleVorteilsprogramm,
            'active'                     => 1,
            'icon'                       => 'fa fa-anchor',
            'url'                        => '/empfehlungen/vorteilsprogramm',
            'target'                     => '_top',
            'exclude_testuser'           => 1,
            'admin_only'                 => 0,
            'sort'                       => 320,
            'exclude_insurancecompanies' => 0,
            'roles'                      => '1,2,3,4,5,6,7,8,9'
        ]);
    }

    public function down()
    {
        $this->delete('system_menu_items', 'title = "' . $this->titleBroschueren . '"');
        $this->delete('system_menu_items', 'title = "' . $this->titleVorteilsprogramm . '"');
    }
}
