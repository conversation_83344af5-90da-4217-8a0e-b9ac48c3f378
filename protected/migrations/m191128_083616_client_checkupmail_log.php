<?php

class m191128_083616_client_checkupmail_log extends CreateTableMigration
{
    public $tableName = 'checkup_mail_log';

    public function getColumns()
    {
        return [
            'id'              => 'pk',
            'client_id'       => 'int unsigned not null',
            'create_datetime' => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('client', 'client_id', 'client', 'CASCADE', 'CASCADE');
    }
}
