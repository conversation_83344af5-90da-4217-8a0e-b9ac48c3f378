<?php

class m191218_110231_add_menu_item_beratungsvorsprung_gesellschaften extends CDbMigration
{
    private const TABLE_NAME         = 'system_menu_items';
    private const MENU_ID_STAMMDATEN = 12;

    public function up(): bool
    {
        $this->insert(self::TABLE_NAME, [
            'menu_id'          => self::MENU_ID_STAMMDATEN,
            'title'            => 'Beratungsvorsprung',
            'active'           => 1,
            'url'              => '/beratungsvorsprung',
            'target'           => '_top',
            'exclude_testuser' => 0,
            'admin_only'       => 0,
            'sort'             => 100, // make sure Beratungsvorsprung is the last menu entry
            'roles'            => '2,8', // Gesellschaften & Gesellschaftsansprechpartner
        ]);

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE_NAME,
            'menu_ID = :menuId AND title = "Beratungsvorsprung"',
            ['menuId' => self::MENU_ID_STAMMDATEN]
        );

        return true;
    }
}
