<?php

class m191218_123209_add_foreignkeys extends CDbMigration
{
    private const FKS = [
        ['table' => 'client_offers', 'ref' => 'client', 'column' => 'client_id', 'update' => 'CASCADE', 'delete' => 'CASCADE'],
        ['table' => 'user_profile_documents', 'ref' => 'user', 'column' => 'user_id', 'update' => 'CASCADE', 'delete' => 'CASCADE'],
        ['table' => 'mail_footer', 'ref' => 'user', 'column' => 'user_id', 'update' => 'CASCADE', 'delete' => 'CASCADE'],
        ['table' => 'contract_field_deleted', 'ref' => 'contract_fields', 'column' => 'field_id', 'update' => 'CASCADE', 'delete' => 'CASCADE'],
        ['table' => 'contract_field_deleted', 'ref' => 'product_combo', 'column' => 'product_combo_id', 'update' => 'CASCADE', 'delete' => 'CASCADE'],
    ];

    public function up()
    {
        $this->alterColumn('contract_field_deleted', 'product_combo_id', 'int unsigned');
        $this->execute('delete from contract_field_deleted where field_id not in (select id from contract_fields);');
        $this->alterColumn('user_profile_documents', 'user_id', 'int unsigned');
        $this->execute('delete from user_profile_documents where user_id not in(select id from user);');
        $this->execute('delete from client_offers where client_id not in(select id from client);');
        $this->execute('delete from mail_footer where user_id not in(select id from user);');
        foreach (self::FKS as $key) {
            if (!$this->fkExists($key)) {
                $this->addForeignKey($this->getFkName($key), $key['table'], $key['column'], $key['ref'], 'id', $key['delete'], $key['update']);
            }
        }
    }

    /**
     * @param array $key
     *
     * @return string
     */
    private function getFkName(array $key): string
    {
        return sprintf('fk_%s_%s', $key['table'], rtrim($key['column'], '_id'));
    }

    public function down()
    {
        foreach (self::FKS as $key) {
            if ($this->fkExists($key)) {
                $this->dropForeignKey($this->getFkName($key), $key['table']);
            }
        }

        return true;
    }

    /**
     * @param array $key
     *
     * @return bool
     * @throws CException
     */
    private function fkExists(array $key): bool
    {
        $result = $this->getDbConnection()
                       ->createCommand()
                       ->select('COLUMN_NAME')
                       ->from('INFORMATION_SCHEMA.KEY_COLUMN_USAGE')
                       ->where('TABLE_NAME = :table', [':table' => $key['table']])
                       ->andWhere('COLUMN_NAME = :column', [':column' => $key['column']])
                       ->andWhere('REFERENCED_TABLE_NAME = :ref', [':ref' => $key['ref']])
                       ->queryAll();

        return !empty($result);
    }
}
