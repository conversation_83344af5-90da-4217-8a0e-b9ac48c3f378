<?php

class m191231_082714_add_generated_birthday_columns_to_client extends CDbMigration
{
    private const TABLE        = 'client';
    private const COLUMN_DAY   = 'birthday_day';
    private const COLUMN_MONTH = 'birthday_month';
    private const INDEX_NAME   = 'client_birthday_month_day';

    public function up()
    {
        $query = sprintf('alter table %s add %s INT GENERATED ALWAYS AS (DAY(birthday)) PERSISTENT after birthday;',
                         self::TABLE,
                         self::COLUMN_DAY
        );
        $this->execute($query);

        $query = sprintf('alter table %s add %s INT GENERATED ALWAYS AS (MONTH(birthday)) PERSISTENT after birthday;',
                         self::TABLE,
                         self::COLUMN_MONTH
        );
        $this->execute($query);
        $this->createIndex(self::INDEX_NAME, self::TABLE, [self::COLUMN_MONTH, self::COLUMN_DAY]);
    }

    public function down()
    {
        $this->dropIndex(self::INDEX_NAME, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN_DAY);
        $this->dropColumn(self::TABLE, self::COLUMN_MONTH);

        return true;
    }
}
