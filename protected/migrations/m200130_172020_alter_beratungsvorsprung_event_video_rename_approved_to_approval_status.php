<?php

class m200130_172020_alter_beratungsvorsprung_event_video_rename_approved_to_approval_status extends CDbMigration
{
    private const TABLE1     = 'beratungsvorsprung_event';
    private const TABLE2     = 'beratungsvorsprung_video';
    private const COLUMN     = 'approved';
    private const COLUMN_NEW = 'approval_status';

    public function up()
    {
        $this->renameColumn(self::TABLE1, self::COLUMN, self::COLUMN_NEW);
        $this->renameColumn(self::TABLE2, self::COLUMN, self::COLUMN_NEW);
    }

    public function down()
    {
        return true;
    }
}
