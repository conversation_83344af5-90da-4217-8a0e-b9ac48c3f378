<?php

class m200205_131812_create_beratungslink_table extends CreateTableMigration
{
    public $tableName = 'client_beratungslink';

    public function getColumns()
    {
        return [
            'id'               => 'pk',
            'client_id'        => 'int unsigned not null',
            'sparte'           => 'VARCHAR(5) not null',
            'type'             => 'int not null',
            'product_combo_id' => 'int unsigned',
            'title'            => 'string',
            'foreign_id'       => 'string not null',
            'url'              => 'string not null',
            'create_datetime'  => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('client', 'client_id', 'client', 'CASCADE', 'CASCADE');
        $this->addOwnFk('product_combo', 'product_combo_id', 'product_combo', 'CASCADE', 'CASCADE');
    }
}
