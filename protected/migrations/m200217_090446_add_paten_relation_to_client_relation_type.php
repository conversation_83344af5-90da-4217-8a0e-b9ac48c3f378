<?php

class m200217_090446_add_paten_relation_to_client_relation_type extends CDbMigration
{
    private const PATENONKELTANTE_ID = 29;
    private const PATENKIND_ID       = 30;

    public function up()
    {
        $this->insert('client_relation_type', [
            'id'                  => self::PATENONKELTANTE_ID,
            'name'                => 'Patenonkel/Patentante',
            'related_relation_id' => self::PATENKIND_ID,
            'category_id'         => 1,
            'not_selectable'      => 0,
        ]);

        $this->insert('client_relation_type', [
            'id'                  => self::PATENKIND_ID,
            'name'                => 'Patenkind',
            'related_relation_id' => self::PATENONKELTANTE_ID,
            'category_id'         => 1,
            'not_selectable'      => 0,
        ]);
    }

    public function down()
    {
        $this->delete('client_relation_type', 'related_relation_id = ' . self::PATENONKELTANTE_ID);
        $this->delete('client_relation_type', 'related_relation_id = ' . self::PATENKIND_ID);
    }
}
