<?php

class m200217_091633_add_bipro_settings extends CDbMigration
{
    private const TABLE = 'system_settings';

    public function up(): void
    {
        $this->insertSetting(Feature::GDV2BIPRO_SHARE, 'GDV2BiPRO aktiv');
        $this->insertSetting(Feature::GDV2BIPRO_CREATE_VERMITTLERNUMMER, 'Vermittlernummer anlegen');
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            sprintf('category = "bipro" AND slug = "%s"', Feature::GDV2BIPRO_SHARE)
        );

        $this->delete(
            self::TABLE,
            sprintf('category = "bipro" AND slug = "%s"', Feature::GDV2BIPRO_CREATE_VERMITTLERNUMMER)
        );

        return true;
    }

    private function insertSetting(string $slug, string $bezeichnung): void
    {
        $settings = Setting::model()->findByAttributes(
            [
                'slug'     => $slug,
                'category' => 'bipro',
            ]
        );

        if ($settings !== null) {
            return;
        }

        $this->insert(
            self::TABLE,
            [
                'category' => 'bipro',
                'name'     => $bezeichnung,
                'slug'     => $slug,
                'value'    => 0,
            ]
        );
    }
}
