<?php

class m200217_111800_adresszusatz_vorgang_brief extends CDbMigration
{
    private const TABLE  = 'user_writing';
    private const COLUMN = 'addition';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'VARCHAR(128) AFTER receiver_name_affix');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
