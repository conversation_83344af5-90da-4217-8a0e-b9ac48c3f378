<?php

class m200217_152752_add_empfehlung_menu_item extends CDbMigration
{
    private $title = 'Vorsorge und Patientenrechte';

    public function up()
    {
        $this->down();

        $this->insert('system_menu_items', [
            'menu_id'                    => 8,
            'title'                      => $this->title,
            'active'                     => 1,
            'icon'                       => 'fa fa-anchor',
            'url'                        => '/empfehlungen/vorsorgepatientenrechte',
            'target'                     => '_top',
            'exclude_testuser'           => 1,
            'admin_only'                 => 0,
            'sort'                       => 410,
            'exclude_insurancecompanies' => 0,
            'roles'                      => '1,2,3,4,5,6,7,8,9'
        ]);
    }

    public function down()
    {
        $this->delete('system_menu_items', 'title = "' . $this->title . '"');
    }
}
