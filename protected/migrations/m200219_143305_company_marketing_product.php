<?php

class m200219_143305_company_marketing_product extends CDbMigration
{
    const         TABLE       = 'insurance_company_marketing_product';
    private const FK_EVENT_ID = 'fk_cmp_icid';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                   => 'pk',
                'product_type'         => 'int NOT NULL',
                'insurance_company_id' => 'int unsigned NOT NULL',
                'expiration_date'      => 'date NOT NULL',
                'create_datetime'      => 'datetime NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_EVENT_ID,
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id'
        );
    }

    public function down()
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
