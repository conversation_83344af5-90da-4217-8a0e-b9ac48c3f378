<?php

class m200226_101951_add_abrechnungstool_feature_setting extends CDbMigration
{
    private const TABLE = 'system_settings';

    public function up(): void
    {
        $this->insertSetting(Feature::ABRECHNUNGSTOOL, 'Abrechnungstool aktiv');
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            sprintf('category = "allgemein" AND slug = "%s"', Feature::ABRECHNUNGSTOOL)
        );

        return true;
    }

    private function insertSetting(string $slug, string $bezeichnung): void
    {
        $settings = Setting::model()->findByAttributes(
            [
                'slug'     => $slug,
                'category' => 'allgemein',
            ]
        );

        if ($settings !== null) {
            return;
        }

        $this->insert(
            self::TABLE,
            [
                'category' => 'allgemein',
                'name'     => $bezeichnung,
                'slug'     => $slug,
                'value'    => 0,
            ]
        );
    }
}
