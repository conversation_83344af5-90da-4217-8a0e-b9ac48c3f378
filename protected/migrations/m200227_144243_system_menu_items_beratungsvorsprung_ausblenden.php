<?php

class m200227_144243_system_menu_items_beratungsvorsprung_ausblenden extends CDbMigration
{
    private const TABLE_NAME               = 'system_menu_items';
    private const COLUMN_NAME              = 'excluded_roles';
    private const EXCLUDED_ROLES           = '1,3,4,5,6,7,9';
    private const MENU_ID_STAMMDATEN       = 12;
    private const TITLE_BERATUNGSVORSPRUNG = 'Beratungsvorsprung';

    public function up(): bool
    {
        $this->update(
            self::TABLE_NAME,
            [
                self::COLUMN_NAME => self::EXCLUDED_ROLES,
            ],
            'menu_id=:menuId AND title=:title',
            [
                'title'  => self::TITLE_BERATUNGSVORSPRUNG,
                'menuId' => self::MENU_ID_STAMMDATEN,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE_NAME,
            [
                self::COLUMN_NAME => '',
            ],
            'menu_id=:menuId AND title=:title',
            [
                'title'  => self::TITLE_BERATUNGSVORSPRUNG,
                'menuId' => self::MENU_ID_STAMMDATEN,
            ]
        );

        return true;
    }
}
