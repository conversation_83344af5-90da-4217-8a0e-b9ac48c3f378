<?php

class m200227_152204_system_menu_items_fix_gesellschaft_ansprechpartner_menu extends CDbMigration
{
    private const TABLE_NAME            = 'system_menu_items';
    private const COLUMN_NAME_EXCLUDED  = 'excluded_roles';
    private const COLUMN_NAME_ROLES     = 'roles';
    private const MENU_ID_STAMMDATEN    = 12;
    private const TITLE_COURTAGEUMZUEGE = 'Courtageumzüge';
    private const TITLE_SYSTEMNUTZER    = 'Systemnutzer';

    private const EXCLUDED_ROLES_GESELLSCHAFT  = '2,8';
    private const ROLES_EXCLUDING_GESELLSCHAFT = '1,3,4,5,6,7,9';

    private const EXCLUDED_ROLES_ANSPRECHPARTNER  = '8';
    private const ROLES_EXCLUDING_ANSPRECHPARTNER = '1,2,3,4,5,6,7,9';

    private const ROLES_ALL = '1,2,3,4,5,6,7,8,9';

    public function up(): bool
    {
        $this->update(
            self::TABLE_NAME,
            [
                self::COLUMN_NAME_EXCLUDED => self::EXCLUDED_ROLES_GESELLSCHAFT,
                self::COLUMN_NAME_ROLES    => self::ROLES_EXCLUDING_GESELLSCHAFT,
            ],
            'menu_id=:menuId AND title=:title',
            [
                'title'  => self::TITLE_COURTAGEUMZUEGE,
                'menuId' => self::MENU_ID_STAMMDATEN,
            ]
        );

        $this->update(
            self::TABLE_NAME,
            [
                self::COLUMN_NAME_EXCLUDED => self::EXCLUDED_ROLES_ANSPRECHPARTNER,
                self::COLUMN_NAME_ROLES    => self::ROLES_EXCLUDING_ANSPRECHPARTNER,
            ],
            'menu_id=:menuId AND title=:title',
            [
                'title'  => self::TITLE_SYSTEMNUTZER,
                'menuId' => self::MENU_ID_STAMMDATEN,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE_NAME,
            [
                self::COLUMN_NAME_EXCLUDED => '',
                self::COLUMN_NAME_ROLES    => self::ROLES_ALL,
            ],
            'menu_id=:menuId AND title=:title',
            [
                'title'  => self::TITLE_COURTAGEUMZUEGE,
                'menuId' => self::MENU_ID_STAMMDATEN,
            ]
        );

        $this->update(
            self::TABLE_NAME,
            [
                self::COLUMN_NAME_EXCLUDED => '',
                self::COLUMN_NAME_ROLES    => self::ROLES_ALL,
            ],
            'menu_id=:menuId AND title=:title',
            [
                'title'  => self::TITLE_SYSTEMNUTZER,
                'menuId' => self::MENU_ID_STAMMDATEN,
            ]
        );

        return true;
    }
}
