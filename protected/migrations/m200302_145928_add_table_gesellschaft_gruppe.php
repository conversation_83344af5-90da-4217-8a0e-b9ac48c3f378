<?php

class m200302_145928_add_table_gesellschaft_gruppe extends CDbMigration
{
    private const TABLE = 'gesellschaft_gruppe';

    public function up(): void
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                 => 'pk',
                'company_class'      => 'varchar(255) NOT NULL',
                'company_id'         => 'int unsigned NOT NULL',
                'active'             => 'boolean DEFAULT 0',
                'last_edit_datetime' => 'datetime NOT NULL',
                'last_edit_user_id'  => 'int unsigned NOT NULL',
            ]
        );

        $this->execute(
            sprintf('ALTER TABLE %s ADD CONSTRAINT unique_company UNIQUE KEY(company_class(30), company_id)', self::TABLE)
        );
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
