<?php

use Business\Company\Allianz;
use Business\Company\Auxilia;
use Business\Company\Axa;
use Business\Company\Condor;
use Business\Company\DBV;
use Business\Company\DeutscheLeben;
use Business\Company\DieDortmunder;
use Business\Company\Garanta;
use Business\Company\Gothaer;
use Business\Company\Ideal;
use Business\Company\Kravag;
use Business\Company\Nuernberger;
use Business\Company\RheinLand;
use Business\Company\Rhion;
use Business\Company\Roland;
use Business\Company\RundV;
use Business\Company\Vhv;
use Business\Company\VWB;

class m200304_081907_insert_data_gesellschaft_gruppe extends CDbMigration
{
    private const AXA = [
        Axa::ALLGEMEIN,
        Axa::KRANKEN,
        Axa::LEBEN,
        Axa::SACH,
        Axa::DEUTSCHE_AERZTE,
        DBV::ID,
        Roland::ID
    ];

    private const VHV = [
        Vhv::ID,
        Vhv::LEBEN
    ];

    private const GOTHAER = [
        Gothaer::ALLGEMEIN,
        Gothaer::KRANKEN,
        Gothaer::LEBEN,
        Gothaer::PENSIONSKASSE,
        Gothaer::UNTERSTUETZUNGSKASSE
    ];

    private const NUERNBERGER = [
        Nuernberger::ID,
        Garanta::ID,
    ];

    private const VWB = [
        VWB::ID,
        DieDortmunder::ID,
    ];

    private const RUNDV = [
        RundV::ALLGEMEIN,
        RundV::LEBEN,
        RundV::TIER,
        Condor::LEBEN,
        Condor::ALLGEMEIN,
        Kravag::ALLGEMEIN,
        Kravag::LOGISTIC,
    ];

    private const ALLIANZ = [
        Allianz::LEBEN,
        Allianz::PKV,
        Allianz::ALLGEMEIN,
        Allianz::PENSION,
        DeutscheLeben::ID,
    ];

    private const RHION = [
        Rhion::ID,
        RheinLand::ID,
    ];

    private const IDEAL = [
        Ideal::ID,
    ];

    private const AUXI = [
        Auxilia::ID,
    ];

    private const GROUPS = [
        Allianz::class     => self::ALLIANZ,
        Axa::class         => self::AXA,
        Vhv::class         => self::VHV,
        Gothaer::class     => self::GOTHAER,
        Nuernberger::class => self::NUERNBERGER,
        VWB::class         => self::VWB,
        RundV::class       => self::RUNDV,
        Rhion::class       => self::RHION,
        Ideal::class       => self::IDEAL,
        Auxilia::class     => self::AUXI,
    ];

    private const TABLE  = 'gesellschaft_gruppe';

    public function up(): void
    {
        $today = date('Y-m-d H:i:s');
        foreach (self::GROUPS as $class => $gruppe) {
            $active = $class === Allianz::class ? true : false;
            foreach ($gruppe as $value) {
                $this->insert(
                    self::TABLE,
                    [
                        'company_class'      => $class,
                        'company_id'         => $value,
                        'active'             => $active,
                        'last_edit_datetime' => $today,
                        'last_edit_user_id'  => 0,
                    ]
                );
            }
        }
    }

    public function down(): bool
    {
        $this->truncateTable(self::TABLE);

        return true;
    }
}
