<?php

class m200305_105534_add_table_gesellschaft_gruppen extends CDbMigration
{
    private const TABLE            = 'gesellschaft_gruppen';
    private const REFERENCED_TABLE = 'gesellschaft_gruppe';
    private const COLUMN           = 'faktor_id';
    const         FK_NAME          = 'fk_gesellschaftgruppen_id';

    public function up(): void
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                 => 'pk',
                'bezeichnung'        => 'varchar(255) NOT NULL',
                'last_edit_datetime' => 'datetime NOT NULL',
                'last_edit_user_id'  => 'int unsigned NOT NULL',
            ]
        );
        $this->addColumn(self::REFERENCED_TABLE, self::COLUMN, 'int AFTER id');
        $this->addForeignKey(
            self::FK_NAME,
            self::REFERENCED_TABLE,
            self::COLUMN,
            self::TABLE,
            'id'
        );
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_NAME, self::REFERENCED_TABLE);
        $this->dropTable(self::TABLE);
        $this->dropColumn(self::REFERENCED_TABLE, self::COLUMN);

        return true;
    }
}
