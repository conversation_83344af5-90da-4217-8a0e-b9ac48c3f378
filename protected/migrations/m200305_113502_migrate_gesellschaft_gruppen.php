<?php

use Business\Company\Affiliation\Model\GesellschaftGruppeAR;

class m200305_113502_migrate_gesellschaft_gruppen extends CDbMigration
{
    private const TABLE     = 'gesellschaft_gruppen';
    private const REF_TABLE = 'gesellschaft_gruppe';

    public function up(): void
    {
        $today              = date('Y-m-d H:i:s');
        $criteria           = new CDbCriteria();
        $criteria->distinct = true;
        $criteria->group    = 'company_class';

        /** @var GesellschaftGruppeAR $item */
        foreach (GesellschaftGruppeAR::model()->findAll($criteria) as $item) {
            $this->insert(
                self::TABLE,
                [
                    'bezeichnung'        => $item->company_class,
                    'last_edit_datetime' => $today,
                    'last_edit_user_id'  => 0,
                ]
            );

            $insertId = Yii::app()->db->getLastInsertID();
            if (!empty($insertId)) {
                $this->update(
                    self::REF_TABLE,
                    ['faktor_id' => $insertId],
                    'company_class = :class',
                    [':class' => $item->company_class]
                );
            }
        }
    }

    public function down(): bool
    {
        /** @var GesellschaftGruppeAR $item */
        foreach (GesellschaftGruppeAR::model()->findAll() as $item) {
            if (!empty($item->gesellschaftGruppeFaktor->bezeichnung)) {
                $this->update(
                    self::REF_TABLE,
                    [
                        'company_class' => $item->gesellschaftGruppeFaktor->bezeichnung,
                    ],
                    'faktor_id = :faktor_id',
                    ['faktor_id' => $item->faktor_id]
                );
            }
        }

        $this->update(self::REF_TABLE, ['faktor_id' => null]);
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');
        $this->truncateTable(self::TABLE);
        $this->execute('SET FOREIGN_KEY_CHECKS = 1');

        return true;
    }
}
