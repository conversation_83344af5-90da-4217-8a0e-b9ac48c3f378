<?php

class m200311_074711_clean_up_gesellschafts_gruppe extends CDbMigration
{
    private const TABLE     = 'gesellschaft_gruppen';
    private const REF_TABLE = 'gesellschaft_gruppe';

    private const ALLIANZ = [
        1, 29, 122, 874, 75
    ];

    private const AXA = [
        28, 63, 72, 127, 128, 229, 868
    ];

    private const GRUPPEN = [
        'Business\Company\Allianz' => self::ALLIANZ,
        'Business\Company\Axa'     => self::AXA,
    ];

    public function up(): void
    {
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');
        $this->truncateTable(self::TABLE);
        $this->truncateTable(self::REF_TABLE);
        $this->execute(sprintf('ALTER TABLE %s DROP INDEX `unique_company`', self::REF_TABLE));
        $this->dropColumn(self::REF_TABLE, 'company_class');
        $this->dropColumn(self::REF_TABLE, 'active');
        $this->execute(
            sprintf('ALTER TABLE %s ADD CONSTRAINT unique_company_faktor UNIQUE KEY(faktor_id, company_id)', self::REF_TABLE)
        );
        $this->execute('SET FOREIGN_KEY_CHECKS = 1');

        $today = date('Y-m-d H:i:s');
        foreach (self::GRUPPEN as $gruppe => $daten) {
            $this->insert(
                self::TABLE,
                [
                    'bezeichnung'        => $gruppe,
                    'last_edit_datetime' => $today,
                    'last_edit_user_id'  => 0,
                ]
            );
            $insertId = Yii::app()->db->getLastInsertId();
            foreach ($daten as $companyId) {
                $this->insert(
                    self::REF_TABLE,
                    [
                        'faktor_id'          => $insertId,
                        'company_id'         => $companyId,
                        'last_edit_datetime' => $today,
                        'last_edit_user_id'  => 0
                    ]
                );
            }
        }
    }

    public function down(): bool
    {
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');
        $this->truncateTable(self::TABLE);
        $this->truncateTable(self::REF_TABLE);
        $this->addColumn(self::REF_TABLE, 'company_class', 'varchar(255) NOT NULL');
        $this->addColumn(self::REF_TABLE, 'active', 'bool NOT NULL DEFAULT 0');
        $this->execute(
            sprintf('ALTER TABLE %s ADD CONSTRAINT unique_company UNIQUE KEY(company_id)', self::REF_TABLE)
        );
        $this->execute(sprintf('ALTER TABLE %s DROP INDEX `unique_company_faktor`', self::REF_TABLE));
        $this->execute('SET FOREIGN_KEY_CHECKS = 1');

        return true;
    }
}
