<?php

class m200330_175446_add_request_response_logs_to_antrag_tracking extends CDbMigration
{
    private const         TABLE                   = 'modules.rechner__antragslog';
    private const         REQUEST_COLUMN          = 'request_url';
    private const         RESPONSE_COLUMN         = 'response_url';
    private const         VERSANDART_COLUMN       = 'dispatch_mode';
    private const         ANTRAGSLOG_SPARTE       = 'antragslog_sparte';
    private const         ANTRAGSLOG_CLIENT_ID    = 'antragslog_client_id';
    private const         ANTRAGSLOG_USER_ID      = 'antragslog_user_id';
    private const         ANTRAGSLOG_ANTRAGSDATUM = 'antragslog_antragsdatum';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::VERSANDART_COLUMN, 'VARCHAR(50)');
        $this->addColumn(self::TABLE, self::REQUEST_COLUMN, 'VARCHAR(255)');
        $this->addColumn(self::TABLE, self::RESPONSE_COLUMN, 'VARCHAR(255)');

        $this->createIndex(self::ANTRAGSLOG_SPARTE, self::TABLE, 'sparte');
        $this->createIndex(self::ANTRAGSLOG_CLIENT_ID, self::TABLE, 'client_id');
        $this->createIndex(self::ANTRAGSLOG_USER_ID, self::TABLE, 'user_id');
        $this->createIndex(self::ANTRAGSLOG_ANTRAGSDATUM, self::TABLE, 'antragsdatum');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::VERSANDART_COLUMN);
        $this->dropColumn(self::TABLE, self::REQUEST_COLUMN);
        $this->dropColumn(self::TABLE, self::RESPONSE_COLUMN);

        $this->dropIndex(self::ANTRAGSLOG_SPARTE, self::TABLE);
        $this->dropIndex(self::ANTRAGSLOG_CLIENT_ID, self::TABLE);
        $this->dropIndex(self::ANTRAGSLOG_USER_ID, self::TABLE);
        $this->dropIndex(self::ANTRAGSLOG_ANTRAGSDATUM, self::TABLE);

        return true;
    }
}
