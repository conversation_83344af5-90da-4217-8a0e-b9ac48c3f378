<?php

class m200407_123317_add_feature_flag_gdv2bipro_postfach_enabled extends CDbMigration
{
    private const TABLE = 'system_settings';

    public function up(): void
    {
        $this->insertSetting(Feature::GDV2BIPRO_POSTFACH, 'GDV2BiPRO Postfach aktiv');

        /** @var Setting $setting */
        $setting = Setting::model()->findByAttributes(
            [
                'slug'     => 'gdv2bipro-enabled',
                'category' => 'bipro',
            ]
        );

        if ($setting !== null) {
            $setting->slug = Feature::GDV2BIPRO_SHARE;
            $setting->name = 'GDV2BiPRO Share aktiv';
            $setting->save();
        }
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            sprintf('category = "bipro" AND slug = "%s"', Feature::GDV2BIPRO_POSTFACH)
        );

        /** @var Setting $setting */
        $setting = Setting::model()->findByAttributes(
            [
                'slug'     => Feature::GDV2BIPRO_SHARE,
                'category' => 'bipro',
            ]
        );

        if ($setting !== null) {
            $setting->slug = 'gdv2bipro-enabled';
            $setting->save();
        }

        return true;
    }

    private function insertSetting(string $slug, string $bezeichnung): void
    {
        $settings = Setting::model()->findByAttributes(
            [
                'slug'     => $slug,
                'category' => 'bipro',
            ]
        );

        if ($settings !== null) {
            return;
        }

        $this->insert(
            self::TABLE,
            [
                'category' => 'bipro',
                'name'     => $bezeichnung,
                'slug'     => $slug,
                'value'    => 0,
            ]
        );
    }
}
