<?php

class m200611_105110_drop_table_vertragsservice_retry_import extends CDbMigration
{
    private const TABLE     = 'vertragsservice_retry_import';
    private const FK_TABLE  = 'vertragsservice_tracking';
    private const FK_COLUMN = 'tracking_id';
    private const FK_NAME   = 'fk_vertragsservice_tracking_id';

    public function up(): void
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE);
        $this->dropTable(self::TABLE);
    }

    public function down(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                  => 'pk',
                'tracking_id'         => 'int NOT NULL',
                'first_tried_at'      => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
                'last_tried_at'       => 'datetime NOT NULL',
                'next_try'            => 'datetime NOT NULL',
                'try_count_threshold' => 'tinyint unsigned NOT NUll DEFAULT 0',
                'try_count'           => 'int unsigned NOT NUll DEFAULT 0',
            ]
        );
        $this->addForeign<PERSON>ey(
            self::FK_NAME,
            self::TABLE,
            self::FK_COLUMN,
            self::FK_TABLE,
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }
}
