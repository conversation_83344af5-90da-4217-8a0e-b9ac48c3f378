<?php

class m200714_062612_client_sign_upload extends CreateTableMigration
{
    public $tableName = 'client_sign_upload';

    public function getColumns()
    {
        return [
            'id'             => 'pk',
            'client_id'      => 'int unsigned not null',
            'name'           => 'string not null',
            'client_file_id' => 'int unsigned',
            'sign_id'        => 'string not null',
            'signed'         => 'bool',
            'url_prepare'    => 'string not null',
            'url_edit'       => 'string not null',
            'url_download'   => 'string not null',
            'url_status'     => 'string not null',
            'uploaded_at'    => 'datetime not null',
            'expires_at'     => 'datetime not null',
            'context'        => 'string not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('client', 'client_id', 'client', 'CASCADE', 'CASCADE');
        $this->addOwnFk('client_file', 'client_file_id', 'client_files', 'CASCADE', 'CASCADE');
    }
}
