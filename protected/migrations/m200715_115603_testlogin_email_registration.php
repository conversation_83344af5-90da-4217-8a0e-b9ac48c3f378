<?php

class m200715_115603_testlogin_email_registration extends CreateTableMigration
{
    public $tableName = 'testlogin_email_registration';

    public function getColumns()
    {
        return [
            'id'              => 'pk',
            'email'           => 'string not null',
            'landingpage'     => 'string not null',
            'user_id'         => 'int unsigned',
            'create_datetime' => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
