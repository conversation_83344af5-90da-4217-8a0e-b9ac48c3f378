<?php

use Demv\Werte\Person\Beziehung\<PERSON><PERSON><PERSON>lich\Berufsbetreuer;
use Demv\Werte\Person\Beziehung\Beruflich\Betreuter;

class m200721_143826_add_relation_betreuer_beru<PERSON>betreuer extends CDbMigration
{
    public function up(): bool
    {
        $this->insert('client_relation_type', ['id' => Betreuter::ID, 'name' => 'Betreuter', 'category_id' => 2, 'related_relation_id' => 1]);
        $this->insert('client_relation_type', ['id' => Berufsbetreuer::ID, 'name' => 'Berufsbetreuer', 'category_id' => 2, 'related_relation_id' => 32]);
        $this->update('client_relation_type', ['related_relation_id' => Berufsbetreuer::ID], 'id = ' . Betreuter::ID);

        return true;
    }

    public function down(): bool
    {
        $this->delete('client_relation_type', 'id = ' . Berufsbetreuer::ID);
        $this->delete('client_relation_type', 'id = ' . Betreuter::ID);

        return true;
    }
}
