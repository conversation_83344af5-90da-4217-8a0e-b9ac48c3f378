<?php

class m200722_111953_add_sachbearbeiter_to_contracts_table extends CDbMigration
{
    private const TABLE = 'contracts';

    public function up(): void
    {
        $this->addColumn(self::TABLE, 'sachbearbeiter_name', 'string default null');
        $this->addColumn(self::TABLE, 'sachbearbeiter_telefon', 'string default null');
        $this->addColumn(self::TABLE, 'sachbearbeiter_email', 'string default null');
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $this->dropColumn(self::TABLE, 'sachbearbeiter_name');
        $this->dropColumn(self::TABLE, 'sachbearbeiter_telefon');
        $this->dropColumn(self::TABLE, 'sachbearbeiter_email');

        return true;
    }
}
