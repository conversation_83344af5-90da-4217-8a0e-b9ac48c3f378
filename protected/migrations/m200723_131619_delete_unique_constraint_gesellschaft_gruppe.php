<?php

class m200723_131619_delete_unique_constraint_gesellschaft_gruppe extends CDbMigration
{
    private const FK_NAME = 'unique_company_faktor';
    private const TABLE   = 'gesellschaft_gruppe';

    public function up(): void
    {
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');
        $this->execute(sprintf('ALTER TABLE %s DROP INDEX `%s`', self::TABLE, self::FK_NAME));
        $this->execute('SET FOREIGN_KEY_CHECKS = 1');
    }

    public function down(): bool
    {
        $this->execute(sprintf('ALTER TABLE %s ADD CONSTRAINT %s UNIQUE KEY(gruppen_id, company_id)', self::TABLE, self::FK_NAME));

        return true;
    }
}
