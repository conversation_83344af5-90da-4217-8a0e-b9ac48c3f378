<?php

class m200731_125538_add_abschliessendes_zeichen_to_table_contracts_kfz_kennzei<PERSON> extends CDbMigration
{
    private const TABLE  = 'contracts_kfz_kennzeichen';
    private const COLUMN = 'abschliessendeszeichen';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'varchar(1) NULL');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
