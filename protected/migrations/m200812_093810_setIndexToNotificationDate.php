<?php

class m200812_093810_setIndexToNotificationDate extends CDbMigration
{
    private const INDEXPREFIX = 'idx';
    private const MAILTABLE  = 'user_mail';
    private const WRITINGTABLE  = 'user_writing';
    private const COLUMN = 'notification_date';

    public function up(): void
    {
        $this->createIndex(
            self::INDEXPREFIX .'-' . self::MAILTABLE . '-' . self::COLUMN,
            self::MAILTABLE,
            self::COLUMN
        );
        $this->createIndex(
            self::INDEXPREFIX .'-' . self::WRITINGTABLE . '-' . self::COLUMN,
            self::WRITINGTABLE,
            self::COLUMN
        );
    }

    public function down(): bool
    {
        $this->dropIndex(
            self::INDEXPREFIX .'-' . self::MAILTABLE . '-' . self::COLUMN,
            self::MAILTABLE
        );
        $this->dropIndex(
            self::INDEXPREFIX .'-' . self::WRITING<PERSON>BL<PERSON> . '-' . self::COLUMN,
            self::WRITINGTABLE
        );

        return true;
    }
}
