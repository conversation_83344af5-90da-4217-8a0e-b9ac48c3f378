<?php

class m200813_115451_client_sign_upload_orig_file extends CDbMigration
{
    private const TABLE   = 'client_sign_upload';
    private const COLUMN  = 'orig_client_file_id';
    private const FK_NAME = 'client_sign_upload_orig_file';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned');
        $this->addForeignKey(self::FK_NAME, self::TABLE, self::COLUMN, 'client_files', 'id', 'set null', 'set null');
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);
        $this->dropForeignKey(self::FK_NAME, self::TABLE);

        return true;
    }
}
