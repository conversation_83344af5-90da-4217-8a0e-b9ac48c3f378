<?php

class m200902_121137_remove_abrechnungstool_featureflag extends CDbMigration
{
    private const TABLE = 'system_settings';

    public function up()
    {
        $this->delete(
            self::TABLE,
            sprintf('category = "allgemein" AND slug = "%s"', Feature::ABRECHNUNGSTOOL)
        );
    }

    public function down()
    {
        $this->insertSetting(Feature::ABRECHNUNGSTOOL, 'Abrechnungstool aktiv');

        return true;
    }

    private function insertSetting(string $slug, string $bezeichnung): void
    {
        $settings = Setting::model()->findByAttributes(
            [
                'slug'     => $slug,
                'category' => 'allgemein',
            ]
        );

        if ($settings !== null) {
            return;
        }

        $this->insert(
            self::TABLE,
            [
                'category' => 'allgemein',
                'name'     => $bezeichnung,
                'slug'     => $slug,
                'value'    => 0,
            ]
        );
    }
}
