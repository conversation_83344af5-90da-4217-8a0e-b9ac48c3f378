<?php

class m200909_085845_alter_postfachupload_log extends CDbMigration
{
    private const TABLE = 'abrechnungstool_csv_upload_log';

    public function up()
    {
        $this->alterColumn(self::TABLE, 'create_datetime', 'datetime');
        $this->addColumn(self::TABLE, 'insurance_company_id', 'int unsigned');
        $this->addColumn(self::TABLE, 'user_id', 'int unsigned');
        $this->addForeignKey('fk_' . self::TABLE . '_company', self::TABLE, 'insurance_company_id', 'insurance_company', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_' . self::TABLE . '_user', self::TABLE, 'user_id', 'user', 'id', 'CASCADE', 'CASCADE');
    }

    public function down()
    {
        $this->dropForeignKey('fk_' . self::TABLE . '_company', self::TABLE);
        $this->dropColumn(self::TABLE, 'insurance_company_id');
        $this->dropForeignKey('fk_' . self::TABLE . '_user', self::TABLE);
        $this->dropColumn(self::TABLE, 'user_id');

        return true;
    }
}
