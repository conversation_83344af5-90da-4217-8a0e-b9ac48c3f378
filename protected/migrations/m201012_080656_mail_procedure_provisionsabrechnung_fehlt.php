<?php

class m201012_080656_mail_procedure_provisionsabrechnung_fehlt extends CDbMigration
{
    public function up()
    {
        $model = MailProcedure::model()->findByPk(MailProcedure::PROVISIONSABRECHNUNG_FEHLT);
        if ($model === null) {
            $this->insert(MailProcedure::model()->tableName(),
                          [
                              'id'                            => MailProcedure::PROVISIONSABRECHNUNG_FEHLT,
                              'name'                          => 'Provisionsabrechnung fehlt',
                              'support_assignment_subject_id' => 1
                          ]
            );
        }
    }

    public function down()
    {
        MailProcedure::model()->deleteByPk(MailProcedure::PROVISIONSABRECHNUNG_FEHLT);

        return true;
    }
}
