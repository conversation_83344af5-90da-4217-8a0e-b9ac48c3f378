<?php

class m201015_171235_add_encryption_flags extends CDbMigration
{
    private const TABLES = [
        'bank_account',
        'user_smtp_settings',
        'authtoken',
        'client',
        'bipro_user_data'
    ];

    public function up()
    {
        foreach (self::TABLES as $tablename) {
            $this->addColumn($tablename, 'sodium_encrypted', 'bool NOT NUll DEFAULT 1');
        }
    }

    public function down()
    {
        foreach (self::TABLES as $tablename) {
            $this->dropColumn($tablename, 'sodium_encrypted');
        }

        return true;
    }
}
