<?php

class m201021_074939_ab_verbunddaten_split extends CreateTableMigration
{
    public $tableName = 'ab_verbunddaten_split';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'insurance_company_id' => 'int unsigned not null',
            'identifier'           => 'string not null',
            'create_datetime'      => 'datetime',
            'vermittlernummer_pw'  => 'string',
            'user_id'              => 'int unsigned',
            'import_id'            => 'string',
            'upload_datetime'      => 'datetime',
            'file'                 => 'string',
            'orig_file'            => 'string',
            'orig_filename'        => 'string',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
