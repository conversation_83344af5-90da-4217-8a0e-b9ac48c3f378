<?php

class m201029_102403_create_table_maklerbetreuer_requested_companies extends CDbMigration
{
    private const TABLE         = 'maklerbetreuer_requested_companies';
    private const FK_COMPANY_ID = 'fk_mrc_icid';
    private const FK_USER_ID    = 'fk_mrc_uid';

    public function up()
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                   => 'pk',
                'user_id'              => 'int unsigned NOT NULL',
                'insurance_company_id' => 'int unsigned NOT NULL',
                'created_at'           => 'datetime NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_COMPANY_ID,
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id'
        );

        $this->addForeignKey(
            self::FK_USER_ID,
            self::TABLE,
            'user_id',
            'user',
            'id'
        );
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
