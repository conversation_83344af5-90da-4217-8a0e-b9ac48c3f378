<?php

class m201110_080916_share_upload_user extends CDbMigration
{
    private const TABLE  = 'share';
    private const COLUMN = 'upload_user_id';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned');
        $this->addForeignKey('share_upload_user', self::TABLE, self::COLUMN, 'user', 'id', 'CASCADE', 'CASCADE');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
