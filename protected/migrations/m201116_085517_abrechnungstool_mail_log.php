<?php

class m201116_085517_abrechnungstool_mail_log extends CreateTableMigration
{
    public $tableName = 'abrechnungstool_mail_log';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'mail_id'              => 'int not null',
            'insurance_company_id' => 'int unsigned not null',
            'status'               => 'int',
            'message'              => 'string',
            'create_datetime'      => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
