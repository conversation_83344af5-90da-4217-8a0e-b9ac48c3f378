<?php

class m201118_065652_add_uknown_brokerid_index extends CDbMigration
{
    private const TABLE     = 'vertragsservice_unknown_brokerid';
    private const COLUMN    = 'vermittlernummer';
    private const INDEXNAME = self::TABLE . '_' . self::COLUMN;

    public function up(): void
    {
        $this->createIndex(self::INDEXNAME, self::TABLE, self::COLUMN);
    }

    public function down(): bool
    {
        $this->dropIndex(self::INDEXNAME, self::TABLE);

        return true;
    }
}
