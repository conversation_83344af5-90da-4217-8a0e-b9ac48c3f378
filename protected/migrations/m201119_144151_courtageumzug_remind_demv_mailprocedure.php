<?php

use Carbon\Carbon;

class m201119_144151_courtageumzug_remind_demv_mailprocedure extends CDbMigration
{
    private const  TABLE_MAIL_PROCEDURE = 'mail_procedure';
    private const  TABLE_MAIL_TEMPLATE  = 'mail_demv_template';
    private const  MAIL_PROCEDURE_ID    = 150;

    public function up()
    {
        $this->insert(self::TABLE_MAIL_PROCEDURE,
            [
                'id'   => self::MAIL_PROCEDURE_ID,
                'name' => 'DEMV- Hilfe für Courtageumzug bei DEMV beantragen',
            ]
        );

        $this->insert(self::TABLE_MAIL_TEMPLATE,
            [
                'name'              => 'Hilfe für Courtageumzug bei DEMV beantragen',
                'description'       => 'Hilfe für Courtageumzug bei DEMV beantragen.',
                'mail_procedure_id' => self::MAIL_PROCEDURE_ID,
                'reciever'          => MailHelper::RECEIVER_DEMV,
                'subject'           => '{gesellschaftsname} reagiert nicht auf Courtageumzug',
                'last_edit_user_id' => currentUser()->id,
                'last_edit_date'    => Carbon::now(),
                'content'           => '<p>Sehr geehrter DEMV-Innendienst,</p>
<p>die {gesellschaftsname} wurde bereits mehrfach an den beantragten Courtageumzug erinnert.</p>
<p>Bitte setzen Sie sich mit der Gesellschaft in Verbindung, damit der Courtageumzug erfolgreich abgeschlossen werden kann.</p>
<p>Bitte teilen Sie Sie mir innerhalb der n&auml;chsten sieben Tage mit, ob Sie einen Fortschritt erwirken konnten.</p>
<p>Vielen Dank vorab.</p>
<p>{maklersignatur}</p>
<p>&nbsp;</p>
<p>&nbsp;</p>',

            ]
        );
    }

    public function down()
    {
        $this->delete(self::TABLE_MAIL_TEMPLATE,
            'mail_procedure_id = :procedureId',
            [
                'procedureId' => self::MAIL_PROCEDURE_ID,
            ]
        );

        $this->delete(self::TABLE_MAIL_PROCEDURE,
            'id = :procedureId and name = :name',
            [
                'procedureId' => self::MAIL_PROCEDURE_ID,
                'name'        => 'DEMV- Hilfe für Courtageumzug bei DEMV beantragen',
            ]
        );

        return true;
    }
}
