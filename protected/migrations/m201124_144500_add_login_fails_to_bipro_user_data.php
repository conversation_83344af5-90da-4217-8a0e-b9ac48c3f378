<?php

class m201124_144500_add_login_fails_to_bipro_user_data extends CDbMigration
{
    private const TABLE                  = 'bipro_user_data';
    private const COLUMN_LOGIN_FAILES    = 'count_login_failes';
    private const COLUMN_LAST_LOGIN_FAIL = 'last_login_fail_at';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN_LOGIN_FAILES, 'int default 0');
        $this->addColumn(self::TABLE, self::COLUMN_LAST_LOGIN_FAIL, 'datetime default null');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_LOGIN_FAILES);
        $this->dropColumn(self::TABLE, self::COLUMN_LAST_LOGIN_FAIL);

        return true;
    }
}
