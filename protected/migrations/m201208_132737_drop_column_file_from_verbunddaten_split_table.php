<?php

use Courtageerfassung\Abrechnungstool\Verbunddaten\Models\VerbunddatenSplit;

class m201208_132737_drop_column_file_from_verbunddaten_split_table extends CDbMigration
{
    public function up(): void
    {
        $this->dropColumn(VerbunddatenSplit::model()->tableName(), 'file');
    }

    public function down(): bool
    {
        $this->addColumn(VerbunddatenSplit::model()->tableName(), 'file', 'string');

        return true;
    }
}
