<?php

class m201214_083457_create_table_abrechnungstool_postfach_log extends CreateTableMigration
{
    public $tableName = 'abrechnungstool_postfach_log';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'file_id'              => 'int unsigned not null',
            'insurance_company_id' => 'int unsigned not null',
            'status'               => 'int',
            'message'              => 'string',
            'create_datetime'      => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
