<?php

class m210107_072000_verbesserungsvorschlaege_menu_item extends CDbMigration
{
    private const MENU    = 'Verbesserungsvorschläge';
    private const MENU_ID = 42;
    private const TITLE   = 'Wunschliste';

    public function up(): void
    {
        $this->insert('system_menus', [
            'id'            => self::MENU_ID,
            'title'         => self::MENU,
            'active'        => 1,
            'menu_group_id' => 1,
            'admin_only'    => 0,
            'sort'          => 30,
            'icon'          => 'fa fa-comment-lines'
        ]);
        $this->insert('system_menu_items', [
            'menu_id'                    => self::MENU_ID,
            'title'                      => self::TITLE,
            'active'                     => 1,
            'url'                        => '/home/<USER>',
            'target'                     => '_top',
            'exclude_testuser'           => 0,
            'admin_only'                 => 0,
            'sort'                       => 10,
            'exclude_insurancecompanies' => 0,
            'roles'                      => '1,2,3,4,5,6,7,8,9',
        ]);
    }

    public function down(): bool
    {
        $this->delete('system_menu_items', 'title = "' . self::TITLE . '"');
        $this->delete('system_menus', 'id = ' . self::MENU_ID);

        return true;
    }
}
