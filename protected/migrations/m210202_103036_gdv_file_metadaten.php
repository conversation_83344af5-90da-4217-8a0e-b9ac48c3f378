<?php

class m210202_103036_gdv_file_metadaten extends CreateTableMigration
{
    public $tableName = 'gdv_file_metadaten';

    public function getColumns(): array
    {
        return [
            'id'               => 'pk',
            'vunummer'         => 'varchar(5)',
            'vermittlernummer' => 'varchar(17)',
            'vertragsnummer'   => 'varchar(20)',
            'schadennummer'    => 'varchar(20)',
            'satzart'          => 'varchar(2)',
            'sparte'           => 'varchar(3)',
            'gdv_file_id'      => 'integer',
        ];
    }

    public function addForeignKeys(): void
    {
        $this->addOwnFk('gdv_file', 'gdv_file_id', 'gdv_file', 'CASCADE', 'CASCADE');
    }
}
