<?php

class m210208_141827_company_classification extends CDbMigration
{
    private const TABLE = 'insurance_company';

    public function up(): void
    {
        $this->addColumn(self::TABLE, 'is_versicherungsgesellschaft', 'bool NOT NUll DEFAULT 1');
        $this->addColumn(self::TABLE, 'is_assekuradeur', 'bool NOT NUll DEFAULT 0');
        $this->addColumn(self::TABLE, 'is_bank', 'bool NOT NUll DEFAULT 0');
        $this->addColumn(self::TABLE, 'is_sonstige', 'bool NOT NUll DEFAULT 0');

        // Neue Gesellschaften sollen default false sein:
        $this->alterColumn(self::TABLE, 'is_versicherungsgesellschaft', 'bool NOT NUll DEFAULT 0');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, 'is_versicherungsgesellschaft');
        $this->dropColumn(self::TABLE, 'is_assekuradeur');
        $this->dropColumn(self::TABLE, 'is_bank');
        $this->dropColumn(self::TABLE, 'is_sonstige');

        return true;
    }
}
