<?php

class m210209_140105_Create_OAuth_Connection_Table extends CDbMigration
{
    private const TABLE_OAUTH2_CONNECTION           = 'oauth2_connections';
    private const OAUTH_CONNECTION_TO_USER          = 'oauth_connection_to_user';
    private const TABLE_USER_SMTP_SETTINGS          = 'user_smtp_settings';
    private const COLUMN_OAUTH_2_CONNECTION_ID      = 'oauth2_connection_id';

    public function up(): void
    {
        $this->createTable(self::TABLE_OAUTH2_CONNECTION, [
            'id'                => 'pk',
            'user_id'           => 'int(11) unsigned NOT NULL',
            'provider'          => 'string NOT NULL',
            'access_token'      => 'varchar(1024) NOT NULL',
            'refresh_token'     => 'varchar(1024) NOT NULL',
            'expires_at'        => 'datetime NOT NULL',
            'data'              => 'json',
            'provider_user_id'  => 'string',
            'provider_nickname' => 'string',
            'provider_name'     => 'string',
            'provider_email'    => 'string',
            'provider_avatar'   => 'string',
            'sodium_encrypted'  => 'bool NOT NUll DEFAULT 0',
            'created_at'        => 'datetime NOT NULL',
        ]);

        $this->addForeignKey(
            self::OAUTH_CONNECTION_TO_USER,
            self::TABLE_OAUTH2_CONNECTION,
            'user_id',
            'user',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createIndex('oauth2_connections_userid_index', self::TABLE_OAUTH2_CONNECTION, 'user_id');

        $this->addColumn(self::TABLE_USER_SMTP_SETTINGS, self::COLUMN_OAUTH_2_CONNECTION_ID, 'int(11) unsigned');
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::OAUTH_CONNECTION_TO_USER, self::TABLE_OAUTH2_CONNECTION);

        $this->dropTable(self::TABLE_OAUTH2_CONNECTION);

        $this->dropColumn(self::TABLE_USER_SMTP_SETTINGS, self::COLUMN_OAUTH_2_CONNECTION_ID);

        return true;
    }
}
