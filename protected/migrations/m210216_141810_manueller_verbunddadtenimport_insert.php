<?php

class m210216_141810_manueller_verbunddadtenimport_insert extends CDbMigration
{
    private const TABLE = 'system_menu_items';
    private $title      = 'Manueller Verbunddatenimport';

    public function up(): void
    {
        $this->insert(self::TABLE, [
            'menu_id'                    => 19,
            'title'                      => $this->title,
            'active'                     => 1,
            'regex'                      => '',
            'icon'                       => '',
            'url'                        => '/verbunddaten/manuellerImport',
            'target'                     => '_top',
            'exclude_testuser'           => 1,
            'admin_only'                 => 1,
            'right_id'                   => null,
            'sort'                       => 5,
            'exclude_insurancecompanies' => '',
            'rights'                     => '',
            'roles'                      => '4',
        ]);
    }

    public function down(): bool
    {
        $this->delete(self::TABLE, 'title = "' . $this->title . '"');

        return true;
    }
}
