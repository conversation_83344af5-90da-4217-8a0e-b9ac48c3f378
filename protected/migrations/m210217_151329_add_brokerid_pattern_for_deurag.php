<?php

use Business\Company\Deurag;

class m210217_151329_add_brokerid_pattern_for_deurag extends CDbMigration
{
    public function up(): void
    {
        $this->insert(BrokeridPattern::model()->tableName(), [
            'insurance_company_id' => Deurag::ID,
            'regex'                => '/^(\\d{5})$/',
            'replacement'          => '${1}',
        ]);

        $this->insert(BrokeridPattern::model()->tableName(), [
            'insurance_company_id' => Deurag::ID,
            'regex'                => '/^00000(\\d{5})$/',
            'replacement'          => '00000${1}',
        ]);
    }

    public function down(): bool
    {
        return true;
    }
}
