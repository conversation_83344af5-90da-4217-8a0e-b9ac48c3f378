<?php

declare(strict_types=1);

use Admin\Smtp\Models\SmtpConnectionErrorLog;

class m210303_140847_smtp_connection_error_logs extends CDbMigration
{
    private const SMTP_CONNECTION_ERROR_LOG_TO_USER = 'smtp_connection_error_log_to_user';

    public function up()
    {
        $this->createTable(SmtpConnectionErrorLog::TABLE,
            [
                'id'            => 'pk',
                'user_id'       => 'int(11) unsigned NOT NULL',
                'username'      => 'string NOT NULL',
                'host'          => 'string NOT NULL',
                'email'         => 'string NOT NULL',
                'error_message' => 'string NOT NULL',
                'created_at'    => 'datetime NOT NULL',
            ]);
        $this->addForeignKey(
            self::SMTP_CONNECTION_ERROR_LOG_TO_USER,
            SmtpConnectionErrorLog::TABLE,
            'user_id',
            'user',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down(): bool
    {
        $this->dropForeignKey(
            self::SMTP_CONNECTION_ERROR_LOG_TO_USER,
            SmtpConnectionErrorLog::TABLE
        );
        $this->dropTable(SmtpConnectionErrorLog::TABLE);

        return true;
    }
}
