<?php

class m210309_105553_delete_share_settings_table extends CDbMigration
{
    public function up(): void
    {
        $this->dropTable('share_settings');
    }

    public function down(): bool
    {
        $this->execute('
            CREATE TABLE `share_settings` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `demv_user_ids` text DEFAULT NULL,
              `upload_time` int(11) DEFAULT NULL,
              `warning_time` int(11) DEFAULT NULL,
              `upload_size` int(11) DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8'
        );

        return true;
    }
}
