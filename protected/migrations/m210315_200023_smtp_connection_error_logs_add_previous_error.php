<?php

declare(strict_types=1);

use Admin\Smtp\Models\SmtpConnectionErrorLog;

class m210315_200023_smtp_connection_error_logs_add_previous_error extends CDbMigration
{
    public function up(): void
    {
        $this->addColumn(
            SmtpConnectionErrorLog::TABLE,
            'previous_error_message',
            'string'
        );
    }

    public function down(): bool
    {
        $this->dropColumn(
            SmtpConnectionErrorLog::TABLE,
            'previous_error_message'
        );

        return true;
    }
}
