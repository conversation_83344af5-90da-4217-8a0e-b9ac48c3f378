<?php

class m210317_140657_insurance_company_add_digitalradar_hin<PERSON>s extends CDbMigration
{
    private const TABLE = 'digitaler_maklerbetreuer_insurance_company';

    public function up(): void
    {
        $this->createTable(self::TABLE, [
            'insurance_company_id' => 'int unsigned not null',
            'digitalradar_hinweis' => 'text',
        ]);
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
