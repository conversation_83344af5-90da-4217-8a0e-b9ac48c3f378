<?php

declare(strict_types=1);

use Admin\Smtp\Models\SmtpConnectionErrorLog;
use Components\Mail\SmtpSettings\Exceptions\SmtpExceptionTypes;

class m210318_092148_smtp_connection_error_logs_add_error_type extends CDbMigration
{
    public function up(): void
    {
        $this->addColumn(
            SmtpConnectionErrorLog::TABLE,
            'error_type',
            "string DEFAULT '" . SmtpExceptionTypes::UNKNOWN . "'"
        );
    }

    public function down(): bool
    {
        $this->dropColumn(
            SmtpConnectionErrorLog::TABLE,
            'error_type'
        );

        return true;
    }
}
