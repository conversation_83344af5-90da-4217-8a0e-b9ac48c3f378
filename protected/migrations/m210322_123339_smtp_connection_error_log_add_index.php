<?php

use Admin\Smtp\Models\SmtpConnectionErrorLog;

class m210322_123339_smtp_connection_error_log_add_index extends CDbMigration
{
    const SMTP_CONNECTION_ERROR_LOG_INDEX = 'smtp_connection_error_log_index';

    public function up(): void
    {
        $this->createIndex(self::SMTP_CONNECTION_ERROR_LOG_INDEX, SmtpConnectionErrorLog::TABLE, ['host', 'created_at']);
    }

    public function down(): bool
    {
        $this->dropIndex(self::SMTP_CONNECTION_ERROR_LOG_INDEX, SmtpConnectionErrorLog::TABLE);

        return true;
    }
}
