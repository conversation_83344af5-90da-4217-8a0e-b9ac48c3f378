<?php

/**
 * Class m210324_133526_add_right_zugangsdaten_postfach_verwalten
 */
final class m210324_133526_add_right_zugangsdaten_postfach_ver<PERSON>ten extends CDbMigration
{
    private const TABLE_RIGHTS      = 'rights';
    private const TABLE_ROLE_RIGHTS = 'user_role_rights';
    private const TABLE_USER_RIGHTS = 'user_rights';
    private const NAME              = 'Zugangsdaten im Postfach verwalten';

    /**
     *
     */
    public function up(): void
    {
        $this->insert(
            self::TABLE_RIGHTS,
            [
                'id'          => Rights::BIPRO_POSTFACH_EINSTELLUNGEN_ANDERE_NUTZER,
                'name'        => self::NAME,
                'description' => 'Es dürfen die BIPRO-Logindaten für andere Nutzer der Firma verwaltet werden',
            ]
        );

        foreach ($this->getUserRoles() as $userRoleId) {
            $this->insert(
                self::TABLE_ROLE_RIGHTS,
                [
                    'user_role_id' => $userRoleId,
                    'right_id'     => Rights::BIPRO_POSTFACH_EINSTELLUNGEN_ANDERE_NUTZER,
                ]
            );
        }

        $userIds = UserRights::model()->getDbConnection()->createCommand()
                             ->selectDistinct('user_id')
                             ->from(UserRights::model()->tableName())
                             ->where('`right` = :right', [':right' => Rights::BIPRO_POSTFACH])
                             ->queryColumn();

        if (empty($userId)) {
            return;
        }

        $values = array_map(static function ($userId): string {
            return sprintf('(%s,%s)', $userId, Rights::BIPRO_POSTFACH_EINSTELLUNGEN_ANDERE_NUTZER);
        }, $userIds);

        UserRights::model()->getDbConnection()
                  ->createCommand(
                      sprintf('insert into %s (user_id, `right`) values %s',
                              UserRights::model()->tableName(),
                              implode(',', $values)
                      ))
                  ->execute();
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $criteria = new CDbCriteria();
        $criteria->compare('name', self::NAME);
        $right = Rights::model()->find($criteria);

        if ($right === null) {
            return true;
        }

        $this->delete(
            self::TABLE_ROLE_RIGHTS,
            'right_id = :right',
            [
                ':right' => $right->id
            ]
        );

        $this->delete(
            self::TABLE_USER_RIGHTS,
            '`right` = :right',
            [
                ':right' => $right->id
            ]
        );

        $this->delete(
            self::TABLE_RIGHTS,
            sprintf('name = "%s"', self::NAME)
        );

        return true;
    }

    /**
     * @return int[]
     */
    private function getUserRoles(): array
    {
        return [
            UserRole::BROKER,
            UserRole::ADMIN,
            UserRole::MAINBROKER,
            UserRole::BACKOFFICE,
            UserRole::TESTUSER,
        ];
    }
}
