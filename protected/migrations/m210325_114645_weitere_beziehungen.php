<?php

use Demv\Werte\Person\Beziehung\<PERSON><PERSON><PERSON>lich\FirmaVonGesellschafter;
use Demv\Werte\Person\Beziehung\<PERSON>ruflich\Gesellschafter;
use Demv\Werte\Person\Beziehung\Be<PERSON><PERSON>lich\Hausverwaltung;
use Demv\Werte\Person\Beziehung\<PERSON><PERSON><PERSON>lich\ImmobilienVerwaltetVon;
use Demv\Werte\Person\Beziehung\Beruflich\Muttergesellschaft;
use Demv\Werte\Person\Beziehung\Beruflich\Tochtergesellschaft;
use Demv\Werte\Person\Beziehung\BeziehungsTypInterface;
use Demv\Werte\Person\Beziehung\Privat\Pflegeeltern;
use Demv\Werte\Person\Beziehung\Privat\Pflegekind;

class m210325_114645_weitere_beziehungen extends CDbMigration
{
    const TABLE = 'client_relation_type';

    public function up()
    {
        $this->insertBeziehungen(new Muttergesellschaft(), new Tochtergesellschaft(), ClientRelationType::CATEGORY_WORK);
        $this->insertBeziehungen(new Pflegeeltern(), new Pflegekind(), ClientRelationType::CATEGORY_PRIVATE);
        $this->insertBeziehungen(new Hausverwaltung(), new ImmobilienVerwaltetVon(), ClientRelationType::CATEGORY_WORK);
        $this->insertBeziehungen(new Gesellschafter(), new FirmaVonGesellschafter(), ClientRelationType::CATEGORY_WORK);
        $this->update(self::TABLE, ['text' => '<b>{kunde2}</b> ist Gesellschafter von <b>{kunde1}</b>'], 'id = ' . FirmaVonGesellschafter::ID);
        $this->update(self::TABLE, ['text' => '<b>{kunde2}</b> verwaltet die Immobilien von <b>{kunde1}</b>'], 'id = ' . ImmobilienVerwaltetVon::ID);
    }

    private function insertBeziehungen(BeziehungsTypInterface $beziehung1, BeziehungsTypInterface $beziehung2, int $typ): void
    {
        $this->insert(self::TABLE, [
            'id'          => $beziehung1->getId(),
            'name'        => $beziehung1->getName(),
            'category_id' => $typ
        ]);

        $this->insert(self::TABLE, [
            'id'                  => $beziehung2->getId(),
            'name'                => $beziehung2->getName(),
            'related_relation_id' => $beziehung1->getId(),
            'category_id'         => $typ
        ]);

        $this->update(self::TABLE, ['related_relation_id' => $beziehung2->getId()], 'id = ' . $beziehung1->getId());
    }

    public function down()
    {
        $ids = [
            Muttergesellschaft::ID,
            Tochtergesellschaft::ID,
            Pflegekind::ID,
            Pflegeeltern::ID,
            Gesellschafter::ID,
            FirmaVonGesellschafter::ID,
            Hausverwaltung::ID, ImmobilienVerwaltetVon::ID
        ];

        foreach ($ids as $id) {
            $this->delete(self::TABLE, 'id = ' . $id);
        }

        return true;
    }
}
