<?php

class m210412_111324_add_reg_court_and_manager_to_agency extends CDbMigration
{
    private const TABLE  = 'agency';
    private const COLUMN_REG_COURT = 'reg_court';
    private const COLUMN_MANAGER = 'manager';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN_REG_COURT, 'string');
        $this->addColumn(self::TABLE, self::COLUMN_MANAGER, 'string');

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN_REG_COURT);
        $this->dropColumn(self::TABLE, self::COLUMN_MANAGER);

        return true;
    }
}
