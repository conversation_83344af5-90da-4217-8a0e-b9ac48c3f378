<?php

use Business\Company\Deurag;

class m210506_124827_neue_formate_der_vermittlernummern_deurag extends CDbMigration
{
    public function up(): void
    {
        $this->insert(
            BrokeridPattern::model()->tableName(),
            [
                'insurance_company_id' => Deurag::ID,
                'regex'                => '/^0{4,6}(\\d{5,6})/',
                'replacement'          => '${1}',
            ]
        );

        $this->insert(
            BrokeridPattern::model()->tableName(),
            [
                'insurance_company_id' => Deurag::ID,
                'regex'                => '/^0{4,6}(\\d{5,6})/',
                'replacement'          => '0000${1}',
            ]
        );

        $this->insert(
            BrokeridPattern::model()->tableName(),
            [
                'insurance_company_id' => Deurag::ID,
                'regex'                => '/^0{4,6}(\\d{5,6})/',
                'replacement'          => '00000${1}',
            ]
        );

        $this->insert(
            BrokeridPattern::model()->tableName(),
            [
                'insurance_company_id' => Deurag::ID,
                'regex'                => '/^0{4,6}(\\d{5,6})/',
                'replacement'          => '000000${1}',
            ]
        );
        $this->insert(
            BrokeridPattern::model()->tableName(),
            [
                'insurance_company_id' => Deurag::ID,
                'regex'                => '/^(\\d{5,6})$/',
                'replacement'          => '${1}',
            ]
        );
    }

    public function down(): bool
    {
        $this->delete(BrokeridPattern::model()->tableName(), 'insurance_company_id=' . Deurag::ID);

        return true;
    }
}
