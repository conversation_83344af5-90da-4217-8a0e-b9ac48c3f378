<?php

class m210507_081843_risikodemver_privat extends CDbMigration
{
    private const MENU_ID = 23;
    private const TITLE   = 'RisikoDEMVer';

    public function up(): void
    {
        $this->insert('system_menu_items', [
            'menu_id'                    => self::MENU_ID,
            'title'                      => self::TITLE,
            'active'                     => 1,
            'url'                        => '/sach-privat/risikodemver',
            'target'                     => '_top',
            'exclude_testuser'           => 0,
            'admin_only'                 => 0,
            'sort'                       => 12,
            'exclude_insurancecompanies' => 0,
            'roles'                      => '1,2,3,4,5,6,7,8,9',
        ]);
    }

    public function down(): bool
    {
        $this->delete('system_menu_items', 'title = "' . self::TITLE . '"');

        return true;
    }
}
