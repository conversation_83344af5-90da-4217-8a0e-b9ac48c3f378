<?php

class m210520_083731_agency_files_company_id extends CDbMigration
{
    private const TABLE  = 'agency_files';
    private const COLUMN = 'insurance_company_id';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned');

        $this->addForeignKey('fk_agency_files_company', self::TABLE, self::COLUMN, 'insurance_company', 'id', 'set null', 'cascade');
    }

    public function down(): bool
    {
        $this->dropForeignKey('fk_agency_files_company', self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
