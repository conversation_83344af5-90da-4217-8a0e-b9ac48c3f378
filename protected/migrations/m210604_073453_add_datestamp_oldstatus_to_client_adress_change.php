<?php

class m210604_073453_add_datestamp_oldstatus_to_client_adress_change extends CDbMigration
{
    private const TABLE                       = 'client_address';
    private const COLUMN_IS_ALTE_HAUPTADRESSE = 'is_alte_hauptadresse';
    private const COLUMN_CREATED_AT           = 'created_at';
    private const COLUMN_UPDATED_AT           = 'updated_at';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN_IS_ALTE_HAUPTADRESSE, 'bool DEFAULT 0');
        $this->addColumn(self::TABLE, self::COLUMN_CREATED_AT, 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP');
        $this->addColumn(self::TABLE, self::COLUMN_UPDATED_AT, 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_IS_ALTE_HAUPTADRESSE);
        $this->dropColumn(self::TABLE, self::COLUMN_CREATED_AT);
        $this->dropColumn(self::TABLE, self::COLUMN_UPDATED_AT);

        return true;
    }
}
