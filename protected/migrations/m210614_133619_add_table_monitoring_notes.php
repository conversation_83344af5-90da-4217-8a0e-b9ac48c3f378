<?php

class m210614_133619_add_table_monitoring_notes extends CDbMigration
{
    private const TABLE = 'bipro_monitoring_notes';

    public function up(): void
    {
        $this->createTable(
            self::TABLE,
            [
                'id'         => 'pk',
                'data'       => 'longtext NOT NULL',
                'created_at' => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
                'updated_at' => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP'
            ]
        );
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
