<?php

class m210629_130526_add_client_address_hide_flag extends CDbMigration
{
    private const TABLE       = 'client_address';
    private const COLUMN_HIDE = 'hide';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN_HIDE, 'bool DEFAULT 0 AFTER is_alte_hauptadresse');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_HIDE);

        return true;
    }
}
