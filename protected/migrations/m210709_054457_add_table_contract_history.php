<?php

class m210709_054457_add_table_contract_history extends CDbMigration
{
    private const TABLE          = 'contract_history';
    private const FK_FROM_CLIENT = 'fk_' . self::TABLE . '_from_client';
    private const FK_TO_CLIENT   = 'fk_' . self::TABLE . '_to_client';
    private const FK_CONTRACT_ID = 'fk_' . self::TABLE . '_contract_id';

    public function up(): void
    {
        $this->createTable(
            self::TABLE,
            [
                'id'             => 'pk',
                'from_client_id' => 'int unsigned NOT NULL',
                'to_client_id'   => 'int unsigned NOT NULL',
                'contract_id'    => 'int unsigned NOT NULL',
                'created_at'     => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
                'updated_at'     => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
            ]
        );

        $this->addForeignKey(
            self::FK_FROM_CLIENT,
            self::TABLE,
            'from_client_id',
            'client',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            self::FK_TO_CLIENT,
            self::TABLE,
            'to_client_id',
            'client',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            self::FK_CONTRACT_ID,
            self::TABLE,
            'contract_id',
            'contracts',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
