<?php

class m210712_075737_add_gesellschaftsdokumente_right extends CDbMigration
{
    private const TABLE       = 'rights';
    private const NAME        = 'Dokumente zu Gesellschaften verwalten';
    private const DESCRIPTION = 'Es können Dokumente auf Firmenebene zu einer Gesellschaft eingesehen, hochgeladen und gelöscht werden.';

    public function up(): void
    {
        $this->down();
        $this->insert(self::TABLE, [
            'id'          => Rights::FIRMENDOKUMENTE_ZU_GESELLSCHAFT,
            'name'        => self::NAME,
            'description' => self::DESCRIPTION
        ]);
    }

    public function down(): bool
    {
        $this->delete(self::TABLE, 'id = ' . Rights::FIRMENDOKUMENTE_ZU_GESELLSCHAFT);

        return true;
    }
}
