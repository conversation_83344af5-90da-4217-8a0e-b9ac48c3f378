<?php

use Business\Company\Condor;
use Business\Company\Kravag;
use Business\Company\RundV;

class m210713_073858_weitere_vermittlernummerformate extends CDbMigration
{
    private const TABLE                       = 'brokerid_pattern';
    private const COLUMN_INSURANCE_COMPANY_ID = 'insurance_company_id';
    private const COLUMN_REGEX                = 'regex';
    private const COLUMN_REPLACEMENT          = 'replacement';

    private const COMPANIES = [
        Kravag::ALLGEMEIN,
        Kravag::LOGISTIC,
        RundV::ALLGEMEIN,
        Condor::ALLGEMEIN,
    ];

    public function up(): void
    {
        foreach (self::COMPANIES as $companyId) {
            $this->insert(
                self::TABLE,
                [
                    self::COLUMN_INSURANCE_COMPANY_ID => $companyId,
                    self::COLUMN_REGEX                => '/^4050(\\d{5})$/',
                    self::COLUMN_REPLACEMENT          => '${1}',
                ]
            );
        }
    }

    public function down(): bool
    {
        foreach (self::COMPANIES as $companyId) {
            $this->delete(
                self::TABLE,
                'insurance_company_id = ' . $companyId . ' and ' .
                self::COLUMN_REGEX . "= '/^4050(\\d{5})$/'" . ' and ' .
                self::COLUMN_REPLACEMENT . " = '\$\{1\}'"
            );
        }

        return true;
    }
}
