<?php

class m210804_132740_update_country_macedonia extends CDbMigration
{
    private const TABLE = 'country';

    private const MACEDONIA_OLD_NAME = 'Mazedonien';
    private const MACEDONIA_NEW_NAME = 'Nordmazedonien';

    private const MACEDONIA_NAMES = [
        'old' => [
            'name'                 => self::MACEDONIA_OLD_NAME,
            'countries'            => 'Macedonia',
            'countries_long'       => 'The Former Yugoslav Republic of Macedonia',
            'nationality_name_ger' => 'Mazedonisch',
        ],
        'new' => [
            'name'                 => self::MACEDONIA_NEW_NAME,
            'countries'            => 'North Macedonia',
            'countries_long'       => 'Republic of North Macedonia',
            'nationality_name_ger' => 'Nordmazedonisch',
        ]
    ];

    public function up(): void
    {
        $this->setDbConnection(Yii::app()->common_data);

        $this->update(self::TABLE,
                      self::MACEDONIA_NAMES['new'],
                      'name = "' . self::MACEDONIA_OLD_NAME . '"'
        );
    }

    public function down(): bool
    {
        $this->update(self::TABLE,
                      self::MACEDONIA_NAMES['old'],
                      'name = "' . self::MACEDONIA_NEW_NAME . '"'
        );

        return true;
    }
}
