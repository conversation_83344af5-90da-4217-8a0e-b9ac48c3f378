<?php

class m210806_080605_user_status_log extends CreateTableMigration
{
    public $tableName = 'user_status_log';

    public function getColumns()
    {
        return [
            'id'              => 'pk',
            'user_id'         => 'int unsigned not null',
            'old_status'      => 'int not null',
            'new_status'      => 'int not null',
            'create_datetime' => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
