<?php

class m210810_084935_create_table_auth_codes extends CDbMigration
{
    private const TABLE     = 'auth_codes';
    private const TABLENAME = self::TABLE;

    public function safeUp(): bool
    {
        $this->createTable(
            self::TABLENAME,
            [
                'id'         => 'pk',
                'user_id'    => 'integer unsigned not null',
                'code'       => 'string not null',
                'device'     => 'text',
                'tries'      => 'integer unsigned not null default 0',
                'created_at' => 'timestamp not null default CURRENT_TIMESTAMP',
                'used_at'    => 'timestamp null default null',
            ]
        );

        $this->addForeignKey(
            'auth_codes_user_fk',
            'auth_codes',
            'user_id',
            'user',
            'id'
        );

        return true;
    }

    public function safeDown(): bool
    {
        $this->dropTable(self::TABLENAME);

        return true;
    }
}
