<?php

class m210810_115640_add_2fa_cols_user_table extends CDbMigration
{
    private const TABLE                       = 'user';
    private const COL_2FA_ACTIVE              = 'is_2fa_active';
    private const COL_EMAIL_VALIDATED         = 'is_email_validated';
    private const COL_EMAIL_VERIFICATION_HASH = 'email_validation_hash';
    private const COL_2FA_ACTIVATION_DATE     = 'activation_date_2fa';
    private const COL_EMAIL_VALIDATION_DATE   = 'email_validated_date';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COL_2FA_ACTIVE, 'bool DEFAULT 0');
        $this->addColumn(self::TABLE, self::COL_EMAIL_VALIDATED, 'bool DEFAULT 0');
        $this->addColumn(self::TABLE, self::COL_EMAIL_VERIFICATION_HASH, 'string');
        $this->addColumn(self::TABLE, self::COL_2FA_ACTIVATION_DATE, 'datetime');
        $this->addColumn(self::TABLE, self::COL_EMAIL_VALIDATION_DATE, 'datetime');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COL_2FA_ACTIVE);
        $this->dropColumn(self::TABLE, self::COL_EMAIL_VALIDATED);
        $this->dropColumn(self::TABLE, self::COL_EMAIL_VERIFICATION_HASH);
        $this->dropColumn(self::TABLE, self::COL_2FA_ACTIVATION_DATE);
        $this->dropColumn(self::TABLE, self::COL_EMAIL_VALIDATION_DATE);

        return true;
    }
}
