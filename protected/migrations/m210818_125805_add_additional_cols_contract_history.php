<?php

class m210818_125805_add_additional_cols_contract_history extends CDbMigration
{
    private const TABLE                 = 'contract_history';
    private const COLUMN_OPERATION_TYPE = 'operation_type';
    private const COLUMN_FILES_AFFECTED = 'files_affected';
    private const COLUMN_NOTES_AFFECTED = 'notes_affected';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN_OPERATION_TYPE, 'tinyint AFTER contract_id');
        $this->addColumn(self::TABLE, self::COLUMN_FILES_AFFECTED, 'bool AFTER operation_type');
        $this->addColumn(self::TABLE, self::COLUMN_NOTES_AFFECTED, 'bool AFTER files_affected');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_OPERATION_TYPE);
        $this->dropColumn(self::TABLE, self::COLUMN_FILES_AFFECTED);
        $this->dropColumn(self::TABLE, self::COLUMN_NOTES_AFFECTED);

        return true;
    }
}
