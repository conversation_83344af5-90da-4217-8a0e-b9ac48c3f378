<?php

use Business\Company\RundV;

class m210826_091531_add_new_r_v_krankenversicherung_prefix extends CDbMigration
{
    private const TABLE                       = 'brokerid_pattern';
    private const COLUMN_INSURANCE_COMPANY_ID = 'insurance_company_id';
    private const COLUMN_REGEX                = 'regex';
    private const COLUMN_REPLACEMENT          = 'replacement';

    public function up(): void
    {
        $this->insert(
            self::TABLE,
            [
                self::COLUMN_INSURANCE_COMPANY_ID => RundV::LEBEN,
                self::COLUMN_REGEX                => '/^427(\\d{6})$/',
                self::COLUMN_REPLACEMENT          => '427${1}',
            ]
        );
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            'insurance_company_id = ' . RundV::LEBEN . ' AND ' .
            self::COLUMN_REGEX . " = '/^427(\\\d{6})$/' AND " .
            self::COLUMN_REPLACEMENT . " = '427\${1}'"
        );

        return true;
    }
}
