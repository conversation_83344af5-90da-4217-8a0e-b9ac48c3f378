<?php

class m210907_095102_product_combo_add_abbreviation extends CDbMigration
{
    private const TABLE               = 'product_combo';
    private const COLUMN_ABBREVIATION = 'abbreviation';

    public function up(): bool
    {
        try {
            $this->addColumn(self::TABLE, self::COLUMN_ABBREVIATION, 'string AFTER name');
        } catch (Exception $e) {
            // NOTE: the test data for this table is exported from the DB in its
            //       current state; so this might fail if it already exists...
            // TODO: we should probably fix this -^ somehow...
        }

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_ABBREVIATION);

        return true;
    }
}
