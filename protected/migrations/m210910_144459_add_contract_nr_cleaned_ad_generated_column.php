<?php

class m210910_144459_add_contract_nr_cleaned_ad_generated_column extends CDbMigration
{
    const TABLE      = 'contracts';
    const NEW_COLUMN = 'contract_nr_cleaned';
    const INDEX_NAME = 'contracts_contract_nr_cleaned_idx';

    public function up(): void
    {
        $query = sprintf('alter table %s add %s VARCHAR(60) GENERATED ALWAYS AS (REGEXP_REPLACE(contract_nr, "[^[:alnum:]]", "")) PERSISTENT after contract_nr;',
                         self::TABLE,
                         self::NEW_COLUMN
        );
        $this->execute($query);
        $this->createIndex(self::INDEX_NAME, self::TABLE, self::NEW_COLUMN);
    }

    public function down(): bool
    {
        $this->dropIndex(self::INDEX_NAME, self::TABLE);
        $this->dropColumn(self::TABLE, self::NEW_COLUMN);

        return true;
    }
}
