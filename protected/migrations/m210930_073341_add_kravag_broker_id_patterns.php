<?php

use Business\Company\Kravag;

class m210930_073341_add_kravag_broker_id_patterns extends CDbMigration
{
    private const TABLE                       = 'brokerid_pattern';
    private const COLUMN_INSURANCE_COMPANY_ID = 'insurance_company_id';
    private const COLUMN_REGEX                = 'regex';
    private const COLUMN_REPLACEMENT          = 'replacement';

    public function up(): bool
    {
        $this->insertKravagPattern('/(\\d{5})/', '0${1}');

        $this->insertKravagPattern('/([1-9]\\d{5})/', '${1}');

        $this->insertKravagPattern('/(\\d{5})/', '7700${1}');

        $this->insertKravagPattern('/(\\d{5})/', '4050{1}');

        return true;
    }

    private function insertKravagPattern(string $regex, string $replacement): void
    {
        $this->insert(
            self::TABLE,
            [
                self::COLUMN_INSURANCE_COMPANY_ID => Kravag::ALLGEMEIN,
                self::COLUMN_REGEX                => $regex,
                self::COLUMN_REPLACEMENT          => $replacement,
            ]
        );
    }

    public function down(): bool
    {
        $this->deleteKravagPattern('/(\\d{5})/', '0${1}');

        $this->deleteKravagPattern('/([1-9]\\d{5})/', '${1}');

        $this->deleteKravagPattern('/(\\d{5})/', '7700${1}');

        $this->deleteKravagPattern('/(\\d{5})/', '4050{1}');

        return true;
    }

    private function deleteKravagPattern(string $regex, string $replacement): void
    {
        $this->delete(
            self::TABLE,
            'insurance_company_id = ' . Kravag::ALLGEMEIN . ' AND ' .
            self::COLUMN_REGEX . " = $regex AND " .
            self::COLUMN_REPLACEMENT . " = $replacement"
        );
    }
}
