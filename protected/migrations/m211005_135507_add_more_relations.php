<?php

use Demv\Werte\Person\Beziehung\<PERSON>ru<PERSON>lich\Angehoeriger;
use Demv\Werte\Person\Beziehung\<PERSON>ruflich\Aufsichtsrat;
use Demv\Werte\Person\Beziehung\Beruflich\FirmaAufsichtsrat;
use Demv\Werte\Person\Beziehung\Beru<PERSON>lich\FirmaVorstand;
use Demv\Werte\Person\Beziehung\Beruflich\InhaberGeschaeftsfuehrer;
use Demv\Werte\Person\Beziehung\Beruflich\KommanditgesellschaftKommanditist;
use Demv\Werte\Person\Beziehung\Beruflich\KommanditgesellschaftKomplementaer;
use Demv\Werte\Person\Beziehung\Beruflich\Kommanditist;
use Demv\Werte\Person\Beziehung\Beruflich\Komplementaer;
use Demv\Werte\Person\Beziehung\Beruflich\VorstandVonFirma;
use Demv\Werte\Person\Beziehung\BeziehungsTypInterface;
use Demv\Werte\Person\Beziehung\Familiaer\CousinCousine;

class m211005_135507_add_more_relations extends CDbMigration
{
    private const TABLE = 'client_relation_type';

    public function up(): void
    {
        $this->insertBeziehungen(
            new VorstandVonFirma(),
            new FirmaVorstand(),
            ClientRelationType::CATEGORY_WORK,
            '<b>{kunde1}</b> ist <i>Vorstand</i> von <b>{kunde2}</b>',
            '<b>{kunde1}</b> hat den <i>Vorstand</i> <b>{kunde2}</b>'
        );

        $this->insertBeziehungen(
            new Aufsichtsrat(),
            new FirmaAufsichtsrat(),
            ClientRelationType::CATEGORY_WORK,
            '<b>{kunde1}</b> ist <i>Aufsichtsrat</i> von <b>{kunde2}</b>',
            '<b>{kunde1}</b> hat den <i>Aufsichtsrat</i> <b>{kunde2}</b>'
        );

        $this->insertBeziehungen(
            new Komplementaer(),
            new KommanditgesellschaftKomplementaer(),
            ClientRelationType::CATEGORY_WORK,
            '<b>{kunde1}</b> ist <i>Komplementär</i> von Kommanditgesellschaft <b>{kunde2}</b>',
            'Kommanditgesellschaft <b>{kunde1}</b> hat als <i>Komplementär</i> <b>{kunde2}</b>'
        );

        $this->insertBeziehungen(
            new Kommanditist(),
            new KommanditgesellschaftKommanditist(),
            ClientRelationType::CATEGORY_WORK,
            '<b>{kunde1}</b> ist <i>Kommanditist</i> von Kommanditgesellschaft <b>{kunde2}</b>',
            'Kommanditgesellschaft <b>{kunde1}</b> hat als <i>Kommanditist</i> <b>{kunde2}</b>'
        );

        $this->insertBeziehungen(
            new Angehoeriger(),
            new InhaberGeschaeftsfuehrer(),
            ClientRelationType::CATEGORY_WORK,
            '<b>{kunde1}</b> ist <i>Angehöriger</i> des Inhabers/Geschäftsführers <b>{kunde2}</b>',
            '<i>Inhabers/Geschäftsführer</i> <b>{kunde1}</b> hat als Angehörige <b>{kunde2}</b>'
        );

        $beziehung = new CousinCousine();
        $this->insert(self::TABLE, [
            'id'                  => $beziehung->getId(),
            'name'                => $beziehung->getName(),
            'category_id'         => ClientRelationType::CATEGORY_FAMILY,
            'related_relation_id' => $beziehung->getId(),
        ]);
    }

    public function down(): bool
    {
        $ids = [
            VorstandVonFirma::ID,
            FirmaVorstand::ID,
            Aufsichtsrat::ID,
            FirmaAufsichtsrat::ID,
            CousinCousine::ID,
            Komplementaer::ID,
            KommanditgesellschaftKomplementaer::ID,
            Kommanditist::ID,
            KommanditgesellschaftKommanditist::ID,
            Angehoeriger::ID,
            InhaberGeschaeftsfuehrer::ID,
        ];

        foreach ($ids as $id) {
            $this->delete(self::TABLE, 'id = ' . $id);
        }

        return true;
    }

    /**
     * @param BeziehungsTypInterface $beziehung1
     * @param BeziehungsTypInterface $beziehung2
     * @param int                    $typ
     * @param string|null            $textBeziehung1
     * @param string|null            $textBeziehung2
     */
    private function insertBeziehungen(
        BeziehungsTypInterface $beziehung1,
        BeziehungsTypInterface $beziehung2,
        int $typ,
        ?string $textBeziehung1 = null,
        ?string $textBeziehung2 = null
    ): void {
        $this->insert(self::TABLE, [
            'id'          => $beziehung1->getId(),
            'name'        => $beziehung1->getName(),
            'category_id' => $typ,
            'text'        => $textBeziehung1,

        ]);

        $this->insert(self::TABLE, [
            'id'                  => $beziehung2->getId(),
            'name'                => $beziehung2->getName(),
            'related_relation_id' => $beziehung1->getId(),
            'category_id'         => $typ,
            'text'                => $textBeziehung2,
        ]);

        $this->update(self::TABLE, ['related_relation_id' => $beziehung2->getId()], 'id = ' . $beziehung1->getId());
    }
}
