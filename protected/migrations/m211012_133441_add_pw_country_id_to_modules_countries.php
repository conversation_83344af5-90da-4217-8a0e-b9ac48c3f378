<?php

class m211012_133441_add_pw_country_id_to_modules_countries extends CDbMigration
{
    const TABLE    = 'modules.innofinance__newkfz_countries';
    const COLUMN   = 'pw_id';
    const ID_PAIRS = [
        19  => 22, // Bosnien und Herzegowina
        27  => 37, // China
        29  => 48, // Dänemark
        31  => 51, // Dominica
        35  => 43, // Elfenbeinküste
        36  => 181, // England (Vereinigtes Königreich)
        79  => 92, // Korea
        100 => 104, // Mazedonien
        118 => 10, // Österreich
        128 => 141, // Rumänien
        129 => 142, // Russland
        134 => 150, // Saudi-Arabien
        149 => 146, // St. Vincent und Grenadinen
        161 => 47, // Tschechien
        169 => 182, // USA
        176 => 16, // Weißrussland
    ];

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int DEFAULT 30');

        $this->execute('UPDATE modules.innofinance__newkfz_countries ik JOIN common_data.country c ON c.name = ik.name SET ik.pw_id = c.id');

        foreach (self::ID_PAIRS as $modulesId => $pwId) {
            $this->execute("UPDATE modules.innofinance__newkfz_countries SET pw_id = $pwId WHERE id = $modulesId");
        }

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
