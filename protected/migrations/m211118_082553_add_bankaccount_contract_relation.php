<?php

class m211118_082553_add_bankaccount_contract_relation extends CDbMigration
{
    private const TABLE          = 'bankaccount_contract_relation';
    private const FK_BANKACCOUNT = 'fk_bcr_bankaccount_id';
    private const FK_CONTRACT    = 'fk_bcr_contract_id';

    public function up(): void
    {
        $this->createTable(
            self::TABLE,
            [
                'id'             => 'pk',
                'bankaccount_id' => 'int NOT NULL',
                'contract_id'    => 'int unsigned NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_BANKACCOUNT,
            self::TABLE,
            'bankaccount_id',
            'bank_account',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            self::FK_CONTRACT,
            self::TABLE,
            'contract_id',
            'contracts',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
