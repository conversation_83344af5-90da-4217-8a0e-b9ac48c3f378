<?php

use Demv\Werte\Sparte\Sparten\Gewerbe;

class m211118_135507_add_gewerbesparten extends CDbMigration
{
    private const TABLE = 'bedarfsanalyse_admin';

    private const PRODUCT_IDS = [
        Gewerbe::BERUFSHAFTPFLICHT,
        Gewerbe::BET<PERSON><PERSON><PERSON><PERSON>FTPFLICHT,
        Gewerbe::CYBER,
        Gewerbe::D_O,
        Gewerbe::RECHTSSCHUTZ,
        Gewerbe::GEBAEUDE,
        Gewerbe::GESCHAEFTSINHALT,
        Gewerbe::VERMOEGENSCHADENHAFTPFLICHT,
    ];

    public function up(): void
    {
        foreach (self::PRODUCT_IDS as $id) {
            $this->insert(self::TABLE, [
                'product_id'            => $id,
                'enabled'               => 1,
                'last_state_changed_at' => date('Y-m-d H:i:s'),
            ]);
        }
    }

    public function down(): bool
    {
        foreach (self::PRODUCT_IDS as $id) {
            $this->delete(self::TABLE, 'product_id = ' . $id);
        }

        return true;
    }
}
