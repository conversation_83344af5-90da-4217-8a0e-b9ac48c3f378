<?php

class m211122_155027_diensthaftpflicht_verschieben extends CDbMigration
{
    public function up(): void
    {
        $criteria = new CDbCriteria();
        $criteria->addCondition('name like :name');
        $criteria->params[':name'] = '%Diensthaftpflicht%';
        $diensthaftpflicht         = ProductCombo::model()->find($criteria);
        if ($diensthaftpflicht !== null) {
            $diensthaftpflicht->level3_id = $diensthaftpflicht->level2_id;
            $diensthaftpflicht->level2_id = 157; //Die level2 ID der Privathaftpflicht
            $diensthaftpflicht->name      = 'Private Sachversicherungen->Privathaftpflicht->Diensthaftpflicht - DHV';
            $diensthaftpflicht->save(false);
        }
    }

    public function down(): bool
    {
        //hier machen wir nichts
        return true;
    }
}
