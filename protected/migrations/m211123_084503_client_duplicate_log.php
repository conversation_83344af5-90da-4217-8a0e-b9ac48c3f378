<?php

class m211123_084503_client_duplicate_log extends CreateTableMigration
{
    public $tableName = 'client_duplicate_log';

    public function getColumns(): array
    {
        return [
            'id'                    => 'pk',
            'client_id'             => 'int unsigned not null',
            'merged_with_client_id' => 'int unsigned not null',
            'merged_by_user_id'     => 'int unsigned not null',
            'merged_at'             => 'datetime',
            'finished_at'           => 'datetime',
            'failed_at'             => 'datetime',
            'error'                 => 'string'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('client', 'client_id', 'client', 'CASCADE', 'CASCADE');
        $this->addOwnFk('merged_with_client_id', 'client_id', 'client', 'CASCADE', 'CASCADE');
    }
}
