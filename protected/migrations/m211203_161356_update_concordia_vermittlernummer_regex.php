<?php

use Business\Company\Concordia;

class m211203_161356_update_concordia_vermittlernummer_regex extends CDbMigration
{
    /** @var string */
    private $brokerIdPatternTableName;

    /** @var string[][] */
    private const NEW_PATTERNS = [
        [ 'regex' => '/^(\\d{5})(\\/\\d{3})?$/', 'replacement' => '${1}${2}' ]
    ];

    /** @var string[][] */
    private const OLD_PATTERNS = [
        [ 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}' ],
        [ 'regex' => '/^(\\d{5})\\/000$/', 'replacement' => '${1}/000' ],
        [ 'regex' => '/^(\\d{5})\\/001$/', 'replacement' => '${1}/001' ],
        [ 'regex' => '/^(\\d{5})\\/002$/', 'replacement' => '${1}/002' ]
    ];

    public function __construct()
    {
        $this->brokerIdPatternTableName = BrokeridPattern::model()->tableName();
    }

    public function up(): bool
    {
        $this->initConcordiaPatterns(self::NEW_PATTERNS);

        return true;
    }

    public function down(): bool
    {
        $this->initConcordiaPatterns(self::OLD_PATTERNS);

        return true;
    }

    /**
     * @param array $patterns
     */
    private function initConcordiaPatterns(array $patterns): void
    {
        $this->clearConcordiaPatterns();

        foreach ($patterns as $pattern) {
            $this->addConcordiaPattern($pattern);
        }
    }

    private function clearConcordiaPatterns(): void
    {
        $this->delete(
            $this->brokerIdPatternTableName,
            'insurance_company_id=' . Concordia::ID
        );
    }

    /**
     * @param array $pattern
     */
    private function addConcordiaPattern(array $pattern): void
    {
        $this->insert($this->brokerIdPatternTableName, [
            'insurance_company_id' => Concordia::ID,
            'regex'                => $pattern['regex'],
            'replacement'          => $pattern['replacement'],
        ]);
    }
}
