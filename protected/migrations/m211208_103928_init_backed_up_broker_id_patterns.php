<?php

class m211208_103928_init_backed_up_broker_id_patterns extends CDbMigration
{
    /** @var string */
    private $brokerIdPatternTableName;

    /** @var array */
    private $brokerIdPatterns;

    public function __construct()
    {
        $this->brokerIdPatternTableName = BrokeridPattern::model()->tableName();

        $this->brokerIdPatterns = [
            // WWK Lebensversicherung a.G.
            ['id' => 4, 'insurance_company_id' => 8, 'regex' => '/^Q(\\d{7})$/', 'replacement' => 'Q${1}'],
            ['id' => 5, 'insurance_company_id' => 8, 'regex' => '/^(\\d{7})$/', 'replacement' => '${1}'],

            // HDI Lebensversicherungen AG
            ['id' => 170, 'insurance_company_id' => 10, 'regex' => '/^2280(\\d{6})$/', 'replacement' => '2280${1}'],
            ['id' => 171, 'insurance_company_id' => 10, 'regex' => '/^2270(\\d{6})$/', 'replacement' => '2270${1}'],
            ['id' => 172, 'insurance_company_id' => 10, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'],
            ['id' => 175, 'insurance_company_id' => 10, 'regex' => '/^2120(\\d{6})$/', 'replacement' => '2120${1}'],

            // Gothaer Lebensversicherung AG
            ['id' => 27, 'insurance_company_id' => 11, 'regex' => '/^0(\\d{3})(\\d{4})$/', 'replacement' => '0${1}${2}'],
            ['id' => 28, 'insurance_company_id' => 11, 'regex' => '/^(\\d{3})(\\d{4})$/', 'replacement' => '${1}${2}'],
            ['id' => 29, 'insurance_company_id' => 11, 'regex' => '/^(\\d{3})0(\\d{4})$/', 'replacement' => '${1}0${2}'],
            ['id' => 30, 'insurance_company_id' => 11, 'regex' => '/^(\\d{3})\\-(\\d{4})$/', 'replacement' => '${1}-${2}'],
            ['id' => 31, 'insurance_company_id' => 11, 'regex' => '/^(\\d{3})\\/(\\d{4})$/', 'replacement' => '${1}/${2}'],
            ['id' => 32, 'insurance_company_id' => 11, 'regex' => '/^00(\\d{3})0(\\d{4})$/', 'replacement' => '00${1}0${2}'],
            ['id' => 33, 'insurance_company_id' => 11, 'regex' => '/^0(\\d{3})0(\\d{4})$/', 'replacement' => '0${1}0${2}'],
            ['id' => 34, 'insurance_company_id' => 11, 'regex' => '/^0(\\d{3})\\-(\\d{4})$/', 'replacement' => '0${1}-${2}'],
            ['id' => 35, 'insurance_company_id' => 11, 'regex' => '/^0(\\d{3})\\/(\\d{4})$/', 'replacement' => '0${1}/${2}'],
            ['id' => 36, 'insurance_company_id' => 11, 'regex' => '/^00(\\d{3})\\-(\\d{4})$/', 'replacement' => '00${1}-${2}'],
            ['id' => 37, 'insurance_company_id' => 11, 'regex' => '/^00(\\d{3})\\/(\\d{4})$/', 'replacement' => '00${1}/${2}'],

            // Stuttgarter
            ['id' => 10, 'insurance_company_id' => 22, 'regex' => '/^72\\.(\\d{8})$/', 'replacement' => '72.${1}'],
            ['id' => 11, 'insurance_company_id' => 22, 'regex' => '/^(\\d{8})$/', 'replacement' => '${1}'],
            ['id' => 58, 'insurance_company_id' => 22, 'regex' => '/^72(\\d{8})$/', 'replacement' => '72${1}'],

            // Volkswohl Bund
            ['id' => 1, 'insurance_company_id' => 27, 'regex' => '/^0(\\d{3})00(\\d{3})$/', 'replacement' => '0${1}00${2}'],
            ['id' => 2, 'insurance_company_id' => 27, 'regex' => '/^00(\\d{3})00(\\d{3})$/', 'replacement' => '00${1}00${2}'],
            ['id' => 3, 'insurance_company_id' => 27, 'regex' => '/^(\\d{3})\\/(\\d{3})$/', 'replacement' => '${1}/${2}'],
            ['id' => 59, 'insurance_company_id' => 27, 'regex' => '/^(\\d{3})00(\\d{3})$/', 'replacement' => '${1}00${2}'],

            // Barmenia
            ['id' => 60, 'insurance_company_id' => 30, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'],
            ['id' => 61, 'insurance_company_id' => 30, 'regex' => '/^00(\\d{6})$/', 'replacement' => '00${1}'],

            // Gothaer Krankenversicherung AG
            ['id' => 43, 'insurance_company_id' => 31, 'regex' => '/^(\\d{2})\\.(\\d{3})$/', 'replacement' => '${1}.${2}'],
            ['id' => 44, 'insurance_company_id' => 31, 'regex' => '/^(\\d{2})(\\d{3})$/', 'replacement' => '${1}${2}'],

            // VHV Allgemeine Versicherung AG
            ['id' => 6, 'insurance_company_id' => 44, 'regex' => '/^(\\d{6})(\\d{3})$/', 'replacement' => '${1}${2}'],
            ['id' => 7, 'insurance_company_id' => 44, 'regex' => '/^(\\d{6})-(\\d{3})$/', 'replacement' => '${1}-${2}'],

            // KRAVAG Versicherung AG
            ['id' => -1, 'insurance_company_id' => 47, 'regex' => '/^(405|770)?(\d{5,6})$/', 'replacement' => '${2}'],
//            ['id' => 64, 'insurance_company_id' => 47, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'], // Replaced
//            ['id' => 65, 'insurance_company_id' => 47, 'regex' => '/^770(\\d{6})$/', 'replacement' => '770${1}'], // Replaced
//            ['id' => 71, 'insurance_company_id' => 47, 'regex' => '/^405(\\d{6})$/', 'replacement' => '405${1}'], // Replaced
//            ['id' => 189, 'insurance_company_id' => 47, 'regex' => '/^(\\d{5})$/', 'replacement' => '0${1}'], // Replaced
//            ['id' => 191, 'insurance_company_id' => 47, 'regex' => '/^(\\d{5})$/', 'replacement' => '7700${1}'], // Replaced
//            ['id' => 192, 'insurance_company_id' => 47, 'regex' => '/^(\\d{5})$/', 'replacement' => '4050${1}'], // Replaced
//            ['id' => 190, 'insurance_company_id' => 47, 'regex' => '/^([1-9]\\d{5})$/', 'replacement' => '${1}'], // Unnecessary altogether

            // Basler Lebensversicherungs AG
            ['id' => 135, 'insurance_company_id' => 50, 'regex' => '/^(\\d{3})\\/(\\d{4})$/', 'replacement' => '${1}/${2}'],
            ['id' => 136, 'insurance_company_id' => 50, 'regex' => '/^(\\d{3})(\\d{4})$/', 'replacement' => '${1}${2}'],

            // Ostangler Brandgilde VVaG
            ['id' => 45, 'insurance_company_id' => 52, 'regex' => '/^90(\\d{5})$/', 'replacement' => '90${1}'],
            ['id' => 46, 'insurance_company_id' => 52, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}'],

            // UKV & BBKK
            ['id' => 124, 'insurance_company_id' => 53, 'regex' => '/^180(\\d{6})$/', 'replacement' => '180${1}'],
            ['id' => 125, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})0$/', 'replacement' => '${1}0'],
            ['id' => 126, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})1$/', 'replacement' => '${1}1'],
            ['id' => 127, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})2$/', 'replacement' => '${1}2'],
            ['id' => 128, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})3$/', 'replacement' => '${1}3'],
            ['id' => 129, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})4$/', 'replacement' => '${1}4'],
            ['id' => 130, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})5$/', 'replacement' => '${1}5'],
            ['id' => 131, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})6$/', 'replacement' => '${1}6'],
            ['id' => 132, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})7$/', 'replacement' => '${1}7'],
            ['id' => 133, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})8$/', 'replacement' => '${1}8'],
            ['id' => 134, 'insurance_company_id' => 53, 'regex' => '/^(\\d{6})9$/', 'replacement' => '${1}9'],

            // KS/AUXILIA Rechtsschutzverischerungs AG
            ['id' => 47, 'insurance_company_id' => 62, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'],
            ['id' => 48, 'insurance_company_id' => 62, 'regex' => '/^10 (\\d{6})$/', 'replacement' => '10 ${1}'],
            ['id' => 49, 'insurance_company_id' => 62, 'regex' => '/^20 (\\d{6})$/', 'replacement' => '20 ${1}'],
            ['id' => 50, 'insurance_company_id' => 62, 'regex' => '/^30 (\\d{6})$/', 'replacement' => '30 ${1}'],
            ['id' => 51, 'insurance_company_id' => 62, 'regex' => '/^40 (\\d{6})$/', 'replacement' => '40 ${1}'],
            ['id' => 53, 'insurance_company_id' => 62, 'regex' => '/^60 (\\d{6})$/', 'replacement' => '60 ${1}'],
            ['id' => 54, 'insurance_company_id' => 62, 'regex' => '/^71 (\\d{6})$/', 'replacement' => '71 ${1}'],
            ['id' => 55, 'insurance_company_id' => 62, 'regex' => '/^81 (\\d{6})$/', 'replacement' => '81 ${1}'],
            ['id' => 56, 'insurance_company_id' => 62, 'regex' => '/^82 (\\d{6})$/', 'replacement' => '82 ${1}'],
            ['id' => 57, 'insurance_company_id' => 62, 'regex' => '/^99 (\\d{6})$/', 'replacement' => '99 ${1}'],

            // die Bayerische
            ['id' => 8, 'insurance_company_id' => 65, 'regex' => '/^1601(\\d{6})$/', 'replacement' => '1601${1}'],
            ['id' => 9, 'insurance_company_id' => 65, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'],
            ['id' => 17, 'insurance_company_id' => 65, 'regex' => '/^6801(\\d{6})$/', 'replacement' => '6801${1}'],
            ['id' => 18, 'insurance_company_id' => 65, 'regex' => '/^9401(\\d{6})$/', 'replacement' => '9401${1}'],
            ['id' => 19, 'insurance_company_id' => 65, 'regex' => '/^8301(\\d{6})$/', 'replacement' => '8301${1}'],
            ['id' => 145, 'insurance_company_id' => 65, 'regex' => '/^0000(\\d{6})$/', 'replacement' => '0000${1}'],

            // Concordia Versicherungen a.G.
            ['id' => 143, 'insurance_company_id' => 69, 'regex' => '/^(\\d{5})(\\/\\d{3})?$/', 'replacement' => '${1}${2}'],
            // Replaced broker id patterns
            // ['id' => 140, 'insurance_company_id' => 69, 'regex' => '/^(\\d{5})\\/000$/', 'replacement' => '${1}/000'],
            // ['id' => 141, 'insurance_company_id' => 69, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}'],
            // ['id' => 142, 'insurance_company_id' => 69, 'regex' => '/^(\\d{5})\\/001$/', 'replacement' => '${1}/001'],
            // ['id' => 143, 'insurance_company_id' => 69, 'regex' => '/^(\\d{5})\\/002$/', 'replacement' => '${1}/002'],

            // R+V & Condor Lebensversicherung AG
            ['id' => 177, 'insurance_company_id' => 70, 'regex' => '/^427(\\d{6})$/', 'replacement' => '427${1}'],
            ['id' => 178, 'insurance_company_id' => 70, 'regex' => '/^419(\\d{6})$/', 'replacement' => '419${1}'],
            ['id' => 179, 'insurance_company_id' => 70, 'regex' => '/^411(\\d{6})$/', 'replacement' => '411${1}'],
            ['id' => 180, 'insurance_company_id' => 70, 'regex' => '/^426(\\d{6})$/', 'replacement' => '426${1}'],
            ['id' => 181, 'insurance_company_id' => 70, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'],
            ['id' => 182, 'insurance_company_id' => 70, 'regex' => '/^426\\/(\\d{6})$/', 'replacement' => '426/${1}'],

            // DEURAG
            ['id' => 150, 'insurance_company_id' => 74, 'regex' => '/^0{4,6}(\\d{5,6})$/', 'replacement' => '${1}'], //  Fehlendes "$" am Ende (regex)
            ['id' => 151, 'insurance_company_id' => 74, 'regex' => '/^0{4,6}(\\d{5,6})$/', 'replacement' => '0000${1}'],  //  Fehlendes "$" am Ende (regex)
            ['id' => 152, 'insurance_company_id' => 74, 'regex' => '/^0{4,6}(\\d{5,6})$/', 'replacement' => '00000${1}'],  //  Fehlendes "$" am Ende (regex)
            ['id' => 153, 'insurance_company_id' => 74, 'regex' => '/^0{4,6}(\\d{5,6})$/', 'replacement' => '000000${1}'],  //  Fehlendes "$" am Ende (regex)
            ['id' => 154, 'insurance_company_id' => 74, 'regex' => '/^(\\d{5,6})$/', 'replacement' => '${1}'],

            // Helvetia Schweizer. Vers-Gesellschaft AG
            ['id' => 38, 'insurance_company_id' => 84, 'regex' => '/^(\\d{3})\\.(\\d{4})$/', 'replacement' => '${1}.${2}'],
            ['id' => 39, 'insurance_company_id' => 84, 'regex' => '/^(\\d{3})(\\d{4})$/', 'replacement' => '${1}${2}'],

            // LV 1871 Lebensversicherung a.G.
            ['id' => 80, 'insurance_company_id' => 91, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}'],
            ['id' => 85, 'insurance_company_id' => 91, 'regex' => '/^92(\\d{5})0$/', 'replacement' => '92${1}0'],
            ['id' => 86, 'insurance_company_id' => 91, 'regex' => '/^15(\\d{5})0$/', 'replacement' => '15${1}0'],
            ['id' => 87, 'insurance_company_id' => 91, 'regex' => '/^83(\\d{5})0$/', 'replacement' => '83${1}0'],
            ['id' => 88, 'insurance_company_id' => 91, 'regex' => '/^29(\\d{5})0$/', 'replacement' => '29${1}0'],
            ['id' => 89, 'insurance_company_id' => 91, 'regex' => '/^57(\\d{5})0$/', 'replacement' => '57${1}0'],
            ['id' => 90, 'insurance_company_id' => 91, 'regex' => '/^76(\\d{5})0$/', 'replacement' => '76${1}0'],
            ['id' => 91, 'insurance_company_id' => 91, 'regex' => '/^66(\\d{5})0$/', 'replacement' => '66${1}0'],
            ['id' => 92, 'insurance_company_id' => 91, 'regex' => '/^87(\\d{5})0$/', 'replacement' => '87${1}0'],
            ['id' => 93, 'insurance_company_id' => 91, 'regex' => '/^41(\\d{5})0$/', 'replacement' => '41${1}0'],
            ['id' => 94, 'insurance_company_id' => 91, 'regex' => '/^51(\\d{5})0$/', 'replacement' => '51${1}0'],
            ['id' => 95, 'insurance_company_id' => 91, 'regex' => '/^46(\\d{5})0$/', 'replacement' => '46${1}0'],
            ['id' => 96, 'insurance_company_id' => 91, 'regex' => '/^67(\\d{5})0$/', 'replacement' => '67${1}0'],
            ['id' => 97, 'insurance_company_id' => 91, 'regex' => '/^74(\\d{5})0$/', 'replacement' => '74${1}0'],
            ['id' => 98, 'insurance_company_id' => 91, 'regex' => '/^23(\\d{5})0$/', 'replacement' => '23${1}0'],
            ['id' => 99, 'insurance_company_id' => 91, 'regex' => '/^10(\\d{5})0$/', 'replacement' => '10${1}0'],
            ['id' => 100, 'insurance_company_id' => 91, 'regex' => '/^80(\\d{5})0$/', 'replacement' => '80${1}0'],
            ['id' => 101, 'insurance_company_id' => 91, 'regex' => '/^60(\\d{5})0$/', 'replacement' => '60${1}0'],
            ['id' => 102, 'insurance_company_id' => 91, 'regex' => '/^70(\\d{5})0$/', 'replacement' => '70${1}0'],
            ['id' => 103, 'insurance_company_id' => 91, 'regex' => '/^55(\\d{5})0$/', 'replacement' => '55${1}0'],
            ['id' => 104, 'insurance_company_id' => 91, 'regex' => '/^95(\\d{5})0$/', 'replacement' => '95${1}0'],
            ['id' => 105, 'insurance_company_id' => 91, 'regex' => '/^73(\\d{5})0$/', 'replacement' => '73${1}0'],
            ['id' => 106, 'insurance_company_id' => 91, 'regex' => '/^43(\\d{5})0$/', 'replacement' => '43${1}0'],
            ['id' => 107, 'insurance_company_id' => 91, 'regex' => '/^61(\\d{5})0$/', 'replacement' => '61${1}0'],
            ['id' => 108, 'insurance_company_id' => 91, 'regex' => '/^08(\\d{5})0$/', 'replacement' => '08${1}0'],
            ['id' => 109, 'insurance_company_id' => 91, 'regex' => '/^42(\\d{5})0$/', 'replacement' => '42${1}0'],
            ['id' => 110, 'insurance_company_id' => 91, 'regex' => '/^47(\\d{5})0$/', 'replacement' => '47${1}0'],
            ['id' => 111, 'insurance_company_id' => 91, 'regex' => '/^84(\\d{5})0$/', 'replacement' => '84${1}0'],
            ['id' => 112, 'insurance_company_id' => 91, 'regex' => '/^12(\\d{5})0$/', 'replacement' => '12${1}0'],
            ['id' => 113, 'insurance_company_id' => 91, 'regex' => '/^37(\\d{5})0$/', 'replacement' => '37${1}0'],
            ['id' => 114, 'insurance_company_id' => 91, 'regex' => '/^36(\\d{5})0$/', 'replacement' => '36${1}0'],
            ['id' => 115, 'insurance_company_id' => 91, 'regex' => '/^27(\\d{5})0$/', 'replacement' => '27${1}0'],
            ['id' => 116, 'insurance_company_id' => 91, 'regex' => '/^52(\\d{5})0$/', 'replacement' => '52${1}0'],
            ['id' => 117, 'insurance_company_id' => 91, 'regex' => '/^75(\\d{5})0$/', 'replacement' => '75${1}0'],
            ['id' => 118, 'insurance_company_id' => 91, 'regex' => '/^16(\\d{5})0$/', 'replacement' => '16${1}0'],
            ['id' => 119, 'insurance_company_id' => 91, 'regex' => '/^39(\\d{5})0$/', 'replacement' => '39${1}0'],
            ['id' => 120, 'insurance_company_id' => 91, 'regex' => '/^44(\\d{5})0$/', 'replacement' => '44${1}0'],
            ['id' => 121, 'insurance_company_id' => 91, 'regex' => '/^31(\\d{5})0$/', 'replacement' => '31${1}0'],
            ['id' => 193, 'insurance_company_id' => 91, 'regex' => '/^71(\\d{5})0$/', 'replacement' => '71${1}0'],

            // Württembergische AG
            ['id' => 72, 'insurance_company_id' => 97, 'regex' => '/^(\\d{4})(\\d{4})(\\d{1})$/', 'replacement' => '${1}${2}${3}'],
            ['id' => 73, 'insurance_company_id' => 97, 'regex' => '/^(\\d{4})-(\\d{4})-(\\d{1})$/', 'replacement' => '${1}-${2}-${3}'],
            ['id' => 74, 'insurance_company_id' => 97, 'regex' => '/^(\\d{4})-(\\d{4})-(\\d{1})-00$/', 'replacement' => '${1}-${2}-${3}-00'],
            ['id' => 75, 'insurance_company_id' => 97, 'regex' => '/^(\\d{4})-(\\d{4})-(\\d{1})-01$/', 'replacement' => '${1}-${2}-${3}-01'],
            ['id' => 139, 'insurance_company_id' => 97, 'regex' => '/^(\\d{4})-(\\d{4})-(\\d{1})-02$/', 'replacement' => '${1}-${2}-${3}-02'],

            // R+V Krankenversicherung AG
            ['id' => 66, 'insurance_company_id' => 112, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'],
            ['id' => 67, 'insurance_company_id' => 112, 'regex' => '/^426(\\d{6})$/', 'replacement' => '426${1}'],
            ['id' => 69, 'insurance_company_id' => 112, 'regex' => '/^411(\\d{6})$/', 'replacement' => '411${1}'],
            ['id' => 70, 'insurance_company_id' => 112, 'regex' => '/^419(\\d{6})$/', 'replacement' => '419${1}'],
            ['id' => 176, 'insurance_company_id' => 112, 'regex' => '/^427(\\d{6})$/', 'replacement' => '427${1}'],

            // Gothaer Allgemeine Versicherung AG
            ['id' => 40, 'insurance_company_id' => 116, 'regex' => '/^(\\d{2})\\.(\\d{3})$/', 'replacement' => '${1}.${2}'],
            ['id' => 41, 'insurance_company_id' => 116, 'regex' => '/^(\\d{2})(\\d{3})$/', 'replacement' => '${1}${2}'],
            ['id' => 42, 'insurance_company_id' => 116, 'regex' => '/^0(\\d{2})(\\d{3})$/', 'replacement' => '0${1}${2}'],

            // HDI Sachversicherungen AG
            ['id' => 173, 'insurance_company_id' => 156, 'regex' => '/^20300(\\d{5})$/', 'replacement' => '20300${1}'],
            ['id' => 174, 'insurance_company_id' => 156, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}'],
            ['id' => 194, 'insurance_company_id' => 156, 'regex' => '/^203\\-00(\\d{5})$/', 'replacement' => '203-00${1}'],
            ['id' => 195, 'insurance_company_id' => 156, 'regex' => '/^203\\-00\\-(\\d{5})$/', 'replacement' => '203-00-${1}'],

            // R+V Allgemeine Versicherung
            ['id' => 63, 'insurance_company_id' => 165, 'regex' => '/^(406|405)?(\d{5,6})$/', 'replacement' => '${2}'],
//            ['id' => 63, 'insurance_company_id' => 165, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}'], // Replaced
//            ['id' => 185, 'insurance_company_id' => 165, 'regex' => '/^0(\\d{5})$/', 'replacement' => '0${1}'], // Unnecessary altogether
//            ['id' => 186, 'insurance_company_id' => 165, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}'], // Replaced
//            ['id' => 187, 'insurance_company_id' => 165, 'regex' => '/^4050(\\d{5})$/', 'replacement' => '4050${1}'], // Unnecessary altogether
//            ['id' => 188, 'insurance_company_id' => 165, 'regex' => '/^4060(\\d{5})$/', 'replacement' => '4060${1}'], // Unnecessary altogether
//            ['id' => 196, 'insurance_company_id' => 165, 'regex' => '/^405(\\d{6})$/', 'replacement' => '405${1}'], // Replaced
//            ['id' => 197, 'insurance_company_id' => 165, 'regex' => '/^406(\\d{6})$/', 'replacement' => '406${1}'], // Replaced

            // DOCURA VVaG
            ['id' => 183, 'insurance_company_id' => 265, 'regex' => '/^doc\\-(\\d{4})$/', 'replacement' => 'doc-${1}'],
            ['id' => 184, 'insurance_company_id' => 265, 'regex' => '/^(\\d{4})$/', 'replacement' => '${1}'],

            // AIG Europa S.A.
            ['id' => 122, 'insurance_company_id' => 352, 'regex' => '/^(\\d{5})(\\d{3})$/', 'replacement' => '${1}${2}'],
            ['id' => 123, 'insurance_company_id' => 352, 'regex' => '/^(\\d{5})-(\\d{3})$/', 'replacement' => '${1}-${2}'],

            // Manufaktur Augsburg GmbH
            ['id' => 155, 'insurance_company_id' => 556, 'regex' => '/^0{4,7}(\\d{5,6})$/', 'replacement' => '${1}'], //  Fehlendes "$" am Ende (regex)
            ['id' => 156, 'insurance_company_id' => 556, 'regex' => '/^0{4,7}(\\d{5,6})$/', 'replacement' => '0000${1}'], //  Fehlendes "$" am Ende (regex)
            ['id' => 157, 'insurance_company_id' => 556, 'regex' => '/^0{4,7}(\\d{5,6})$/', 'replacement' => '00000${1}'], //  Fehlendes "$" am Ende (regex)
            ['id' => 158, 'insurance_company_id' => 556, 'regex' => '/^0{4,7}(\\d{5,6})$/', 'replacement' => '000000${1}'], //  Fehlendes "$" am Ende (regex)
            ['id' => 159, 'insurance_company_id' => 556, 'regex' => '/^(\\d{5,6})$/', 'replacement' => '${1}'],
            ['id' => 162, 'insurance_company_id' => 556, 'regex' => '/^0{4,7}(\\d{5,6})$/', 'replacement' => '0000000${1}'], //  Fehlendes "$" am Ende (regex)

            // KRAVAG Logistic AG
            ['id' => 166, 'insurance_company_id' => 747, 'regex' => '/^4050(\\d{5})$/', 'replacement' => '${1}'],

            // Adam Riese GmbH
            ['id' => 76, 'insurance_company_id' => 1132, 'regex' => '/^(\\d{4})(\\d{4})(\\d{1})$/', 'replacement' => '${1}${2}${3}'],
            ['id' => 77, 'insurance_company_id' => 1132, 'regex' => '/^(\\d{4})-(\\d{4})-(\\d{1})$/', 'replacement' => '${1}-${2}-${3}'],
        ];
    }

    public function up(): bool
    {
        // 1. Remove currently available broker id patterns
        Yii::app()->db->createCommand()->delete($this->brokerIdPatternTableName);

        // 2. Disable foreign key constraint checks
        Yii::app()->db->createCommand('SET foreign_key_checks = 0')->execute();

        // 3. Insert backup data
        usort($this->brokerIdPatterns, function ($a, $b): int {
            return $a['insurance_company_id'] <=> $b['insurance_company_id'];
        });

        foreach ($this->brokerIdPatterns as $pattern) {
            unset($pattern['id']);
            $this->insert($this->brokerIdPatternTableName, $pattern);
        }

        // 4. Re-enable foreign key constraint checks
        Yii::app()->db->createCommand('SET foreign_key_checks = 1')->execute();

        return true;
    }

    public function down(): bool
    {
        // Do nothing

        return true;
    }
}
