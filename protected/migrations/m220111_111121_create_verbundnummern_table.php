<?php

class m220111_111121_create_verbundnummern_table extends CreateTableMigration
{
    public $tableName = 'verbundnummern';

    public function getColumns(): array
    {
        return [
            'id'                   => 'pk',
            'vermittlernummer'     => 'VARCHAR(40)',
            'insurance_company_id' => 'int unsigned not null',
            'user_id'              => 'int unsigned not null',
            'create_datetime'      => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
