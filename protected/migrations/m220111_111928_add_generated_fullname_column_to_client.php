<?php

class m220111_111928_add_generated_fullname_column_to_client extends CDbMigration
{
    private const TABLE      = 'client';
    private const INDEX_NAME = 'client_fullname_idx';
    private const COLUMN     = 'generated_fullname';

    public function up()
    {
        if ($this->columnExists()) {
            return;
        }
        $query = 'alter table client add ' . self::COLUMN . ' varchar(240) GENERATED ALWAYS AS (REPLACE(REPLACE(REPLACE(concat(firstname, lastname), " ", ""), ",", ""), ";", "")) PERSISTENT after lastname;';
        $this->execute($query);

        $this->createIndex(self::INDEX_NAME, self::TABLE, [self::COLUMN, 'user_id']);
    }

    public function down()
    {
        $this->dropIndex(self::INDEX_NAME, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }

    private function columnExists(): bool
    {
        $result = Client::model()->getDbConnection()->createCommand('show columns from client where Field = "' . self::COLUMN . '";')->queryAll();

        return !empty($result);
    }
}
