<?php

use Business\Company\Concordia;
use Business\Company\Kravag;
use Business\Company\RundV;

class m220111_142529_update_easier_broker_id_patterns extends CDbMigration
{
    /** @var string */
    private $brokerIdPatternTableName;

    /** @var array */
    private $oldKravagPatterns;

    /** @var array */
    private $newKravagPatterns;

    /** @var array */
    private $oldConcordiaPatterns;

    /** @var array */
    private $newConcordiaPatterns;

    /** @var array */
    private $oldRundVPatterns;

    /** @var array */
    private $newRundVPatterns;

    public function __construct()
    {
        $this->brokerIdPatternTableName = BrokeridPattern::model()->tableName();

        // KRAVAG Changes

        $this->oldKravagPatterns = [
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^(405|770)?(\d{5,6})$/', 'replacement' => '${2}', ]
        ];

        $this->newKravagPatterns = [
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}', ],
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^0(\\d{5})$/', 'replacement' => '0${1}', ],
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^7700(\\d{5})$/', 'replacement' => '7700${1}', ],
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^4050(\\d{5})$/', 'replacement' => '4050${1}', ],
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}', ],
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^405(\\d{6})$/', 'replacement' => '405${1}', ],
            ['insurance_company_id' => Kravag::ALLGEMEIN, 'regex' => '/^770(\\d{6})$/', 'replacement' => '770${1}', ],
        ];

        // R+V Changes

        $this->oldRundVPatterns = [
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^(406|405)?(\d{5,6})$/', 'replacement' => '${2}', ]
        ];

        $this->newRundVPatterns = [
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}', ],
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^0(\\d{5})$/', 'replacement' => '0${1}', ],
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^4060(\\d{5})$/', 'replacement' => '4060${1}', ],
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^4050(\\d{5})$/', 'replacement' => '4050${1}', ],
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^(\\d{6})$/', 'replacement' => '${1}', ],
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^406(\\d{6})$/', 'replacement' => '406${1}', ],
            ['insurance_company_id' => RundV::ALLGEMEIN, 'regex' => '/^405(\\d{6})$/', 'replacement' => '405${1}', ],
        ];

        // CONCORDIA Changes

        $this->oldConcordiaPatterns = [
            ['insurance_company_id' => Concordia::ID, 'regex' => '/^(\\d{5})(\\/\\d{3})?$/', 'replacement' => '${1}${2}', ]
        ];

        $this->newConcordiaPatterns = [
            ['insurance_company_id' => Concordia::ID, 'regex' => '/^(\\d{5})$/', 'replacement' => '${1}', ],
            ['insurance_company_id' => Concordia::ID, 'regex' => '/^(\\d{5})(\\/\\d{3})$/', 'replacement' => '${1}', ],
        ];
    }

    public function up(): bool
    {
        Yii::app()->db->createCommand('SET foreign_key_checks = 0')->execute();

        // KRAVAG
        $this->updatePatterns($this->oldKravagPatterns, $this->newKravagPatterns);

        // Concordia
        $this->updatePatterns($this->oldConcordiaPatterns, $this->newConcordiaPatterns);

        // R+V
        $this->updatePatterns($this->oldRundVPatterns, $this->newRundVPatterns);

        Yii::app()->db->createCommand('SET foreign_key_checks = 1')->execute();

        return true;
    }

    /**
     * @param array $oldPatterns
     * @param array $newPatterns
     *
     * @return void
     */
    private function updatePatterns(array $oldPatterns, array $newPatterns): void
    {
        foreach ($oldPatterns as $oldPattern) {
            $this->delete(
                $this->brokerIdPatternTableName,
                'insurance_company_id = :insurance_company_id AND regex = :regex AND replacement = :replacement',
                $oldPattern
            );
        }

        foreach ($newPatterns as $newPattern) {
            $this->insert($this->brokerIdPatternTableName, $newPattern);
        }
    }

    public function down(): bool
    {
        // Do nothing

        return true;
    }
}
