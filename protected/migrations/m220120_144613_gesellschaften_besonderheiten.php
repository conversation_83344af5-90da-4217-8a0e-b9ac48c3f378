<?php

class m220120_144613_gesellschaften_besonderheiten extends CreateTableMigration
{
    public $tableName = 'insurance_company_demv_info';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'insurance_company_id' => 'int unsigned not null',
            'content'              => 'text'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
