<?php

class m220121_120056_update_company_specifics extends CDbMigration
{
    private const TABLENAME = 'insurance_company_specifics';

    public function up(): void
    {
        foreach ($this->getColumns() as $column => $type) {
            $this->addColumn(self::TABLENAME, $column, $type);
        }
    }

    private function getColumns(): array
    {
        return [
            'bestandsuebertragung_naechste_hauptfaelligkeit' => 'bool',
            'ausgleichszahlung_bei_bestandsuebertragung'     => 'bool',
            'automatische_provisionsdaten'                   => 'bool',
            'automatische_dokumente'                         => 'bool',
            'notwendige_einstellungen_dokumentenemfpang'     => 'string',
            'sign_moeglich'                                  => 'bool',
            'risikodemver'                                   => 'string',
            'rabatte'                                        => 'string',
            'zeitpunkt_datenlieferung'                       => 'string',
            'verzicht_erstantrag_mindestbestand'             => 'string',
        ];
    }

    public function down(): bool
    {
        foreach ($this->getColumns() as $column => $type) {
            $this->dropColumn(self::TABLENAME, $column);
        }

        return true;
    }
}
