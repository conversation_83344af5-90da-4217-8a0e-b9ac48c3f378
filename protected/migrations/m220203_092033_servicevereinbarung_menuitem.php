<?php

class m220203_092033_servicevereinbarung_menuitem extends CDbMigration
{
    private const TABLE = 'system_menu_items';

	public function up(): void
	{
        $this->insert(self::TABLE, [
            'menu_id'                    => 8,
            'active'                     => true,
            'title'                      => 'Servicevereinbarungen',
            'url'                        => '/empfehlungen/servicevereinbarungen',
            'regex'                      => '/empfehlungen\/servicevereinbarungen/',
            'target'                     => '_top',
            'exclude_testuser'           => 1,
            'admin_only'                 => false,
            'right_id'                   => null,
            'sort'                       => 510,
            'exclude_insurancecompanies' => false,
            'roles'                      => '1,2,3,4,5,6,7,8,9',
            'rights'                     => '',
            'excluded_roles'             => '',
        ]);
	}

	public function down(): bool
	{
        $this->delete(self::TABLE, 'url = "/empfehlungen/servicevereinbarungen"');
		return true;
	}
}
