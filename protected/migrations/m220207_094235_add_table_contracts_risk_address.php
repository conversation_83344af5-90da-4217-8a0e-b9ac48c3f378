<?php

final class m220207_094235_add_table_contracts_risk_address extends CreateTableMigration
{
    public $tableName = 'contracts_risk_address';

    /**
     * @return string[]
     */
    public function getColumns(): array
    {
        return [
            'id'              => 'pk',
            'contract_id'     => 'integer unsigned NOT NULL',
            'street'          => 'string',
            'street_number'   => 'string',
            'postcode'        => 'string',
            'city'            => 'string',
            'create_datetime' => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('contract', 'contract_id', 'contracts', 'CASCADE', 'CASCADE');
    }
}
