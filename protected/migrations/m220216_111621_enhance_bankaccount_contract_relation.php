<?php

class m220216_111621_enhance_bankaccount_contract_relation extends CDbMigration
{
    private const TABLE                  = 'bankaccount_contract_relation';
    private const COLUMN_CREATE_TYPE     = 'create_type';
    private const COLUMN_CREATE_DATETIME = 'create_datetime';
    private const COLUMN_CREATE_USER_ID  = 'create_user_id';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN_CREATE_TYPE, 'int');
        $this->addColumn(self::TABLE, self::COLUMN_CREATE_DATETIME, 'datetime');
        $this->addColumn(self::TABLE, self::COLUMN_CREATE_USER_ID, 'int');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_CREATE_TYPE);
        $this->dropColumn(self::TABLE, self::COLUMN_CREATE_DATETIME);
        $this->dropColumn(self::TABLE, self::COLUMN_CREATE_USER_ID);

        return true;
    }
}
