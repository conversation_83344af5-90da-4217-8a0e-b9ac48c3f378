<?php

class m220217_131600_client_sign_upload_total_finished_signers extends CDbMigration
{
    private const TABLE            = 'client_sign_upload';
    private const TOTAL_SIGNERS    = 'total_signers';
    private const FINISHED_SIGNERS = 'finished_signers';

    public function up()
    {
        $this->addColumn(self::TABLE, self::TOTAL_SIGNERS, 'int DEFAULT 0 AFTER FAILED');
        $this->addColumn(self::TABLE, self::FINISHED_SIGNERS, 'int DEFAULT 0 AFTER FAILED');

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::TOTAL_SIGNERS);
        $this->dropColumn(self::TABLE, self::FINISHED_SIGNERS);

        return true;
    }
}
