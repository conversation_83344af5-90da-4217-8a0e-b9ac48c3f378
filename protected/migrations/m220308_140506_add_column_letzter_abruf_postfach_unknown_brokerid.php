<?php

final class m220308_140506_add_column_letzter_abruf_postfach_unknown_brokerid extends CDbMigration
{
    private const TABLE  = 'postfach_unknown_brokerid';
    private const COLUMN = 'letzter_abruf';

    public function up(): void
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'datetime AFTER erster_abruf');
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
