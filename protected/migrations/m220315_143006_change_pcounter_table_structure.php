<?php

class m220315_143006_change_pcounter_table_structure extends CDbMigration
{
    public function up(): void
    {
        $transaction = $this->dbConnection->beginTransaction();
        $this->execute('drop index if exists `unique` on pcounter_users;');
        if (!$this->pkExists()) {
            $this->execute('alter table pcounter_users
                       add constraint pcounter_users_pk
                           primary key (user_ip);');
        }
        $this->execute('ALTER TABLE demv_crm_release1.pcounter_users ENGINE =InnoDB;');
        $transaction->commit();
    }

    public function down(): bool
    {
        //do nothing! There is no reason to change engine back to myisam
        return true;
    }

    /**
     * @return void
     * @throws CException
     */
    private function pkExists(): bool
    {
        return count($this->getDbConnection()
                          ->createCommand()
                          ->select('TABLE_NAME')
                          ->from('INFORMATION_SCHEMA.TABLE_CONSTRAINTS')
                          ->where('CONSTRAINT_TYPE = "PRIMARY KEY"')
                          ->andWhere('TABLE_NAME = "pcounter_users"')
                          ->queryAll()) > 0;
    }
}
