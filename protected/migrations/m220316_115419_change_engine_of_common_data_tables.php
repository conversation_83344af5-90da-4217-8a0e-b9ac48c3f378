<?php

class m220316_115419_change_engine_of_common_data_tables extends CDbMigration
{
    public function up(): void
    {
        $this->execute('ALTER TABLE common_data.plz_deutschland ENGINE =InnoDB;');
        $this->execute('ALTER TABLE common_data.district ENGINE =InnoDB;');
        $this->execute('ALTER TABLE common_data.zipcode ENGINE =InnoDB;');
        $this->execute('ALTER TABLE common_data.city ENGINE =InnoDB;');
        $this->execute('ALTER TABLE common_data.state ENGINE =InnoDB;');
        $this->execute('ALTER TABLE common_data.bankcodes ENGINE =InnoDB;');
    }

    public function down(): bool
    {
        //DO Nothing
        return true;
    }
}
