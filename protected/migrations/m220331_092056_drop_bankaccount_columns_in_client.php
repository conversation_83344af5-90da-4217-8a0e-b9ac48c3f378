<?php

declare(strict_types=1);

use Components\Database\ValueObject\ColumnDefinition;

class m220331_092056_drop_bankaccount_columns_in_client extends DbMigration
{
    private const TABLE = 'client';

    private ColumnDefinition $bankAccountNotice;
    private ColumnDefinition $bankAccountDepositor;
    private ColumnDefinition $bankNumber;
    private ColumnDefinition $bankCode;
    private ColumnDefinition $bankName;
    private ColumnDefinition $ibanCode;
    private ColumnDefinition $swiftCode;
    private ColumnDefinition $bankAccountCountry;

    public function __construct()
    {
        $this->bankAccountNotice    = new ColumnDefinition('bank_account_notice', 'string');
        $this->bankAccountDepositor = new ColumnDefinition('bank_account_depositor', 'string');
        $this->bankNumber           = new ColumnDefinition('bank_number', 'string');
        $this->bankCode             = new ColumnDefinition('bank_code', 'string');
        $this->bankName             = new ColumnDefinition('bank_name', 'string');
        $this->ibanCode             = new ColumnDefinition('iban_code', 'string');
        $this->swiftCode            = new ColumnDefinition('swift_code', 'string');
        $this->bankAccountCountry   = new ColumnDefinition('bank_account_country', 'int');
    }

    public function up(): void
    {
        $this->dropColumns(
            self::TABLE,
            $this->bankAccountNotice,
            $this->bankAccountDepositor,
            $this->bankNumber,
            $this->bankCode,
            $this->bankName,
            $this->ibanCode,
            $this->swiftCode,
            $this->bankAccountCountry
        );
    }

    public function down(): bool
    {
        $this->addColumns(
            self::TABLE,
            $this->bankAccountNotice,
            $this->bankAccountDepositor,
            $this->bankNumber,
            $this->bankCode,
            $this->bankName,
            $this->ibanCode,
            $this->swiftCode,
            $this->bankAccountCountry
        );

        return true;
    }
}
