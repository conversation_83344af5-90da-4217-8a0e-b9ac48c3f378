<?php

class m220404_163518_change_contract_fields_datetype_erfassungsdatum extends CDbMigration
{
    private const TABLE = 'contract_fields';
    // Entspricht der id des Feldes "Erfassungsdatum" auf dem Livesystem
    private const CONTRACT_FIELD_ID = 15632;

    public function up(): void
    {
        $this->update(self::TABLE, ['datatype' => 0], 'id = :id', [':id' => self::CONTRACT_FIELD_ID]);
    }

    public function down(): bool
    {
        $this->update(self::TABLE, ['datatype' => 2], 'id = :id', [':id' => self::CONTRACT_FIELD_ID]);

        return true;
    }
}
