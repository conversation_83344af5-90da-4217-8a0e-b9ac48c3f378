<?php

final class m220406_082651_remove_concordia_broker_id_pattern extends CDbMigration
{
    private const INSURANCE_COMPANY_ID = 69;
    private const TABLE                = 'brokerid_pattern';

    public function up(): bool
    {
        $this->delete(
            self::TABLE,
            'insurance_company_id = :companyId',
            [
                ':companyId' => self::INSURANCE_COMPANY_ID,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->insert(
            self::TABLE,
            [
                'insurance_company_id' => self::INSURANCE_COMPANY_ID,
                'regex'                => '/^(\d{5})$/',
                'replacement'          => '${1}',
            ]
        );

        $this->insert(
            self::TABLE,
            [
                'insurance_company_id' => self::INSURANCE_COMPANY_ID,
                'regex'                => '/^(\d{5})(\/\d{3})$/',
                'replacement'          => '${1}',
            ]
        );

        return true;
    }
}
