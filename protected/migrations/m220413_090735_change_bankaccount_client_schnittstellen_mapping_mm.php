<?php

class m220413_090735_change_bankaccount_client_schnittstellen_mapping_mm extends CDbMigration
{
    private const TABLE                             = 'schnittstellen_mapping';
    private const COLUMN                            = 'class';
    private const VALUE_IBAN_CODE_ID                = 464;
    private const VALUE_SWIFT_CODE_ID               = 465;
    private const CLASS_CLIENT                      = 'Client';
    private const CLASS_BANKACCOUNTASSIGNMENTCLIENT = 'BankAccountAssignmentClient';

    public function up(): void
    {
        $this->update(
            self::TABLE,
            [
                self::COLUMN => self::CLASS_BANKACCOUNTASSIGNMENTCLIENT
            ],
            'id = :id',
            [
                ':id' => self::VALUE_IBAN_CODE_ID
            ]
        );

        $this->update(
            self::TABLE,
            [
                self::COLUMN => self::CLASS_BANKACCOUNTASSIGNMENTCLIENT
            ],
            'id = :id',
            [
                ':id' => self::VALUE_SWIFT_CODE_ID
            ]
        );
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE,
            [
                self::COLUMN => self::CLASS_CLIENT
            ],
            'id = :id',
            [
                ':id' => self::VALUE_IBAN_CODE_ID
            ]
        );

        $this->update(
            self::TABLE,
            [
                self::COLUMN => self::CLASS_BANKACCOUNTASSIGNMENTCLIENT
            ],
            'id = :id',
            [
                ':id' => self::VALUE_SWIFT_CODE_ID
            ]
        );

        return true;
    }
}
