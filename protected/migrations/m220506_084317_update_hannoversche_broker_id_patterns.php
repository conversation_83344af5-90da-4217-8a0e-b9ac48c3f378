<?php

declare(strict_types=1);

use Business\Company\Hannoversche;

final class m220506_084317_update_hannoversche_broker_id_patterns extends CDbMigration
{
    private const NEW_PATTERNS = [
        ['regex' => '/^(\d{6})-(\d{3})$/', 'replacement' => 'VL${1}-${2}'],
        ['regex' => '/^(\d{6})-(\d{3})$/', 'replacement' => '${1}${2}'],
        ['regex' => '/^(\d{6})(\d{3})$/', 'replacement' => 'VL${1}-${2}'],
        ['regex' => '/^(\d{6})(\d{3})$/', 'replacement' => '${1}-${2}'],
        ['regex' => '/^VL(\d{6})-(\d{3})$/', 'replacement' => '${1}-${2}'],
        ['regex' => '/^VL(\d{6})-(\d{3})$/', 'replacement' => '${1}${2}'],
    ];

    private string $brokerIdPatternTableName;

    public function __construct()
    {
        $this->brokerIdPatternTableName = BrokeridPattern::model()->tableName();
    }

    public function up(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $this->insert($this->brokerIdPatternTableName, array_merge(
                ['insurance_company_id' => Hannoversche::LEBEN],
                $newPattern,
            ));
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $patternCriteria = new CDbCriteria();
            $patternCriteria->addCondition('regex = :regex');
            $patternCriteria->addCondition('replacement = :replacement');
            $patternCriteria->addCondition('insurance_company_id = :insurance_company_id');

            $this->delete($this->brokerIdPatternTableName, $patternCriteria->condition, [
                ':regex'                => $newPattern['regex'],
                ':replacement'          => $newPattern['replacement'],
                ':insurance_company_id' => Hannoversche::LEBEN,
            ]);
        }

        return true;
    }
}
