<?php

final class m220517_092125_create_table_broker_id_sparten extends CreateTableMigration
{
    public $tableName = 'broker_id_sparten';

    /**
     * @return string[]
     */
    public function getColumns(): array
    {
        return [
            'id'               => 'pk',
            'brokerid_id'      => 'int unsigned NOT NULL',
            'product_combo_id' => 'int unsigned NOT NULL',
        ];
    }

    /**
     * @return void
     */
    public function addForeignKeys(): void
    {
        $this->addForeignKey(
            'fk_broker_id_sparten_broker_id',
            'broker_id_sparten',
            'brokerid_id',
            'broker_id',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_broker_id_sparten_product_combo_id',
            'broker_id_sparten',
            'product_combo_id',
            'product_combo',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }
}
