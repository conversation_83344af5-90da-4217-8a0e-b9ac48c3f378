<?php

final class m220518_144229_enhance_table_broker_id_type extends CDbMigration
{
    private const TABLE         = 'broker_id_type';
    private const BEZEICHNUNGEN = [
        'Hauptnummer' => 'Antragsnummer',
        'Nebennummer' => 'Bestandsnummer',
    ];

    public function up(): bool
    {
        foreach (self::BE<PERSON><PERSON><PERSON><PERSON>NGEN as $oldTerm => $newTerm) {
            $this->update(
                self::TABLE,
                [
                    'name' => $newTerm,
                ],
                sprintf('name = "%s"', $oldTerm)
            );
        }

        $this->insert(
            self::TABLE,
            [
                'name' => 'Verwaltungsnummer'
            ]
        );

        return true;
    }

    public function down(): bool
    {
        foreach (self::BEZ<PERSON>CHNUNGEN as $oldTerm => $newTerm) {
            $this->update(
                self::TABLE,
                [
                    'name' => $oldTerm,
                ],
                sprintf('name = "%s"', $newTerm)
            );
        }

        $this->delete(self::TABLE, 'name = "Verwaltungsnummer"');

        return true;
    }
}
