<?php

final class m220519_143549_add_is_pool_and_verkettet_to_table_broker_id extends CDbMigration
{
    private const TABLE               = 'broker_id';
    private const COLUMN_IS_POOL      = 'is_pool';
    private const COLUMN_IS_VERKETTET = 'is_verkettet';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN_IS_POOL, 'bool DEFAULT 0');
        $this->addColumn(self::TABLE, self::COLUMN_IS_VERKETTET, 'bool DEFAULT 0');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_IS_POOL);
        $this->dropColumn(self::TABLE, self::COLUMN_IS_VERKETTET);

        return true;
    }
}
