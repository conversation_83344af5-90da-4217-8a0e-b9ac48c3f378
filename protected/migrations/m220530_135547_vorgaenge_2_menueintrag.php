<?php

final class m220530_135547_vorgaenge_2_menueintrag extends CDbMigration
{
    private const TABLE = 'system_menu_items';
    private const TITLE = 'Vorgänge 2.0';

    public function up(): bool
    {
        $this->insert(self::TABLE, [
            'menu_id'                    => 2,
            'title'                      => self::TITLE,
            'active'                     => 1,
            'regex'                      => '',
            'icon'                       => 'fa fa-sign-out', //Übernommen
            'url'                        => '/procedure/vorgaenge2',
            'target'                     => '_blank',
            'exclude_testuser'           => 0,
            'admin_only'                 => 0,
            'right_id'                   => null,
            'sort'                       => 21,
            'exclude_insurancecompanies' => null,
            'roles'                      => '1,2,3,4,5,6,7,8,9',
            'excluded_roles'             => null,

        ]);

        return true;
    }

    public function down(): bool
    {
        $this->delete(self::TABLE, 'title = "' . self::TITLE . '"');

        return true;
    }
}
