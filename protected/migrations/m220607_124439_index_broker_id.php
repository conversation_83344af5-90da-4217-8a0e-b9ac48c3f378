<?php

final class m220607_124439_index_broker_id extends CDbMigration
{
    private const TABLE           = 'broker_id';
    private const OLD_DESCRIPTION = 'Geschlossen';
    private const NEW_DESCRIPTION = 'Aktiv';

    public function up(): bool
    {
        $sql = <<<SQL
UPDATE broker_id AS B
    JOIN user AS U ON B.last_edit_user_id = U.id
SET B.is_verkettet = 1
WHERE (U.agency_id IN (1, 575) OR U.user_role = 2)
  and B.Status = 'Geschlossen';
SQL;

        Yii::app()->db
            ->getCommandBuilder()
            ->createSqlCommand($sql)
            ->execute();

        $sql = sprintf(
            'UPDATE broker_id SET status="%s" WHERE status = "%s"',
            self::NEW_DESCRIPTION,
            self::OLD_DESCRIPTION
        );
        Yii::app()->db
            ->getCommandBuilder()
            ->createSqlCommand($sql)
            ->execute();

        return true;
    }

    public function down(): bool
    {
        $this->update(self::TABLE, ['verkettet' => 0]);

        $sql = sprintf(
            'UPDATE broker_id SET status="%s" WHERE status = "%s"',
            self::OLD_DESCRIPTION,
            self::NEW_DESCRIPTION
        );
        Yii::app()->db
            ->getCommandBuilder()
            ->createSqlCommand($sql)
            ->execute();

        return true;
    }
}
