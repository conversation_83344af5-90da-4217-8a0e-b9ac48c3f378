<?php

declare(strict_types=1);

final class m220705_135441_update_vertragsservice_postfach_failed_with_fail_types extends CDbMigration
{
    private const TABLE  = 'vertragsservice_postfach_failed';
    private const COLUMN = 'fail_type';

    public function up(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN,
            'INTEGER UNSIGNED NOT NULL DEFAULT 0 AFTER gdv_in_gesellschaft_log_id'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
