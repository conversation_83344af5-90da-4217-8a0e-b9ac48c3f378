<?php

final class m220707_080138_alter_bu_ff_company_bafin extends CDbMigration
{
    private const COLUMN  = 'mak';
    private const COLUMN2 = 'business_division';
    private const TABLE   = 'bu_fondsfinanz_company_bafin';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'string not null');
        $this->addColumn(self::TABLE, self::COLUMN2, 'string not null');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);
        $this->dropColumn(self::TABLE, self::COLUMN2);

        return true;
    }
}
