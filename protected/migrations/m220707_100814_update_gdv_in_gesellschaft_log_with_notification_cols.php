<?php

final class m220707_100814_update_gdv_in_gesellschaft_log_with_notification_cols extends CDbMigration
{
    private const TABLE                 = 'gdv_in_gesellschaft_log';
    private const COLUMN_NOTIFIED_AT    = 'notified_at';
    private const COLUMN_NOTIFIED_COUNT = 'notified_count';

    public function up(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN_NOTIFIED_AT,
            'DATETIME DEFAULT NULL'
        );

        $this->addColumn(
            self::TABLE,
            self::COLUMN_NOTIFIED_COUNT,
            'INTEGER UNSIGNED NOT NULL DEFAULT 0'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_NOTIFIED_AT);
        $this->dropColumn(self::TABLE, self::COLUMN_NOTIFIED_COUNT);

        return true;
    }
}
