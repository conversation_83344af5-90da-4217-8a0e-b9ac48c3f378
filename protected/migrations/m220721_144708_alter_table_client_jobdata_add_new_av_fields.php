<?php

final class m220721_144708_alter_table_client_jobdata_add_new_av_fields extends CDbMigration
{
    const TABLE = 'client_jobdata';

    public function up()
    {
        $this->addColumn(self::TABLE, 'versorgungswerk', 'string');
        $this->addColumn(self::TABLE, 'besoldung_vor_pension', 'string');
        $this->addColumn(self::TABLE, 'pensionsbedarf', 'string');
        $this->addColumn(self::TABLE, 'pensionseintritt', 'int');
        $this->addColumn(self::TABLE, 'dienstzeitbeginn', 'int');
        $this->addColumn(self::TABLE, 'pension', 'string');
        $this->addColumn(self::TABLE, 'private_vorsorge', 'string');

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, 'versorgungswerk');
        $this->dropColumn(self::TABLE, 'besoldung_vor_pension');
        $this->dropColumn(self::TABLE, 'pensionsbedarf');
        $this->dropColumn(self::TABLE, 'pensionseintritt');
        $this->dropColumn(self::TABLE, 'dienstzeitbeginn');
        $this->dropColumn(self::TABLE, 'pension');
        $this->dropColumn(self::TABLE, 'private_vorsorge');

        return true;
    }
}
