<?php

declare(strict_types=1);

final class m220725_135843_add_contracts_bafin_fk extends CDbMigration
{
    private const TABLE    = 'contracts';
    private const KEY_NAME = 'contracts_bafin_fk';

    public function up(): bool
    {
        $this->addForeignKey(self::KEY_NAME, self::TABLE, 'bafin', 'vu_number', 'ref', 'SET NULL', 'CASCADE');

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::KEY_NAME, self::TABLE);

        return true;
    }
}
