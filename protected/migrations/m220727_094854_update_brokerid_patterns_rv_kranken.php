<?php

declare(strict_types=1);

use Business\Company\RundV;

final class m220727_094854_update_brokerid_patterns_rv_kranken extends CDbMigration
{
    private const NEW_PATTERNS = [
        ['regex' => '/^0(\d{5})$/', 'replacement' => '0${1}'],
        ['regex' => '/^4110(\d{5})$/', 'replacement' => '4110${1}'],
        ['regex' => '/^4190(\d{5})$/', 'replacement' => '4190${1}'],
        ['regex' => '/^4260(\d{5})$/', 'replacement' => '4260${1}'],
        ['regex' => '/^4270(\d{5})$/', 'replacement' => '4270${1}'],
    ];

    private string $brokerIdPatternTableName;

    public function __construct()
    {
        $this->brokerIdPatternTableName = BrokeridPattern::model()->tableName();
    }

    public function up(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $this->insert($this->brokerIdPatternTableName, array_merge(
                ['insurance_company_id' => RundV::LEBEN],
                $newPattern,
            ));
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $patternCriteria = new CDbCriteria();
            $patternCriteria->addCondition('regex = :regex');
            $patternCriteria->addCondition('replacement = :replacement');
            $patternCriteria->addCondition('insurance_company_id = :insurance_company_id');

            $this->delete($this->brokerIdPatternTableName, $patternCriteria->condition, [
                ':regex'                => $newPattern['regex'],
                ':replacement'          => $newPattern['replacement'],
                ':insurance_company_id' => RundV::LEBEN,
            ]);
        }

        return true;
    }
}
