<?php

final class m220728_080157_create_bu_fondsfinanz_log_table extends CreateTableMigration
{
    public $tableName = 'bu_fondsfinanz_log';

    public function getColumns()
    {
        return [
            'id'              => 'pk',
            'process_id'      => 'string not null',
            'user_id'         => 'int unsigned not null',
            'client_id'       => 'int unsigned not null',
            'contract_id'     => 'int unsigned not null',
            'create_datetime' => 'datetime',
            'status'          => 'string',
            'error'           => 'string',
            'traceId'         => 'string',
            'uuid'            => 'string',
            'version'         => 'string'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
        $this->addOwnFk('client', 'client_id', 'client', 'CASCADE', 'CASCADE');
        $this->addOwnFk('contracts', 'contract_id', 'contracts', 'CASCADE', 'CASCADE');
    }
}
