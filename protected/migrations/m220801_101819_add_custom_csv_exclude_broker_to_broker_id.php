<?php

final class m220801_101819_add_custom_csv_exclude_broker_to_broker_id extends CDbMigration
{
    private const TABLE  = 'broker_id';
    private const COLUMN = 'custom_csv_exclude_broker';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'bool DEFAULT 0');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
