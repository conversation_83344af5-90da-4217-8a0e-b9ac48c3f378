<?php

declare(strict_types=1);

final class m220822_151011_create_covering_index_for_verbundbestandservice extends CDbMigration
{
    private const TABLE    = 'contracts';
    private const KEY_NAME = 'idx_verbundbestand_vermittlernummer';

    public function up(): bool
    {
        $conn = $this->getDbConnection();

        $sql = $conn->getSchema()->createIndex(
            self::KEY_NAME,
            self::TABLE,
            ['vermittlernummer', 'parentId', 'duplicate_contract_id', 'damage', 'client_id']
        );
        $sql .= ' COMMENT "Covering Index für den Verbundbestand-Service" LOCK=NONE';

        $conn->createCommand($sql)->execute();

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::KEY_NAME, self::TABLE);

        return true;
    }
}
