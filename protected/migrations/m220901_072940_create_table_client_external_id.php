<?php

final class m220901_072940_create_table_client_external_id extends CreateTableMigration
{
    public $tableName = 'client_external_id';

    public function getColumns(): array
    {
        return [
            'id'              => 'pk',
            'client_id'       => 'int unsigned not null',
            'external_id'     => 'varchar(40) not null',
            'create_datetime' => 'datetime not null',
            'create_type'     => 'int',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('client_id', 'client_id', 'client', 'CASCADE', 'CASCADE');
    }
}
