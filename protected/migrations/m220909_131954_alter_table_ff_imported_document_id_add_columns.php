<?php

final class m220909_131954_alter_table_ff_imported_document_id_add_columns extends CDbMigration
{
    private const TABLE   = 'fondsfinanz_imported_document_id';
    private const COLUMN  = 'mak';
    private const COLUMN2 = 'client_file_id';
    private const COLUMN3 = 'contract_id';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'string');
        $this->addColumn(self::TABLE, self::COLUMN2, 'string');
        $this->addColumn(self::TABLE, self::COLUMN3, 'string');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);
        $this->dropColumn(self::TABLE, self::COLUMN2);
        $this->dropColumn(self::TABLE, self::COLUMN3);

        return true;
    }
}
