<?php

use Business\Company\Deurag;

final class m220921_133915_add_deurag_broker_id extends CDbMigration
{
    private const NEW_PATTERNS = [
        ['regex' => '/^\d{5}$/', 'replacement' => '00000${1}'],
        ['regex' => '/^\d{6}$/', 'replacement' => '0000${1}'],
    ];

    private string $brokerIdPatternTableName;

    public function __construct()
    {
        $this->brokerIdPatternTableName = BrokeridPattern::model()->tableName();
    }

    public function up(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $this->insert($this->brokerIdPatternTableName, array_merge(
                ['insurance_company_id' => Deurag::ID],
                $newPattern,
            ));
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $patternCriteria = new CDbCriteria();
            $patternCriteria->addCondition('regex = :regex');
            $patternCriteria->addCondition('replacement = :replacement');
            $patternCriteria->addCondition('insurance_company_id = :insurance_company_id');

            $this->delete($this->brokerIdPatternTableName, $patternCriteria->condition, [
                ':regex'                => $newPattern['regex'],
                ':replacement'          => $newPattern['replacement'],
                ':insurance_company_id' => Deurag::ID,
            ]);
        }

        return true;
    }
}
