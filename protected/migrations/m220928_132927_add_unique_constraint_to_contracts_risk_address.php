<?php

final class m220928_132927_add_unique_constraint_to_contracts_risk_address extends CDbMigration
{
    private const FK_NAME = 'unique_contracts_risk_address_contract_id';
    private const TABLE   = 'contracts_risk_address';

    public function up(): bool
    {
        $this->createIndex(self::FK_NAME, self::TABLE, 'contract_id', true);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::FK_NAME, self::TABLE);

        return true;
    }
}
