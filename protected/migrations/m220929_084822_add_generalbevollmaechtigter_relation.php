<?php

declare(strict_types=1);

use Demv\Werte\Person\Beziehung\BeziehungsTypInterface;
use Demv\Werte\Person\Beziehung\Privat\Generalbevollmaechtigter;
use Demv\Werte\Person\Beziehung\Privat\Vollmachtgeber;

final class m220929_084822_add_generalbevollmaechtigter_relation extends CDbMigration
{
    private const TABLE = 'client_relation_type';

    /**
     * @return bool
     */
    public function up(): bool
    {
        $this->insertBeziehungen(
            new Generalbevollmaechtigter(),
            new Vollmachtgeber(),
            ClientRelationType::CATEGORY_PRIVATE,
            '<b>{kunde1}</b> ist <i>Generalbevollmächtigter</i> von <b>{kunde2}</b>',
            '<b>{kunde1}</b> ist der <i>Vollmachtgeber</i> von <b>{kunde2}</b>'
        );

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $ids = [
            Generalbevollmaechtigter::ID,
            Vollmachtgeber::ID,
        ];

        foreach ($ids as $id) {
            $this->delete(self::TABLE, 'id = ' . $id);
        }

        return true;
    }

    /**
     * @param BeziehungsTypInterface $beziehung1
     * @param BeziehungsTypInterface $beziehung2
     * @param int                    $typ
     * @param string|null            $textBeziehung1
     * @param string|null            $textBeziehung2
     */
    private function insertBeziehungen(
        BeziehungsTypInterface $beziehung1,
        BeziehungsTypInterface $beziehung2,
        int $typ,
        ?string $textBeziehung1 = null,
        ?string $textBeziehung2 = null
    ): void {
        $this->insert(self::TABLE, [
            'id'          => $beziehung1->getId(),
            'name'        => $beziehung1->getName(),
            'category_id' => $typ,
            'text'        => $textBeziehung1,

        ]);

        $this->insert(self::TABLE, [
            'id'                  => $beziehung2->getId(),
            'name'                => $beziehung2->getName(),
            'related_relation_id' => $beziehung1->getId(),
            'category_id'         => $typ,
            'text'                => $textBeziehung2,
        ]);

        $this->update(self::TABLE, ['related_relation_id' => $beziehung2->getId()], 'id = ' . $beziehung1->getId());
    }
}
