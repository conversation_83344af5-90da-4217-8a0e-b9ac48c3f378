<?php

final class m221004_141143_add_table_contracts_external_metadaten extends CreateTableMigration
{
    public $tableName = 'contracts_external_metadaten';

    public function getColumns(): array
    {
        return [
            'id'              => 'pk',
            'contract_id'     => 'int unsigned',
            'external_id'     => 'varchar(64)',
            'create_type'     => 'int',
            'create_datetime' => 'datetime',
        ];
    }

    public function addForeignKeys(): void
    {
        $this->addOwnFk('contract', 'contract_id', 'contracts', 'CASCADE', 'CASCADE');
    }
}
