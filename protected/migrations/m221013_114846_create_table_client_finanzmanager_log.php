<?php

final class m221013_114846_create_table_client_finanzmanager_log extends CreateTableMigration
{
    public $tableName = 'client_finanzmanager_log';

    public function getColumns()
    {
        return [
            'id'              => 'pk',
            'type'            => 'string',
            'properties'      => 'string',
            'client_id'       => 'int unsigned not null',
            'create_datetime' => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('client', 'client_id', 'client', 'CASCADE', 'CASCADE');
    }
}
