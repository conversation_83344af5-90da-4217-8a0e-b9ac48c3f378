<?php

declare(strict_types=1);

final class m221014_111121_remove_client_user_role extends CDbMigration
{
    private const TABLE          = 'user_role';
    private const CLIENT_ROLE_ID = 7;

    public function up(): bool
    {
        $this->delete(self::TABLE, 'id = ' . self::CLIENT_ROLE_ID);

        return true;
    }

    public function down(): bool
    {
        $this->insert(self::TABLE, [
            'id'   => self::CLIENT_ROLE_ID,
            'name' => 'Kunde',
        ]);

        return true;
    }
}
