<?php

class m221018_110233_add_fallback_maklervertrag_template_to_table_user_sign_document_template extends CDbMigration
{
    const TABLE = 'user_sign_document_template';

    public function up()
    {
        $this->insert(self::TABLE, [
            'document_type_id' => DocumentType::MAKLERAUFTRAG,
            'template'         => '{"name":"Maklervertrag","elements":[{"type":"Highlight","placeholder":"Kunde","x":0.53697478991597003,"y":0.83422459893047995,"width":0.25,"height":0.050000000000000003,"page":1},{"type":"Highlight","placeholder":"Kunde","x":0.53529411764706003,"y":0.62210338680927002,"width":0.25,"height":0.050000000000000003,"page":2},{"type":"Highlight","placeholder":"Kunde","x":0.53613445378151003,"y":0.30005941770647998,"width":0.25,"height":0.050000000000000003,"page":5},{"type":"Highlight","placeholder":"Kunde","x":0.10420168067226999,"y":0.54842543077837003,"width":0.25,"height":0.050000000000000003,"page":5},{"type":"Highlight","placeholder":"Kunde","x":0.53445378151261003,"y":0.45335710041591998,"width":0.25,"height":0.050000000000000003,"page":6}]}',
            'fallback'         => 1
        ]);
    }

    public function down()
    {
        return true;
    }
}
