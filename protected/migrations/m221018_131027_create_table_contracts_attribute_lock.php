<?php

final class m221018_131027_create_table_contracts_attribute_lock extends CDbMigration
{
    private const TABLE    = 'contracts_attribute_lock';
    private const FK_NAME  = 'FK_contracts_attribute_lock_contracts_id';
    private const IDX_NAME = 'UNIQUE_contract_id_attribute';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                 => 'pk',
                'contract_id'        => 'int unsigned NOT NULL',
                'attribute'          => 'varchar(25) NOT NULL',
                'is_locked'          => 'tinyint(1) DEFAULT 0',
                'create_datetime'    => 'datetime NOT NULL',
                'last_edit_datetime' => 'datetime NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            'contract_id',
            Contracts::model()->tableName(),
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createIndex(self::IDX_NAME, self::TABLE, ['contract_id', 'attribute'], true);

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
