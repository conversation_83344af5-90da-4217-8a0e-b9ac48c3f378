<?php

use Business\Company\DmbRechtsschutz;

final class m221027_091800_update_brokerid_patterns_dmb extends CDbMigration
{
    private const NEW_PATTERNS = [
        ['regex' => '/^(\d{5})$/', 'replacement' => '0000${1}'],
        ['regex' => '/^(\d{4})$/', 'replacement' => '00000${1}'],
        ['regex' => '/^0000(\d{5})$/', 'replacement' => '${1}'],
        ['regex' => '/^00000(\d{4})$/', 'replacement' => '${1}'],
    ];

    private string $brokerIdPatternTableName;

    public function __construct()
    {
        $this->brokerIdPatternTableName = BrokeridPattern::model()->tableName();
    }

    /**
     * @return bool
     */
    public function up(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $this->insert($this->brokerIdPatternTableName, array_merge(
                ['insurance_company_id' => DmbRechtsschutz::ID],
                $newPattern,
            ));
        }

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        foreach (self::NEW_PATTERNS as $newPattern) {
            $patternCriteria = new CDbCriteria();
            $patternCriteria->addCondition('regex = :regex');
            $patternCriteria->addCondition('replacement = :replacement');
            $patternCriteria->addCondition('insurance_company_id = :insurance_company_id');

            $this->delete($this->brokerIdPatternTableName, $patternCriteria->condition, [
                ':regex'                => $newPattern['regex'],
                ':replacement'          => $newPattern['replacement'],
                ':insurance_company_id' => DmbRechtsschutz::ID,
            ]);
        }

        return true;
    }
}
