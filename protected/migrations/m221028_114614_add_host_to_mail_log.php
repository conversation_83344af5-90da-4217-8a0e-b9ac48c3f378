<?php

final class m221028_114614_add_host_to_mail_log extends CDbMigration
{
    private const DATABASE = 'log_db';
    private const TABLE    = 'log_mail';
    private const COLUMN   = 'host';

    public function getDbConnection()
    {
        return Yii::app()->logdb;
    }

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'string');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
