<?php

final class m221102_075016_add_fondsfinanz_company_insurance_company_id extends CDbMigration
{
    private const TABLE  = 'bu_fondsfinanz_company';
    private const COLUMN = 'insurance_company_id';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned');
        $this->addForeignKey(
            'fk_ff_company_company',
            self::TABLE,
            self::COLUMN,
            'insurance_company',
            'id',
            'SET NULL',
            'SET NULL'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
