<?php

final class m221102_161540_update_client_companydata_with_rechtsform extends CDbMigration
{
    private const TABLE     = 'client_companydata';
    private const REF_TABLE = 'rechtsform';
    private const COLUMN    = 'rechtsform_id';
    private const FK_NAME   = 'rechtsform_id_fk';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'INT UNSIGNED');

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            self::COLUMN,
            self::REF_TABLE,
            'id',
            'SET NULL',
            'SET NULL'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
