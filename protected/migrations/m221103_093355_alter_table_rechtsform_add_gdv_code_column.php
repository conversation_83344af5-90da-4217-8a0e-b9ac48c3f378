<?php

final class m221103_093355_alter_table_rechtsform_add_gdv_code_column extends CDbMigration
{
    private const TABLE            = 'rechtsform';
    private const COLUMN_GDV_CODE  = 'gdv_code';
    private const COLUMN_NAME_LONG = 'name_long';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN_NAME_LONG, 'string AFTER name');
        $this->addColumn(self::TABLE, self::COLUMN_GDV_CODE, 'int');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_GDV_CODE);
        $this->dropColumn(self::TABLE, self::COLUMN_NAME_LONG);

        return true;
    }
}
