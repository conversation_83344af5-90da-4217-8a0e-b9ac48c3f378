<?php

declare(strict_types=1);

final class m221103_093356_create_and_update_rechtsform_options extends CDbMigration
{
    private const TABLE = 'rechtsform';

    private const EXISTING_RECHTSFORM_ENTRIES_MAPPER = [
        'AG'                 => ['gdv_code' => 9, 'name_long' => 'Akitengesellschaft'],
        'GmbH'               => ['gdv_code' => 5, 'name_long' => 'Gesellschaft mit beschränkter Haftung'],
        'UG'                 => ['gdv_code' => 19, 'name_long' => 'Unternehmergesellschaft'],
        'OHG'                => ['gdv_code' => 3, 'name_long' => 'Offene Handelsgesellschaft'],
        'GbR'                => ['gdv_code' => 2, 'name_long' => 'Gesellschaft bürgerlichen Rechts/BGB-Gesellschaft'],
        'KG'                 => ['gdv_code' => 4, 'name_long' => 'Kommanditgesellschaft'],
        'e.K.'               => ['gdv_code' => 14, 'name_long' => 'Eingetragener Kaufmann/Eingetragene Kauffrau'],
        'GmbH & Co KG'       => ['gdv_code' => 7, 'name_long' => 'GmbH & Co. KG'],
        'Sonstige'           => ['gdv_code' => 99, 'name_long' => 'Sonstige'],
        'UG & Co. KG'        => ['gdv_code' => 55, 'name_long' => 'UG (haftungsbeschränkt) & Co. KG'],
        'Einzelunternehmung' => ['gdv_code' => 62, 'name_long' => 'Einzelunternehmen einer sonstigen jurist. Person'],
    ];

    private const NEW_RECHTSFORM_ENTRIES = [
        'Natürliche Person'                             => ['gdv_code' => 1, 'name_long' => 'Natürliche Person'],
        'gGmbH'                                         => ['gdv_code' => 6, 'name_long' => 'Gemeinnützige Gesellschaft mit beschränkter Haftung'],
        'KGaA'                                          => ['gdv_code' => 8, 'name_long' => 'Kommanditgesellschaft auf Aktien'],
        'e. V.'                                         => ['gdv_code' => 10, 'name_long' => 'Eingetragener Verein'],
        'Verein nach BGB'                               => ['gdv_code' => 11, 'name_long' => 'Verein nach BGB'],
        'eG'                                            => ['gdv_code' => 12, 'name_long' => 'Eingetragene Genossenschaft'],
        'Gesellschaft des öffentlichen Rechts'          => ['gdv_code' => 13, 'name_long' => 'Gesellschaft des öffentlichen Rechts'],
        'PartG'                                         => ['gdv_code' => 15, 'name_long' => 'Partnerschaftsgesellschaft (Angehöriger freier Berufe)'],
        'Partnerreederei'                               => ['gdv_code' => 16, 'name_long' => 'Partnerreederei'],
        'AG & Co. KG'                                   => ['gdv_code' => 17, 'name_long' => 'AG & Co. KG'],
        'VVaG'                                          => ['gdv_code' => 18, 'name_long' => 'Versicherungsverein auf Gegenseitigkeit'],
        'GmbH & Co. KGaA'                               => ['gdv_code' => 20, 'name_long' => 'GmbH & Co. KGaA'],
        'AG & Co. KGaA'                                 => ['gdv_code' => 21, 'name_long' => 'AG & Co. KGaA'],
        'Stiftung & Co. KGaA'                           => ['gdv_code' => 22, 'name_long' => 'Stiftung & Co. KGaA'],
        'gAG'                                           => ['gdv_code' => 23, 'name_long' => 'Gemeinnützige Aktiengesellschaft'],
        'InvAG'                                         => ['gdv_code' => 24, 'name_long' => 'Investmentaktiengesellschaft'],
        'Ltd'                                           => ['gdv_code' => 25, 'name_long' => 'Private Company Limited by Shares'],
        'Inc.'                                          => ['gdv_code' => 26, 'name_long' => 'Incorporated'],
        'Juristische Person alten hamburgischen Rechts' => ['gdv_code' => 27, 'name_long' => 'Juristische Person alten hamburgischen Rechts'],
        'KdöR'                                          => ['gdv_code' => 28, 'name_long' => 'Körperschaft des öffentlichen Rechts'],
        'AdöR'                                          => ['gdv_code' => 29, 'name_long' => 'Anstalt des öffentlichen Rechts'],
        'Stiftung des privaten Rechts'                  => ['gdv_code' => 30, 'name_long' => 'Stiftung des privaten Rechts'],
        'Stiftung des öffentlichen Rechts'              => ['gdv_code' => 31, 'name_long' => 'Stiftung des öffentlichen Rechts'],
        'Gemeinnützige Stiftung'                        => ['gdv_code' => 32, 'name_long' => 'Gemeinnützige Stiftung'],
        'Regiebetrieb'                                  => ['gdv_code' => 33, 'name_long' => 'Regiebetrieb'],
        'Eigenbetrieb'                                  => ['gdv_code' => 34, 'name_long' => 'Eigenbetrieb'],
        'Zweckverband'                                  => ['gdv_code' => 35, 'name_long' => 'Zweckverband'],
        'Gewerkschaften'                                => ['gdv_code' => 36, 'name_long' => 'Gewerkschaften'],
        'Politische Parteien'                           => ['gdv_code' => 37, 'name_long' => 'Politische Parteien'],
        'Kolonialges. nach deutschem Schutzgeb.'        => ['gdv_code' => 38, 'name_long' => 'Kolonialgesellschaft nach deutschem Schutzgebietsrecht'],
        'Bergrechtliche Gewerkschaft'                   => ['gdv_code' => 39, 'name_long' => 'Bergrechtliche Gewerkschaft'],
        'ARGE'                                          => ['gdv_code' => 40, 'name_long' => 'Bietergemeinschaft / Arbeitsgemeinschaft'],
        'IG'                                            => ['gdv_code' => 41, 'name_long' => 'Interessengemeinschaft / Strategische Allianz'],
        'Joint-Venture'                                 => ['gdv_code' => 42, 'name_long' => 'Joint-Venture'],
        'Nicht eingetragener Verein'                    => ['gdv_code' => 43, 'name_long' => 'Nicht eingetragener Verein'],
        'Altrechtlicher Verein'                         => ['gdv_code' => 44, 'name_long' => 'Altrechtlicher Verein'],
        'Ltd. & Co. KG'                                 => ['gdv_code' => 45, 'name_long' => 'Ltd. & Co. KG'],
        'Rechtsform ausländischen Rechts'               => ['gdv_code' => 46, 'name_long' => 'Rechtsform ausländischen Rechts'],
        'HRA'                                           => ['gdv_code' => 47, 'name_long' => 'Rechtsform ausländischen Rechts HRA'],
        'HRB'                                           => ['gdv_code' => 48, 'name_long' => 'Rechtsform ausländischen Rechts HRB'],
        'PLC & Co. KG'                                  => ['gdv_code' => 49, 'name_long' => 'PLC & Co. KG'],
        'REITG'                                         => ['gdv_code' => 50, 'name_long' => 'REIT-Aktiengesellschaft'],
        'Stiftung & Co. KG'                             => ['gdv_code' => 51, 'name_long' => 'Stiftung & Co. KG'],
        'Societas Europaea, SE'                         => ['gdv_code' => 52, 'name_long' => 'Europäische Gesellschaft/Aktiengesellschaft'],
        'EWIV'                                          => ['gdv_code' => 53, 'name_long' => 'Europäische wirtschaftliche Interessenvereinigung'],
        'im Handelsregister eingetragene Erbengem.'     => ['gdv_code' => 54, 'name_long' => 'im Handelsregister eingetragene Erbengemeinschaft'],
        'GmbH & Co. OHG'                                => ['gdv_code' => 56, 'name_long' => 'GmbH & Co. OHG'],
        'GmbH & Still'                                  => ['gdv_code' => 57, 'name_long' => 'GmbH & Still'],
        'Wirtschaftlicher Verein'                       => ['gdv_code' => 58, 'name_long' => 'Wirtschaftlicher Verein'],
        'AG & Co. OHG'                                  => ['gdv_code' => 59, 'name_long' => 'AG & Co. OHG'],
        'ausländische Kapitalgesellschaft & Co. KG'     => ['gdv_code' => 60, 'name_long' => 'ausländische Kapitalgesellschaft & Co. KG'],
        'Einzelkaufmann'                                => ['gdv_code' => 61, 'name_long' => 'Einzelkaufmann'],
        'Societas Cooperativa Europaea, SCE'            => ['gdv_code' => 63, 'name_long' => 'Europäische Genossenschaft'],
        'Kleingewerbetreibender'                        => ['gdv_code' => 64, 'name_long' => 'Kleingewerbetreibender'],
        'KGaA & Co.KG'                                  => ['gdv_code' => 65, 'name_long' => 'KGaA & Co.KG'],
        'KGaA & Co.OHG'                                 => ['gdv_code' => 66, 'name_long' => 'KGaA & Co.OHG'],
        'OHG mbH'                                       => ['gdv_code' => 67, 'name_long' => 'OHG mbH'],
        'ohne Rechtsform'                               => ['gdv_code' => 98, 'name_long' => 'ohne Rechtsform'],
    ];

    public function up(): bool
    {
        foreach (self::EXISTING_RECHTSFORM_ENTRIES_MAPPER as $name => $updateData) {
            $this->update(self::TABLE, $updateData, 'name = :name', [':name' => $name]);
        }

        foreach (self::NEW_RECHTSFORM_ENTRIES as $name => $createData) {
            $this->insert(self::TABLE, array_merge(['name' => $name], $createData));
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::NEW_RECHTSFORM_ENTRIES as $name => $_) {
            $this->delete(self::TABLE, 'name = :name', [':name' => $name]);
        }

        foreach (self::EXISTING_RECHTSFORM_ENTRIES_MAPPER as $name => $_) {
            $this->update(self::TABLE, [
                'name_long' => null,
                'gdv_code'  => null,
            ], 'name = :name', [':name' => $name]);
        }

        return true;
    }
}
