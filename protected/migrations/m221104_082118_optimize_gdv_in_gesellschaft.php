<?php

final class m221104_082118_optimize_gdv_in_gesellschaft extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                gdv_in_gesellschaft
            DROP COLUMN
                id,
            MODIFY COLUMN
                is_gdv TINYINT(1) NOT NULL,
            ADD PRIMARY KEY (transfer_id);
        EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                gdv_in_gesellschaft
            DROP PRIMARY KEY,
            ADD COLUMN
                id INT AUTO_INCREMENT PRIMARY KEY FIRST,
            MODIFY COLUMN
                transfer_id INT,
            MODIFY COLUMN
                is_gdv TINYINT(1);
        EOSQL);

        return true;
    }
}
