<?php

final class m221104_171002_create_table_broker_id__log extends CreateTableMigration
{
    public $tableName = 'broker_id_log';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'vermittlernummer'     => 'string',
            'insurance_company_id' => 'int unsigned not null',
            'type_id'              => 'int',
            'status'               => 'varchar(50)',
            'is_verkettet'         => 'int',
            'create_user_id'       => 'int unsigned not null',
            'create_datetime'      => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'NO ACTION', 'CASCADE');
        $this->addOwnFk('user', 'create_user_id', 'user', 'NO ACTION', 'CASCADE');
    }
}
