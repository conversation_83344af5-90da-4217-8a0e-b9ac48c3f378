<?php

final class m221115_140738_alter_table_broker_id_log extends CDbMigration
{
    private const TABLE        = 'broker_id_log';
    private const VNR_COL      = 'vermittlernummer';
    private const BROKER_ID    = 'broker_id';
    private const BROKER_ID_FK = 'fk_' . self::TABLE . '_broker_id';
    private const VALIDATED    = 'validated';
    private const BY_USER      = 'by_user';

    public function up(): bool
    {
        $this->dropColumn(self::TABLE, self::VNR_COL);

        $this->addColumn(self::TABLE, self::BROKER_ID, 'int unsigned AFTER id');
        $this->addForeignKey(
            self::BROKER_ID_FK,
            self::TABLE,
            self::BROKER_ID,
            'broker_id',
            'id',
            'NO ACTION',
            'CASCADE');

        $this->addColumn(self::TABLE, self::VALIDATED, 'tinyint(1) AFTER create_user_id');
        $this->addColumn(self::TABLE, self::BY_USER, 'tinyint(1) AFTER validated');

        return true;
    }

    public function down(): bool
    {
        $this->addColumn(self::TABLE, self::VNR_COL, 'string');
        $this->dropForeignKey(self::BROKER_ID_FK, self::TABLE);
        $this->dropColumn(self::TABLE, self::BROKER_ID);
        $this->dropColumn(self::TABLE, self::VALIDATED);
        $this->dropColumn(self::TABLE, self::BY_USER);

        return true;
    }
}
