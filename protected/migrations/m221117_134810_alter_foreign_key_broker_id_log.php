<?php

final class m221117_134810_alter_foreign_key_broker_id_log extends CDbMigration
{
    private const TABLE        = 'broker_id_log';
    private const BROKER_ID_FK = 'fk_' . self::TABLE . '_broker_id';
    private const BROKER_ID    = 'broker_id';

    public function up(): bool
    {
        $this->dropForeignKey(self::BROKER_ID_FK, self::TABLE);
        $this->addForeignKey(
            self::BROKER_ID_FK,
            self::TABLE,
            self::BROKER_ID,
            'broker_id',
            'id',
            'SET NULL',
            'CASCADE');

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::BROKER_ID_FK, self::TABLE);
        $this->addForeignKey(
            self::BROKER_ID_FK,
            self::TABLE,
            self::BROKER_ID,
            'broker_id',
            'id',
            'NO ACTION',
            'CASCADE');

        return true;
    }
}
