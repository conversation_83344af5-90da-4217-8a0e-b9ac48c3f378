<?php

final class m221121_132523_add_vorgangv2_ids_bestandsuebertragung extends CDbMigration
{
    private const TABLES = [
        'bu_portfolio_transfer',
        'bu_release_declaration'
    ];

    private const COLUMN = 'vorgang_id';

    public function up(): bool
    {
        foreach (self::TABLES as $table) {
            $this->addColumn($table, self::COLUMN, 'int');
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::TABLES as $table) {
            $this->dropColumn($table, self::COLUMN);
        }

        return true;
    }
}
