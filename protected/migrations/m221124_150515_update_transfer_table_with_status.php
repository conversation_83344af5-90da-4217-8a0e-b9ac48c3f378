<?php

declare(strict_types=1);

final class m221124_150515_update_transfer_table_with_status extends CDbMigration
{
    /**
     * @return bool
     * @throws CDbException
     */
    public function up(): bool
    {
        $this->execute(<<<SQL
            ALTER TABLE bipro.transfer
                DROP COLUMN status_is_computed,
                ADD status TINYINT NULL DEFAULT NULL,
                ADD status_last_updated_at DATETIME NULL DEFAULT NULL;
        SQL);

        return true;
    }

    /**
     * @return bool
     * @throws CDbException
     */
    public function down(): bool
    {
        $this->execute(<<<SQL
            ALTER TABLE bipro.transfer
                DROP COLUMN status_last_updated_at,
                DROP COLUMN status,
                ADD status_is_computed TINYINT(1) DEFAULT 0 NULL;
        SQL);

        return true;
    }
}
