<?php

class m221124_164156_alter_right_description_dokumente_m_k extends CDbMigration
{
    private const TABLE = 'rights';

    private const COLUMN = 'description';

    public function up()
    {
        $this->update(self::TABLE, [self::COLUMN => 'Der zugeordnete Vermittler steht selber in den (M-K)-Dokumenten, sofern eine IHK-Registernummer eingetragen ist.'], 'id = ' . Rights::DOKUMENTE_M_K);
    }

    public function down()
    {
        $this->update(self::TABLE, [self::COLUMN => 'Der zugeordnete Vermittler steht selber im Maklerauftrag, sofern eine IHK-Registernummer eingetragen ist.'], 'id = ' . Rights::DOKUMENTE_M_K);

        return true;
    }
}
