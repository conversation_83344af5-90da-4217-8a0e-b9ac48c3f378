<?php

class m221124_164256_alter_right_description_profil extends CDbMigration
{
    private const TABLE = 'rights';

    private const COLUMN = 'description';

    public function up()
    {
        $this->update(self::TABLE, [self::COLUMN => 'Es können unter Stammdaten/Profile SMTP-Daten, eine eigene Signatur, Profile der Vergleichsrechner eingestellt werden, Einstellung der ablaufenden Verträge vorgenommen werden, eine eigene Unterschrift hochgeladen und individuelle (M-K)-Dokumente hinterlegt werden.'], 'id = ' . Rights::PROFIL);
    }

    public function down()
    {
        $this->update(self::TABLE, [self::COLUMN => 'Es können unter Stammdaten/Profile SMTP-Daten, eine eigene Signatur, Profile der Vergleichsrechner eingestellt werden, Einstellung der ablaufenden Verträge vorgenommen werden, eine eigene Unterschrift hochgeladen und individuelle Makleraufträge hinterlegt werden.'], 'id = ' . Rights::PROFIL);

        return true;
    }
}
