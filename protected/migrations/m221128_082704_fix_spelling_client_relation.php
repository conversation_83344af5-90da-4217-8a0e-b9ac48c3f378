<?php

use Demv\Werte\Person\Beziehung\Beruflich\InhaberGeschaeftsfuehrer;

final class m221128_082704_fix_spelling_client_relation extends CDbMigration
{
    private const TABLE           = 'client_relation_type';
    private const OLD_DESCRIPTION = 'Inhabers/Geschäftsführer';
    private const NEW_DESCRIPTION = 'Inhaber/Geschäftsführer';
    private const OLD_TEXT        = '<i>Inhabers/Geschäftsführer</i> <b>{kunde1}</b> hat als Angehörige <b>{kunde2}</b>';
    private const NEW_TEXT        = '<i>Inhaber/Geschäftsführer</i> <b>{kunde1}</b> hat als Angehörige <b>{kunde2}</b>';

    public function up(): bool
    {
        $this->update(
            self::TABLE,
            [
                'name' => self::NEW_DESCRIPTION,
                'text' => self::NEW_TEXT
            ],
            'id = :id',
            [':id' => InhaberGeschaeftsfuehrer::ID]
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE,
            [
                'name' => self::OLD_DESCRIPTION,
                'text' => self::OLD_TEXT
            ],
            'id = :id',
            [':id' => InhaberGeschaeftsfuehrer::ID]
        );

        return true;
    }
}
