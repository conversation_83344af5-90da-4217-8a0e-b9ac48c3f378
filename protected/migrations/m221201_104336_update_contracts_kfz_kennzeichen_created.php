<?php

declare(strict_types=1);

final class m221201_104336_update_contracts_kfz_ken<PERSON><PERSON><PERSON>_created extends CDbMigration
{
    private const TABLE = 'contracts_kfz_kennzeichen';

    /**
     * @return bool
     */
    public function up(): bool
    {
        $this->addColumn(self::TABLE, 'create_datetime', 'DATETIME NULL');
        $this->addColumn(self::TABLE, 'update_datetime', 'DATETIME NULL');
        $this->addColumn(self::TABLE, 'created_by', 'TINYINT NULL');

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $this->dropColumn(self::TABLE, 'created_by');
        $this->dropColumn(self::TABLE, 'update_datetime');
        $this->dropColumn(self::TABLE, 'create_datetime');

        return true;
    }
}
