<?php

final class m221213_091735_create_table_client_attribute_lock extends CDbMigration
{
    private const TABLE    = 'client_attribute_lock';
    private const FK_NAME  = 'FK_client_attribute_lock_client_id';
    private const IDX_NAME = 'UNIQUE_client_id_attribute';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                 => 'pk',
                'client_id'          => 'int unsigned NOT NULL',
                'attribute'          => 'varchar(40) NOT NULL',
                'is_locked'          => 'tinyint(1) DEFAULT 0',
                'create_datetime'    => 'datetime NOT NULL',
                'last_edit_datetime' => 'datetime NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            'client_id',
            Client::model()->tableName(),
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createIndex(self::IDX_NAME, self::TABLE, ['client_id', 'attribute'], true);

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
