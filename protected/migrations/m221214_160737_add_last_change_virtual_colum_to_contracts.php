<?php

declare(strict_types=1);

final class m221214_160737_add_last_change_virtual_colum_to_contracts extends CDbMigration
{
    private const TABLENAME         = 'contracts';
    private const INDEX_LAST_CHANGE = 'index_last_change';
    private const COLUMN            = 'last_change';

    public function up(): bool
    {
        $this->execute(<<<SQL
            ALTER TABLE contracts
            ADD last_change timestamp AS
                (COALESCE(last_edit_timestamp,create_datetime)) VIRTUAL;
        SQL);

        $this->createIndex(self::INDEX_LAST_CHANGE, self::TABLENAME, self::COLUMN);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::INDEX_LAST_CHANGE, self::TABLENAME);
        $this->dropColumn(self::TABLENAME, self::COLUMN);

        return true;
    }
}
