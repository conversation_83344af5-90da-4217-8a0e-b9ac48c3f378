<?php

declare(strict_types=1);

final class m221216_081235_update_bipro_file_table_with_client_file_id extends CDbMigration
{
    private const TABLE       = 'bipro_file';
    private const DB          = 'bipro';
    private const TABLE_NAME  = self::DB . '.' . self::TABLE;
    private const COLUMN_NAME = 'client_file_id';
    private const FK_NAME     = 'fk_bipro_file_client_files';

    /**
     * @return bool
     */
    public function up(): bool
    {
        $this->execute('SET SESSION FOREIGN_KEY_CHECKS = 0');

        $this->addColumn(self::TABLE_NAME, self::COLUMN_NAME, 'INTEGER UNSIGNED NULL');

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE_NAME,
            self::COLUMN_NAME,
            'demv_crm_release1.client_files',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->execute('SET SESSION FOREIGN_KEY_CHECKS = 1');

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE_NAME);
        $this->dropColumn(self::TABLE_NAME, self::COLUMN_NAME);

        return true;
    }
}
