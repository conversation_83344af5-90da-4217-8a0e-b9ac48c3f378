<?php

declare(strict_types=1);

final class m221227_091048_create_document_type_tags_table extends CreateTableMigration
{
    private const FK_NAME = 'fk_document_type_tags_document_type';

    /** @var string $tableName */
    public $tableName = 'document_type_tags';

    public function getColumns(): array
    {
        return [
            'id'               => 'pk',
            'document_type_id' => 'INT UNSIGNED NOT NULL',
            'tag'              => 'string NOT NULL',
        ];
    }

    public function addForeignKeys(): void
    {
        $this->addForeignKey(
            self::FK_NAME,
            $this->tableName,
            'document_type_id',
            'document_type',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }
}
