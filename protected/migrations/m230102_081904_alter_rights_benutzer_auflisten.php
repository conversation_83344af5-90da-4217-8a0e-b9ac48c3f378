<?php

final class m230102_081904_alter_rights_ben<PERSON><PERSON>_auflisten extends CDbMigration
{
    private const TABLE_RIGHTS      = 'rights';
    private const TABLE_USER        = 'user';
    private const TABLE_ROLE_RIGHTS = 'user_role_rights';
    private const TABLE_USER_RIGHTS = 'user_rights';

    private const RIGHT = [
        'name'        => '<PERSON><PERSON><PERSON><PERSON> in den Vorgängen auflisten',
        'description' => 'E<PERSON> dürfen in den Vorgängen alle Benutzer einer Firma aufgelistet werden',
        'sort'        => 5,
    ];

    public function up(): bool
    {
        $this->insert(
            self::TABLE_RIGHTS,
            self::RIGHT
        );

        foreach ($this->getUserRoles() as $userRoleId) {
            $this->insert(
                self::TABLE_ROLE_RIGHTS,
                [
                    'user_role_id' => $userRoleId,
                    'right_id'     => Rights::VORGAENGE_BENUTZER_FIRMA_ANZEIGEN,
                ]
            );
        }

        $userIds = User::model()->getDbConnection()
            ->createCommand()
            ->select('id')
            ->from(self::TABLE_USER)
            ->where(['in', 'user_role', $this->getUserRoles()])
            ->queryColumn();

        if (empty($userIds)) {
            echo 'No users found.' . PHP_EOL;
            echo User::model()->getDbConnection()
                ->createCommand()
                ->select('id')
                ->from(self::TABLE_USER)
                ->where(['in', 'user_role', $this->getUserRoles()])
                ->text . PHP_EOL;

            return true;
        }

        $values = array_map(static function ($userId): string {
            return sprintf('(%s,%s)', $userId, Rights::VORGAENGE_BENUTZER_FIRMA_ANZEIGEN);
        }, $userIds);

        UserRights::model()->getDbConnection()
            ->createCommand(
                sprintf('insert into %s (user_id, `right`) values %s',
                    UserRights::model()->tableName(),
                    implode(',', $values)
                ))
            ->execute();

        return true;
    }

    public function down(): bool
    {
        $criteria = new CDbCriteria();
        $criteria->compare('name', self::RIGHT['name']);
        $right = Rights::model()->find($criteria);

        if ($right === null) {
            return true;
        }

        $this->delete(
            self::TABLE_ROLE_RIGHTS,
            'right_id = :right',
            [':right' => $right->id]
        );

        $this->delete(
            self::TABLE_USER_RIGHTS,
            '`right` = :right',
            [':right' => $right->id]
        );

        $this->delete(
            self::TABLE_RIGHTS,
            'name = :name',
            ['name' => self::RIGHT['name']]
        );

        return true;
    }

    /**
     * @return int[]
     */
    private function getUserRoles(): array
    {
        return [
            UserRole::ADMIN,
            UserRole::MAINBROKER,
            UserRole::BACKOFFICE,
            UserRole::TESTUSER,
        ];
    }
}
