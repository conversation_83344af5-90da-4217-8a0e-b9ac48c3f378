<?php

declare(strict_types=1);

final class m230102_143027_client_file_document_type_log_table extends CreateTableMigration
{
    public $tableName = 'client_file_document_type_log';

    /**
     * @return string[]
     */
    public function getColumns(): array
    {
        return [
            'id'               => 'pk',
            'client_file_id'   => 'int UNSIGNED NOT NULL',
            'document_type_id' => 'int UNSIGNED NOT NULL',
            'create_user_id'   => 'int UNSIGNED NOT NULL',
            'create_datetime'  => 'datetime',
        ];
    }

    public function addForeignKeys(): void
    {
        $this->addOwnFk(
            'client_files',
            'client_file_id',
            'client_files',
            'CASCADE',
            'CASCADE'
        );

        $this->addOwnFk(
            'document_type',
            'document_type_id',
            'document_type',
            'CASCADE',
            'CASCADE'
        );

        $this->addOwnFk(
            'user',
            'create_user_id',
            'user',
            'CASCADE',
            'CASCADE'
        );
    }

    public function up()
    {
        parent::up();

        $this->createIndex("idx-{$this->tableName}-client_file_id", $this->tableName, 'client_file_id');
    }
}
