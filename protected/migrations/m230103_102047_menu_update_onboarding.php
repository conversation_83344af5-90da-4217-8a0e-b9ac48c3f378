<?php

final class m230103_102047_menu_update_onboarding extends CDbMigration
{
    public function up(): bool
    {
        $this->delete('system_menu_items', 'title = "Live-Webinare / Mediathek"');
        $this->delete('system_menu_items', 'title = "Schulungsvideos"');
        $this->delete('system_menu_items', 'title = "FAQ"');
        $this->delete('system_menu_items', 'title = "Schulungen"');
        $this->update('system_menus', ['title' => 'Wissen'], 'title = "Weiterbildung"');
        $this->update('system_menu_items', ['sort' => 40], 'title = "Facebook-Forum"');
        $this->update('system_menu_items', ['title' => 'Weiterbildungskonto'], 'title = "Beratungsvorsprung"');
        $this->insert('system_menu_items', [
            'menu_id'                    => 5,
            'title'                      => 'Schulungen',
            'active'                     => 1,
            'regex'                      => '(schulungen)',
            'url'                        => '/schulungen',
            'target'                     => '_top',
            'exclude_testuser'           => 0,
            'admin_only'                 => 0,
            'sort'                       => 5,
            'exclude_insurancecompanies' => 1,
        ]);

        return true;
    }

    public function down(): bool
    {
        echo "m230103_102047_menu_update_onboarding does not support migration down.\n";

        return true;
    }
}
