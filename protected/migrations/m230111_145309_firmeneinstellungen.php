<?php

final class m230111_145309_firmeneinstellungen extends CreateTableMigration
{
    public $tableName = 'agency_settings';

    public function getColumns()
    {
        return [
            'id'                  => 'pk',
            'agency_id'           => 'int unsigned unique not null',
            'zusammenarbeit_demv' => 'bool default 1',
            'last_edit_datetime'  => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('agency', 'agency_id', 'agency', 'CASCADE', 'CASCADE');
    }
}
