<?php

declare(strict_types=1);

final class m230117_162751_remove_transfer_status_table extends CDbMigration
{
    private const TABLE = 'bipro.transfer_status';

    private const FK_NAME = 'fk_transfer_status_transfer';

    /**
     * @return bool
     */
    public function up(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'              => 'pk',
                'tid'             => 'int NOT NULL',
                'status'          => 'string NOT NULL',
                'type'            => 'string NOT NULL',
                'create_datetime' => 'datetime',
            ]
        );

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            'tid',
            'transfer',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }
}
