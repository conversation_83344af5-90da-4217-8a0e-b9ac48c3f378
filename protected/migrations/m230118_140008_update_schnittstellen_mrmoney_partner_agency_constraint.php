<?php

declare(strict_types=1);

final class m230118_140008_update_schnittstellen_mrmoney_partner_agency_constraint extends CDbMigration
{
    private const TABLE = 'schnittstellen_mrmoney_partner';

    private const FK = 'fk_partner_agency';

    /**
     * @return bool
     */
    public function up(): bool
    {
        $this->dropForeignKey(self::FK, self::TABLE);

        $this->addForeignKey(
            self::FK,
            self::TABLE,
            'agency_id',
            'agency',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $this->dropForeignKey(self::FK, self::TABLE);

        $this->addForeignKey(
            self::FK,
            self::TABLE,
            'agency_id',
            'agency',
            'id',
        );

        return true;
    }
}
