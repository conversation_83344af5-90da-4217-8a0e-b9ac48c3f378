<?php

final class m230123_161215_alter_agency_add_update_date extends CDbMigration
{
    const TABLE  = 'agency';
    const COLUMN = 'last_edit_datetime';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'datetime not null DEFAULT NOW()');
        $this->execute('update agency set last_edit_datetime = IFNULL(last_edit_datetime, create_datetime);');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
