<?php

final class m230125_104835_alter_agency_settings_add_filter_columns extends CDbMigration
{
    private const TABLE = 'agency_settings';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, 'product_filter', "ENUM ('exclude', 'include')");
        $this->addColumn(self::TABLE, 'company_filter', "ENUM ('exclude', 'include')");

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, 'product_filter');
        $this->dropColumn(self::TABLE, 'company_filter');

        return true;
    }
}
