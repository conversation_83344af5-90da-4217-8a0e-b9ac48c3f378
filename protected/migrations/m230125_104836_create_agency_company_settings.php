<?php

declare(strict_types=1);

final class m230125_104836_create_agency_company_settings extends CDbMigration
{
    private const TABLE = 'agency_company_settings';

    public function up(): bool
    {
        $this->createTable(self::TABLE, [
            'id'                   => 'int(11) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
            'agency_id'            => 'int(11) unsigned NOT NULL',
            'insurance_company_id' => 'int(11) unsigned NOT NULL',
            'type'                 => "ENUM ('include','exclude')",
        ]);

        $this->createIndex(
            'unique_agency_id_insurance_company_id',
            self::TABLE,
            ['agency_id', 'insurance_company_id'],
            true,
        );

        $this->addForeignKey(
            'fk_' . self::TABLE . '_agency',
            self::TABLE,
            'agency_id',
            'agency',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            'fk_' . self::TABLE . '_insurance_company',
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id',
            'CASCADE',
            'CASCADE',
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
