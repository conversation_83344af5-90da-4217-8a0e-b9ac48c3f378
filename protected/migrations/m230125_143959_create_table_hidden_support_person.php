<?php

final class m230125_143959_create_table_hidden_support_person extends CDbMigration
{
    private const TABLE = 'hidden_support_person';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                        => 'pk',
                'insurance_company_data_id' => 'int unsigned NOT NULL',
                'support_person_id'         => 'int NOT NULL',
            ]
        );

        $this->addForeignKey(
            'FK_hidden_support_person_insurance_company_data_id',
            self::TABLE,
            'insurance_company_data_id',
            InsuranceCompanyData::model()->tableName(),
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            'FK_hidden_support_person_support_person_id',
            self::TABLE,
            'support_person_id',
            SupportPerson::model()->tableName(),
            'id',
            'CASCADE',
            'CASCADE',
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
