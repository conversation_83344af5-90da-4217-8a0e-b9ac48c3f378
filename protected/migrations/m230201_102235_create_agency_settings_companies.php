<?php

final class m230201_102235_create_agency_settings_companies extends CDbMigration
{
    private const TABLE = 'agency_settings_companies';

    public function up(): bool
    {
        $this->createTable(self::TABLE, [
            'agency_id'            => 'int(11) unsigned NOT NULL',
            'insurance_company_id' => 'int(11) unsigned NOT NULL',
        ]);

        $this->addPrimaryKey(
            'pk_' . self::TABLE,
            self::TABLE,
            ['agency_id', 'insurance_company_id'],
        );

        $this->addForeignKey(
            'fk_' . self::TABLE . '_agency_settings',
            self::TABLE,
            'agency_id',
            'agency_settings',
            'agency_id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            'fk_' . self::TABLE . '_insurance_company',
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id',
            'CASCADE',
            'CASCADE',
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
