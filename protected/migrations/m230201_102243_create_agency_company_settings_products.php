<?php

declare(strict_types=1);

final class m230201_102243_create_agency_company_settings_products extends CDbMigration
{
    private const TABLE = 'agency_company_settings_products';

    public function up(): bool
    {
        $this->createTable(self::TABLE, [
            'agency_company_settings_id' => 'int(11) unsigned NOT NULL',
            'product_combo_id'           => 'int(11) unsigned NOT NULL',
        ]);

        $this->addPrimaryKey(
            'pk_' . self::TABLE,
            self::TABLE,
            ['agency_company_settings_id', 'product_combo_id'],
        );

        $this->addForeignKey(
            'fk_' . self::TABLE . '_agency_company_settings',
            self::TABLE,
            'agency_company_settings_id',
            'agency_company_settings',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            'fk_' . self::TABLE . '_product_combo',
            self::TABLE,
            'product_combo_id',
            'product_combo',
            'id',
            'CASCADE',
            'CASCADE',
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
