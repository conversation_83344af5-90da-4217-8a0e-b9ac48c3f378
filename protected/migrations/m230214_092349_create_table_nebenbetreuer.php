<?php

final class m230214_092349_create_table_neben<PERSON><PERSON><PERSON> extends CreateTableMigration
{
    public $tableName = 'nebenbetreuer';

    public function getColumns()
    {
        return [
            'id'         => 'pk',
            'user_id'    => 'int unsigned not null',
            'adviser_id' => 'int unsigned not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
        $this->addOwnFk('adviser', 'adviser_id', 'user', 'CASCADE', 'CASCADE');
    }
}
