<?php

final class m230214_110822_gesellschafts_highlights_entfernen extends CDbMigration
{
    public function up(): bool
    {
        $this->dropTable('system_news');
        $this->dropTable('insurance_company_highlights');
        $criteria = new CDbCriteria();

        $rights = [
            90, //Gesellschaftsnews bearbeiten
            91, //Gesellschaftsnews löschen
            92, //Gesellschaftsnews Übersicht
        ];
        UserRights::model()->deleteAllByAttributes(['right' => $rights]);
        Rights::model()->deleteAllByAttributes(['id' => $rights]);

        return true;
    }

    public function down(): bool
    {
        echo "m230214_110822_gesellschafts_highlights_entfernen does not support migration down.\n";

        return true;
    }
}
