<?php

final class m230214_150649_create_table_hidden_tarif extends CreateTableMigration
{
    public $tableName = 'hidden_tarif';

    public function getColumns()
    {
        return [
            'id'                        => 'pk',
            'tarif_id'                  => 'int(11) NOT NULL',
            'insurance_company_data_id' => 'int unsigned NOT NULL',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('tarif', 'tarif_id', 'tarif', 'CASCADE', 'CASCADE');
        $this->addOwnFk('insurance_company_data', 'insurance_company_data_id', 'insurance_company_data', 'CASCADE', 'CASCADE');
    }
}
