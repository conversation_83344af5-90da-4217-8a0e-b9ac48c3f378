<?php

final class m230214_154719_add_column_new_contract_nr_to_custom_csv_import extends CDbMigration
{
    private const TABLE  = 'custom_csv_import';
    private const COLUMN = 'new_contract_nr';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'string');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
