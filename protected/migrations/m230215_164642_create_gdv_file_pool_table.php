<?php

final class m230215_164642_create_gdv_file_pool_table extends CreateTableMigration
{
    public $tableName = 'gdv_file_pool';

    public function getColumns(): array
    {
        return [
            'id'                   => 'pk',
            'gdv_file_id'          => 'INT NOT NULL',
            'broker_id'            => 'INT UNSIGNED',
        ];
    }

    public function addForeignKeys(): void
    {
        $this->addOwnFk(
            'gdv_file',
            'gdv_file_id',
            'gdv_file',
            'CASCADE',
            'CASCADE'
        );

        $this->addOwnFk(
            'broker',
            'broker_id',
            'broker_id',
            'SET NULL',
            'CASCADE'
        );
    }
}
