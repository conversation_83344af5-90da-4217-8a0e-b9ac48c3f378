<?php

final class m230221_070620_add_benutzer_auflisten_as_standard_for_role_broker extends CDbMigration
{
    public function up(): bool
    {
        $this->insert(
            UserRoleRights::model()->tableName(),
            [
                'user_role_id'  => UserRole::BROKER,
                'right_id'      => Rights::VORGAENGE_BENUTZER_FIRMA_ANZEIGEN,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            UserRoleRights::model()->tableName(),
            'user_role_id = :user_role_id AND right_id = :right_id AND agency_id = :agency_id',
            [
                'user_role_id'  => UserRole::BROKER,
                'right_id'      => Rights::VORGAENGE_BENUTZER_FIRMA_ANZEIGEN,
                'agency_id'     => null,
            ]
        );

        return true;
    }
}
