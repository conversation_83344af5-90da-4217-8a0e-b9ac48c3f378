<?php

use League\Csv\Reader;

final class m230302_201012_create_table_brokerid_validation_rules extends CDbMigration
{
    private const TABLE = 'brokerid_validation_rule';

    public function up(): bool
    {
        $this->createTable(self::TABLE,
            [
                'id'                   => 'pk',
                'insurance_company_id' => 'integer unsigned not null',
                'regex'                => 'string',
                'example'              => 'string',
                'anzahl'               => 'int'
            ]);

        $this->addForeignKey(
            'fk_brokerid_rule_company',
            self::TABLE,
            'insurance_company_id',
            'insurance_company',
            'id'
        );

        //Da nicht alle Gesellschaften in allen Umgebungen vorhanden sind sollen die Regeln einzelnd inserted werden
        //Dadurch sind zumindest die Regeln vorhanden, wo es auch Gesellschaften gibt.
        $csv = Reader::createFromPath(__DIR__ . DS . 'files' . DS . 'brokerid-validation-rules.csv', 'r');
        $csv->setDelimiter(',');
        $csv->setHeaderOffset(0);

        foreach ($csv->getRecords() as $row) {
            try {
                $this->insert(self::TABLE, $row);
            } catch (Exception $e) {
                //Do nothing
            }
        }

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
