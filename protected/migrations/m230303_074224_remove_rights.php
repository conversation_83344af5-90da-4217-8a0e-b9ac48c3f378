<?php

final class m230303_074224_remove_rights extends CDbMigration
{
    private const RIGHTS = [
        14, //Gesellschaft anlegen -> Die Action gibt es dort gar nicht mehr
        27, //Schwerpunkte der Systemnutzer verwalten -> Haben nur 7 HV, was ein <PERSON> ist. Feature fliegt hier eh raus, da es in den Firmeneinstellungen aufgeht
        28, //Wiedervorlagen verwalten -> Ist jetzt Teil der Vorgänge und überschneidet sich damit mit dem Recht
        90, //Gesellschaftsnews bearbeiten -> Gibt es nicht mehr
        91, //Gesellschaftsnews löschen -> Gibt es nicht mehr
        92, //Gesellschaftsnews Übersicht -> Gibt es nicht mehr
        110, //Baufinanzierung - Qualitypool -> "Kann raus, das hat aktuell schon keine Auswirkung"
        130, //Kundenakte - "Dokumente ausgeblendet" -> Haben nur 5 Leute gesetzt. Kann dann eigentlich auch raus
        47,  //Notizen der Systemnutzer verwalten -> <PERSON><PERSON> nur Admins,
        32, //Hat keine Auswirkung, die Ansicht wird trotzdem angezeigt, da es in einen anderen Controller gewandert ist. Da es seit Jahren keinem aufgefallen ist brauchen wir es wohl auch nicht

    ];

    public function up(): bool
    {
        UserRoleRights::model()->deleteAllByAttributes(['right_id' => self::RIGHTS]);
        UserRights::model()->deleteAllByAttributes(['right' => self::RIGHTS]);
        Rights::model()->deleteAllByAttributes(['id' => self::RIGHTS]);

        return true;
    }

    public function down(): bool
    {
        return true;
    }
}
