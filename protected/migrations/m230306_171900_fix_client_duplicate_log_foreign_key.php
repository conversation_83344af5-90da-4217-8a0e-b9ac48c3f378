<?php

declare(strict_types=1);

final class m230306_171900_fix_client_duplicate_log_foreign_key extends CDbMigration
{
    private const TABLE            = 'client_duplicate_log';
    private const FK_CLIENT        = 'fk_client_duplicate_log_client';
    private const FK_MERGED_CLIENT = 'fk_client_duplicate_log_merged_with_client_id';
    private const FK_USER          = 'fk_client_duplicate_log_user';

    public function up(): bool
    {
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $this->dropForeignKey(self::FK_CLIENT, self::TABLE);
        $this->dropForeignKey(self::FK_MERGED_CLIENT, self::TABLE);

        $this->alterColumn(self::TABLE, 'client_id', 'INT(11) UNSIGNED');
        $this->alterColumn(self::TABLE, 'merged_with_client_id', 'INT(11) UNSIGNED');
        $this->alterColumn(self::TABLE, 'merged_by_user_id', 'INT(11) UNSIGNED');

        $this->addForeignKey(
            self::FK_CLIENT,
            self::TABLE,
            'client_id',
            'client',
            'id',
            'SET NULL',
            'CASCADE',
        );

        $this->addForeignKey(
            self::FK_MERGED_CLIENT,
            self::TABLE,
            'merged_with_client_id',
            'client',
            'id',
            'SET NULL',
            'CASCADE',
        );

        $this->addForeignKey(
            self::FK_USER,
            self::TABLE,
            'merged_by_user_id',
            'user',
            'id',
            'SET NULL',
            'CASCADE',
        );

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');

        return true;
    }

    public function down(): bool
    {
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $this->dropForeignKey(self::FK_CLIENT, self::TABLE);
        $this->dropForeignKey(self::FK_MERGED_CLIENT, self::TABLE);
        $this->dropIndex(self::FK_MERGED_CLIENT, self::TABLE);
        $this->dropForeignKey(self::FK_USER, self::TABLE);
        $this->dropIndex(self::FK_USER, self::TABLE);

        $this->alterColumn(self::TABLE, 'client_id', 'INT UNSIGNED NOT NULL');
        $this->alterColumn(self::TABLE, 'merged_with_client_id', 'INT UNSIGNED NOT NULL');
        $this->alterColumn(self::TABLE, 'merged_by_user_id', 'INT UNSIGNED NOT NULL');

        $this->addForeignKey(
            self::FK_CLIENT,
            self::TABLE,
            'client_id',
            'client',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            self::FK_MERGED_CLIENT,
            self::TABLE,
            'client_id',
            'client',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');

        return true;
    }
}
