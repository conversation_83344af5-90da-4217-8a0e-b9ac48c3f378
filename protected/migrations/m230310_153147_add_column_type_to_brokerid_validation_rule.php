<?php

final class m230310_153147_add_column_type_to_brokerid_validation_rule extends CDbMigration
{
    private const TABLE  = 'brokerid_validation_rule';
    private const COLUMN = 'type_id';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'integer unsigned');

        $this->addForeignKey(
            'fk_brokerid_rule_type',
            self::TABLE,
            'type_id',
            'broker_id_type',
            'id'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey('fk_brokerid_rule_type', self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
