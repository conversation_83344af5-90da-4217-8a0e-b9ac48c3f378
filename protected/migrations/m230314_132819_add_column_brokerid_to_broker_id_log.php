<?php

final class m230314_132819_add_column_brokerid_to_broker_id_log extends CDbMigration
{
    const TABLE  = 'broker_id_log';
    const COLUMN = 'new_broker_id';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'varchar(45)');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
