<?php

final class m230321_181304_add_indexes_for_nepatec_missing_import_tracking extends CDbMigration
{
    public function up(): bool
    {
        $this->createIndex('idx_gdv_file_process_id', 'gdv_file', ['process_id']);
        $this->createIndex('idx_vertragsservice_tracked_identities_process_id', 'vertragsservice_tracked_identities', ['process_id']);

        $this->setDbConnection(Yii::app()->bipro);
        $this->createIndex('idx_lieferung_lieferant_einstellzeitpunkt', 'lieferung', ['lieferant', 'einstellzeitpunkt']);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex('idx_gdv_file_process_id', 'gdv_file');
        $this->dropIndex('idx_vertragsservice_tracked_identities_process_id', 'vertragsservice_tracked_identities');

        $this->setDbConnection(Yii::app()->bipro);
        $this->dropIndex('idx_lieferung_lieferant_einstellzeitpunkt', 'lieferung');

        return true;
    }
}
