<?php

declare(strict_types=1);

final class m230327_085755_einarbeitung_betreuung_stats extends CreateTableMigration
{
    public $tableName = 'einarbeitung_betreuung_stats';

    public function getColumns()
    {
        return [
            'id'                     => 'pk',
            'create_datetime'        => 'datetime',
            'user_id'                => 'int unsigned',
            'adviser_before'         => 'int unsigned',
            'adviser_after'          => 'int unsigned',
            'adviser_type'           => 'int',
            'brokerid_count'         => 'int',
            'courtage_request_count' => 'int',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
        $this->addOwnFk('adviser_before', 'adviser_before', 'user', 'CASCADE', 'CASCADE');
        $this->addOwnFk('adviser_after', 'adviser_after', 'user', 'CASCADE', 'CASCADE');
    }
}
