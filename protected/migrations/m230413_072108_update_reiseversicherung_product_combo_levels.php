<?php

final class m230413_072108_update_reiseversicherung_product_combo_levels extends CDbMigration
{
    const TABLE                                 = 'product_combo';
    const LEVEL2_COLUMN                         = 'level2_id';
    const LEVEL3_COLUMN                         = 'level3_id';
    const NAME_COLUMN                           = 'name';
    const LEVEL2_ID                             = 524;
    const REISERUECKTRITTVERSICHERUNG_ID        = 257;
    const REISERUECKTRITTVERSICHERUNG_LEVEL3_ID = 249;
    const REISEGEPAECKVERSICHERUNG_ID           = 258;
    const REISEGEPAECKVERSICHERUNG_LEVEL3_ID    = 250;

    public function up(): bool
    {
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $this->update(self::TABLE,
            [
                self::LEVEL2_COLUMN => self::LEVEL2_ID,
                self::LEVEL3_COLUMN => self::REISERUECKTRITTVERSICHERUNG_LEVEL3_ID,
                self::NAME_COLUMN   => 'Private Sachversicherungen->Reiseversicherung->Reiserücktrittsversicherung'
            ],
            'id = :id',
            [':id' => self::REISERUECKTRITTVERSICHERUNG_ID]
        );

        $this->update(self::TABLE,
            [
                self::LEVEL2_COLUMN => self::LEVEL2_ID,
                self::LEVEL3_COLUMN => self::REISEGEPAECKVERSICHERUNG_LEVEL3_ID,
                self::NAME_COLUMN   => 'Private Sachversicherungen->Reiseversicherung->Reisegepäckversicherung'
            ],
            'id = :id',
            [':id' => self::REISEGEPAECKVERSICHERUNG_ID]
        );

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');

        return true;
    }

    public function down(): bool
    {
        $this->execute('SET FOREIGN_KEY_CHECKS = 0');

        $this->update(self::TABLE,
            [
                self::LEVEL2_COLUMN => self::REISERUECKTRITTVERSICHERUNG_LEVEL3_ID,
                self::LEVEL3_COLUMN => new CDbExpression('NULL'),
                self::NAME_COLUMN   => 'Private Sachversicherungen->Reiserücktrittsversicherung'
            ],
            'id = :id',
            [':id' => self::REISERUECKTRITTVERSICHERUNG_ID]
        );

        $this->update(self::TABLE,
            [
                self::LEVEL2_COLUMN => self::REISEGEPAECKVERSICHERUNG_LEVEL3_ID,
                self::LEVEL3_COLUMN => new CDbExpression('NULL'),
                self::NAME_COLUMN   => 'Private Sachversicherungen->Reisegepäckversicherung'
            ],
            'id = :id',
            [':id' => self::REISEGEPAECKVERSICHERUNG_ID]
        );

        $this->execute('SET FOREIGN_KEY_CHECKS = 1');

        return true;
    }
}
