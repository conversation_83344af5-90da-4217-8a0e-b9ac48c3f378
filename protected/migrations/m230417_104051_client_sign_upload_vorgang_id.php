<?php

class m230417_104051_client_sign_upload_vorgang_id extends CDbMigration
{
    private const TABLE   = 'client_sign_upload';
    private const COLUMN  = 'vorgang_id';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned');

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
