<?php

class m230420_143659_create_table_ab_transfer_log extends CreateTableMigration
{
    public $tableName = 'ab_transfer_log';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'insurance_company_id' => 'int unsigned not null',
            'source'               => 'int',
            'detected_at'          => 'datetime not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
