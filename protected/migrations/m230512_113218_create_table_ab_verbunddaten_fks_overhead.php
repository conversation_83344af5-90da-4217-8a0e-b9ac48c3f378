<?php

final class m230512_113218_create_table_ab_verbunddaten_fks_overhead extends CreateTableMigration
{
    public $tableName = 'ab_verbunddaten_fks_overhead';

    public function getColumns()
    {
        return [
            'id'                   => 'int unsigned not null auto_increment primary key',
            'insurance_company_id' => 'int unsigned not null',
            'filename'             => 'varchar(255) not null',
            'create_datetime'      => 'datetime not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
