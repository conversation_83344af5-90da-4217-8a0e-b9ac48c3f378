<?php

final class m230512_114435_create_table_ab_verbunddaten_fks_bestand extends CreateTableMigration
{
    public $tableName = 'ab_verbunddaten_fks_bestand';

    public function getColumns()
    {
        return [
            'id'                   => 'int unsigned not null auto_increment primary key',
            'filename'             => 'varchar(255) not null',
            'insurance_company_id' => 'int unsigned not null',
            'overhead_id'          => 'int unsigned not null',
            'create_datetime'      => 'datetime not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
        $this->addOwnFk('overhead', 'overhead_id', 'ab_verbunddaten_fks_overhead', 'CASCADE', 'CASCADE');
    }
}
