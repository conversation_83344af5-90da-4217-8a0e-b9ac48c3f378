<?php

final class m230512_114609_create_table_ab_verbunddaten_fks_export extends CreateTableMigration
{
    public $tableName = 'ab_verbunddaten_fks_export';

    public function getColumns()
    {
        return [
            'id'          => 'int unsigned not null auto_increment primary key',
            'filename'    => 'varchar(255)',
            'tries'       => 'tinyint unsigned not null default 0',
            'overhead_id' => 'int unsigned not null',
            'bestand_id'  => 'int unsigned not null',
            'upload_date' => 'datetime not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('overhead', 'overhead_id', 'ab_verbunddaten_fks_overhead', 'CASCADE', 'CASCADE');
        $this->addOwnFk('bestand', 'bestand_id', 'ab_verbunddaten_fks_bestand', 'CASCADE', 'CASCADE');
    }
}
