<?php

final class m230516_170746_add_service_id_field_to_authtoken_table extends CDbMigration
{
    private const TABLE = 'authtoken';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, 'service_id', 'varchar(255) default null');
        $this->alterColumn(self::TABLE, 'user_id', 'integer unsigned default null');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, 'user_id', 'integer unsigned not null');
        $this->dropColumn(self::TABLE, 'service_id');

        return true;
    }
}
