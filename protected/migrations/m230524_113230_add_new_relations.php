<?php

use Demv\Werte\Person\Beziehung\<PERSON><PERSON><PERSON>lich\Arzt;
use Demv\Werte\Person\Beziehung\<PERSON><PERSON><PERSON>lich\BautraegerKaeufer;
use Demv\Werte\Person\Beziehung\Be<PERSON><PERSON>lich\BeruflicheBeziehung;
use Demv\Werte\Person\Beziehung\Beru<PERSON>lich\ImmobilienMakler;
use Demv\Werte\Person\Beziehung\Beruflich\Notar;
use Demv\Werte\Person\Beziehung\Beruflich\NotarMandant;
use Demv\Werte\Person\Beziehung\Beruflich\Patient;
use Demv\Werte\Person\Beziehung\Beruflich\Steuerberater;
use Demv\Werte\Person\Beziehung\Beruflich\SteuerberaterMandant;

final class m230524_113230_add_new_relations extends CDbMigration
{
    private const TABLE            = 'client_relation_type';
    private const RELATION         = 'relation';
    private const COUNTER_RELATION = 'counter_relation';

    private array $daten = [];

    public function __construct()
    {
        $this->daten = [
            [
                self::RELATION         => new Steuerberater(),
                self::COUNTER_RELATION => new SteuerberaterMandant(),
            ],
            [
                self::RELATION         => new Notar(),
                self::COUNTER_RELATION => new NotarMandant(),
            ],
            [
                self::RELATION         => new Arzt(),
                self::COUNTER_RELATION => new Patient(),
            ],
            [
                self::RELATION         => new ImmobilienMakler(),
                self::COUNTER_RELATION => new BautraegerKaeufer(),
            ],
        ];
    }

    public function up(): bool
    {
        /** @var BeruflicheBeziehung[] $relationen */
        foreach ($this->daten as $relationen) {
            $this->insert(
                self::TABLE,
                [
                    'id'                  => $relationen[self::RELATION]->getId(),
                    'name'                => $relationen[self::RELATION]->getName(),
                    'related_relation_id' => $relationen[self::COUNTER_RELATION]->getId(),
                    'category_id'         => ClientRelationType::CATEGORY_WORK,
                ]
            );

            $this->insert(
                self::TABLE,
                [
                    'id'                  => $relationen[self::COUNTER_RELATION]->getId(),
                    'name'                => $relationen[self::COUNTER_RELATION]->getName(),
                    'related_relation_id' => $relationen[self::RELATION]->getId(),
                    'category_id'         => ClientRelationType::CATEGORY_WORK,
                ]
            );
        }

        return true;
    }

    public function down(): bool
    {
        /** @var BeruflicheBeziehung[] $relationen */
        foreach ($this->daten as $relationen) {
            $this->delete(
                self::TABLE,
                'id = :id',
                [
                    ':id' => $relationen[self::RELATION]->getId()
                ]
            );

            $this->delete(
                self::TABLE,
                'id = :id',
                [
                    ':id' => $relationen[self::COUNTER_RELATION]->getId()
                ]
            );
        }

        return true;
    }
}
