<?php

final class m230525_121312_create_table_ab_verbunddaten_fks_no_inventory_data extends CreateTableMigration
{
    public $tableName = 'ab_verbunddaten_fks_no_inventory_data';

    public function getColumns()
    {
        return [
            'id'                    => 'int unsigned not null auto_increment primary key',
            'insurance_company_id'  => 'int unsigned not null',
            'contract_number'       => 'varchar(255)',
            'create_datetime'       => 'datetime not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
