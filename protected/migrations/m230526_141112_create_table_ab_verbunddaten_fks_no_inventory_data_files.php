<?php

final class m230526_141112_create_table_ab_verbunddaten_fks_no_inventory_data_files extends CreateTableMigration
{
    public $tableName = 'ab_verbunddaten_fks_no_inventory_data_files';

    public function getColumns()
    {
        return [
            'id'                    => 'int unsigned not null auto_increment primary key',
            'filename'              => 'varchar(255)',
            'insurance_company_id'  => 'int unsigned not null',
            'status'                => 'int unsigned not null',
            'create_datetime'       => 'datetime not null',
            'comment'               => 'text'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('insurance_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
