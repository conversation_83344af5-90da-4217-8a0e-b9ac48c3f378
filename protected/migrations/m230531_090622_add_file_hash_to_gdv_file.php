<?php

final class m230531_090622_add_file_hash_to_gdv_file extends CDbMigration
{
    private const TABLE      = 'gdv_file';
    private const COLUMN     = 'file_content_hash';
    private const COLUMN_IDX = 'idx_gdv_file_file_content_hash';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'char(32) default null');
        $this->createIndex(self::COLUMN_IDX, self::TABLE, [self::COLUMN]);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::COLUMN_IDX, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
