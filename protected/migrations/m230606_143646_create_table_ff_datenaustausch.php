<?php

final class m230606_143646_create_table_ff_datenaustausch extends CreateTableMigration
{
    public $tableName = 'fondsfinanz_datenaustausch';

    public function getColumns()
    {
        return [
            'id'                   => 'int unsigned not null auto_increment primary key',
            'mak_nr'               => 'varchar(255)',
            'user_id'              => 'integer unsigned',
            'access_token'         => 'text',
            'create_user_id'       => 'integer unsigned',
            'last_edit_user_id'    => 'integer unsigned',
            'create_datetime'      => 'datetime',
            'last_edit_datetime'   => 'datetime'
        ];
    }
}
