<?php

final class m230615_100736_revert_nullable_user_in_db_token extends CDbMigration
{
    private const TABLE = 'authtoken';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, 'user_id', 'integer unsigned not null');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, 'user_id', 'integer unsigned default null');

        return true;
    }
}
