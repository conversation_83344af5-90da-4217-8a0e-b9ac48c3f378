<?php

final class m230616_083002_create_ff_company_mapping_table extends CreateTableMigration
{
    public $tableName = 'bu_fondsfinanz_company_mapping';

    public function getColumns(): array
    {
        return [
            'id'                   => 'pk',
            'ff_company_id'        => 'int not null',
            'insurance_company_id' => 'int unsigned not null'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('ff_company', 'ff_company_id', 'bu_fondsfinanz_company', 'CASCADE', 'CASCADE');
        $this->addOwnFk('pw_company', 'insurance_company_id', 'insurance_company', 'CASCADE', 'CASCADE');
    }
}
