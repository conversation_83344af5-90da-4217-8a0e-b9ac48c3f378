<?php

final class m230616_124610_seed_relation_text extends CDbMigration
{
    private const TABLE        = 'client_relation_text';
    private const SOURCE_TABLE = 'client_relation_type';

    public function up(): bool
    {
        $sql = sprintf(
            'INSERT INTO %s SELECT null, id, text FROM %s WHERE text IS NOT NULL',
            self::TABLE,
            self::SOURCE_TABLE
        );

        Yii::app()->db
            ->getCommandBuilder()
            ->createSqlCommand($sql)
            ->execute();

        return true;
    }

    public function down(): bool
    {
        $this->delete(self::TABLE, '1');

        return true;
    }
}
