<?php

final class m230616_131803_drop_table_client_relation_type extends CDbMigration
{
    private const TABLE    = 'client_relation_type';
    private const FK_NAME  = 'fk_client_relation_4';
    private const FK_TABLE = 'client_relation';

    public function up(): bool
    {
        $this->dropForeignKey(self::FK_NAME, self::FK_TABLE);
        $this->dropTable(self::TABLE);

        return true;
    }

    public function down(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                  => 'pk',
                'name'                => 'varchar(128)',
                'related_relation_id' => 'int null',
                'not_selectable'      => 'int',
                'text'                => 'varchar(255)',
            ]
        );

        $this->addForeignKey(
            self::FK_NAME,
            self::FK_TABLE,
            'type',
            self::TABLE,
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }
}
