<?php

declare(strict_types=1);

final class m230619_142204_create_fks_ftp_connections extends CreateTableMigration
{
    public $tableName = 'ab_fks_ftp_connections';

    public function getColumns(): array
    {
        return [
            'id'                        => 'int unsigned not null auto_increment primary key',
            'agency_id'                 => 'int unsigned not null unique',
            'ftp_host'                  => 'varchar(255) not null',
            'ftp_user'                  => 'varchar(255) not null',
            'ftp_password'              => 'varchar(255) not null',
            'ftp_root'                  => 'varchar(255) not null',
            'ftp_port'                  => 'int not null default 21',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('agency', 'agency_id', 'agency', 'CASCADE', 'CASCADE');
    }
}
