<?php

final class m230620_125852_add_user_uuid extends CDbMigration
{
    private const TABLE    = 'user';
    private const UUID_COL = 'uid';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::UUID_COL, 'VARCHAR(36) NOT NULL DEFAULT UUID()');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::UUID_COL);

        return true;
    }
}
