<?php

final class m230628_121857_add_agency_uuid extends CDbMigration
{
    private const TABLE    = 'agency';
    private const UUID_COL = 'uid';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::UUID_COL, 'VARCHAR(36) NOT NULL DEFAULT UUID()');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::UUID_COL);

        return true;
    }
}
