<?php

final class m230711_140858_rename_importeddocumentid_contract_id_to_external_id extends CDbMigration
{
    private const TABLE    = 'fondsfinanz_imported_document_id';
    private const OLD_NAME = 'contract_id';
    private const NEW_NAME = 'external_id';

    public function up(): bool
    {
        $this->renameColumn(self::TABLE, self::OLD_NAME, self::NEW_NAME);

        return true;
    }

    public function down(): bool
    {
        $this->renameColumn(self::TABLE, self::NEW_NAME, self::OLD_NAME);

        return true;
    }
}
