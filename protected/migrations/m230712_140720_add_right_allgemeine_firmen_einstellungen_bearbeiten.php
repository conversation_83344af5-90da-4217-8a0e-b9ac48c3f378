<?php

final class m230712_140720_add_right_allgemeine_firmen_einstellungen_bearbeiten extends CDbMigration
{
    private const RIGHTS_TABLE              = 'rights';
    private const RIGHT_NAME                = 'Gesellschaften und Sparten ein- und ausblenden';

    private const USER_ROLE_RIGHTS_TABLE    = 'user_role_rights';

    private const USER_RIGHTS_TABLE         = 'user_rights';

    public function up(): bool
    {
        $this->insert(
            self::RIGHTS_TABLE,
            [
                'name'          => self::RIGHT_NAME,
                'sort'          => 3,
                'description'   => 'Es können Gesellschaften und Sparten für die Tarifierungsseite sowie die DEMV-Vergleichsrechner ein- und ausgeblendet werden.'
            ]
        );

        $right = Rights::model()->findByAttributes(['name' => self::RIGHT_NAME]);

        if ($right === null) {
            return false;
        }

        $targetUserRoles = $this->getUserRoles();

        foreach ($targetUserRoles as $userRole) {
            $this->insert(
                self::USER_ROLE_RIGHTS_TABLE,
                [
                    'user_role_id'  => $userRole,
                    'right_id'      => $right->id,
                ]
            );
        }

        $targetUserIdsQuery = sprintf(
            'SELECT id, %s FROM `user` WHERE user_role IN (%s)',
            $right->id,
            implode(', ', $targetUserRoles)
        );

        $userRightsInsert = sprintf(
            'INSERT INTO %s (user_id, `right`) %s',
            self::USER_RIGHTS_TABLE,
            $targetUserIdsQuery
        );

        $this->execute($userRightsInsert);

        return true;
    }

    public function down(): bool
    {
        $right = Rights::model()->findByAttributes(['name' => self::RIGHT_NAME]);

        if ($right === null) {
            return true;
        }

        $this->delete(
            self::USER_ROLE_RIGHTS_TABLE,
            sprintf('right_id = %s', $right->id)
        );

        $this->delete(
            self::USER_RIGHTS_TABLE,
            sprintf('`right` = %s', $right->id),
        );

        $this->delete(
            self::RIGHTS_TABLE,
            sprintf('id = %s', $right->id),
        );

        return true;
    }

    public function getUserRoles(): array
    {
        return [
            UserRole::ADMIN,
            UserRole::MAINBROKER,
            UserRole::TESTUSER,
        ];
    }
}
