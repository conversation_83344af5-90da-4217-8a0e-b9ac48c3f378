<?php

final class m230720_100842_alter_table_abrechnungstool_csv_upload_log_add_column_target extends CDbMigration
{
    private const TABLE  = 'abrechnungstool_csv_upload_log';
    private const COLUMN = 'target';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, "CHAR(3) NOT NULL DEFAULT 'ABT'");

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
