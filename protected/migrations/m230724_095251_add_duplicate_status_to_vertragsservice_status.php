<?php

use Components\Bipro\Vertragsservice\Status\Status;

final class m230724_095251_add_duplicate_status_to_vertragsservice_status extends CDbMigration
{
    private const TABLE     = 'vertragsservice_status';

    public function up(): bool
    {
        $this->insert(self::TABLE, [
            'id'      => Status::FILE_IS_DUPLICATE,
            'message' => 'Die Datei wurde nicht verarbeitet, da bereits eine identische Datei eingespielt wurde',
        ]);

        return true;
    }

    public function down(): bool
    {
        $this->delete(self::TABLE, 'id = :id', [':id' => Status::FILE_IS_DUPLICATE]);

        return true;
    }
}
