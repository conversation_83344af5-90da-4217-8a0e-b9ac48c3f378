<?php

declare(strict_types=1);

class m230724_150321_add_fk_for_fks_overhead extends CDbMigration
{
    private const TABLE     = 'ab_verbunddaten_fks_no_inventory_data_files';
    private const FK_TABLE  = 'ab_verbunddaten_fks_overhead';
    private const COLUMN    = 'overhead_id';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned');

        $this->addForeignKey(
            self::COLUMN,
            self::TABLE,
            'overhead_id',
            self::FK_TABLE,
            'id',
            'SET NULL',
            'CASCADE',
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);
        $this->dropForeignKey(self::COLUMN, self::TABLE);

        return true;
    }
}
