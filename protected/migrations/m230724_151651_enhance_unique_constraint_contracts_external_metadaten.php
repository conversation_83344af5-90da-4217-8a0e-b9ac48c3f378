<?php

final class m230724_151651_enhance_unique_constraint_contracts_external_metadaten extends CDbMigration
{
    private const TABLE    = 'contracts_external_metadaten';
    private const IDX_NAME = 'idx_cem_contract_id_external_id_create_type_unique';

    public function up(): bool
    {
        $this->createIndex(self::IDX_NAME, self::TABLE, ['contract_id', 'external_id', 'create_type'], true);

        return true;
    }

    public function down(): bool
    {
        self::dropIndex(self::IDX_NAME, self::TABLE);

        return true;
    }
}
