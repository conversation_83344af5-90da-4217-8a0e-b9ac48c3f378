<?php

final class m230725_135433_alter_table_contracts_default_type extends CDbMigration
{
    private const TABLE  = 'contracts';
    private const COLUMN = 'type_id';
    private string $indexName;

    public function __construct()
    {
        $this->indexName = implode('_', [self::TABLE, self::COLUMN]);
    }

    /**
     * @return bool
     */
    public function up(): bool
    {
        $this->createIndex(
            $this->indexName,
            self::TABLE,
            self::COLUMN
        );

        $this->alterColumn(self::TABLE, self::COLUMN, 'INT DEFAULT 1');

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'INT NULL');

        $this->dropIndex($this->indexName, self::TABLE);

        return true;
    }
}
