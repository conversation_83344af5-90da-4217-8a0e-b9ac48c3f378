<?php

final class m230726_091944_alter_table_contracts_default_type_2 extends CDbMigration
{
    private const TABLE  = 'contracts';
    private const COLUMN = 'type_id';
    private string $indexName;

    public function __construct()
    {
        $this->indexName = implode('_', [self::TABLE, self::COLUMN]);
    }

    /**
     * @return bool
     */
    public function up(): bool
    {
        $this->update(self::TABLE, [self::COLUMN => 1], self::COLUMN . ' IS NULL');

        $this->alterColumn(self::TABLE, self::COLUMN, 'TINYINT NOT NULL DEFAULT 1');

        $this->dropIndex($this->indexName, self::TABLE);

        return true;
    }

    /**
     * @return bool
     */
    public function down(): bool
    {
        $this->createIndex(
            $this->indexName,
            self::TABLE,
            self::COLUMN
        );

        $this->alterColumn(self::TABLE, self::COLUMN, 'INT DEFAULT 1');

        return true;
    }
}
