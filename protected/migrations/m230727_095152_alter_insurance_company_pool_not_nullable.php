<?php

final class m230727_095152_alter_insurance_company_pool_not_nullable extends CDbMigration
{
    private const TABLE  = 'insurance_company';
    private const COLUMN = 'pool';

    public function up(): bool
    {
        $this->update(self::TABLE, [self::COLUMN => 0], self::COLUMN . ' IS NULL');

        $this->alterColumn(self::TABLE, self::COLUMN, 'tinyint(1) default 0 not null');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'tinyint(1) default 0 null');

        return true;
    }
}
