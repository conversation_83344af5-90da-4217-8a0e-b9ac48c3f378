<?php

declare(strict_types=1);

final class m230727_143542_create_table_fks_settings extends CreateTableMigration
{
    public $tableName = 'ab_fks_company_settings';

    public function getColumns(): array
    {
        return [
            'id'                        => 'int unsigned not null auto_increment primary key',
            'agency_id'                 => 'int unsigned not null unique',
            'create_datetime'           => 'datetime'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('agency', 'agency_id', 'agency', 'CASCADE', 'CASCADE');
    }
}
