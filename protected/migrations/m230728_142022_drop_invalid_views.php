<?php

final class m230728_142022_drop_invalid_views extends CDbMigration
{
    public function up(): bool
    {
        $db = $this->getDbConnection();

        // These views don't exist in production, the migration is just there
        // to cleanup developer databases, thus the `IF EXISTS` part
        $db->createCommand('DROP VIEW IF EXISTS damage')->execute();
        $db->createCommand('DROP VIEW IF EXISTS damage_duplicates_on_different_clients')->execute();
        $db->createCommand('DROP VIEW IF EXISTS gdv_damages')->execute();
        $db->createCommand('DROP VIEW IF EXISTS gdv_main_contracts')->execute();
        $db->createCommand('DROP VIEW IF EXISTS gdv_sub_contracts')->execute();

        return true;
    }

    public function down(): bool
    {
        // The views deleted in the up() method didn't exist in production in
        // the first place, so we do not want to restore them here

        return true;
    }
}
