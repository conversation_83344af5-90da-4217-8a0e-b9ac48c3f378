<?php

final class m230808_075254_alter_insurance_company_specifics extends CDbMigration
{
    private const TABLE  = 'insurance_company_specifics';
    private const COLUMN = 'other_matters';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(768)');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(512)');

        return true;
    }
}
