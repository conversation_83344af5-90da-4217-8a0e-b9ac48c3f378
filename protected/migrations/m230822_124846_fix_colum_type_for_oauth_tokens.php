<?php

declare(strict_types=1);

final class m230822_124846_fix_colum_type_for_oauth_tokens extends CDbMigration
{
    const TABLE                = 'oauth2_connections';
    const COLUMN_ACCESS_TOKEN  = 'access_token';
    const COLUMN_REFRESH_TOKEN = 'refresh_token';

    public function up()
    {
        $this->alterColumn(self::TABLE, self::COLUMN_ACCESS_TOKEN, 'TEXT');
        $this->alterColumn(self::TABLE, self::COLUMN_REFRESH_TOKEN, 'TEXT');

        return true;
    }

    public function down()
    {
        $this->alterColumn(self::TABLE, self::COLUMN_ACCESS_TOKEN, 'VARCHAR(2048)');
        $this->alterColumn(self::TABLE, self::COLUMN_REFRESH_TOKEN, 'VARCHAR(2048)');

        return true;
    }
}
