<?php

declare(strict_types=1);

final class m230828_132301_remove_tmp_client_id extends CDbMigration
{
    private const TABLE  = 'bank_account_assignment_client';
    private const COLUMN = 'tmp_client_id';
    private const INDEX  = 'idx_tmp_client_id';

    public function up(): bool
    {
        $db = $this->getDbConnection();
        $db->createCommand(sprintf('DROP INDEX IF EXISTS %s ON %s', self::INDEX, self::TABLE))->execute();
        $db->createCommand(sprintf('ALTER TABLE %s DROP COLUMN IF EXISTS %s', self::TABLE, self::COLUMN))->execute();

        return true;
    }

    public function down(): bool
    {
        // Falls es doch noch benötigt wird
        $this->addColumn(self::TABLE, self::COLUMN, 'int');
        $this->createIndex(self::INDEX, self::TABLE, self::COLUMN);

        return true;
    }
}
