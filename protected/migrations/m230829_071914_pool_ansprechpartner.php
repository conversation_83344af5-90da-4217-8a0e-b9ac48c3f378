<?php

declare(strict_types=1);

final class m230829_071914_pool_ansp<PERSON><PERSON><PERSON>t<PERSON> extends CDbMigration
{
    private const TABLE  = 'support_person';
    private const COLUMN = 'responsible_for_pool';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'integer unsigned');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
