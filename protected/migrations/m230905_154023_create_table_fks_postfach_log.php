<?php

declare(strict_types=1);

final class m230905_154023_create_table_fks_postfach_log extends CreateTableMigration
{
    public $tableName = 'ab_fks_postfach_log';

    public function getColumns(): array
    {
        return [
            'id'              => 'int unsigned not null auto_increment primary key',
            'datei_id'        => 'int unsigned not null',
            'export_id'       => 'int unsigned',
            'status'          => "ENUM ('success', 'failed')",
            'create_datetime' => 'datetime not null',
            'upload_at'       => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('export', 'export_id', 'ab_verbunddaten_fks_export', 'CASCADE', 'CASCADE');
    }
}
