<?php

declare(strict_types=1);

final class m230905_154024_create_table_fks_verbunddaten_split_log extends CreateTableMigration
{
    public $tableName = 'ab_fks_verbunddaten_split_log';

    public function getColumns(): array
    {
        return [
            'id'                    => 'int unsigned not null auto_increment primary key',
            'verbunddaten_split_id' => 'int not null',
            'export_id'             => 'int unsigned',
            'status'                => "ENUM ('success', 'failed')",
            'create_datetime'       => 'datetime not null',
            'upload_at'             => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk(
            'verbunddatensplit',
            'verbunddaten_split_id',
            'ab_verbunddaten_split',
            'CASCADE',
            'CASCADE'
        );
        $this->addOwnFk(
            'export',
            'export_id',
            'ab_verbunddaten_fks_export',
            'CASCADE',
            'CASCADE'
        );
    }
}
