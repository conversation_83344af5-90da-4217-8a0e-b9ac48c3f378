<?php

declare(strict_types=1);

final class m230910_211330_alter_table_user_copy_stock_log_add_column_skip_duplicates extends CDbMigration
{
    private const TABLE  = 'user_copy_stock_log';
    private const COLUMN = 'skip_duplicates';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'BOOLEAN NOT NULL DEFAULT 0 AFTER status');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
