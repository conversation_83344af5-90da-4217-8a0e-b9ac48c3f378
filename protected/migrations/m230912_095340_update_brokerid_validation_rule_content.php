<?php

declare(strict_types=1);

use League\Csv\Reader;

final class m230912_095340_update_brokerid_validation_rule_content extends CDbMigration
{
    private const TABLE = 'brokerid_validation_rule';

    public function up(): bool
    {
        $this->delete(self::TABLE);

        $insuranceCompanies = InsuranceCompany::model()->findAll(['index' => 'id']);

        foreach ($this->loadData() as $row) {
            try {
                if (!array_key_exists(1, $insuranceCompanies)) {
                    continue;
                }
                $this->insert(self::TABLE, $row);
            } catch (Exception $e) {
                echo 'Insert ignored! ID: ' . $row['id'];
            }
        }

        return true;
    }

    public function down(): bool
    {
        return true;
    }

    private function loadData(): array
    {
        $csv = Reader::createFromPath(__DIR__ . DS . 'files' . DS . 'brokerid_validation_rule_add_type_id.csv', 'r');
        $csv->setDelimiter(',');
        $csv->setHeaderOffset(0);

        return iterator_to_array($csv->getRecords());
    }
}
