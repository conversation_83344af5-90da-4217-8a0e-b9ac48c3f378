<?php

declare(strict_types=1);

final class m230914_122618_modify_table_bankaccount_contract_relation extends CDbMigration
{
    private const FK_NAME = 'fk_bcr_contract_id';
    private const TABLE   = 'bankaccount_contract_relation';

    public function up(): bool
    {
        $this->execute(
            <<<EOSQL
            ALTER TABLE
                bankaccount_contract_relation
            DROP COLUMN
                id,
            MODIFY COLUMN
                contract_id INT UNSIGNED PRIMARY KEY FIRST
            EOSQL
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE);

        $this->execute(
            <<<EOSQL
            ALTER TABLE
                bankaccount_contract_relation
            DROP PRIMARY KEY,
            ADD COLUMN
                id INT AUTO_INCREMENT PRIMARY KEY FIRST,
            MODIFY COLUMN
                contract_id INT UNSIGNED NOT NULL AFTER bankaccount_id
            EOSQL
        );

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            'contract_id',
            'contracts',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }
}
