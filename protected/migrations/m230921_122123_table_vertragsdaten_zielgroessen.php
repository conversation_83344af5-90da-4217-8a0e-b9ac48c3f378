<?php

declare(strict_types=1);

final class m230921_122123_table_vertragsdaten_zielgroessen extends CreateTableMigration
{
    public $tableName = 'vertragsdaten_zielgroessen';

    public function getColumns(): array
    {
        return [
            'id'                    => 'int unsigned not null auto_increment primary key',
            'question'              => 'string not null',
            'zielformat'            => 'string not null',
            'product_combo_id'      => 'int unsigned not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('product_combo', 'product_combo_id', 'product_combo', 'CASCADE', 'CASCADE');
    }
}
