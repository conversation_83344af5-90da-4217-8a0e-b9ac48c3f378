<?php

declare(strict_types=1);

final class m230922_124013_drop_fks_no_inventory_data_table extends CDbMigration
{
    public const TABLE_NAME = 'ab_verbunddaten_fks_no_inventory_data';

    public function up(): bool
    {
        $this->dropTable(self::TABLE_NAME);

        return true;
    }

    public function down(): bool
    {
        $this->createTable(
            self::TABLE_NAME,
            [
                'id'                    => 'int unsigned not null auto_increment primary key',
                'insurance_company_id'  => 'int unsigned not null',
                'contract_number'       => 'varchar(255)',
                'create_datetime'       => 'datetime not null',
            ]
        );

        $this->addForeignKey(
            'fk_ab_verbunddaten_fks_no_inventory_data_insurance_company',
            self::TABLE_NAME,
            'insurance_company_id',
            'insurance_company',
            'id',
            'CASCADE',
            'CASCADE',
        );

        return true;
    }
}
