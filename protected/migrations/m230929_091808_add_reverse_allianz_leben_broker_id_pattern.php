<?php

declare(strict_types=1);

use Business\Company\Allianz;

final class m230929_091808_add_reverse_allianz_leben_broker_id_pattern extends CDbMigration
{
    private const TABLE_NAME            = 'brokerid_pattern';
    private const INSURANCE_COMPANY_ID  = Allianz::LEBEN;
    private const REGEX                 = '/^(\\d{1})0(\\d{7})$/';

    private const REPLACEMENT           = '${1}${2}';

    public function up(): bool
    {
        $this->insert(self::TABLE_NAME, [
            'insurance_company_id' => self::INSURANCE_COMPANY_ID,
            'regex'                => self::REGEX,
            'replacement'          => self::REPLACEMENT,
        ]);

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE_NAME,
            'insurance_company_id = :insurance_company_id AND regex = :regex AND replacement = :replacement',
            [
                ':insurance_company_id' => self::INSURANCE_COMPANY_ID,
                ':regex'                => self::REGEX,
                ':replacement'          => self::REPLACEMENT,
            ],
        );

        return true;
    }
}
