<?php

declare(strict_types=1);

final class m231012_134312_add_fk_to_fondsfinanz_imported_document_id extends CDbMigration
{
    private const TABLE = 'fondsfinanz_imported_document_id';

    private const FK_NAME = 'fk_client_files_id';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, 'client_file_id', 'int unsigned null');

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            'client_file_id',
            'client_files',
            'id',
            'SET NULL',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, 'client_file_id', 'string null');
        $this->dropForeignKey(self::FK_NAME, self::TABLE);

        return true;
    }
}
