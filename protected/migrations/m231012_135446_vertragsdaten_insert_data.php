<?php

declare(strict_types=1);

use Vertragsdaten\Models\VertragsdatenZielgroessen;

final class m231012_135446_vertragsdaten_insert_data extends CDbMigration
{
    private const DATA = [
        // new
        [
            'question'         => 'Gibt es eine grantierte Rentensteigerung ?',
            'format'           => 'garantierte_rentensteigerung: Boolean',
            'product_combo_id' => '135',
        ],
        [
            'question'         => 'Wann ist das Leistungsendalter ?',
            'format'           => 'leistungsendalter: int',
            'product_combo_id' => '135',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme pro Monat ?',
            'format'           => 'versicherungssumme_pro_monat: int',
            'product_combo_id' => '135',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'Versicherungssumme: int',
            'product_combo_id' => '136',
        ],
        [
            'question'         => 'Wann ist das Leistungsendalter erreicht ?',
            'format'           => 'leistungsendalter: int',
            'product_combo_id' => '137',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme pro Monat ?',
            'format'           => 'versicherungssumme-pro-monat: int',
            'product_combo_id' => '137',
        ],
        [
            'question'         => 'Gibt es eine grantierte Rentensteigerung im Vertrag ?',
            'format'           => 'garantierte-rentensteigerung: boolean',
            'product_combo_id' => '138',
        ],
        [
            'question'         => 'Wann mit welchem Alter ist das Leistungsendalter im Vertrag angebeben ?',
            'format'           => 'leistungsendalter: int',
            'product_combo_id' => '138',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme pro Monat ?',
            'format'           => 'versicherungssumme-pro-monat: int',
            'product_combo_id' => '138',
        ],
        [
            'question'         => 'Ist der Schutz des Vertrages besser als der Schutz der gesetzlichen Krankenversicherung ?',
            'format'           => 'besser-als-grundschutz-in-gkv: int',
            'product_combo_id' => '156',
        ],
        [
            'question'         => 'Wie hoch ist die Verischerungssumme pro Monat ?',
            'format'           => 'versicherungssumme-pro-monat: int',
            'product_combo_id' => '159',
        ],
        [
            'question'         => 'Gibt es eine Einmalleistung ?',
            'format'           => 'einmalleistung: boolean',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 1 und ambulanter Pflege ?',
            'format'           => 'pflegegrad-1-ambulant: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 1 und statinärer Pflege ?',
            'format'           => 'pflegegrad-1-stationaer: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 2 und ambulanter Pflege ?',
            'format'           => 'pflegegrad-2-ambulant: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 2 und statinärer Pflege ?',
            'format'           => 'pflegegrad-2-stationaer: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 3 und ambulanter Pflege ?',
            'format'           => 'pflegegrad-3-ambulant: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 3 und statinärer Pflege ?',
            'format'           => 'pflegegrad-3-stationaer: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 4 und ambulanter Pflege ?',
            'format'           => 'pflegegrad-4-ambulant: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 4 und statinärer Pflege ?',
            'format'           => 'pflegegrad-4-stationaer: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 5 und ambulanter Pflege ?',
            'format'           => 'pflegegrad-5-ambulant: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 5 und statinärer Pflege ?',
            'format'           => 'pflegegrad-5-stationaer: int',
            'product_combo_id' => '160',
        ],
        [
            'question'         => 'Wie lautet der Name der Gesellschaft ?',
            'format'           => 'Gesellschafts_Name: String',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Wie lautet die Versicherungsschein-Nr ?',
            'format'           => 'VersicherungsscheinNr: String',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Wann ist der Beginn der Versicherung ?',
            'format'           => 'Versicherungs_beginn: Date or Datetime',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Wann ist das Ende der Versicherung ?',
            'format'           => 'Versicherungs_ende: Date or Datetime',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Wie ist die Zahlweise des Versicherungsbeitrages des Kunden ?',
            'format'           => 'Versicherungsbeitrag: Float, Zahlweise: int [1=jährlich, 2=halbjährlich, usw.]',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Handelt es sich um einen Single oder Familien Tarif ?',
            'format'           => 'Tarifart_single_familie: String',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Handelt es sich um eine Dienst Haftpflicht ?',
            'format'           => 'DienstHV: Boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Sind Hobbys des Kunden mitversichert ?',
            'format'           => 'Hobbys_mitversichert: Boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Gibt es Forderungsausfälle ?',
            'format'           => 'Forderungsaudfälle: Boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'In welcher höhe sind Personen und Sachschäden versichert ?',
            'format'           => 'Versicherungssumme_personen_sach_schaeden: Boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Sind geliehene Sachen mitversichert ?',
            'format'           => 'Geliehene_sachen: Boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Sind reine Vermögenschäden mit versichert ?',
            'format'           => 'Vermoegensschaeden: Boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Gibt es zusätliche Informationen in den Dokumenten die für unsere Digitale Kundenakte hilfreich sein könnten ?',
            'format'           => 'Zusatzinformationen: zusatzinfo1: datentyp, zusatzinfo2: datentyp, usw',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Ist eine Drohne mit versichert ?',
            'format'           => 'drohne: boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Sind Kinder unter 7 Jahren mit versichert ?',
            'format'           => 'kinder-unter-7-jahre: boolean',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '165',
        ],
        [
            'question'         => 'Gibt es eine Elementarversicherung ?',
            'format'           => 'elementarversicherung: boolean',
            'product_combo_id' => '166',
        ],
        [
            'question'         => 'Ist der Diebstahl von Fahrräder abgedeckt ?',
            'format'           => 'fahrraddiebstahl: boolean',
            'product_combo_id' => '166',
        ],
        [
            'question'         => 'Gibt es eine Glasversicherung ?',
            'format'           => 'glasversicherung: boolean',
            'product_combo_id' => '166',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '166',
        ],
        [
            'question'         => 'Sind Elementarschäden mit versichert ?',
            'format'           => 'elementar: boolean',
            'product_combo_id' => '167',
        ],
        [
            'question'         => 'Sind Schäden durch Feuer, Sturm, Hagel oder leitungswasser abgedeckt ?',
            'format'           => 'feuer-sturm-hagel-leitungswasser: boolean',
            'product_combo_id' => '167',
        ],
        [
            'question'         => 'Gibt es eine Glasversicherung ?',
            'format'           => 'glasversicherung: boolean',
            'product_combo_id' => '167',
        ],
        [
            'question'         => 'Wie hoch ist die Grundinvaliditätssumme ?',
            'format'           => 'grundinvaliditaetssumme: int',
            'product_combo_id' => '168',
        ],
        [
            'question'         => 'Gibt es Krankenaustagegeld ?',
            'format'           => 'krankenhaustagegeld: boolean',
            'product_combo_id' => '168',
        ],
        [
            'question'         => 'Ist das Krankentagegeld mit dieser Versicherung abgedeckt ?',
            'format'           => 'krankentagegeld: boolean',
            'product_combo_id' => '168',
        ],
        [
            'question'         => 'Wie hoch ist die Todesfallsumme der Versicherung ?',
            'format'           => 'todesfallsumme: int',
            'product_combo_id' => '168',
        ],
        [
            'question'         => 'Ist eine Unfallrente in der versicherung enthalten ?',
            'format'           => 'unfallrente: int',
            'product_combo_id' => '168',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '168',
        ],
        [
            'question'         => 'Wie hoch ist die Vollinvaliditätssumme ?',
            'format'           => 'vollinvaliditaetssumme: int',
            'product_combo_id' => '168',
        ],
        [
            'question'         => 'Wie hoch ist die Berufsversicherungssumme ?',
            'format'           => 'beruf-versicherungssumme: int',
            'product_combo_id' => '169',
        ],
        [
            'question'         => 'Wie Hoch ist die Mieter- und Eigentümerversicherungssumme ?',
            'format'           => 'mieter-eigentuemer-versicherungssumme: int',
            'product_combo_id' => '169',
        ],
        [
            'question'         => 'Wie hoch ist die Privateversicherungssumme ?',
            'format'           => 'privat-versicherungssumme: int',
            'product_combo_id' => '169',
        ],
        [
            'question'         => 'Wie hoch ist die Verkehrversicherungssumme ?',
            'format'           => 'verkehr-versicherungssumme: int',
            'product_combo_id' => '169',
        ],
        [
            'question'         => 'Wie hoch ist die Vermieterversicherungssumme ?',
            'format'           => 'vermieter-versicherungssumme: int',
            'product_combo_id' => '169',
        ],
        [
            'question'         => 'Sind grobe fahrlässigkeiten im Rahmen dieser Versicherung abgedeckt ?',
            'format'           => 'grobe-fahrlaessigkeit: boolean',
            'product_combo_id' => '170',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '170',
        ],
        [
            'question'         => 'Umfasst die Versicherung einen Fahrerschutz?',
            'format'           => 'fahrerschutz: boolean',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Sind grobe fahrlässigkeiten im Rahmen dieser Versicherung abgedeckt ?',
            'format'           => 'grobe-fahrlaessigkeit: boolean',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Gibt es einen Rabatschutz im Vertrag ?',
            'format'           => 'rabattschutz: boolean',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Gibt es einen Schutzbrief im Vertrag ?',
            'format'           => 'schutzbrief: boolean',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Handelt es sich um eine Teilkasko Versicherung?',
            'format'           => 'teilkasko: boolean',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Handelt es sich um eine Vollkaskoversicherung ?',
            'format'           => 'vollkasko: int',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Umfasst der Vertrag auch eine Rabattschutz ?',
            'format'           => 'rabattschutz: int',
            'product_combo_id' => '173',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '173',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '174',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '233',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 1 und abulanter Pflege ?',
            'format'           => 'pflegegrad-1-ambulant: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 1 und statinärer Pflege ?',
            'format'           => 'pflegegrad-1-ambulant: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 2 und abulanter Pflege ?',
            'format'           => 'pflegegrad-2-ambulant: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 2 und statinärer Pflege ?',
            'format'           => 'pflegegrad-2-stationaer: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 3 und abulanter Pflege ?',
            'format'           => 'pflegegrad-3-ambulant: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 3 und statinärer Pflege ?',
            'format'           => 'pflegegrad-3-stationaer: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 4 und abulanter Pflege ?',
            'format'           => 'pflegegrad-4-ambulant: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 4 und statinärer Pflege ?',
            'format'           => 'pflegegrad-4-stationaer: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 5 und abulanter Pflege ?',
            'format'           => 'pflegegrad-5-ambulant: int',
            'product_combo_id' => '239',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 5 und statinärer Pflege ?',
            'format'           => 'pflegegrad-5-stationaer: int',
            'product_combo_id' => '171',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '240',
        ],
        [
            'question'         => 'Sind grobe fahrlässigkeiten im Rahmen dieser Versicherung abgedeckt ?',
            'format'           => 'grobe-fahrlaessigkeit: int',
            'product_combo_id' => '242',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '242',
        ],
        [
            'question'         => 'Sind grobe fahrlässigkeiten im Rahmen dieser Versicherung abgedeckt ?',
            'format'           => 'grobe-fahrlaessigkeit: boolean',
            'product_combo_id' => '243',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungsumme',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '243',
        ],
        [
            'question'         => 'Umfasst der Vertrag eine grantierte Rentensteigerung ?',
            'format'           => 'garantierte-rentensteigerung: boolean',
            'product_combo_id' => '250',
        ],
        [
            'question'         => 'Auf welches Alter definiert der Vertrag das Leistungsendalter ?',
            'format'           => 'leistungsendalter: int',
            'product_combo_id' => '250',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme pro Monat ?',
            'format'           => 'versicherungssumme-pro-monat: int',
            'product_combo_id' => '250',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '253',
        ],
        [
            'question'         => 'Ist die Abdeckung des Vertrages besser als der Schutz der gesetzlichen Krankenversicherung ?',
            'format'           => 'besser-als-grundschutz-in-gkv: boolean',
            'product_combo_id' => '273',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '276',
        ],
        [
            'question'         => 'Ist die Abdeckung des Vertrages besser als der Schutz der gesetzlichen Krankenversicherung ?',
            'format'           => 'besser-als-grundschutz-in-gkv: boolean',
            'product_combo_id' => '284',
        ],
        [
            'question'         => 'Ist die Abdeckung des Vertrages besser als der Schutz der gesetzlichen Krankenversicherung ?',
            'format'           => 'besser-als-grundschutz-in-gkv: boolean',
            'product_combo_id' => '285',
        ],
        [
            'question'         => 'Umfasst die Versicherung einen anspruch auf die Behandlung durch einen Chefartzt ?',
            'format'           => 'chefarzt: boolean',
            'product_combo_id' => '285',
        ],
        [
            'question'         => 'Umfasst die Versicherung auch einen Anspruch auf ein Ein- oder Zweibettzimmer ?',
            'format'           => 'ein-oder-zweibettzimmer: boolean',
            'product_combo_id' => '285',
        ],
        [
            'question'         => 'Ist die Abdeckung des Vertrages besser als der Schutz der gesetzlichen Krankenversicherung ?',
            'format'           => 'besser-als-grundschutz-in-gkv: boolean',
            'product_combo_id' => '286',
        ],
        [
            'question'         => 'Wie hoch ist der Prozentuale Anteil der Zuzahlung bei Kieferorthopädischer Behandlung ?',
            'format'           => 'kieferorthopaedie: float',
            'product_combo_id' => '286',
        ],
        [
            'question'         => 'Wie hoch ist der Prozentuale Anteil der Zuzahlung bei einer Zahnbehandlung ?',
            'format'           => 'zahnbehandlung: float',
            'product_combo_id' => '286',
        ],
        [
            'question'         => 'Wie hoch ist der Prozentuale Anteil der Zuzahlung bei Zahnersatz ?',
            'format'           => 'zahnersatz: float',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 1 und abulanter Pflege ?',
            'format'           => 'pflegegrad-1-ambulant: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 1 und statinärer Pflege ?',
            'format'           => 'pflegegrad-1-stationaer: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 2 und abulanter Pflege ?',
            'format'           => 'pflegegrad-2-ambulant: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 2 und statinärer Pflege ?',
            'format'           => 'pflegegrad-2-stationaer: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 3 und abulanter Pflege ?',
            'format'           => 'pflegegrad-3-ambulant: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 3 und statinärer Pflege ?',
            'format'           => 'pflegegrad-3-stationaer: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 4 und abulanter Pflege ?',
            'format'           => 'pflegegrad-4-ambulant: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 4 und statinärer Pflege ?',
            'format'           => 'pflegegrad-4-stationaer: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 5 und abulanter Pflege ?',
            'format'           => 'pflegegrad-5-ambulant: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Auszahlungssumme bei Pflegegrad 5 und statinärer Pflege ?',
            'format'           => 'pflegegrad-5-stationaer: int',
            'product_combo_id' => '291',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '311',
        ],
        [
            'question'         => 'Sind schwere erkrannkungen mit abgesichert ?',
            'format'           => 'dread-disease: boolean',
            'product_combo_id' => '311',
        ],
        [
            'question'         => 'Ist die erwerbsunfähigkeit mit abgesichert ?',
            'format'           => 'erwerbsunfaehigkeit: boolean',
            'product_combo_id' => '322',
        ],
        [
            'question'         => 'Ist eine Pflegeversicherung enthalten ?',
            'format'           => 'pflegeversicherung: boolean',
            'product_combo_id' => '322',
        ],
        [
            'question'         => 'Ist eine Unfallversicherung bestandteil dieser Versicherung ?',
            'format'           => 'unfallversicherung: boolean',
            'product_combo_id' => '322',
        ],
        [
            'question'         => 'Ist der Verlust der Grundfähigkeiten mit versichert ?',
            'format'           => 'verlust-grundfaehigkeit: boolean',
            'product_combo_id' => '322',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme pro Monat ?',
            'format'           => 'versicherungssumme-pro-monat: int',
            'product_combo_id' => '322',
        ],
        [
            'question'         => 'Handelt es sich hier um eine Skipperhaftpflicht ?',
            'format'           => 'skipperhaftpflicht: boolean',
            'product_combo_id' => '345',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '345',
        ],
        [
            'question'         => 'Ist eine Wassersport Haftpflicht enthalten ?',
            'format'           => 'wassersporthaftpflicht: boolean',
            'product_combo_id' => '345',
        ],
        [
            'question'         => 'Handelt es sich um eine Wassersportkasko ?',
            'format'           => 'wassersportkasko: boolean',
            'product_combo_id' => '345',
        ],
        [
            'question'         => 'Gibt es eine grantierte Rentensteigung in dieser Versicherung ?',
            'format'           => 'garantierte-rentensteigerung: boolean',
            'product_combo_id' => '358',
        ],
        [
            'question'         => 'Wann defniert dieser Vertrag das Leistungsendalter ?',
            'format'           => 'leistungsendalter: int',
            'product_combo_id' => '358',
        ],
        [
            'question'         => 'Wie hoch fällt die Monatlich Rente in diesem Vertrag aus ?',
            'format'           => 'monatliche-rente: int',
            'product_combo_id' => '358',
        ],
        [
            'question'         => 'Wie hoch ist die Versicherungssumme ?',
            'format'           => 'versicherungssumme: int',
            'product_combo_id' => '373',
        ],
        [
            'question'         => 'Gibt es eine Grantierte Rentensteigerung in diesem Vertrag ?',
            'format'           => 'garantierte-rentensteigerung: boolean',
            'product_combo_id' => '375',
        ],
        [
            'question'         => 'Wie viele Jahre umfasst die grantierte Rentenzeit ?',
            'format'           => 'garantierte-rentenzeit: int',
            'product_combo_id' => '375',
        ],
        [
            'question'         => 'Wie ist die Rentenhöhe pro Monat in diesem Vertrag ?',
            'format'           => 'rentenhoehe-pro-monat: int',
            'product_combo_id' => '375',
        ],
    ];

    public function up(): bool
    {
        foreach (self::DATA as $data) {
            $model                   = new VertragsdatenZielgroessen();
            $model->question         = $data['question'];
            $model->zielformat       = $data['format'];
            $model->product_combo_id = $data['product_combo_id'];

            $model->save();
        }

        return true;
    }

    public function down(): bool
    {
        VertragsdatenZielgroessen::model()->deleteAll();

        return true;
    }
}
