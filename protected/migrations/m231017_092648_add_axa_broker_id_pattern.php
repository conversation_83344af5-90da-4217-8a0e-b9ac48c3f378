<?php

declare(strict_types=1);

use Business\Company\Axa;

final class m231017_092648_add_axa_broker_id_pattern extends CDbMigration
{
    private const TABLE                   = 'brokerid_pattern';
    private const VALIDATION_RULE_TABLE   = 'brokerid_validation_rule';
    private const PATTERN                 = '/^(\d{10})(\.0{5})$/';
    private const REPLACEMENT             = '${1}';
    private const VALIDATION_RULE_PATTERN = '^(\d{10})(\.0{5})$';

    private const INSURANCE_COMPANY_IDS = [
        Axa::KRANKEN,
        Axa::LEBEN,
        Axa::SACH,
        Axa::ALLGEMEIN,
    ];

    public function up(): bool
    {
        foreach (self::INSURANCE_COMPANY_IDS as $insuranceCompanyId) {
            $this->insert(
                self::TABLE,
                [
                    'insurance_company_id' => $insuranceCompanyId,
                    'regex'                => self::PATTERN,
                    'replacement'          => self::REPLACEMENT,
                ]
            );

            $this->insert(
                self::VALIDATION_RULE_TABLE,
                [
                    'insurance_company_id' => $insuranceCompanyId,
                    'regex'                => self::VALIDATION_RULE_PATTERN,
                    'example'              => '0000000000.00000',
                    'type_id'              => BrokerIdType::ANTRAGSNUMMER,
                ]
            );
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::INSURANCE_COMPANY_IDS as $insuranceCompanyId) {
            $this->delete(
                self::TABLE,
                'insurance_company_id = :insurance_company_id and regex = :regex and replacement = :replacement',
                [
                    ':insurance_company_id' => $insuranceCompanyId,
                    ':regex'                => self::PATTERN,
                    ':replacement'          => self::REPLACEMENT,
                ]
            );

            $this->delete(
                self::VALIDATION_RULE_TABLE,
                'insurance_company_id = :insurance_company_id and regex = :regex and example = "0000000000.00000" and type_id = :type_id',
                [
                    ':insurance_company_id' => $insuranceCompanyId,
                    ':regex'                => self::VALIDATION_RULE_PATTERN,
                    ':type_id'              => BrokerIdType::ANTRAGSNUMMER,
                ]
            );
        }

        return true;
    }
}
