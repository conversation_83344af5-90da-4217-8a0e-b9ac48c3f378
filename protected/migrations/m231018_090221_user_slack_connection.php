<?php

declare(strict_types=1);

final class m231018_090221_user_slack_connection extends CreateTableMigration
{
    public $tableName = 'user_slack_connection';

    public function getColumns()
    {
        return [
            'id'       => 'pk',
            'user_id'  => 'int unsigned not null',
            'slack_id' => 'string'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
