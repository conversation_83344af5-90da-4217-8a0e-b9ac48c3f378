<?php

declare(strict_types=1);

final class m231020_091903_add_table_vertragsdaten_client_files_submit extends CreateTableMigration
{
    public $tableName = 'vertragsdaten_client_files_submit';

    public function getColumns(): array
    {
        return [
            'id'          => 'pk',
            'contract_id' => 'int unsigned not null',
            'created_at'  => 'datetime',
            'finished_at' => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('contractId', 'contract_id', 'contracts', 'CASCADE', 'CASCADE');
    }
}
