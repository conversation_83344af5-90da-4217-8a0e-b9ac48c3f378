<?php

final class m231026_132205_contract_kfz_kennz<PERSON><PERSON>_use_contract_id_as_pk extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                contracts_kfz_kennzeichen
            DROP COLUMN
                id,
            MODIFY COLUMN
                contract_id INT UNSIGNED PRIMARY KEY FIRST,
            DROP KEY
                kfz_kennzeichen_contracts
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                contracts_kfz_kennzeichen
            ADD INDEX
                kfz_kennzeichen_contracts (contract_id),
            DROP PRIMARY KEY,
            ADD COLUMN
                id INT AUTO_INCREMENT PRIMARY KEY FIRST
            EOSQL);

        return true;
    }
}
