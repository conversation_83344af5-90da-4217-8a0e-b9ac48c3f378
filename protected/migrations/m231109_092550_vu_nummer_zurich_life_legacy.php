<?php

declare(strict_types=1);

final class m231109_092550_vu_nummer_zurich_life_legacy extends CDbMigration
{
    private const TABLE = 'vu_number';

    private const VU = 1351;

    private const VU_NAME = 'Zurich Life Legacy Versicherung AG';

    public function up(): bool
    {
        $company = InsuranceCompany::model()->findByAttributes(['name' => self::VU_NAME]);
        if ($company === null) {
            return true;
        }
        $this->insert(
            self::TABLE,
            [
                'ref'                  => self::VU,
                'name'                 => self::VU_NAME,
                'kind'                 => VuNumber::BAFIN,
                'insurance_company_id' => $company->id,
                'type'                 => 'Lebensversicherer unter Bundesaufsicht',
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $company = InsuranceCompany::model()->findByAttributes(['name' => self::VU_NAME]);
        if ($company === null) {
            return true;
        }
        $this->delete(self::TABLE, 'ref = :ref', [':ref' => self::VU]);

        return true;
    }
}
