<?php

declare(strict_types=1);

final class m231109_093602_fix_mail_footer_charset extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                mail_footer
            MODIFY COLUMN
                `text` TEXT CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                mail_footer
            MODIFY COLUMN
                `text` TEXT CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL
            EOSQL);

        return true;
    }
}
