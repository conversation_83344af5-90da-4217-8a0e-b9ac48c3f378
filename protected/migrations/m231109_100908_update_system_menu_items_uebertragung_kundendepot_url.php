<?php

declare(strict_types=1);

final class m231109_100908_update_system_menu_items_uebertragung_kundendepot_url extends CDbMigration
{
    private const TABLE        = 'system_menu_items';
    private const COLUMN       = 'url';
    private const MENU_ITEM_ID = 89;

    public function up(): bool
    {
        $this->update(self::TABLE, [self::COLUMN => '/kooperation/fondsforum'], 'id = :id', [':id' => self::MENU_ITEM_ID]);

        return true;
    }

    public function down(): bool
    {
        $this->update(self::TABLE, [self::COLUMN => '/fonds-offen/uebertragungkundendepots'], 'id = :id', [':id' => self::MENU_ITEM_ID]);

        return true;
    }
}
