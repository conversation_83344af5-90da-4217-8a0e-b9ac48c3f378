<?php

declare(strict_types=1);

use League\Csv\Reader;

final class m231117_142654_update_broker_id_bip2855 extends CDbMigration
{
    private const TABLE = 'brokerid_validation_rule';

    public function up(): bool
    {
        $insuranceCompanies = InsuranceCompany::model()->findAll(['index' => 'id']);
        $val                = BrokerIdValidationRule::model()->findAll(['index' => 'id']);

        foreach ($this->loadData() as $row) {
            try {
                if (!array_key_exists($row['insurance_company_id'], $insuranceCompanies)) {
                    // If key does not exist in Insurance company table then skip
                    continue;
                }
                if (array_key_exists($row['id'], $val)) {
                    // If the PK already exists then update the row
                    $this->update(self::TABLE,
                        $row,
                        'id = ' . $row['id'],
                    );
                } else {
                    // Insert row in table if PK does not exist
                    $this->insert(self::TABLE, $row);
                }
            } catch (Exception $e) {
                echo 'There was an Error with ID: ' . $row['id'];
            }
        }

        return true;
    }

    public function down(): bool
    {
        echo "m231117_142654_update_broker_id_bip2855 does not support migration down.\n";

        return true;
    }

    private function loadData(): array
    {
        $csv = Reader::createFromPath(__DIR__ . DS . 'files' . DS . 'brokerid_validation_BIP-2855.csv', 'r');
        $csv->setDelimiter(',');
        $csv->setHeaderOffset(0);

        return iterator_to_array($csv->getRecords());
    }
}
