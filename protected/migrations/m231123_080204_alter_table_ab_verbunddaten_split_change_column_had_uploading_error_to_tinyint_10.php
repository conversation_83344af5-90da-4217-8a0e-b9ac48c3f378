<?php

declare(strict_types=1);

final class m231123_080204_alter_table_ab_verbunddaten_split_change_column_had_uploading_error_to_tinyint_10 extends CDbMigration
{
    private const TABLE  = 'ab_verbunddaten_split';
    private const COLUMN = 'had_uploading_error';

    public function up(): bool
    {
        $this->execute(
            <<<EOSQL
            alter table ab_verbunddaten_split
            change had_uploading_error error_code tinyint(10) default 0 null;
            EOSQL
        );

        return true;
    }

    public function down(): bool
    {
        $this->execute(
            <<<EOSQL
            alter table ab_verbunddaten_split
            change error_code had_uploading_error tinyint(1) default 0 null;
            EOSQL
        );

        return true;
    }
}
