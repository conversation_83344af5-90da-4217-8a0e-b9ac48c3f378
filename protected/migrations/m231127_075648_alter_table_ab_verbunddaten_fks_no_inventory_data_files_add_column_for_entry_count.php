<?php

declare(strict_types=1);

final class m231127_075648_alter_table_ab_verbunddaten_fks_no_inventory_data_files_add_column_for_entry_count extends CDbMigration
{
    private const TABLE  = 'ab_verbunddaten_fks_no_inventory_data_files';
    private const COLUMN = 'row_count';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'INT UNSIGNED');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
