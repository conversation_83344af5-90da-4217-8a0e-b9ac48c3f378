<?php

declare(strict_types=1);

use Business\Company\SwissLife;

final class m231129_083021_add_swiss_life_broker_id_pattern extends CDbMigration
{
    private const TABLE                 = 'brokerid_pattern';
    private const VALIDATION_RULE_TABLE = 'brokerid_validation_rule';
    private const PATTERN               = '/^\d{6}000[1-9]$/';

    public function up(): bool
    {
        $this->insert(self::TABLE, [
            'insurance_company_id' => SwissLife::ID,
            'regex'                => self::PATTERN,
            'replacement'          => '${1}',
        ]);

        $this->update(self::VALIDATION_RULE_TABLE, [
            'regex'                => self::PATTERN,
            'example'              => '0000000001 | 0001-9 am Ende einfügen | 10 Ziffern',
        ], 'insurance_company_id = ' . SwissLife::ID . " AND regex = '^\\\d{6}0001$'");

        return true;
    }

    public function down(): bool
    {
        $this->delete(self::TABLE, [
            'insurance_company_id' => SwissLife::ID,
            'regex'                => self::PATTERN,
            'replacement'          => '${1}',
        ]);

        return true;
    }
}
