<?php

declare(strict_types=1);

final class m231129_101102_alter_table_ab_verbunddaten_fks_no_inventory_data_files_add_retry_column extends CDbMigration
{
    private const TABLE  = 'ab_verbunddaten_fks_no_inventory_data_files';
    private const COLUMN = 'retry';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'TINYINT(1) NOT NULL DEFAULT 0');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
