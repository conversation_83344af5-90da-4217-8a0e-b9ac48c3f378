<?php

declare(strict_types=1);

final class m231129_131146_add_table_vertragsdaten_testing_results extends CreateTableMigration
{
    public $tableName = 'vertragsdaten_testing_results';

    public function getColumns(): array
    {
        return [
            'id'               => 'pk',
            'client_id'        => 'integer unsigned not null',
            'contract_id'      => 'integer unsigned not null',
            'product_combo_id' => 'integer unsigned not null',
            'client_file_id'   => 'integer unsigned not null',
            'client_file_name' => 'string',
            'query'            => 'string',
            'answer'           => 'string',
            'answer_format'    => 'string',
            'created_at'       => 'Datetime',
        ];
    }

    public function addForeignKeys(): void
    {
        $this->addOwnFk('client', 'client_id', 'client', 'CASCADE', 'CASCADE');
        $this->addOwnFk('contract', 'contract_id', 'contracts', 'CASCADE', 'CASCADE');
        $this->addOwnFk('client_file', 'client_file_id', 'client_files', 'CASCADE', 'CASCADE');
        $this->addOwnFk('sparte', 'product_combo_id', 'product_combo', 'CASCADE', 'CASCADE');
    }
}
