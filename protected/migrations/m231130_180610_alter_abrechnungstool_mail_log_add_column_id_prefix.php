<?php

declare(strict_types=1);

final class m231130_180610_alter_abrechnungstool_mail_log_add_column_id_prefix extends CDbMigration
{
    const TABLE             = 'abrechnungstool_mail_log';
    const VALIDITY_COLUMN   = 'mail_id_validity';
    const RECIEVED_COLUMN   = 'mail_recieved_at';
    const SUBJECT_COLUMN    = 'mail_subject';
    const FROM_COLUMN       = 'mail_from';
    const MESSAGE_ID_COLUMN = 'mail_message_id';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::VALIDITY_COLUMN, 'INT AFTER mail_id');
        $this->addColumn(self::TABLE, self::MESSAGE_ID_COLUMN, 'VARCHAR(128) AFTER ' . self::VALIDITY_COLUMN);
        $this->addColumn(self::TABLE, self::FROM_COLUMN, 'VARCHAR(128) AFTER ' . self::MESSAGE_ID_COLUMN);
        $this->addColumn(self::TABLE, self::SUBJECT_COLUMN, 'VARCHAR(128) AFTER ' . self::FROM_COLUMN);
        $this->addColumn(self::TABLE, self::RECIEVED_COLUMN, 'DATETIME AFTER ' . self::SUBJECT_COLUMN);

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::VALIDITY_COLUMN);
        $this->dropColumn(self::TABLE, self::MESSAGE_ID_COLUMN);
        $this->dropColumn(self::TABLE, self::FROM_COLUMN);
        $this->dropColumn(self::TABLE, self::SUBJECT_COLUMN);
        $this->dropColumn(self::TABLE, self::RECIEVED_COLUMN);

        return true;
    }
}
