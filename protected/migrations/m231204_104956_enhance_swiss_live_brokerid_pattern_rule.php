<?php

declare(strict_types=1);

final class m231204_104956_enhance_swiss_live_brokerid_pattern_rule extends CDbMigration
{
    private const TABLE       = 'brokerid_validation_rule';
    private const PATTERN     = '^\d{6}000[1-9]$';
    private const OLD_PATTERN = '/^\d{6}000[1-9]$/';

    public function up(): bool
    {
        $this->update(
            self::TABLE,
            [
                'regex' => self::PATTERN,
            ],
            'insurance_company_id = 25'
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE,
            [
                'regex' => self::OLD_PATTERN,
            ],
            'insurance_company_id = 25'
        );

        return true;
    }
}
