<?php

declare(strict_types=1);

final class m231204_125837_create_fk_client_to_client_relation extends CDbMigration
{
    private const TABLE_RELATION                = 'client_relation';
    private const TABLE_CLIENT                  = 'client';
    private const FK_CLIENT_RELATION_CLIENT_ID  = 'fk_client_relaion_client_1';
    private const FK_CLIENT_RELATION_RELATED_ID = 'fk_client_relaion_client_2';
    private const COLUMN_CLIENT_ID              = 'client_id';
    private const COLUMN_RELATION_RELATED_ID    = 'related_client_id';

    public function up(): bool
    {
        // First a clean up
        ClientRelation::model()->deleteAll('client_id NOT IN (SELECT id FROM client) or related_client_id NOT IN (SELECT id FROM client)');

        // second create two new foreign keys
        $this->addForeignKey(self::FK_CLIENT_RELATION_CLIENT_ID,
            self::TABLE_RELATION,
            self::COLUMN_CLIENT_ID,
            self::TABLE_CLIENT,
            'id',
            'CASCADE',
            'CASCADE');

        $this->addForeignKey(self::FK_CLIENT_RELATION_RELATED_ID,
            self::TABLE_RELATION,
            self::COLUMN_RELATION_RELATED_ID,
            self::TABLE_CLIENT,
            'id',
            'CASCADE',
            'CASCADE');

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_CLIENT_RELATION_CLIENT_ID, self::TABLE_RELATION);
        $this->dropForeignKey(self::FK_CLIENT_RELATION_RELATED_ID, self::TABLE_RELATION);

        return true;
    }
}
