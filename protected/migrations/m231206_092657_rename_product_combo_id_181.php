<?php

declare(strict_types=1);

final class m231206_092657_rename_product_combo_id_181 extends CDbMigration
{
    private const TABLE           = 'product_combo';
    private const ID              = 181;
    private const NEW_DESCRIPTION = 'Investmentdepot';
    private const OLD_DESCRIPTION = 'Offene Investmentfonds';

    public function up(): bool
    {
        $this->update(
            self::TABLE,
            [
                'name' => self::NEW_DESCRIPTION,
            ],
            'id = :id',
            [
                ':id' => self::ID,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE,
            [
                'name' => self::OLD_DESCRIPTION,
            ],
            'id = :id',
            [
                ':id' => self::ID,
            ]
        );

        return true;
    }
}
