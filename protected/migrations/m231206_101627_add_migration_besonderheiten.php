<?php

declare(strict_types=1);

final class m231206_101627_add_migration_besonderheiten extends CDbMigration
{
    private const TABLE = 'insurance_company_specifics';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, 'datenaustausch_fks', 'bool');
        $this->addColumn(self::TABLE, 'rd_overheadabrechnung', 'bool');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, 'datenaustausch_fks');
        $this->dropColumn(self::TABLE, 'rd_overheadabrechnung');

        return true;
    }
}
