<?php

declare(strict_types=1);

final class m231212_151227_add_identifier_to_abrechnungstool_csv_upload_log extends CDbMigration
{
    private const TABLE  = 'abrechnungstool_csv_upload_log';
    private const COLUMN = 'process_id';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'varchar(100) DEFAULT NULL');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
