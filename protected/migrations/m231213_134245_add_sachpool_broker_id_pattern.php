<?php

use Business\Company\Sachpool;

final class m231213_134245_add_sachpool_broker_id_pattern extends CDbMigration
{
    private const TABLE = 'brokerid_pattern';

    public function up(): bool
    {
        $this->insert(
            self::TABLE,
            [
                'insurance_company_id' => Sachpool::ID,
                'regex'                => '/^(\d{5})-(\d{2})$/',
                'replacement'          => '${1}${2}',
            ]
        );

        $this->insert(
            self::TABLE,
            [
                'insurance_company_id' => Sachpool::ID,
                'regex'                => '/^(\d{5})(\d{2})$/',
                'replacement'          => '${1}-${2}',
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            'insurance_company_id = :insurance_company_id',
            [
                ':insurance_company_id' => Sachpool::ID,
            ]
        );

        return true;
    }
}
