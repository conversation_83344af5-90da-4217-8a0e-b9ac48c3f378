<?php

declare(strict_types=1);

final class m231219_110201_truncate_fks_tables extends CDbMigration
{
    private const TABLES = [
        'ab_fks_postfach_log',
        'ab_fks_verbunddaten_split_log',
        'ab_verbunddaten_fks_export',
        'ab_verbunddaten_fks_bestand',
        'ab_verbunddaten_fks_no_inventory_data_files',
        'ab_verbunddaten_fks_overhead',
    ];

    public function up(): bool
    {
        echo 'This truncate process was only required once (FKS go-live) and should not be carried out again under any circumstances.';

        return true;
        if (getenv('APP_ENV') === 'local') {
            echo 'Local data will not be truncated.';

            return true;
        }

        foreach (self::TABLES as $table) {
            $this->execute('SET FOREIGN_KEY_CHECKS = 0');
            $this->truncateTable($table);
            $this->execute('SET FOREIGN_KEY_CHECKS = 1');
        }

        return true;
    }

    public function down(): bool
    {
        echo "m231219_110201_truncate_fks_tables does not support migration down.\n";

        return true;
    }
}
