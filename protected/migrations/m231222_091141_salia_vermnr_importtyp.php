<?php

declare(strict_types=1);

final class m231222_091141_salia_vermnr_importtyp extends CDbMigration
{
    private const TABLE     = 'contracts';
    private const COLUMN    = 'import_type_id';
    private const IMPORTTYP = 5;

    public function up(): bool
    {
        $this->update(
            self::TABLE,
            [self::COLUMN => self::IMPORTTYP],
            self::COLUMN . ' <> ' . self::IMPORTTYP . ' AND vermittlernummer like "%SALIA%"');

        return true;
    }

    public function down(): bool
    {
        echo "m231222_091141_salia_vermnr_importtyp does not support migration down.\n";

        return true;
    }
}
