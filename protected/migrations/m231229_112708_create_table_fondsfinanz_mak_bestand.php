<?php

declare(strict_types=1);

final class m231229_112708_create_table_fondsfinanz_mak_bestand extends CreateTableMigration
{
    public $tableName = 'fondsfinanz_mak_bestand';

    public function getColumns()
    {
        return [
            'id'                            => 'pk',
            'user_id'                       => 'INT(11) UNSIGNED  NOT NULL',
            'mak_nr'                        => 'VARCHAR(15) NOT NULL',
            'concluded_contracts_count'     => 'INT(11) UNSIGNED DEFAULT 0',
            'not_concluded_contracts_count' => 'INT(11) UNSIGNED DEFAULT 0',
            'create_datetime'               => 'DATETIME',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user_id', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
