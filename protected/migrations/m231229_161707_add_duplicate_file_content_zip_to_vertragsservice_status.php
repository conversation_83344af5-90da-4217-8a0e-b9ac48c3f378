<?php

declare(strict_types=1);

use Components\Bipro\Vertragsservice\Status\Metastatus;
use Components\Bipro\Vertragsservice\Status\Status;

final class m231229_161707_add_duplicate_file_content_zip_to_vertragsservice_status extends CDbMigration
{
    private const TABLE = 'vertragsservice_status';

    public function up(): bool
    {
        $this->insert(
            self::TABLE,
            [
                'id'            => Status::FILE_IS_DUPLICATE_ZIP,
                'metastatus_id' => Metastatus::FILE_IS_DUPLICATE,
                'message'       => 'Eine oder mehrere Dateien in der Zip Datei sind bereits im System vorhanden',
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            'id = :id',
            [
                ':id' => Status::FILE_IS_DUPLICATE_ZIP,
            ]
        );

        return true;
    }
}
