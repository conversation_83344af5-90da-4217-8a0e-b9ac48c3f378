<?php

declare(strict_types=1);

final class m240115_144119_add_more_konfiguration_allgemein_options extends CDbMigration
{
    public const TABLE           = 'agency_settings';
    public const COLUMN_1        = 'dv_aufbau';
    public const COLUMN_2        = 'dv_mr_money';
    public const COLUMN_3        = 'dv_thinksurance';
    public const COLUMN_PREVIOUS = 'zusammenarbeit_demv';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN_1, 'int AFTER ' . self::COLUMN_PREVIOUS);
        $this->addColumn(self::TABLE, self::COLUMN_2, 'int AFTER ' . self::COLUMN_1);
        $this->addColumn(self::TABLE, self::COLUMN_3, 'int AFTER ' . self::COLUMN_2);

        $this->execute('UPDATE agency_settings aset SET
aset.' . self::COLUMN_1 . ' = aset.' . self::COLUMN_PREVIOUS . ',
aset.' . self::COLUMN_2 . ' = aset.' . self::COLUMN_PREVIOUS . ',
aset.' . self::COLUMN_3 . ' = aset.' . self::COLUMN_PREVIOUS . ';');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_1);
        $this->dropColumn(self::TABLE, self::COLUMN_2);
        $this->dropColumn(self::TABLE, self::COLUMN_3);

        return true;
    }
}
