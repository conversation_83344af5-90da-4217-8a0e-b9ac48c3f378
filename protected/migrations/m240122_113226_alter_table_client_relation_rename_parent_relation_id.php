<?php

declare(strict_types=1);

final class m240122_113226_alter_table_client_relation_rename_parent_relation_id extends CDbMigration
{
    private const TABLE      = 'client_relation';
    private const COLUMN     = 'parent_relation_id';
    private const NEW_COLUMN = 'opposite_relation_id';

    public function up(): bool
    {
        $this->renameColumn(self::TABLE, self::COLUMN, self::NEW_COLUMN);

        return true;
    }

    public function down(): bool
    {
        $this->renameColumn(self::TABLE, self::NEW_COLUMN, self::COLUMN);

        return true;
    }
}
