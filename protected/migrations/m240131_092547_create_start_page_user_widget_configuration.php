<?php

declare(strict_types=1);

final class m240131_092547_create_start_page_user_widget_configuration extends CreateTableMigration
{
    public $tableName = 'start_page_user_widget_configuration';

    public function getColumns()
    {
        return [
            'id'             => 'pk',
            'user_id'        => 'INT(11) UNSIGNED  NOT NULL',
            'configuration'  => 'BLOB NOT NULL',
            'created_at'     => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
            'updated_at'     => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP'
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user_id', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
