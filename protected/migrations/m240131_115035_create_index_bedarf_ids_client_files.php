<?php

declare(strict_types=1);

final class m240131_115035_create_index_bedarf_ids_client_files extends CDbMigration
{
    private const TABLE_NAME  = 'client_files';
    private const INDEX_NAME  = 'client_files_bedarf_id_idx';
    private const COLUMN_NAME = 'bedarf_id';

    public function up(): bool
    {
        $this->createIndex(self::INDEX_NAME, self::TABLE_NAME, self::COLUMN_NAME);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::INDEX_NAME, self::TABLE_NAME);

        return true;
    }
}
