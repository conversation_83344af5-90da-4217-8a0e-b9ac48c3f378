<?php

declare(strict_types=1);

final class m240202_084247_create_table_gdv2bipro_email_pgp_keys extends CreateTableMigration
{
    public $tableName = 'gdv2bipro_email_pgp_keys';

    public function getColumns()
    {
        return [
            'id'               => 'pk',
            'name'             => 'VARCHAR(255) NOT NULL',
            'fingerprint'      => 'VARCHAR(255) NOT NULL',
            'passphrase'       => 'VARCHAR(255) NOT NULL',
            'expire_datetime'  => 'DATETIME NULL',
            'create_datetime'  => 'DATETIME NOT NULL',
            'sodium_encrypted' => 'TINYINT(1) NOT NUll DEFAULT 0',
        ];
    }
}
