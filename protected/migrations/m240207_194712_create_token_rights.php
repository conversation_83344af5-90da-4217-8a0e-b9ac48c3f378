<?php

declare(strict_types=1);

final class m240207_194712_create_token_rights extends CDbMigration
{
    public function up(): bool
    {
        $this->insert('rights', [
            'id'          => Rights::PW_API_TOKENS_VERWALTEN,
            'name'        => 'PW API Tokens verwalten',
            'description' => 'API Tokens können erstellt und gelöscht werden.',
        ]);

        return true;
    }

    public function down(): bool
    {
        $this->delete('rights', 'id=' . Rights::PW_API_TOKENS_VERWALTEN);

        return true;
    }
}
