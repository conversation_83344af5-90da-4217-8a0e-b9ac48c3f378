<?php

declare(strict_types=1);

final class m240208_124613_alter_document_type_table extends CDbMigration
{
    private const TABLE  = 'document_type';
    private const COLUMN = 'name';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(100)');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(45)');

        return true;
    }
}
