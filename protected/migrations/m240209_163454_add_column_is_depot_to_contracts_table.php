<?php

declare(strict_types=1);

final class m240209_163454_add_column_is_depot_to_contracts_table extends CDbMigration
{
    private const TABLE           = 'contracts';
    private const COLUMN_IS_DEPOT = 'is_depot';
    private const IS_DEPOT_IDX    = 'is_depot_idx';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN_IS_DEPOT, 'tinyint default 0 after wagnisart_sparte');
        $this->createIndex(self::IS_DEPOT_IDX, self::TABLE, self::COLUMN_IS_DEPOT);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::IS_DEPOT_IDX, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN_IS_DEPOT);

        return true;
    }
}
