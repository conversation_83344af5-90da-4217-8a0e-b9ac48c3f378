<?php

declare(strict_types=1);

final class m240212_170454_add_json_checks extends CDbMigration
{
    public function up(): bool
    {
        $this->execute('ALTER TABLE event MODIFY payload JSON NOT NULL');
        $this->execute('ALTER TABLE oauth2_connections MODIFY data JSON');
        $this->execute(<<<'EOSQL'
            ALTER TABLE tarifierung_risikodemver
            MODIFY details JSON NOT NULL,
            MODIFY buttons JSON NOT NULL
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        echo "m240212_170454_add_json_checks does not support migration down.\n";

        return true;
    }
}
