<?php

declare(strict_types=1);

final class m240213_100235_add_column_pgp_key_to_gesellschaft_gruppen_table extends CDbMigration
{
    private const TABLE          = 'gesellschaft_gruppen';
    private const COLUMN         = 'pgp_key_id';
    private const PGP_KEYS_TABLE = 'gdv2bipro_email_pgp_keys';
    private const FK             = 'fk_' . self::TABLE . '_' . self::PGP_KEYS_TABLE . '_' . self::COLUMN;

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'INT NULL');
        $this->addForeignKey(self::FK, self::TABLE, self::COLUMN, self::PGP_KEYS_TABLE, 'id');

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
