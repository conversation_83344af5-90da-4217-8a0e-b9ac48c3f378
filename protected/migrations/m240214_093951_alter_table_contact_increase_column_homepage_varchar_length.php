<?php

declare(strict_types=1);

final class m240214_093951_alter_table_contact_increase_column_homepage_varchar_length extends CDbMigration
{
    public const TABLE  = 'contact';
    public const COLUMN = 'homepage';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(255) NULL');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(64) NULL');

        return true;
    }
}
