<?php

declare(strict_types=1);

final class m240219_150643_create_table_fm_interessent_log extends CreateTableMigration
{
    public $tableName = 'fm_interessent_log';

    public function getColumns(): array
    {
        return [
            'id'         => 'pk',
            'client_id'  => 'int(11) unsigned default null',
            'user_id'    => 'int(11) unsigned',
            'firstname'  => 'varchar(120)',
            'lastname'   => 'varchar(120)',
            'email'      => 'varchar(100)',
            'status'     => 'varchar(255) not null',
            'created_at' => 'datetime not null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk(
            'client',
            'client_id',
            'client',
            'CASCADE',
            'CASCADE'
        );
        $this->addOwnFk(
            'user',
            'user_id',
            'user',
            'CASCADE',
            'CASCADE'
        );
    }
}
