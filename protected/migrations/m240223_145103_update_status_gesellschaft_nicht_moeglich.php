<?php

declare(strict_types=1);

final class m240223_145103_update_status_gesellschaft_nicht_moeglich extends CDbMigration
{
    private const TABLE       = 'vertragsservice_status';
    private const ID          = 4;
    private const MESSAGE     = 'Datei enthält unbekannte Vu-Nummern.';
    private const OLD_MESSAGE = 'Ein automatischer Import ist nicht möglich.';

    public function up(): bool
    {
        $this->update(
            self::TABLE,
            ['message' => self::MESSAGE],
            'id = :id',
            [':id' => self::ID]
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE,
            ['message' => self::OLD_MESSAGE],
            'id = :id',
            [':id' => self::ID]
        );

        return true;
    }
}
