<?php

declare(strict_types=1);

final class m240227_183555_create_fm_makler_settings extends CreateTableMigration
{
    public $tableName = 'fm_makler_settings';

    public function getColumns(): array
    {
        return [
            'id'              => 'pk',
            'user_id'         => 'int(11) unsigned unique',
            'start_page_text' => 'text',
            'updated_at'      => 'datetime',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk(
            'user',
            'user_id',
            'user',
            'CASCADE',
            'CASCADE'
        );
    }
}
