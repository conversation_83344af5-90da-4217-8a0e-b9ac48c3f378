<?php

declare(strict_types=1);

final class m240307_104511_rename_column_depot_id_to_contract_id_in_table_depot_fonds extends CDbMigration
{
    private const TABLE           = 'depot_fond';
    private const NEW_COLUMN_NAME = 'contract_id';
    private const OLD_COLUMN_NAME = 'depot_id';
    private const FK_NAME         = 'fk_depot_fond_contract_id_contracts';

    public function up(): bool
    {
        $this->renameColumn(self::TABLE, self::OLD_COLUMN_NAME, self::NEW_COLUMN_NAME);
        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE,
            self::NEW_COLUMN_NAME,
            'contracts',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE);

        $this->renameColumn(self::TABLE, self::NEW_COLUMN_NAME, self::OLD_COLUMN_NAME);

        return true;
    }
}
