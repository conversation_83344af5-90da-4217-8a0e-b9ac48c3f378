<?php

declare(strict_types=1);

final class m240315_105626_create_table_expected_interval_config extends CreateTableMigration
{
    public $tableName = 'monitoring_datenlieferung_expected_interval_config';

    public function getColumns()
    {
        return [
            'id'         => 'pk',
            'type'       => 'VARCHAR(255) NOT NULL',
            'type_value' => 'INT(11) NOT NULL',
            'days'       => 'INT(11) NOT NULL DEFAULT 0',
            'months'     => 'INT(11) NOT NULL DEFAULT 0',
            'years'      => 'INT(11) NOT NULL DEFAULT 0',
            'weekdays'   => 'INT(11) NOT NULL DEFAULT 0',
        ];
    }
}
