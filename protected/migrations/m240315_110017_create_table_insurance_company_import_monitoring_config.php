<?php

declare(strict_types=1);

final class m240315_110017_create_table_insurance_company_import_monitoring_config extends CreateTableMigration
{
    public $tableName = 'monitoring_datenlieferung_insurance_company_import_config';

    public function getColumns()
    {
        return [
            'id'                   => 'pk',
            'insurance_company_id' => 'int(255) unsigned NOT NULL',
            'expected_interval_id' => 'INT(11)',
            'combined_call'        => 'TINYINT',
            'delivery_method'      => 'TINYINT',
            'file_type'            => 'TINYINT',
            'data_type'            => 'TINYINT',
            'encoding'             => 'VARCHAR(255)',
            'email'                => 'VARCHAR(255)',
            'bipro_settings'       => 'INT(11)',
            'data_settings'        => 'INT(11)',
            'note'                 => 'TEXT',
            'links'                => 'TEXT',
            'created_at'           => 'DATETIME',
            'updated_at'           => 'DATETIME',
            'created_by'           => 'INT(11) UNSIGNED  NOT NULL',
        ];
    }

    public function addForeignKeys()
    {
        $this->addForeignKey(
            'monitoring_datenlieferung_import_config_insurance_company_id_fk',
            $this->tableName,
            'insurance_company_id',
            'insurance_company',
            'id',
            'CASCADE',
            'CASCADE');
        $this->addForeignKey(
            'monitoring_datenlieferung_import_config_expected_interval_id_fk',
            $this->tableName,
            'expected_interval_id',
            'monitoring_datenlieferung_expected_interval_config',
            'id',
            'CASCADE',
            'CASCADE');
    }
}
