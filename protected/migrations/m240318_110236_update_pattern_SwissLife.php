<?php

declare(strict_types=1);

use Business\Company\SwissLife;

final class m240318_110236_update_pattern_SwissLife extends CDbMigration
{
    public const REGEX        = '/^(\d{6})$/';
    public const REPLACEMENTS = [
        '${1}0001',
        '${1}0002',
        '${1}0003',
        '${1}0004',
        '${1}0005',
        '${1}0006',
        '${1}0007',
        '${1}0008',
        '${1}0009',
        '${1}0010',
        '${1}0011',
    ];

    private const TABLE_NAME = 'brokerid_pattern';

    public function safeUp(): bool
    {
        $this->execute('DELETE FROM ' . self::TABLE_NAME . ' WHERE insurance_company_id = ?', [SwissLife::ID]);

        foreach (self::REPLACEMENTS as $replacement) {
            $this->insert(
                self::TABLE_NAME,
                [
                    'insurance_company_id' => SwissLife::ID,
                    'regex'                => self::REGEX,
                    'replacement'          => $replacement,
                ]
            );
        }

        $this->insert(
            self::TABLE_NAME,
            [
                'insurance_company_id' => SwissLife::ID,
                'regex'                => '/^(\d{6})00(0[1-9]|1[0-1])$/',
                'replacement'          => '${1}',
            ]
        );

        return true;
    }

    public function safeDown(): bool
    {
        $this->execute('DELETE FROM ' . self::TABLE_NAME . ' WHERE insurance_company_id = ?', [SwissLife::ID]);

        $this->insert(
            self::TABLE_NAME,
            [
                'insurance_company_id' => SwissLife::ID,
                'regex'                => '/^\d{6}000[1-9]$/',
                'replacement'          => '${1}',
            ]
        );

        return true;
    }
}
