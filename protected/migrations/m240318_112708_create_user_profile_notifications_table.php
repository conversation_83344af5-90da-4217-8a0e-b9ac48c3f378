<?php

declare(strict_types=1);

final class m240318_112708_create_user_profile_notifications_table extends CreateTableMigration
{
    public $tableName = 'user_profile_notifications';

    public function getColumns(): array
    {
        return [
            'id'              => 'pk',
            'user_id'         => 'INT(11) UNSIGNED  NOT NULL',
            'app_name'        => 'VARCHAR(80) NOT NULL',
            'key'             => 'VARCHAR(255) NOT NULL',
            'value'           => 'VARCHAR(255) NOT NULL',
            'create_datetime' => 'DATETIME',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('user_id', 'user_id', 'user', 'CASCADE', 'CASCADE');
    }
}
