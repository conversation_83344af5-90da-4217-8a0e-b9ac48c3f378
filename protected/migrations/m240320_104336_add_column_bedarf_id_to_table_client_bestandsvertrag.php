<?php

declare(strict_types=1);

final class m240320_104336_add_column_bedarf_id_to_table_client_bestandsvertrag extends CDbMigration
{
    private const TABLE  = 'client_bestandsvertrag';
    private const COLUMN = 'bedarf_id';

    public function safeUp(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN,
            'varchar(255) NULL DEFAULT NULL COLLATE utf8mb3_general_ci AFTER title'
        );

        return true;
    }

    public function safeDown(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
