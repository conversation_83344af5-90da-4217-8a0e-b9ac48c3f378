<?php

declare(strict_types=1);

final class m240415_113323_remove_vorgaenge_verwalten_right extends CDbMigration
{
    public function up(): bool
    {
        $this->delete('rights', 'id = 21');

        return true;
    }

    public function down(): bool
    {
        $this->insert('rights', [
            'id'              => 21,
            'name'            => 'Vorgänge/Anfragen verwalten',
            'controller'      => 'ProcedureController',
            'action'          => 'actionAllowAll',
            'parent_right_id' => null,
            'description'     => 'Es können Vorgänge und Risikovoranfragen angelegt und verschickt werden. Es können Vorlagen sowie die Erinnerungsfunktion genutzt werden.',
            'sort'            => 5,
        ]);

        return true;
    }
}
