<?php

declare(strict_types=1);

final class m240416_092906_add_column_comment_to_verbunddaten_split extends CDbMigration
{
    private const TABLE  = 'ab_verbunddaten_split';
    private const COLUMN = 'comment';

    public function up(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN,
            'varchar(255) NULL DEFAULT NULL COLLATE utf8mb3_general_ci'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
