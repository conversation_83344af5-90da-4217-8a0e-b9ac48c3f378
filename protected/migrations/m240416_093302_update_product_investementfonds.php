<?php

declare(strict_types=1);

final class m240416_093302_update_product_investementfonds extends CDbMigration
{
    private const TABLE     = 'product';
    private const NAME      = 'Investmentdepot';
    private const OLD_NAME  = 'Offene Investmentfonds';

    public function up(): bool
    {
        $this->update(
            self::TABLE,
            ['name' => self::NAME],
            'id = :id',
            [':id' => Product::INVESTMENTDEPOT]
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE,
            ['name' => self::OLD_NAME],
            'id = :id',
            [':id' => Product::INVESTMENTDEPOT]
        );

        return true;
    }
}
