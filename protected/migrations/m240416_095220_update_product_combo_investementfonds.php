<?php

declare(strict_types=1);

final class m240416_095220_update_product_combo_investementfonds extends CDbMigration
{
    private const NAME      = 'Investmentdepot';
    private const OLD_NAME  = 'Offene Investmentfonds';

    private function getInvestmentDepotCriteria(string $name): CdbCriteria
    {
        $criteria = new CDbCriteria();
        $criteria->addCondition('level1_id = :LEVEL1_ID');
        $criteria->params[':LEVEL1_ID'] = Product::INVESTMENTDEPOT;
        $criteria->addSearchCondition('name', $name);

        return $criteria;
    }

    public function up(): bool
    {
        $productCombos = ProductCombo::model()->findAll($this->getInvestmentDepotCriteria(self::OLD_NAME));

        foreach ($productCombos as $productCombo) {
            $productCombo->name = str_replace(self::OLD_NAME, self::NAME, $productCombo->name);
            $productCombo->save();
        }

        return true;
    }

    public function down(): bool
    {
        $productCombos = ProductCombo::model()->findAll($this->getInvestmentDepotCriteria(self::NAME));

        foreach ($productCombos as $productCombo) {
            $productCombo->name = str_replace(self::NAME, self::OLD_NAME, $productCombo->name);
            $productCombo->save();
        }

        return true;
    }
}
