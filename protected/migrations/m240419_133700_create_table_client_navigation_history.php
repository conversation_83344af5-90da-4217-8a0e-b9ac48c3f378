<?php

declare(strict_types=1);

final class m240419_133700_create_table_client_navigation_history extends CDbMigration
{
    private const TABLE      = 'client_navigation_history';
    private const FK_CLIENT  = 'FK_client_navigation_history_client';
    private const FK_USER    = 'fk_client_navigation_history_user';
    private const IDX_WRITE  = 'UNIQUE_user_id_client_id';
    private const IDX_READ   = 'IDX_user_id_visit';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'                 => 'pk',
                'user_id'            => 'int unsigned NOT NULL',
                'client_id'          => 'int unsigned NOT NULL',
                'last_visit'         => 'timestamp NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_USER,
            self::TABLE,
            'user_id',
            'user',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            self::FK_CLIENT,
            self::TABLE,
            'client_id',
            'client',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createIndex(self::IDX_WRITE, self::TABLE, ['user_id', 'client_id'], true);
        $this->createIndex(self::IDX_READ, self::TABLE, ['user_id', 'last_visit']);

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
