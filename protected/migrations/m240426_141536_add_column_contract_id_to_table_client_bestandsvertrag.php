<?php

declare(strict_types=1);

final class m240426_141536_add_column_contract_id_to_table_client_bestandsvertrag extends CDbMigration
{
    public const COLUMN_PREVIOUS                   = 'bedarf_id';
    private const TABLE                            = 'client_bestandsvertrag';
    private const COLUMN_CONTRACT_ID               = 'contract_id';
    private const COLUMN_CREATION_DATETIME         = 'create_datetime';

    public function safeUp(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN_CONTRACT_ID,
            'int unsigned NULL AFTER ' . self::COLUMN_PREVIOUS
        );

        $this->addColumn(
            self::TABLE,
            self::COLUMN_CREATION_DATETIME,
            'datetime not null DEFAULT NOW() AFTER ' . self::COLUMN_CONTRACT_ID
        );

        $this->execute('update client_bestandsvertrag set create_datetime = IFNULL(datetime, create_datetime);');

        return true;
    }

    public function safeDown(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_CONTRACT_ID);
        $this->dropColumn(self::TABLE, self::COLUMN_CREATION_DATETIME);

        return true;
    }
}
