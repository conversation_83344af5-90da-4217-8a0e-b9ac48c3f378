<?php

declare(strict_types=1);

final class m240430_124617_add_column_send_customers_without_contracts_to_ab_fks_ftp_connections extends CDbMigration
{
    private const TABLE  = 'ab_fks_ftp_connections';
    private const COLUMN = 'send_customers_without_contracts';

    public function up(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN,
            'tinyint(1) DEFAULT 0'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
