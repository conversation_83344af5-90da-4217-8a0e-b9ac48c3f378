<?php

declare(strict_types=1);

final class m240503_074801_add_column_create_datetime_to_bank_account extends CDbMigration
{
    private const TABLE  = 'bank_account';
    private const COLUMN = 'create_datetime';

    public function up(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN,
            'DATETIME'
        );

        $this->alterColumn(
            self::TABLE,
            self::COLUMN,
            'DATETIME DEFAULT CURRENT_TIMESTAMP'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
