<?php

declare(strict_types=1);

final class m240507_144336_add_columns_to_table_client_bestandsvertrag extends CDbMigration
{
    private const TABLE   = 'client_bestandsvertrag';
    private const COLUMNS = [
        'insurance_id'   => 'int(255) NULL DEFAULT NULL AFTER bedarf_id',
        'contribution'   => 'double NULL DEFAULT NULL AFTER bedarf_id',
        'payment_method' => 'varchar(20) NULL DEFAULT NULL AFTER bedarf_id',
        'term'           => 'int(11) NULL DEFAULT NULL AFTER bedarf_id'
    ];

    public function safeUp(): bool
    {
        foreach (self::COLUMNS as $column => $type) {
            $this->addColumn(self::TABLE, $column, $type);
        }

        return true;
    }

    public function safeDown(): bool
    {
        foreach (self::COLUMNS as $column => $type) {
            $this->dropColumn(self::TABLE, $column);
        }

        return true;
    }
}
