<?php

declare(strict_types=1);

final class m240510_114233_create_view_kpi_advisers extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            CREATE VIEW
                kpi_advisers AS
            SELECT
                a.id adviser_id,
                TRIM(CONCAT(a.firstname, ' ', a.lastname)) adviser_name,
                u.id user_id,
                u.agency_id
            FROM
                user u
            INNER JOIN
                hauptbetreuer hb ON hb.user_id = u.id
            INNER JOIN
                user a ON a.id = hb.adviser_id
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute('DROP VIEW kpi_advisers');

        return true;
    }
}
