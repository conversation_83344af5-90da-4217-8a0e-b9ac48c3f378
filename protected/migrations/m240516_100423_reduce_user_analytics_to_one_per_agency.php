<?php

declare(strict_types=1);

final class m240516_100423_reduce_user_analytics_to_one_per_agency extends CDbMigration
{
    public function up(): bool
    {
        $this->addColumn('kpi_user_analytics', 'agency_id', 'INT(11) UNSIGNED');

        $this->execute(<<<EOSQL
            UPDATE
                kpi_user_analytics
            JOIN
                user ON user.id = user_id
            SET
                kpi_user_analytics.agency_id = user.agency_id
            EOSQL);

        // keep only one row per agency
        $this->execute(<<<EOSQL
            DELETE FROM
                kpi_user_analytics
            WHERE
                (user_id, capture_date) IN (
                    SELECT
                        user_id, capture_date
                    FROM (
                        SELECT
                            user_id, capture_date,
                            RANK() OVER (PARTITION BY capture_date, agency_id ORDER BY user_id) r
                        FROM
                            kpi_user_analytics
                    ) x
                    WHERE
                        r>1
                )
            EOSQL);

        $this->execute(<<<EOSQL
            ALTER TABLE kpi_user_analytics
                MODIFY agency_id INT(11) UNSIGNED NOT NULL AFTER capture_date,
                DROP PRIMARY KEY,
                ADD PRIMARY KEY (capture_date, agency_id),
                DROP user_id;
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->addColumn('kpi_user_analytics', 'user_id', 'INT(10) UNSIGNED');

        $this->execute(<<<EOSQL
            UPDATE
                kpi_user_analytics k
            SET
                user_id = (SELECT MIN(id) FROM user WHERE agency_id = k.agency_id AND user_role = 5 AND deleted = 0)
            EOSQL);

        $this->execute(<<<EOSQL
            ALTER TABLE kpi_user_analytics
                MODIFY user_id INT(10) UNSIGNED NOT NULL FIRST,
                DROP PRIMARY KEY,
                DROP agency_id,
                ADD PRIMARY KEY (capture_date, user_id)
            EOSQL);

        return true;
    }
}
