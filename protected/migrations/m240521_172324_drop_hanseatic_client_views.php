<?php

declare(strict_types=1);

final class m240521_172324_drop_hanseatic_client_views extends CDbMigration
{
    public function up(): bool
    {
        $this->execute('DROP VIEW hanseatic_client');
        $this->execute('DROP VIEW non_hanseatic_client');

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            CREATE VIEW `hanseatic_client` AS SELECT
                `client`.`id` AS `id`,
                `client`.`firstname` AS `firstname`,
                `client`.`lastname` AS `lastname`,
                `client`.`birthday` AS `birthday`,
                `client`.`user_id` AS `user_id`,
                `client`.`deleted` AS `deleted`,
                `client`.`duplicate_parent_client_id` AS `duplicate_parent_client_id`
            FROM
                `client`
            WHERE
                `client`.`user_id` IN (38,39)
            EOSQL);

        $this->execute(<<<EOSQL
            CREATE VIEW `non_hanseatic_client` AS SELECT
                `client`.`id` AS `id`,
                `client`.`firstname` AS `firstname`,
                `client`.`lastname` AS `lastname`,
                `client`.`birthday` AS `birthday`,
                `client`.`user_id` AS `user_id`,
                `client`.`deleted` AS `deleted`,
                `client`.`duplicate_parent_client_id` AS `duplicate_parent_client_id`
            FROM
                `client`
            WHERE
                !(`client`.`user_id` IN (SELECT `user`.`id` FROM `user` WHERE `user`.`agency_id` = 3))
            EOSQL);

        return true;
    }
}
