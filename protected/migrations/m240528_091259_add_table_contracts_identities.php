<?php

declare(strict_types=1);

final class m240528_091259_add_table_contracts_identities extends CreateTableMigration
{
    public $tableName = 'contracts_identities';

    public function getColumns()
    {
        return [
            'id'                    => 'pk',
            'contract_id'           => 'int unsigned not null',
            'antragsnummer'         => 'varchar(255) null',
            'vorgangsnummer'        => 'varchar(255) null',
            'interne_antragsnummer' => 'varchar(255) null',
        ];
    }

    public function addForeignKeys()
    {
        $this->addOwnFk('contracts', 'contract_id', 'contracts', 'CASCADE', 'CASCADE');
        $this->createIndex('idx_contracts_identities_contract_id', 'contracts_identities', 'contract_id', true);
    }
}
