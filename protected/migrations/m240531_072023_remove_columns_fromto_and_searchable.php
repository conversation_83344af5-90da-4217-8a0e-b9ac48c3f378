<?php

declare(strict_types=1);

final class m240531_072023_remove_columns_fromto_and_searchable extends CDbMigration
{
    private const TABLE_NAME = 'product_combo_basic_field_assignment';

    public function up(): bool
    {
        $this->dropColumn(self::TABLE_NAME, 'from_to');
        $this->dropColumn(self::TABLE_NAME, 'searchable');

        return true;
    }

    public function down(): bool
    {
        $this->addColumn(self::TABLE_NAME, 'from_to', 'int(11) DEFAULT 0');
        $this->addColumn(self::TABLE_NAME, 'searchable', 'int(11) DEFAULT 0');

        return true;
    }
}
