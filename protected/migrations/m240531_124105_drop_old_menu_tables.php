<?php

declare(strict_types=1);

final class m240531_124105_drop_old_menu_tables extends CDbMigration
{
    public function up(): bool
    {
        $this->dropTable('menu_item');
        $this->dropTable('menu');

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            CREATE TABLE `menu` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `scope` int(11) NOT NULL,
                `title` varchar(45) DEFAULT NULL,
                `sort` int(11) DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `index2` (`scope`)
            )
            EOSQL);

        $this->execute(<<<EOSQL
            CREATE TABLE `menu_item` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `menu_id` int(11) NOT NULL,
                `name` varchar(64) NOT NULL,
                `type` int(11) DEFAULT NULL,
                `url` varchar(45) NOT NULL,
                `sort` int(11) DEFAULT NULL,
                PRIMARY KEY (`id`),
                <PERSON><PERSON>Y `fk_menu_item_1` (`menu_id`),
                CONSTRAINT `menu_item_ibfk_2` FOREIGN KEY (`menu_id`) REFERENCES `menu` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            )
            EOSQL);

        return true;
    }
}
