<?php

declare(strict_types=1);

use Courtageerfassung\Fks\Models\FksFtpConnection;

final class m240603_065403_add_column_transfer_stammdaten_to_ab_fks_ftp_connections extends CDbMigration
{
    private const TABLE           = 'ab_fks_ftp_connections';
    private const COLUMN_SETTING  = 'transfer_stammdaten';
    private const COLUMN_LAST_RUN = 'transfer_stammdaten_last_run';

    public function up(): bool
    {
        $this->addColumn(
            self::TABLE,
            self::COLUMN_SETTING,
            'VARCHAR(255) DEFAULT "' . FksFtpConnection::TRANSFER_STAMMDATEN_DISABLED . '"',
        );

        $this->addColumn(
            self::TABLE,
            self::COLUMN_LAST_RUN,
            'DATETIME',
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN_SETTING);
        $this->dropColumn(self::TABLE, self::COLUMN_LAST_RUN);

        return true;
    }
}
