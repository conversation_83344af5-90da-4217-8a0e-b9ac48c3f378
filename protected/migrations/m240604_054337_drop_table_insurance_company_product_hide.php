<?php

declare(strict_types=1);

final class m240604_054337_drop_table_insurance_company_product_hide extends CDbMigration
{
    public function up(): bool
    {
        $this->dropTable('insurance_company_product_hide');

        return true;
    }

    public function down(): bool
    {
        $this->createTable('insurance_company_product_hide', [
            'id'                        => 'pk',
            'agency_id'                 => 'integer unsigned not null',
            'product_combo_id'          => 'integer unsigned not null',
            'insurance_company_data_id' => 'integer unsigned not null',
        ]);

        $this->addForeignKey(
            'insurance_company_product_hide_insurance_company_data',
            'insurance_company_product_hide',
            'insurance_company_data_id',
            'insurance_company_data',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'insurance_company_product_hide_product_combo',
            'insurance_company_product_hide',
            'product_combo_id',
            'product_combo',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'insurance_company_product_hide_user',
            'insurance_company_product_hide',
            'agency_id',
            'agency',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }
}
