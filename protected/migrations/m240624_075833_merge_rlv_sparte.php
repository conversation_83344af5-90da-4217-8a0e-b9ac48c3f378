<?php

declare(strict_types=1);

use Bestandsuebertragung\Cache\Db\BuUserDistribution;
use Bestandsuebertragung\Qualitypool\Log\BafinSparteLog;
use Inhaltsseiten\Models\BesonderheitenFile;
use Tarifierung\Models\Discount;
use Tarifierung\Models\ListCard;
use Vertragsdaten\Models\VertragsdatenTestingResults;
use Vertragsdaten\Models\VertragsdatenZielgroessen;

final class m240624_075833_merge_rlv_sparte extends CDbMigration
{
    public function up(): bool
    {
        Yii::import('bestandsuebertragung.models.*');
        Yii::import('bestandsuebertragung.common.*');
        Yii::import('bestandsuebertragung.common.traits.*');
        Yii::import('beratungsdoku.modules.backend.models.*');

        /*
         * This migration fixes a situation in the database were we have two third level Sparte which points to
         * the same base Sparte; this makes it selectable for Users but not for Admins.
         *  “Vorsorge->Risikolebensversicherung->Todesfallleistung steigend - RLV” (ID 563)
         *  “Vorsorge->Risikolebensversicherung->Todesfallleistung steigend” (ID 565)
         * The solution is to keep the ID with 563 and to change the Contracts with the ID 565 to 563 and then delete
         * the entry with ID 565. (at least in the live system)
         */
        $tlvName    = 'Vorsorge->Risikolebensversicherung->Todesfallleistung steigend';
        $tlvRlvName = 'Vorsorge->Risikolebensversicherung->Todesfallleistung steigend - RLV';

        $tlv    = ProductCombo::model()->findByAttributes(['name' => $tlvName]);
        $tlvRlv = ProductCombo::model()->findByAttributes(['name' => $tlvRlvName]);

        if ($tlv === null || $tlvRlv === null) {
            /* skip if missing either entry */
            return true;
        }

        $tlvContracts = Contracts::model()->findAllByAttributes(['specific_product_id' => $tlv->id]);

        /*
        fix contract product_id's and rename instances of the old name to the new name
        NOTE: There seems to be some specific_product_names which don't conform to the naming convention of the
              `specific_product_id`s, so we'll just leave them be in case they were manually set as such
        */
        foreach ($tlvContracts as $contract) {
            $contract->specific_product_id = $tlvRlv->id;
            if ($contract->specific_product_name == $tlvName) {
                $contract->specific_product_name = $tlvRlvName;
            }

            $contract->save();
        }

        /* replace all instances of the old tlv ID used as foreign keys */

        $models = [
            AgencySettingsProducts::model(),
            BafinSparteLog::model(),
            Webreport::model(),
            BesonderheitenFile::model(),
            BrokerIdSparten::model(),
            BuUserDistribution::model(),
            ClientFiles::model(),
            ClientTargetGroupProductComboAssignment::model(),
            ContractFieldDeleted::model(),
            CourtageData::model(),
            Criteria::model(),
            DateTerminateContract::model(),
            Discount::model(),
            ExcludedProduct::model(),
            InsuranceCompanyFiles::model(),
            InsuranceCompanyLink::model(),
            InsuranceCompanyProduct::model(),
            ListCard::model(),
            MailDemvTemplate::model(),
            MailUserTemplate::model(),
            MenuProductAssignment::model(),
            PoolCompanyProduct::model(),
            PortfolioContract::model(),
            SmartinsurtecProductMapping::model(),
            Tarif::model(),
            Textmodule::model(),
            TrailerCommission::model(),
            UserFiles::model(),
            UserMail::model(),
            UserProductFocus::model(),
            UserWriting::model(),
            VertragsdatenTestingResults::model(),
            VertragsdatenZielgroessen::model(),
            WritingTemplate::model()
        ];

        foreach ($models as $model) {
            $model->updateAll(
                ['product_combo_id' => $tlvRlv->id],
                'product_combo_id = :product_combo_id',
                ['product_combo_id' => $tlv->id]
            );
        }

        // remove the old ProductCombo entry
        $tlv->delete();

        return true;
    }

    public function down(): bool
    {
        echo "m240624_075833_merge_rlv_sparte_563 does not support migration down.\n";

        return true;
    }
}
