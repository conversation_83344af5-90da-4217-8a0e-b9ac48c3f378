<?php

declare(strict_types=1);

final class m240628_083011_risk_address_fields_not_null extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE contracts_risk_address
                MODIFY `street` varchar(255) NOT NULL DEFAULT '',
                MODIFY `street_number` varchar(255) NOT NULL DEFAULT '',
                MODIFY `postcode` varchar(255) NOT NULL DEFAULT '',
                MODIFY `city` varchar(255) NOT NULL DEFAULT '';
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE contracts_risk_address
                MODIFY `street` varchar(255) DEFAULT NULL,
                MODIFY `street_number` varchar(255) DEFAULT NULL,
                MODIFY `postcode` varchar(255) DEFAULT NULL,
                MODIFY `city` varchar(255) DEFAULT NULL;
            EOSQL);

        return true;
    }
}
