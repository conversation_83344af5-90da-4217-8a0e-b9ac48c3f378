<?php

declare(strict_types=1);

final class m240703_111728_update_depositary_fondsfinanz extends CDbMigration
{
    private const TABLE          = 'contracts';
    private const DEPOSITARY_NEW = 'Fonds Finanz Maklerservice GmbH';
    private const DEPOSITARY_OLD = 'Fonds Finanz';

    public function up(): bool
    {
        $this->update(
            self::TABLE,
            ['verwahrstelle' => self::DEPOSITARY_NEW],
            'verwahrstelle = "' . self::DEPOSITARY_OLD . '"'
        );

        return true;
    }

    public function down(): bool
    {
        echo "m240703_111728_update_depositary_fondsfinanz does not support migration down.\n";

        return true;
    }
}
