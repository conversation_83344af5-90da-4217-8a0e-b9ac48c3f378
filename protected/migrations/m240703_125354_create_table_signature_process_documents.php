<?php

declare(strict_types=1);

final class m240703_125354_create_table_signature_process_documents extends CDbMigration
{
    private const TABLE_NAME = 'signature_process_documents';

    public function up(): bool
    {
        $this->createTable(self::TABLE_NAME, [
            'id'                    => 'pk',
            'name'                  => 'string',
            'sign_id'               => 'string not null',
            'url_download'          => 'string not null',
            'signature_process_id'  => 'int not null',
            'signed_client_file_id' => 'int unsigned',
            'orig_client_file_id'   => 'int unsigned'
        ]);

        $this->addForeignKey('fk_client_sign_upload_sign_id', self::TABLE_NAME, 'signature_process_id', 'client_sign_upload', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_client_files_signed_client_file_id', self::TABLE_NAME, 'signed_client_file_id', 'client_files', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_client_files_orig_client_file_id', self::TABLE_NAME, 'orig_client_file_id', 'client_files', 'id', 'SET NULL', 'SET NULL');

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey('fk_client_sign_upload_sign_id', self::TABLE_NAME);
        $this->dropForeignKey('fk_client_files_signed_client_file_id', self::TABLE_NAME);
        $this->dropForeignKey('fk_client_files_orig_client_file_id', self::TABLE_NAME);

        $this->dropTable(self::TABLE_NAME);

        return true;
    }
}
