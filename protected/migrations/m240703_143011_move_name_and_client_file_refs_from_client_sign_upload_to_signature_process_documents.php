<?php

declare(strict_types=1);

final class m240703_143011_move_name_and_client_file_refs_from_client_sign_upload_to_signature_process_documents extends CDbMigration
{
    public function safeUp()
    {
        $this->execute(<<<EOSQL
            INSERT INTO signature_process_documents (signature_process_id, name, signed_client_file_id, orig_client_file_id, url_download, sign_id)
            SELECT
                csu.id,
                csu.name,
                csu.client_file_id,
                csu.orig_client_file_id,
                csu.url_download,
                SUBSTRING(csu.url_download, REGEXP_INSTR(csu.url_download , '[a-zA-Z0-9]{40}'), 40) as sign_id
            FROM client_sign_upload csu;
        EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->delete('signature_process_documents');

        return true;
    }
}
