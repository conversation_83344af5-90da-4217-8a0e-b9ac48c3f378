<?php

declare(strict_types=1);

final class m240705_131915_remove_signature_process_related_properties_from_client_sign_upload extends CDbMigration
{
    private const CLIENT_SIGN_UPLOAD_TABLE = 'client_sign_upload';

    private const CLIENT_FILE_COLUMN      = 'client_file_id';
    private const ORIG_CLIENT_FILE_COLUMN = 'orig_client_file_id';
    private const URL_DOWNLOAD_COLUMN     = 'url_download';
    private const NAME_COLUMN             = 'name';
    private const FK_CLIENT_FILE          = 'fk_client_sign_upload_client_file';
    private const FK_ORIG_CLIENT_FILE     = 'client_sign_upload_orig_file';

    public function up(): bool
    {
        $this->dropForeignKey(self::FK_CLIENT_FILE, self::CLIENT_SIGN_UPLOAD_TABLE);
        $this->dropForeignKey(self::FK_ORIG_CLIENT_FILE, self::CLIENT_SIGN_UPLOAD_TABLE);

        $this->dropColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::CLIENT_FILE_COLUMN);
        $this->dropColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::ORIG_CLIENT_FILE_COLUMN);
        $this->dropColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::URL_DOWNLOAD_COLUMN);
        $this->dropColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::NAME_COLUMN);

        return true;
    }

    public function safeDown()
    {
        $this->addColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::NAME_COLUMN, 'string AFTER client_id');
        $this->addColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::CLIENT_FILE_COLUMN, 'int unsigned AFTER name');
        $this->addColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::URL_DOWNLOAD_COLUMN, 'string AFTER url_edit');
        $this->addColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::ORIG_CLIENT_FILE_COLUMN, 'int unsigned AFTER context');

        $this->addForeignKey(self::FK_CLIENT_FILE, self::CLIENT_SIGN_UPLOAD_TABLE, self::CLIENT_FILE_COLUMN, 'client_files', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey(self::FK_ORIG_CLIENT_FILE, self::CLIENT_SIGN_UPLOAD_TABLE, self::ORIG_CLIENT_FILE_COLUMN, 'client_files', 'id', 'SET NULL', 'SET NULL');

        $this->execute(<<<EOSQL
            UPDATE
                client_sign_upload csu
            JOIN signature_process_documents spd ON spd.signature_process_id = csu.id
            SET
                csu.name = spd.name,
                csu.client_file_id = spd.signed_client_file_id,
                csu.orig_client_file_id = spd.orig_client_file_id,
                csu.url_download = spd.url_download
            WHERE
                spd.signature_process_id = csu.id
        EOSQL);

        $this->alterColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::URL_DOWNLOAD_COLUMN, 'string not null');
        $this->alterColumn(self::CLIENT_SIGN_UPLOAD_TABLE, self::NAME_COLUMN, 'string not null');
    }
}
