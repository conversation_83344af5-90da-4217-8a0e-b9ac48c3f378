<?php

declare(strict_types=1);

final class m240708_153427_rename_table_client_sign_upload_to_signature_process extends CDbMigration
{
    private const TABLE_NAME     = 'client_sign_upload';
    private const NEW_TABLE_NAME = 'signature_processes';

    public function up(): bool
    {
        $this->renameTable(self::TABLE_NAME, self::NEW_TABLE_NAME);

        return true;
    }

    public function down(): bool
    {
        $this->renameTable(self::NEW_TABLE_NAME, self::TABLE_NAME);

        return true;
    }
}
