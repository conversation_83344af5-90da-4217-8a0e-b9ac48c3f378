<?php

declare(strict_types=1);

final class m240723_135631_add_column_context_to_signature_process_documents extends CDbMigration
{
    public const TABLE_NAME = 'signature_process_documents';

    public function up(): bool
    {
        $this->addColumn(self::TABLE_NAME, 'context', 'string');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE_NAME, 'context');

        return true;
    }
}
