<?php

declare(strict_types=1);

final class m240723_142236_move_context_from_signature_processes_to_signature_process_documents extends CDbMigration
{
    private const TABLE_NAME  = 'signature_process_documents';
    private const COLUMN      = 'context';

    public function safeUp(): bool
    {
        $this->execute(<<<EOSQL
            UPDATE
                signature_process_documents spd
            JOIN signature_processes sp ON
                sp.id = spd.signature_process_id
            SET
                spd.context = sp.context
            WHERE
                sp.id = spd.signature_process_id
        EOSQL);

        $this->alterColumn(self::TABLE_NAME, self::COLUMN, 'string not null');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE_NAME, self::COLUMN, 'string');
        $this->update(self::TABLE_NAME, [self::COLUMN => null]);

        return true;
    }
}
