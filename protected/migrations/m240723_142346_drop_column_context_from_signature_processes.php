<?php

declare(strict_types=1);

final class m240723_142346_drop_column_context_from_signature_processes extends CDbMigration
{
    private const TABLE_NAME = 'signature_processes';
    private const COLUMN     = 'context';

    public function up(): bool
    {
        $this->dropColumn(self::TABLE_NAME, self::COLUMN);

        return true;
    }

    public function safeDown(): bool
    {
        $this->addColumn(self::TABLE_NAME, self::COLUMN, 'string');

        $this->execute(<<<EOSQL
            UPDATE
                signature_processes sp
            JOIN signature_process_documents spd ON
                sp.id = spd.signature_process_id
            SET
                sp.context = spd.context
            WHERE
                sp.id = spd.signature_process_id
        EOSQL);

        $this->alterColumn(self::TABLE_NAME, self::COLUMN, 'string not null');

        return true;
    }
}
