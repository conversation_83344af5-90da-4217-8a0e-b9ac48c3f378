<?php

declare(strict_types=1);

final class m240726_153716_contracts_add_current_status extends CDbMigration
{
    public function up(): bool
    {
        $this->addColumn(
            'contracts',
            'current_status',
            'TINYINT AS (COALESCE(IF(contracts.status_date > CURDATE(), contracts.old_status, NULL), contracts.status)) VIRTUAL');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn('contracts', 'current_status');

        return true;
    }
}
