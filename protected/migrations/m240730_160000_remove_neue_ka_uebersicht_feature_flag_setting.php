<?php

declare(strict_types=1);

class m240730_160000_remove_neue_ka_uebersicht_feature_flag_setting extends CDbMigration
{
    private const TABLE = 'system_settings';

    public function up(): bool
    {
        $this->delete(
            self::TABLE,
            sprintf('category = "allgemein" AND slug = "%s"', 'client-einstieg')
        );

        return true;
    }

    public function down(): bool
    {
        $this->insertSetting('client-einstieg', 'Neue Kundenübersicht - Kundenakte');

        return true;
    }

    private function insertSetting(string $slug, string $bezeichnung): void
    {
        $settings = Setting::model()->findByAttributes(
            [
                'slug'     => $slug,
                'category' => 'allgemein',
            ]
        );

        if ($settings !== null) {
            return;
        }

        $this->insert(
            self::TABLE,
            [
                'category' => 'allgemein',
                'name'     => $bezeichnung,
                'slug'     => $slug,
                'value'    => 0,
            ]
        );
    }
}
