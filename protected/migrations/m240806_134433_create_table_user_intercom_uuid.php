<?php

declare(strict_types=1);

final class m240806_134433_create_table_user_intercom_uuid extends CDbMigration
{
    private const TABLE   = 'user_intercom';
    private const FK_USER = 'fk_user_intercom_user';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'user_id'     => 'int unsigned PRIMARY KEY',
                'id_intercom' => 'char(24) NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_USER,
            self::TABLE,
            'user_id',
            'user',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
