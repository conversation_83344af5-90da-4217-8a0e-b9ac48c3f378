<?php

declare(strict_types=1);

final class m240829_145000_add_job_status_extend_bundeswehr_and_beamter extends CDbMigration
{
    public function up(): void
    {
        $this->setDbConnection(Yii::app()->common_data);

        $this->insert(JobStatus::model()->tableName(), [
            'id'   => '27',
            'name' => 'Zeitsoldat',
        ]);

        $this->insert(JobStatus::model()->tableName(), [
            'id'   => '28',
            'name' => 'Berufssoldat',
        ]);

        $this->insert(JobStatus::model()->tableName(), [
            'id'   => '29',
            'name' => 'Beamter auf Widerruf',
        ]);

        $this->insert(JobStatus::model()->tableName(), [
            'id'   => '30',
            'name' => 'Beamter auf Probe',
        ]);

        $this->insert(JobStatus::model()->tableName(), [
            'id'   => '31',
            'name' => '<PERSON><PERSON><PERSON> auf Zeit',
        ]);
    }

    public function down(): bool
    {
        $this->setDbConnection(Yii::app()->common_data);

        $this->delete(JobStatus::model()->tableName(), 'id IN (27, 28, 29, 30, 31)');

        return true;
    }
}
