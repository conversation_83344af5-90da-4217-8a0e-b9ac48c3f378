<?php

declare(strict_types=1);

final class m240906_155316_contracts_add_internal_remark extends CDbMigration
{
    private const COLUMN = 'internal_remark';
    private const TABLE  = 'contracts';
    private const INDEX  = 'index_last_change';

    public function up(): bool
    {
        if ($this->columnExists()) {
            return true;
        }

        /**
         * we have performance issues because of DEFAULT alter_algorithm
         * without index index_last_change we can change alter_algorithm to INSTANT
         */

        $this->dropIndex(self::INDEX, self::TABLE);
        $this->execute("SET SESSION alter_algorithm='INSTANT'");

        try {
            $this->addColumn(
                self::TABLE,
                self::COLUMN,
                'MEDIUMTEXT'
            );
        } finally {
            $this->execute("SET SESSION alter_algorithm='DEFAULT'");
            $this->createIndex(self::INDEX, self::TABLE, 'last_change');
        }

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::INDEX, self::TABLE);
        $this->execute("SET SESSION alter_algorithm='INSTANT'");

        try {
            $this->dropColumn(self::TABLE, self::COLUMN);
        } finally {
            $this->execute("SET SESSION alter_algorithm='DEFAULT'");
            $this->createIndex(self::INDEX, self::TABLE, 'last_change');
        }

        return true;
    }

    private function columnExists(): bool
    {
        $db     = $this->getDbConnection();
        $result = $db->createCommand('show columns from contracts where Field = "' . self::COLUMN . '";')->queryAll();

        return !empty($result);
    }
}
