<?php

declare(strict_types=1);

final class m240909_143201_cleanup_user_id_in_tables_client_and_contracts extends CDbMigration
{
    public function up(): bool
    {
        $sql = <<<SQL
                    UPDATE contracts c
                    JOIN client cl on c.client_id = cl.id
                    SET c.user_id = cl.user_id
                    WHERE c.user_id <> cl.user_id
                SQL;

        $this->execute($sql);

        return true;
    }

    public function down(): bool
    {
        echo "m240909_143201_cleanup_user_id_in_tables_client_and_contracts does not support migration down.\n";

        return true;
    }
}
