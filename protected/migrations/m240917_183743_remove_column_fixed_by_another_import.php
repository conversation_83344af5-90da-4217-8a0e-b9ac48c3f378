<?php

declare(strict_types=1);

final class m240917_183743_remove_column_fixed_by_another_import extends CDbMigration
{
    private const TABLE  = 'vertragsservice_tracking';
    private const COLUMN = 'fixed_by_another_import';

    public function up(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }

    public function down(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'tinyint(1) NOT NULL DEFAULT 0');

        return true;
    }
}
