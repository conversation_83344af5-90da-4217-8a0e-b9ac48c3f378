<?php

declare(strict_types=1);

final class m240920_062552_create_evbs_table extends CDbMigration
{
    private const EVB_TABLE               = 'rechner__evb_logs';
    private const USER_TABLE              = 'user';
    private const CLIENT_TABLE            = 'client';
    private const INSURANCE_COMPANY_TABLE = 'insurance_company';
    private const FK_USER_ID              = 'fk_evbs_user_id';
    private const FK_CLIENT_ID            = 'fk_evbs_client_id';
    private const FK_INSURANCE_COMPANY_ID = 'fk_evbs_insurance_company_id';

    public function up(): bool
    {
        $this->createTable(
            self::EVB_TABLE,
            [
                'id'                   => 'pk',
                'evb_nummer'           => 'varchar(8) NOT NULL',
                'sparte'               => 'varchar(8) NOT NULL',
                'wkz'                  => 'varchar(4) NOT NULL',
                'insurance_company_id' => 'int UNSIGNED NOT NULL',
                'user_id'              => 'int UNSIGNED NOT NULL',
                'client_id'            => 'int UNSIGNED',
                'vn_anrede'            => 'int UNSIGNED NOT NULL',
                'vn_vorname'           => 'varchar(256)',
                'vn_nachname'          => 'varchar(256) NOT NULL',
                'vn_geburtsdatum'      => 'date',
                'vn_strasse'           => 'varchar(256) NOT NULL',
                'vn_hausnummer'        => 'varchar(8) NOT NULL',
                'vn_plz'               => 'varchar(8) NOT NULL',
                'vn_ort'               => 'varchar(256) NOT NULL',
                'vermittlernummer'     => 'varchar(32)',
                'vertriebsweg'         => "enum('fondsfinanz', 'quality', 'direktvereinbarung', 'erstantrag') NOT NULL",
                'vorlaeufige_deckung'  => "enum('haftpflicht', 'teilkasko', 'vollkasko')",
                'created_at'           => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
            ]
        );

        $this->addForeignKey(self::FK_USER_ID, self::EVB_TABLE, 'user_id', self::USER_TABLE, 'id');
        $this->addForeignKey(self::FK_CLIENT_ID, self::EVB_TABLE, 'client_id', self::CLIENT_TABLE, 'id');
        $this->addForeignKey(self::FK_INSURANCE_COMPANY_ID, self::EVB_TABLE, 'insurance_company_id', self::INSURANCE_COMPANY_TABLE, 'id');

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_INSURANCE_COMPANY_ID, self::EVB_TABLE);
        $this->dropForeignKey(self::FK_CLIENT_ID, self::EVB_TABLE);
        $this->dropForeignKey(self::FK_USER_ID, self::EVB_TABLE);

        $this->dropTable(self::EVB_TABLE);

        return true;
    }
}
