<?php

declare(strict_types=1);

final class m240923_152231_kpi_refine_brokerid_stats extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                kpi_user_analytics
            CHANGE
                count_brokerid_verkettet count_antragsnummern_verkettet SMALLINT UNSIGNED NOT NULL,
            ADD
                count_bestandsnummern_verkettet SMALLINT UNSIGNED NOT NULL AFTER count_antragsnummern_verkettet
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                kpi_user_analytics
            CHANGE
                count_antragsnummern_verkettet count_brokerid_verkettet SMALLINT UNSIGNED NOT NULL,
            DROP
                count_bestandsnummern_verkettet
            EOSQL);

        return true;
    }
}
