<?php

declare(strict_types=1);

final class m240927_074322_add_index_on_gdv_product_assignment_product_combo_assignment_gdv_product_id extends CDbMigration
{
    private const TABLENAME = 'gdv_product_assignment_product_combo_assignment';
    private const INDEXNAME = 'gdv_product_id_idx';
    private const COLUMN    = 'gdv_product_id';

    public function up(): bool
    {
        $this->createIndex(self::INDEXNAME, self::TABLENAME, self::COLUMN);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::INDEXNAME, self::TABLENAME);

        return true;
    }
}
