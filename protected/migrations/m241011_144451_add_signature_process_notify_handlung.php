<?php

class m241011_144451_add_signature_process_notify_handlung extends CDbMigration
{
    private const TABLE   = 'signature_processes';
    private const COLUMN  = 'notify_handlung';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'bool DEFAULT 0');

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
