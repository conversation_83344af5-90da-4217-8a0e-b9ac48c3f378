<?php

class m241011_154451_add_signature_process_handlung_id extends CDbMigration
{
    private const TABLE   = 'signature_processes';
    private const COLUMN  = 'handlung_id';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'varchar(255) DEFAULT NULL');

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
