<?php

declare(strict_types=1);

final class m241015_084349_add_indexes_to_table_contracts_identities extends CDbMigration
{
    private const TABLENAME                  = 'contracts_identities';
    private const IDX_VORGANGSNUMMER_NAME    = 'idx_contracts_identities_vorgangsnummer';
    private const IDX_INT_ANTRAGSNUMMER_NAME = 'idx_contracts_identities_interne_antragsnummer';

    public function up(): bool
    {
        $this->createIndex(
            self::IDX_VORGANGSNUMMER_NAME,
            self::TABLENAME,
            'vorgangsnummer',
        );

        $this->createIndex(
            self::IDX_INT_ANTRAGSNUMMER_NAME,
            self::TABLENAME,
            'interne_antragsnummer',
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::IDX_VORGANGSNUMMER_NAME, self::TABLENAME);
        $this->dropIndex(self::IDX_INT_ANTRAGSNUMMER_NAME, self::TABLENAME);

        return true;
    }
}
