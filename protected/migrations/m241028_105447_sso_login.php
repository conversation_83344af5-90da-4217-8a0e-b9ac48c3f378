<?php

declare(strict_types=1);

final class m241028_105447_sso_login extends CDbMigration
{
    public function up(): bool
    {
        $this->createTable('sso_login', [
            'user_id'     => 'int unsigned',
            'provider'    => 'varchar(32)',
            'external_id' => 'varchar(128)',
        ]);

        $this->addPrimaryKey('PRIMARY', 'sso_login', ['user_id', 'provider']);
        $this->createIndex('provider_external_id', 'sso_login', ['provider', 'external_id'], true);
        $this->addForeignKey('fk_sso_login_user_id', 'sso_login', 'user_id', 'user', 'id', 'CASCADE', 'CASCADE');

        return true;
    }

    public function down(): bool
    {
        $this->dropTable('sso_login');

        return true;
    }
}
