<?php

class m241103_000001_drop_user_db_auth_tokens_table extends CDbMigration
{
    public function up()
    {
        $this->dropTable('authtoken_user');
    }

    public function down()
    {
        $this->createTable('authtoken_user', [
            'id'          => 'pk',
            'user_id'     => 'int unsigned NOT NULL',
            'service_id'  => 'string NOT NULL',
            'token'       => 'VARBINARY(256) NOT NULL UNIQUE',
            'lookup_hash' => 'BIGINT NOT NULL',
            'created_at'  => 'datetime NOT NULL',
        ]);

        $this->createIndex('idx_user_service', 'authtoken_user', ['user_id', 'service_id']);
        $this->createIndex('idx_lookup_hash', 'authtoken_user', 'lookup_hash');

        $this->addForeignKey('fk_authtoken_user_user', 'authtoken_user', 'user_id', 'user', 'id');
    }
}
