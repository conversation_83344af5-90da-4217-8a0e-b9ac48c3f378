<?php

declare(strict_types=1);

final class m241113_102003_kpi_user_add_status_data extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            CREATE OR REPLACE VIEW kpi_user AS SELECT
                u.id,
                agency_id,
                us.name as status,
                status_last_edit,
                (
                    SELECT us.name
                    FROM user_status_log usl
                    JOIN user_status us ON us.id = old_status
                    WHERE user_id = u.id
                    ORDER BY usl.create_datetime DESC, usl.id DESC
                    LIMIT 1
                ) previous_status,
                ur.name as role,
                created_timestamp,
                duplicate_parent_id,
                deleted,
                delete_timestamp,
                parent_id
            FROM user u
            JOIN user_status us ON us.id = u.user_status
            JOIN user_role ur ON ur.id = u.user_role
            WHERE
                user_role NOT IN (2, 8)
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            CREATE OR REPLACE VIEW kpi_user AS SELECT
                u.id,
                agency_id,
                us.name as status,
                ur.name as role,
                created_timestamp,
                duplicate_parent_id,
                deleted,
                delete_timestamp,
                parent_id
            FROM user u
            JOIN user_status us ON us.id = u.user_status
            JOIN user_role ur ON ur.id = u.user_role
            WHERE
                user_role NOT IN (2, 8)
            EOSQL);

        return true;
    }
}
