<?php

declare(strict_types=1);

final class m241113_110107_kpi_vertraege_add_edit_import_timestamps extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            CREATE OR REPLACE VIEW kpi_vertraege AS
            SELECT
                contracts.id,
                contracts.type_id,
                contracts.date,
                contracts.due_date,
                contracts.company_id,
                insurance_company.name AS gesellschaft,
                specific_product_id,
                CASE product_combo.level1_id
                    WHEN 121 THEN 'LV'
                    WHEN 147 THEN 'KV'
                    WHEN 156 THEN 'SACH'
                    WHEN 164 THEN 'SACH'
                    WHEN 173 THEN 'DEPOT'
                    ELSE 'SONSTIGE'
                END AS sparte,
                CASE import_type_id
                    WHEN 3 THEN 'auto'
                    ELSE 'manuell'
                END AS import_type,
                vermittlernummer,
                verwahrstelle,
                contracts.create_datetime,
                contracts.last_edit_timestamp,
                contracts.import_datetime,
                cl.id as client_id,
                cl.user_id as user_id,
                contracts.status,
                contracts.old_status,
                contracts.current_status,
                contracts.status_date,
                contracts.net_fee,
                contracts.gross_fee,
                contracts.payment_type,
                (
                    SELECT
                        CASE
                            WHEN insurance_company_id = 701 THEN 'ff'
                            WHEN is_pool THEN 'pool'
                            WHEN is_verkettet = 1 THEN 'dv'
                            ELSE 'unverkettet'
                        END
                    FROM
                        broker_id
                    WHERE
                        brokerid <> '' AND
                        brokerid = contracts.vermittlernummer AND
                        user_id = cl.user_id
                    LIMIT 1
                ) AS anbindung
            FROM
                contracts
            LEFT JOIN
                insurance_company ON insurance_company.id = contracts.company_id
            LEFT JOIN
                product_combo ON product_combo.id = contracts.specific_product_id
            JOIN
                client cl ON cl.id = contracts.client_id
            WHERE
                contracts.duplicate_contract_id IS NULL AND
                contracts.parentId IS NULL AND
                contracts.damage = 0 AND
                cl.duplicate_parent_client_id IS NULL AND
                cl.deleted = 0;
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            CREATE OR REPLACE VIEW kpi_vertraege AS
            SELECT
                contracts.id,
                contracts.type_id,
                contracts.date,
                contracts.due_date,
                contracts.company_id,
                insurance_company.name AS gesellschaft,
                specific_product_id,
                CASE product_combo.level1_id
                    WHEN 121 THEN 'LV'
                    WHEN 147 THEN 'KV'
                    WHEN 156 THEN 'SACH'
                    WHEN 164 THEN 'SACH'
                    WHEN 173 THEN 'DEPOT'
                    ELSE 'SONSTIGE'
                END AS sparte,
                CASE import_type_id
                    WHEN 3 THEN 'auto'
                    ELSE 'manuell'
                END AS import_type,
                vermittlernummer,
                verwahrstelle,
                contracts.create_datetime,
                cl.id as client_id,
                cl.user_id as user_id,
                contracts.status,
                contracts.old_status,
                contracts.current_status,
                contracts.status_date,
                contracts.net_fee,
                contracts.gross_fee,
                contracts.payment_type,
                (
                    SELECT
                        CASE
                            WHEN insurance_company_id = 701 THEN 'ff'
                            WHEN is_pool THEN 'pool'
                            WHEN is_verkettet = 1 THEN 'dv'
                            ELSE 'unverkettet'
                        END
                    FROM
                        broker_id
                    WHERE
                        brokerid <> '' AND
                        brokerid = contracts.vermittlernummer AND
                        user_id = cl.user_id
                    LIMIT 1
                ) AS anbindung
            FROM
                contracts
            LEFT JOIN
                insurance_company ON insurance_company.id = contracts.company_id
            LEFT JOIN
                product_combo ON product_combo.id = contracts.specific_product_id
            JOIN
                client cl ON cl.id = contracts.client_id
            WHERE
                contracts.duplicate_contract_id IS NULL AND
                contracts.parentId IS NULL AND
                contracts.damage = 0 AND
                cl.duplicate_parent_client_id IS NULL AND
                cl.deleted = 0;
            EOSQL);

        return true;
    }
}
