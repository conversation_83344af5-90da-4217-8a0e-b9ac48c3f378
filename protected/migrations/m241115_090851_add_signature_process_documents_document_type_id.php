<?php

declare(strict_types=1);

class m241115_090851_add_signature_process_documents_document_type_id extends CDbMigration
{
    private const TABLE  = 'signature_process_documents';
    private const COLUMN = 'document_type_id';

    public function up()
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int unsigned', );

        return true;
    }

    public function down()
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
