<?php

declare(strict_types=1);

final class m241125_113846_drop_basic_auth extends CDbMigration
{
    public function up(): bool
    {
        $this->dropTable('basic_auth');

        return true;
    }

    public function down(): bool
    {
        $this->createTable('basic_auth', [
            'id'       => 'pk',
            'name'     => 'string NOT NULL',
            'username' => 'string NOT NULL',
            'password' => 'string NOT NULL',
        ]);

        $this->createIndex('name', 'basic_auth', 'name', true);

        return true;
    }
}
