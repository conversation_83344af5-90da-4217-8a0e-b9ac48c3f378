<?php

declare(strict_types=1);

final class m241126_090132_rename_client_beratungslink_to_client_webreports extends CDbMigration
{
    private const OLD_TABLE_NAME = 'client_beratungslink';
    private const NEW_TABLE_NAME = 'client_webreports';

    public function safeUp(): bool
    {
        $this->renameTable(self::OLD_TABLE_NAME, self::NEW_TABLE_NAME);

        $this->dropForeignKey('fk_client_beratungslink_client', self::NEW_TABLE_NAME);
        $this->addForeignKey('fk_client_webreports_client', self::NEW_TABLE_NAME, 'client_id', 'client', 'id', 'CASCADE', 'CASCADE');

        return true;
    }

    public function safeDown(): bool
    {
        $this->renameTable(self::NEW_TABLE_NAME, self::OLD_TABLE_NAME);

        $this->dropForeign<PERSON>ey('fk_client_webreports_client', self::OLD_TABLE_NAME);
        $this->addForeign<PERSON>ey('fk_client_beratungslink_client', self::OLD_TABLE_NAME, 'client_id', 'client', 'id', 'CASCADE', 'CASCADE');

        return true;
    }
}
