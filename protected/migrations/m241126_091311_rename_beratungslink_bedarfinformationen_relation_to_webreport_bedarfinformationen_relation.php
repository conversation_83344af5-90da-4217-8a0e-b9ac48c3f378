<?php

declare(strict_types=1);

final class m241126_091311_rename_beratungslink_bedarfinformationen_relation_to_webreport_bedarfinformationen_relation extends CDbMigration
{
    private const OLD_TABLE_NAME = 'beratungslink_bedarfinformation_relation';
    private const NEW_TABLE_NAME = 'webreport_bedarfinformation_relation';

    private const OLD_COLUMN_NAME = 'beratungslink_id';
    private const NEW_COLUMN_NAME = 'webreport_id';

    public function safeUp(): bool
    {
        $this->renameTable(self::OLD_TABLE_NAME, self::NEW_TABLE_NAME);

        $this->dropForeignKey('fk_beratungslink_bedarfinformation_relation_beratungslink', self::NEW_TABLE_NAME);
        $this->dropForeignKey('fk_beratungslink_bedarfinformation_relation_config', self::NEW_TABLE_NAME);

        $this->renameColumn(self::NEW_TABLE_NAME, self::OLD_COLUMN_NAME, self::NEW_COLUMN_NAME);

        $this->addForeignKey('fk_webreport_bedarfinformation_relation_webreport', self::NEW_TABLE_NAME, self::NEW_COLUMN_NAME, 'client_webreports', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_webreport_bedarfinformation_relation_config', self::NEW_TABLE_NAME, 'config_id', 'bedarf_informationen', 'id', 'CASCADE', 'CASCADE');

        return true;
    }

    public function safeDown(): bool
    {
        $this->dropForeignKey('fk_webreport_bedarfinformation_relation_webreport', self::NEW_TABLE_NAME);
        $this->dropForeignKey('fk_webreport_bedarfinformation_relation_config', self::NEW_TABLE_NAME);

        $this->renameColumn(self::NEW_TABLE_NAME, self::NEW_COLUMN_NAME, self::OLD_COLUMN_NAME);

        $this->addForeignKey('fk_beratungslink_bedarfinformation_relation_beratungslink', self::NEW_TABLE_NAME, self::OLD_COLUMN_NAME, 'client_webreports', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk_beratungslink_bedarfinformation_relation_config', self::NEW_TABLE_NAME, 'config_id', 'bedarf_informationen', 'id', 'CASCADE', 'CASCADE');

        $this->renameTable(self::NEW_TABLE_NAME, self::OLD_TABLE_NAME);

        return true;
    }
}
