<?php

declare(strict_types=1);

final class m241128_135146_create_antragslog_table extends CDbMigration
{
    const TABLE = 'antragslog';

    public function up(): bool
    {
        $this->createTable(self::TABLE, [
            'id'                        => 'pk',
            'user_id'                   => 'int unsigned',
            'client_id'                 => 'int unsigned',
            'antragsdatum'              => 'datetime',
            'broker_nr'                 => 'string',
            'distribution_insurance_id' => 'int unsigned',
            'insurance_company_id'      => 'int unsigned',
            'gesellschaft'              => 'string',
            'dispatch_mode'             => 'varchar(50)',
            'sparte'                    => 'varchar(100)',
            'wkz'                       => 'string',
            'tariflink'                 => 'string',
            'beginn'                    => 'string',
            'laufzeit'                  => 'int',
            'beitrag_brutto'            => 'string',
            'beitrag_netto'             => 'string',
            'zahlweise'                 => 'varchar(45)',
            'vn_anrede'                 => 'varchar(10)',
            'vn_vorname'                => 'string',
            'vn_nachname'               => 'string',
            'vn_geburtsdatum'           => 'varchar(20)',
            'vn_strasse'                => 'string',
            'vn_hausnummer'             => 'varchar(20)',
            'vn_plz'                    => 'varchar(5)',
            'vn_ort'                    => 'string',
            'request_url'               => 'string',
            'response_url'              => 'string',
        ]);

        $this->createIndex('user_id', self::TABLE, 'user_id');
        $this->createIndex('antragsdatum', self::TABLE, 'antragsdatum');
        $this->createIndex('sparte', self::TABLE, 'sparte');
        $this->createIndex('client_id', self::TABLE, 'client_id');
        $this->createIndex('distribution_insurance_id', self::TABLE, 'distribution_insurance_id');

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
