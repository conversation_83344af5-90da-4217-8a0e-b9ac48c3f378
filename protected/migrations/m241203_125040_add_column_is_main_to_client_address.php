<?php

declare(strict_types=1);

final class m241203_125040_add_column_is_main_to_client_address extends CDbMigration
{
    private const TABLE  = 'client_address';
    private const COLUMN = 'is_main';
    private const INDEX  = 'IDX_client_id_is_main';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'BOOLEAN NOT NULL DEFAULT FALSE AFTER `is_alte_hauptadresse`');
        $this->createIndex(self::INDEX, self::TABLE, ['client_id', self::COLUMN]);

        return true;
    }

    public function down(): bool
    {
        $this->dropIndex(self::INDEX, self::TABLE);
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }
}
