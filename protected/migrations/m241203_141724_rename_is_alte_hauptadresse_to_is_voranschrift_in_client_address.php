<?php

declare(strict_types=1);

final class m241203_141724_rename_is_alte_hauptadresse_to_is_voranschrift_in_client_address extends CDbMigration
{
    private const TABLE_NAME      = 'client_address';
    private const OLD_COLUMN_NAME = 'is_alte_hauptadresse';
    private const NEW_COLUMN_NAME = 'is_voranschrift';

    public function up(): bool
    {
        $this->renameColumn(self::TABLE_NAME, self::OLD_COLUMN_NAME, self::NEW_COLUMN_NAME);

        return true;
    }

    public function down(): bool
    {
        $this->renameColumn(self::TABLE_NAME, self::NEW_COLUMN_NAME, self::OLD_COLUMN_NAME);

        return true;
    }
}
