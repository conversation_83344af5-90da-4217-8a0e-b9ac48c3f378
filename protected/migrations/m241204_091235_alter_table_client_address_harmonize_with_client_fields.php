<?php

declare(strict_types=1);

final class m241204_091235_alter_table_client_address_harmonize_with_client_fields extends CDbMigration
{
    private const TABLE           = 'client_address';
    private const STREET_COLUMN   = 'street';
    private const CITY_COLUMN     = 'city';
    private const ADDITION_COLUMN = 'addition';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, self::STREET_COLUMN, 'VARCHAR(100) NULL');
        $this->alterColumn(self::TABLE, self::CITY_COLUMN, 'VARCHAR(120) NULL');
        $this->alterColumn(self::TABLE, self::ADDITION_COLUMN, 'VARCHAR(120) NULL');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::ADDITION_COLUMN, 'VARCHAR(100) NULL');
        $this->alterColumn(self::TABLE, self::CITY_COLUMN, 'VARCHAR(45) NULL');
        $this->alterColumn(self::TABLE, self::STREET_COLUMN, 'VARCHAR(45) NULL');

        return true;
    }
}
