<?php

declare(strict_types=1);

final class m241206_131827_add_columns_lat_long_state_to_client_address extends CDbMigration
{
    private const TABLE        = 'client_address';
    private const LAT_COLUMN   = 'lat';
    private const LONG_COLUMN  = 'long';
    private const STATE_COLUMN = 'state';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, self::LAT_COLUMN, 'FLOAT AFTER `country_id`');
        $this->addColumn(self::TABLE, self::LONG_COLUMN, sprintf('FLOAT AFTER %s', self::LAT_COLUMN));
        $this->addColumn(self::TABLE, self::STATE_COLUMN, 'VARCHAR(120) AFTER `city`');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, self::STATE_COLUMN);
        $this->dropColumn(self::TABLE, self::LONG_COLUMN);
        $this->dropColumn(self::TABLE, self::LAT_COLUMN);

        return true;
    }
}
