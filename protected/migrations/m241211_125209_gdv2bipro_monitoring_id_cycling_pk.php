<?php

declare(strict_types=1);

final class m241211_125209_gdv2bipro_monitoring_id_cycling_pk extends CDbMigration
{
    public function up(): bool
    {
        // Find a starting value for the sequence, we assume that there's
        // enough space either after the max id or before the min id, otherwise
        // we're f'ed, but we'll handle this manually in production anyway
        $minValue = $this->getDbConnection()->createCommand(<<<EOSQL
            SELECT
                IF(MAX(id) < 2147483647 - 10000, MAX(id) + 10000, 1)
            FROM
                gdv2bipro_monitoring
            EOSQL)->queryScalar();

        $this->execute(<<<EOSQL
            CREATE SEQUENCE seq_gdv2bipro_monitoring_id
                MINVALUE 1
                START {$minValue}
                MAXVALUE 2147483647
                CACHE 1000
                CYCLE
            EOSQL);

        $this->alterColumn('gdv2bipro_monitoring', 'id', 'int not null default nextval(seq_gdv2bipro_monitoring_id)');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn('gdv2bipro_monitoring', 'id', 'int not null auto_increment');

        $this->execute('DROP SEQUENCE IF EXISTS seq_gdv2bipro_monitoring_id');

        return true;
    }
}
