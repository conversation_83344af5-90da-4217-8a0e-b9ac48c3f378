<?php

declare(strict_types=1);

final class m241213_104640_remove_default_attribute_from_client_address extends CDbMigration
{
    private const TABLE  = 'client_address';
    private const COLUMN = 'default';

    public function up(): bool
    {
        $this->dropColumn(self::TABLE, self::COLUMN);

        return true;
    }

    public function down(): bool
    {
        $this->addColumn(self::TABLE, self::COLUMN, 'int NOT NULL');

        return true;
    }
}
