<?php

declare(strict_types=1);

final class m241224_114640_change_client_address_notice_column_to_tinyint extends CDbMigration
{
    private const TABLE  = 'client_address';
    private const COLUMN = 'notice';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(100) NULL');

        $this->execute(<<<SQL
            UPDATE client_address
            SET notice = NULL
            WHERE notice = '';
SQL
        );

        $this->alterColumn(self::TABLE, self::COLUMN, 'TINYINT NULL');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'VARCHAR(100)');

        return true;
    }
}
