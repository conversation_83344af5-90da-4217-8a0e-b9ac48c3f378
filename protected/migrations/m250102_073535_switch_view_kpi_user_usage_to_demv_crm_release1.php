<?php

declare(strict_types=1);

final class m250102_073535_switch_view_kpi_user_usage_to_demv_crm_release1 extends CDbMigration
{
    public function up(): bool
    {
        // These views don't exist in production, the migration is just there
        // to cleanup developer databases, thus the `IF EXISTS` part
        $this->execute('DROP VIEW IF EXISTS demv_crm_release1.kpi_user_usage');

        $this->execute('RENAME TABLE demv_crm_release1.antragslog TO demv_crm_release1.rechner__antrag_log');

        $this->execute(<<<EOSQL
            CREATE VIEW
                demv_crm_release1.kpi_user_usage AS
            SELECT `u`.`id` AS `user_id`,

              (SELECT DATE_FORMAT(`tl`.`create_datetime`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`testlogin` `tl`
               WHERE `tl`.`user_id` = `u`.`id`
               ORDER BY `tl`.`create_datetime`
               LIMIT 1) AS `testlogin_erstellt`,

              (SELECT `uf`.`upload_date`
               FROM `demv_crm_release1`.`user_files` `uf`
               WHERE `uf`.`user_id` = `u`.`id`
                AND `uf`.`document_type_id` = 46
               ORDER BY `uf`.`upload_date`
               LIMIT 1) AS `beitrittserklaerung_demv`,

              (SELECT DATE_FORMAT(`ci`.`start`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`custom_csv_import` `ci`
               WHERE `ci`.`user_id` = `u`.`id`
               ORDER BY `ci`.`start`
               LIMIT 1) AS `erster_csv_import`,

              (SELECT DATE_FORMAT(`cu`.`create_datetime`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`courtageumzug` `cu`
               WHERE `cu`.`created_by_user_id` = `u`.`id`
               ORDER BY `cu`.`create_datetime`
               LIMIT 1) AS `erster_courtage_umzug`,

              (SELECT DATE_FORMAT(`cr`.`request_date`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`courtage_request` `cr`
               WHERE `cr`.`user_id` = `u`.`id`
               ORDER BY `cr`.`request_date`
               LIMIT 1) AS `erste_courtage_anfrage`,

              (SELECT DATE_FORMAT(`ra`.`antragsdatum`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`rechner__antrag_log` `ra`
               WHERE `ra`.`user_id` = `u`.`id`
               ORDER BY `ra`.`antragsdatum`
               LIMIT 1) AS `erster_antrag_vergleichrechner`,

              (SELECT DATE_FORMAT(`bue`.`create_datetime`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`bu_portfolio_transfer` `bue`
               WHERE `bue`.`user_id` = `u`.`id`
               ORDER BY `bue`.`create_datetime`
               LIMIT 1) AS `erste_bue`
            FROM `demv_crm_release1`.`user` `u`
            WHERE `u`.`user_role` = 5
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        echo "m250102_073535_switch_view_kpi_user_usage_to_demv_crm_release1 does not support migration down.\n";

        $this->execute('RENAME TABLE demv_crm_release1.rechner__antrag_log TO demv_crm_release1.antragslog');

        $this->execute('DROP VIEW IF EXISTS demv_crm_release1.kpi_user_usage');

        $this->execute(<<<EOSQL
            CREATE VIEW
                demv_crm_release1.kpi_user_usage AS
            SELECT `u`.`id` AS `user_id`,

              (SELECT DATE_FORMAT(`tl`.`create_datetime`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`testlogin` `tl`
               WHERE `tl`.`user_id` = `u`.`id`
               ORDER BY `tl`.`create_datetime`
               LIMIT 1) AS `testlogin_erstellt`,

              (SELECT `uf`.`upload_date`
               FROM `demv_crm_release1`.`user_files` `uf`
               WHERE `uf`.`user_id` = `u`.`id`
                AND `uf`.`document_type_id` = 46
               ORDER BY `uf`.`upload_date`
               LIMIT 1) AS `beitrittserklaerung_demv`,

              (SELECT DATE_FORMAT(`ci`.`start`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`custom_csv_import` `ci`
               WHERE `ci`.`user_id` = `u`.`id`
               ORDER BY `ci`.`start`
               LIMIT 1) AS `erster_csv_import`,

              (SELECT DATE_FORMAT(`cu`.`create_datetime`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`courtageumzug` `cu`
               WHERE `cu`.`created_by_user_id` = `u`.`id`
               ORDER BY `cu`.`create_datetime`
               LIMIT 1) AS `erster_courtage_umzug`,

              (SELECT DATE_FORMAT(`cr`.`request_date`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`courtage_request` `cr`
               WHERE `cr`.`user_id` = `u`.`id`
               ORDER BY `cr`.`request_date`
               LIMIT 1) AS `erste_courtage_anfrage`,

              (SELECT DATE_FORMAT(`ra`.`antragsdatum`, '%Y-%m-%d')
               FROM `modules`.`rechner__antragslog` `ra`
               WHERE `ra`.`user_id` = `u`.`id`
               ORDER BY `ra`.`antragsdatum`
               LIMIT 1) AS `erster_antrag_vergleichrechner`,

              (SELECT DATE_FORMAT(`bue`.`create_datetime`, '%Y-%m-%d')
               FROM `demv_crm_release1`.`bu_portfolio_transfer` `bue`
               WHERE `bue`.`user_id` = `u`.`id`
               ORDER BY `bue`.`create_datetime`
               LIMIT 1) AS `erste_bue`
            FROM `demv_crm_release1`.`user` `u`
            WHERE `u`.`user_role` = 5
            EOSQL);

        return true;
    }
}
