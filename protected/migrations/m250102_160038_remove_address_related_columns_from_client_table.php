<?php

declare(strict_types=1);

final class m250102_160038_remove_address_related_columns_from_client_table extends CDbMigration
{
    public function up(): bool
    {
        $query = <<<SQL
        ALTER TABLE client
            DROP COLUMN `street`,
            DROP COLUMN `street_number`,
            <PERSON>OP COLUMN `zip_code`,
            DROP COLUMN `city`,
            DROP COLUMN `state`,
            DROP COLUMN `country`,
            DROP COLUMN `lat`,
            DROP COLUMN `long`,
            DROP COLUMN `address_extra`;
        SQL;

        $this->execute($query);

        return true;
    }

    public function down(): bool
    {
        $query = <<<SQL
        ALTER TABLE client
            ADD COLUMN `street`        VARCHAR(100) AFTER `companyname`,
            ADD COLUMN `street_number` VARCHAR(100) AFTER `street`,
            ADD COLUMN `zip_code`      VARCHAR(100) AFTER `street_number`,
            ADD COLUMN `city`          VARCHAR(120) AFTER `zip_code`,
            ADD COLUMN `state`         VARCHAR(120) AFTER `city`,
            ADD COLUMN `country`       INT          AFTER `state`,
            ADD COLUMN `lat`           FLOAT        AFTER `country`,
            ADD COLUMN `long`          FLOAT        AFTER `lat`,
            ADD COLUMN `address_extra` VARCHAR(120) AFTER `long`;
        SQL;

        $this->execute($query);

        return true;
    }
}
