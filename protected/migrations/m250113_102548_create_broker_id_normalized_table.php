<?php

declare(strict_types=1);

final class m250113_102548_create_broker_id_normalized_table extends CDbMigration
{
    private const BROKER_ID_NORMALIZED_TABLE = 'broker_id_normalized';
    private const BROKER_ID_TABLE            = 'broker_id';
    private const FK_BROKER_ID               = 'fk_broker_id_normalized_broker_id';

    public function up(): bool
    {
        $this->createTable(
            self::BROKER_ID_NORMALIZED_TABLE,
            [
                'broker_id_id'    => 'INT UNSIGNED NOT NULL PRIMARY KEY',
                'brokerid'        => 'VARCHAR(45) NOT NULL',
                'create_datetime' => 'DATETIME NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_BROKER_ID,
            self::BROKER_ID_NORMALIZED_TABLE,
            'broker_id_id',
            self::BROKER_ID_TABLE,
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::BROKER_ID_NORMALIZED_TABLE);

        return true;
    }
}
