<?php

declare(strict_types=1);

final class m250113_122548_create_broker_id_pattern_normalized_table extends CDbMigration
{
    private const BROKER_ID_PATTERN_NORMALIZED_TABLE = 'broker_id_pattern_normalized';
    private const INSURANCE_COMPANY_TABLE            = 'insurance_company';
    private const FK_INSURANCE_COMPANY_ID            = 'fk_broker_id_pattern_normalized_insurance_company';

    public function up(): bool
    {
        $this->createTable(
            self::BROKER_ID_PATTERN_NORMALIZED_TABLE,
            [
                'insurance_company_id' => 'INT UNSIGNED NOT NULL PRIMARY KEY',
                'regex'                => 'VARCHAR(255) NOT NULL',
                'replacement'          => 'VARCHAR(255) NOT NULL',
            ]
        );

        $this->addForeignKey(
            self::FK_INSURANCE_COMPANY_ID,
            self::BROKER_ID_PATTERN_NORMALIZED_TABLE,
            'insurance_company_id',
            self::INSURANCE_COMPANY_TABLE,
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::BROKER_ID_PATTERN_NORMALIZED_TABLE);

        return true;
    }
}
