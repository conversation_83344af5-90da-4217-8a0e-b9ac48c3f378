<?php

declare(strict_types=1);

use Business\Company\Zurich;

final class m250113_132115_create_broker_id_pattern_normalized_for_<PERSON><PERSON><PERSON> extends CDbMigration
{
    private const BROKER_ID_PATTERN_NORMALIZED_TABLE = 'broker_id_pattern_normalized';

    public function up(): bool
    {
        $this->insert(
            self::BROKER_ID_PATTERN_NORMALIZED_TABLE,
            ['insurance_company_id' => Zurich::ID, 'regex' => '/^(\d{7})\s*\/\s*(\d{6})$/', 'replacement' => '${1}'],
        );
        $this->insert(
            self::BROKER_ID_PATTERN_NORMALIZED_TABLE,
            ['insurance_company_id' => Zurich::LEBEN, 'regex' => '/^(\d{7})\s*\/\s*(\d{6})$/', 'replacement' => '${1}'],
        );

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            self::BROKER_ID_PATTERN_NORMALIZED_TABLE,
            'insurance_company_id = :icid',
            [':icid' => Zurich::ID],
        );

        $this->delete(
            self::BROKER_ID_PATTERN_NORMALIZED_TABLE,
            'insurance_company_id = :icid',
            [':icid' => Zurich::LEBEN],
        );

        return true;
    }
}
