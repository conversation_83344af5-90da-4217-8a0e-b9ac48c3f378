<?php

declare(strict_types=1);

final class m250113_132540_insert_bic_for_postbank extends CDbMigration
{
    private const TABLE = 'bankcodes';
    private const PAN   = '21396';
    private const BLZ   = '********';
    private const BIC   = 'DEUTDEDBP29';

    public function up(): bool
    {
        $this->setDbConnection(Yii::app()->common_data);
        $this->insert(
            self::TABLE,
            [
                'bankleitzahl'    => self::BLZ,
                'bezeichnung'     => 'Postbank/DSL Ndl der Deutsche Bank',
                'kurzbezeichnung' => 'Postbank',
                'pan'             => self::PAN,
                'bic'             => self::BIC,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->setDbConnection(Yii::app()->common_data);
        $this->delete(self::TABLE, 'bankleitzahl = :blz', [':blz' => self::BLZ]);

        return true;
    }
}
