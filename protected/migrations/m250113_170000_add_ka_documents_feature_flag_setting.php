<?php

class m250113_170000_add_ka_documents_feature_flag_setting extends CDbMigration
{
    private const TABLE = 'system_settings';

    public function up(): void
    {
        $this->insertSetting(Feature::KA_DOKUMENTE_NEU_ANZEIGEN, 'Dokumente-Tab - Kundenakte');
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            sprintf('category = "allgemein" AND slug = "%s"', Feature::KA_DOKUMENTE_NEU_ANZEIGEN)
        );

        return true;
    }

    private function insertSetting(string $slug, string $bezeichnung): void
    {
        $settings = Setting::model()->findByAttributes(
            [
                'slug'     => $slug,
                'category' => 'allgemein',
            ]
        );

        if ($settings !== null) {
            return;
        }

        $this->insert(
            self::TABLE,
            [
                'category' => 'allgemein',
                'name'     => $bezeichnung,
                'slug'     => $slug,
                'value'    => 0,
            ]
        );
    }
}
