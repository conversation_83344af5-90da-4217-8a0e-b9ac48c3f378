<?php

declare(strict_types=1);

final class m250115_124843_add_risikodatenerfassung_to_mail_procedure_table extends CDbMigration
{
    private const  TABLE_MAIL_PROCEDURE = 'mail_procedure';
    private const  MAIL_PROCEDURE_ID    = 167;
    private const  MAIL_PROCEDURE_NAME  = 'Risikodatenerfassung';

    public function up(): bool
    {
        $this->insert(self::TABLE_MAIL_PROCEDURE,
            [
                'id'   => self::MAIL_PROCEDURE_ID,
                'name' => self::MAIL_PROCEDURE_NAME,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->delete(self::TABLE_MAIL_PROCEDURE,
            'id = :procedureId and name = :name',
            [
                'procedureId' => self::MAIL_PROCEDURE_ID,
                'name'        => self::MAIL_PROCEDURE_NAME,
            ]
        );

        return true;
    }
}
