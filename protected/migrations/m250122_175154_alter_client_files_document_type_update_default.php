<?php

declare(strict_types=1);

final class m250122_175154_alter_client_files_document_type_update_default extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<SQL
            ALTER TABLE client_files
            ALTER document_type_id SET DEFAULT 50,         -- 50: "Sonstiges"
            ALGORITHM INSTANT
            SQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<SQL
            ALTER TABLE client_files
            ALTER document_type_id SET DEFAULT NULL,
            ALGORITHM INSTANT
            SQL);

        return true;
    }
}
