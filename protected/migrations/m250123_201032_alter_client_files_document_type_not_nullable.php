<?php

declare(strict_types=1);

final class m250123_201032_alter_client_files_document_type_not_nullable extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<SQL
            UPDATE client_files
            SET document_type_id = 50       -- 50: "Sonstiges"
            WHERE document_type_id IS NULL
            SQL);

        $this->execute(<<<SQL
            ALTER TABLE client_files
            MODIFY document_type_id INT(10) UNSIGNED DEFAULT 50 NOT NULL
            SQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<SQL
            ALTER TABLE client_files
            MODIFY document_type_id INT(10) UNSIGNED DEFAULT 50 NULL
        SQL);

        return true;
    }
}
