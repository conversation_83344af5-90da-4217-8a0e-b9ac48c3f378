<?php

declare(strict_types=1);

final class m250131_143250_create_table_ai_document_extraction extends CDbMigration
{
    private const TABLE_NAME             = 'ai_document_extraction';
    private const PUBLIC_ID_INDEX        = 'idx_ai_document_extraction_public_id';
    private const USER_ID_INDEX          = 'idx_ai_document_extraction_user_id';
    private const FOREIGN_KEY_USER_TABLE = 'fk_ai_document_extraction_user_id';

    public function up(): bool
    {
        $this->createTable(self::TABLE_NAME, [
            'id'          => 'pk',
            'public_id'   => 'varchar(36) NOT NULL', // uuid v4 has 36 characters but we leave it loose for testing
            'user_id'     => 'int unsigned NOT NULL',
            'document_id' => 'text NOT NULL',
            'content'     => 'json NOT NULL',
            'created_at'  => 'datetime NOT NULL',
        ]);

        $this->createIndex(self::PUBLIC_ID_INDEX, self::TABLE_NAME, 'public_id', unique: true);
        $this->createIndex(self::USER_ID_INDEX, self::TABLE_NAME, 'user_id');

        $this->addForeignKey(
            self::FOREIGN_KEY_USER_TABLE,
            self::TABLE_NAME,
            'user_id',
            'user',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FOREIGN_KEY_USER_TABLE, self::TABLE_NAME);

        $this->dropTable(self::TABLE_NAME);

        return true;
    }
}
