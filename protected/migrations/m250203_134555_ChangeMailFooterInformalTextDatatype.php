<?php

declare(strict_types=1);

final class m250203_134555_ChangeMailFooterInformalTextDatatype extends CDbMigration
{
    private const TABLE     = 'mail_footer';
    private const COLUMN    = 'informal_text';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'mediumtext');

        return true;
    }

    public function down(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'text');

        return true;
    }
}
