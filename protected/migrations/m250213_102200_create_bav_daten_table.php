<?php

declare(strict_types=1);

final class m250213_102200_create_bav_daten_table extends CDbMigration
{
    private const FK_NAME              = 'fk_pid_produkt_id';
    private const BAV_DATEN_TABLE_NAME = 'bav_daten';

    public function up(): bool
    {
        $this->setDbConnection(Yii::app()->bipro);

        $this->createTable(self::BAV_DATEN_TABLE_NAME, [
            'id'                => 'pk',
            'pid'               => 'int',
            'durchfuehrungsweg' => 'string',
        ]);

        $this->addForeignKey(self::FK_NAME, self::BAV_DATEN_TABLE_NAME, 'pid', 'produkt', 'id', 'cascade', 'cascade');

        return true;
    }

    public function down(): bool
    {
        $this->setDbConnection(Yii::app()->bipro);

        $this->dropTable(self::BAV_DATEN_TABLE_NAME);

        return true;
    }
}
