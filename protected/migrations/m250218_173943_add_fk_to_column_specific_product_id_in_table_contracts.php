<?php

declare(strict_types=1);

final class m250218_173943_add_fk_to_column_specific_product_id_in_table_contracts extends CDbMigration
{
    private const TABLE_NAME  = 'contracts';
    private const COLUMN_NAME = 'specific_product_id';
    private const FK_NAME     = 'fk_contracts_specific_product_id';

    public function up(): bool
    {
        $this->execute(
            <<<SQL
                UPDATE contracts
                    SET specific_product_id = 271 # 271 is the id of the product combo Sonstiges
                    WHERE specific_product_id NOT IN (SELECT id FROM product_combo);
            SQL
        );

        $this->alterColumn(self::TABLE_NAME, self::COLUMN_NAME, 'INT(3) unsigned NULL');

        $this->addForeignKey(
            self::FK_NAME,
            self::TABLE_NAME,
            self::COLUMN_NAME,
            'product_combo',
            'id',
            'RESTRICT',
            'RESTRICT'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_NAME, self::TABLE_NAME);
        $this->alterColumn(self::TABLE_NAME, self::COLUMN_NAME, 'INT(3) NULL');

        return true;
    }
}
