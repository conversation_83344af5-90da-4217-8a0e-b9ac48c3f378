<?php

declare(strict_types=1);

final class m250303_104421_more_detailed_brokerid_stats extends CDbMigration
{
    public function up(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                kpi_user_analytics
            DROP COLUMN count_antragsnummern_verkettet,
            DROP COLUMN count_bestandsnummern_verkettet,
            ADD COLUMN count_brokerid_hv_antrag SMALLINT UNSIGNED NOT NULL AFTER count_brokerid_unverkettet,
            ADD COLUMN count_brokerid_hv_bestand SMALLINT UNSIGNED NOT NULL AFTER count_brokerid_hv_antrag,
            ADD COLUMN count_brokerid_sonstige_antrag SMALLINT UNSIGNED NOT NULL AFTER count_brokerid_hv_bestand,
            ADD COLUMN count_brokerid_sonstige_bestand SMALLINT UNSIGNED NOT NULL AFTER count_brokerid_sonstige_antrag,
            ADD COLUMN count_brokerid_mak_hv SMALLINT UNSIGNED NOT NULL AFTER count_brokerid_sonstige_bestand,
            ADD COLUMN count_brokerid_mak_sonstige SMALLINT UNSIGNED NOT NULL AFTER count_brokerid_mak_hv
            EOSQL);

        return true;
    }

    public function down(): bool
    {
        $this->execute(<<<EOSQL
            ALTER TABLE
                kpi_user_analytics
            DROP COLUMN count_brokerid_hv_antrag,
            DROP COLUMN count_brokerid_hv_bestand,
            DROP COLUMN count_brokerid_sonstige_antrag,
            DROP COLUMN count_brokerid_sonstige_bestand,
            DROP COLUMN count_brokerid_mak_hv,
            DROP COLUMN count_brokerid_mak_sonstige,
            ADD COLUMN count_antragsnummern_verkettet SMALLINT UNSIGNED NOT NULL AFTER count_brokerid_unverkettet,
            ADD COLUMN count_bestandsnummern_verkettet SMALLINT UNSIGNED NOT NULL AFTER count_antragsnummern_verkettet
            EOSQL);

        return true;
    }
}
