<?php

declare(strict_types=1);

use Components\Database\DemvConnection;

final class m250303_195416_create_deckungen_table_and_seed extends CDbMigration
{
    public function getDbConnection(): DemvConnection
    {
        return Yii::app()->bipro;
    }

    public function up(): bool
    {
        $this->createTable(
            table: 'deckungen',
            columns: [
                'id'          => 'pk',
                'type'        => 'string NOT NULL',
                'code_label'  => 'string NOT NULL',
                'code_number' => 'integer NOT NULL',
                'is_custom'   => 'bool NOT NULL DEFAULT 0 COMMENT "Indicates whether the entry is a custom coverage code not defined in the GDV Norm but received from other insurance companies."',
                'name'        => 'string NOT NULL',
            ]
        );

        $this->createIndex(
            name: 'idx-unique-type-code_number',
            table: 'deckungen',
            columns: ['type', 'code_number'],
            unique: true
        );

        $this->seedBaseDeckungen();
        $this->seedKhDeckungen();
        $this->seedKuDeckungen();

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(table: 'deckungen');

        return true;
    }

    private function seedBaseDeckungen(): void
    {
        $baseDeckungenData = require_once __DIR__ . '/files/deckungen-base.php';

        foreach ($baseDeckungenData as $codeLabel => $name) {
            $this->getDbConnection()
                ->createCommand()
                ->insert(
                    table: 'deckungen',
                    columns: [
                        'type'        => 'base',
                        'code_label'  => $codeLabel,
                        'code_number' => (int) $codeLabel,
                        'name'        => $name,
                    ]
                );
        }
    }

    private function seedKhDeckungen(): void
    {
        $khDeckungenData = require_once __DIR__ . '/files/deckungen-kh.php';

        foreach ($khDeckungenData as $codeLabel => $name) {
            $this->getDbConnection()
                ->createCommand()
                ->insert(
                    table: 'deckungen',
                    columns: [
                        'type'        => 'kh',
                        'code_label'  => $codeLabel,
                        'code_number' => (int) $codeLabel,
                        'name'        => $name,
                        // This is a one-off - marks the "57" entry as a custom entry (not found within the GDV Norm)
                        'is_custom'   => (int) $codeLabel === 57,
                    ]
                );
        }
    }

    private function seedKuDeckungen(): void
    {
        $kuDeckungenBasis = require_once __DIR__ . '/files/deckungen-ku.php';

        foreach ($kuDeckungenBasis as $codeLabel => $name) {
            $this->getDbConnection()
                ->createCommand()
                ->insert(
                    table: 'deckungen',
                    columns: [
                        'type'        => 'ku',
                        'code_label'  => $codeLabel,
                        'code_number' => (int) $codeLabel,
                        'name'        => $name,
                    ]
                );
        }
    }
}
