<?php

declare(strict_types=1);

final class m250305_083024_create_table_ai_responses extends CDbMigration
{
    const TABLE = 'ai_responses';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'            => 'pk',
                'action'        => 'string NOT NULL COMMENT "Kennzeichnet die durchgeführte Aktion"',
                'response'      => 'text NOT NULL COMMENT "Speichert die API-Response der AI"',
                'success_at'    => 'datetime NULL COMMENT "Erfolgreiche Verarbeitung?"',
                'failed_at'     => 'datetime NULL COMMENT "Nicht erfolgreiche Verarbeitung?"',
                'error_message' => 'text NULL COMMENT "Enthält Fehlermeldungen, falls vorhanden"',
            ],
            'COMMENT="Speichert Ergebnisse von AI-Anfragen"'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
