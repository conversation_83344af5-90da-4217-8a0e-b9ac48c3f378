<?php

declare(strict_types=1);

final class m250317_084322_fix_default_value_for_damage_done_date_in_table_contracts extends CDbMigration
{
    private const TABLE  = 'contracts';
    private const COLUMN = 'done_date';

    public function up(): bool
    {
        $this->alterColumn(self::TABLE, self::COLUMN, 'DATE DEFAULT NULL');

        return true;
    }

    public function down(): bool
    {
        echo "m250317_084322_fix_damage_done_date_in_table_contracts does not support migration down.\n";

        return true;
    }
}
