<?php

declare(strict_types=1);

final class m250318_104039_rename_risikodatenerfassung_mail_procedure extends CDbMigration
{
    private const  TABLE_MAIL_PROCEDURE     = 'mail_procedure';
    private const  MAIL_PROCEDURE_ID        = 167;
    private const  MAIL_PROCEDURE_NAME_OLD  = 'Risikodatenerfassung';
    private const  MAIL_PROCEDURE_NAME_NEW  = 'Versicherungsangaben';

    public function up(): bool
    {
        $this->update(
            self::TABLE_MAIL_PROCEDURE,
            ['name' => self::MAIL_PROCEDURE_NAME_NEW],
            'id = :id',
            [':id' => self::MAIL_PROCEDURE_ID]
        );

        return true;
    }

    public function down(): bool
    {
        $this->update(
            self::TABLE_MAIL_PROCEDURE,
            ['name' => self::MAIL_PROCEDURE_NAME_OLD],
            'id = :id',
            [':id' => self::MAIL_PROCEDURE_ID]
        );

        return true;
    }
}
