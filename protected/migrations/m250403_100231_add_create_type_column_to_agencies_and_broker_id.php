<?php

declare(strict_types=1);

final class m250403_100231_add_create_type_column_to_agencies_and_broker_id extends CDbMigration
{
    public function up(): bool
    {
        $this->addColumn('agency', 'create_type', 'tinyint unsigned default 0');
        $this->addColumn('broker_id', 'create_type', 'tinyint unsigned default 0');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn('agency', 'create_type');
        $this->dropColumn('broker_id', 'create_type');

        return true;
    }
}
