<?php

declare(strict_types=1);

final class m250417_102217_add_create_type_column_to_immobilien_client_vehicle_client_animal extends CDbMigration
{
    public function up(): bool
    {
        $this->addColumn('immobilien', 'create_type', 'tinyint unsigned default 0');
        $this->addColumn('client_vehicle', 'create_type', 'tinyint unsigned default 0');
        $this->addColumn('client_animal', 'create_type', 'tinyint unsigned default 0');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn('immobilien', 'create_type');
        $this->dropColumn('client_vehicle', 'create_type');
        $this->dropColumn('client_animal', 'create_type');

        return true;
    }
}
