<?php

declare(strict_types=1);

final class m250422_101009_create_table_four_circles extends CDbMigration
{
    private const TABLE = 'four_circles_data';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'id'              => 'pk',
                'user_id'         => 'int(11) unsigned NOT NULL',
                'four_circles_id' => 'string NOT NULL',
                'status'          => 'string NOT NULL',
                'updated_at'      => 'datetime NOT NULL',
            ],
        );

        $this->addForeignKey(
            name: 'fk-four-circles-user-id',
            table: self::TABLE,
            columns: 'user_id',
            refTable: 'user',
            refColumns: 'id',
            delete: 'CASCADE',
            update: 'CASCADE'
        );

        $this->createIndex(
            name: 'idx-four-circles-id',
            table: self::TABLE,
            columns: 'four_circles_id',
            unique: true,
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
