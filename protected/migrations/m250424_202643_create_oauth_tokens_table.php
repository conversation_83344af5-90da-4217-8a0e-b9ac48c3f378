<?php

declare(strict_types=1);

final class m250424_202643_create_oauth_tokens_table extends CDbMigration
{
    private const TABLE_NAME = 'oauth2_tokens';
    private const FK_USER    = 'fk_oauth2_tokens_user_id';

    public function up(): bool
    {
        $this->createTable(self::TABLE_NAME, [
            'id'                      => 'pk',
            'user_id'                 => 'int unsigned not null',
            'iss'                     => 'varchar(255) not null',
            'aud'                     => 'varchar(255)',
            'scope'                   => 'text not null',
            'access_token'            => 'BLOB not null',
            'access_token_expires_at' => 'datetime not null',
            'id_token'                => 'BLOB',
            'refresh_token'           => 'BLOB',
            'refresh_token_expires_at'=> 'datetime',
            'created_at'              => 'datetime not null default current_timestamp',
        ]);

        // Index für schnellere Abfragen
        $this->createIndex('idx_oauth2_tokens_user_id', self::TABLE_NAME, 'user_id');

        // Foreign Key zur user-Tabelle
        $this->addForeignKey(
            self::FK_USER,
            self::TABLE_NAME,
            'user_id',
            'user',
            'id'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_USER, self::TABLE_NAME);
        $this->dropTable(self::TABLE_NAME);

        return true;
    }
}
