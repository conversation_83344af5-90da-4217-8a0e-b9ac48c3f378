<?php

declare(strict_types=1);

final class m250425_113232_create_table_gdv_process_id_blacklist extends CDbMigration
{
    const TABLE = 'gdv_process_id_blacklist';

    public function up(): bool
    {
        $this->createTable(
            self::TABLE,
            [
                'process_id' => 'string PRIMARY KEY'
            ],
            'COMMENT="GDV2Bipro Nepatec Prozess-ID Blacklist"'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE);

        return true;
    }
}
