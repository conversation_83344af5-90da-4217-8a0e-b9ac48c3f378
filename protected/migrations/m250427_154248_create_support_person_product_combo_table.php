<?php

declare(strict_types=1);

final class m250427_154248_create_support_person_product_combo_table extends CDbMigration
{
    private const TABLE_NAME        = 'support_person_product_combo';
    private const FK_SUPPORT_PERSON = 'fk_spp_support_person_id';
    private const FK_PRODUCT        = 'fk_spp_product_combo_id';

    public function up(): bool
    {
        $this->createTable(self::TABLE_NAME, [
            'id'                => 'pk',
            'support_person_id' => 'int not null',
            'product_combo_id'  => 'int unsigned not null',
        ]);

        $this->createIndex('uniq_spp_support_person_product_combo', self::TABLE_NAME, ['support_person_id', 'product_combo_id'], true);

        // Foreign Key zur support_person-Tabelle
        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id'
        );

        // Foreign Key zur product_combo-Tabelle
        $this->addForeignKey(
            self::FK_PRODUCT,
            self::TABLE_NAME,
            'product_combo_id',
            'product_combo',
            'id'
        );

        $this->copyData();

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE_NAME);

        return true;
    }

    private function copyData(): void
    {
        $step   = 1000;
        $lastId = 0;

        // get all product combos ids
        $productCombosIds = Yii::app()->db->createCommand()
                                          ->select('id')
                                          ->from('product_combo')
                                          ->order('id ASC')
                                          ->queryColumn();

        $productCombosIds = array_map(static fn ($id) => (int) $id, $productCombosIds);

        do {
            $supportPersons = Yii::app()->db->createCommand()
                ->select('id, products')
                ->from('support_person')
                ->where('id > :lastId', [':lastId' => $lastId])
                ->order('id ASC')
                ->limit($step)
                ->queryAll();

            foreach ($supportPersons as $supportPerson) {
                if (! isset($supportPerson['products'])) {
                    // If there are no products assigned to the support person, skip to the next one
                    continue;
                }

                $products = explode(';', (string) $supportPerson['products']);

                if ($products === false || count($products) === 0) {
                    continue;
                }

                $existingProductCombos = array_intersect($productCombosIds, $products);

                if (count($existingProductCombos) === 0) {
                    continue;
                }

                $this->insertMultiple(
                    self::TABLE_NAME,
                    array_map(static fn ($productId) => [
                        'support_person_id' => (int) $supportPerson['id'],
                        'product_combo_id'  => (int) $productId,
                    ], $existingProductCombos)
                );
            }

            if (!empty($supportPersons)) {
                $lastId = end($supportPersons)['id'];
            }
        } while (count($supportPersons) > 0);
    }
}
