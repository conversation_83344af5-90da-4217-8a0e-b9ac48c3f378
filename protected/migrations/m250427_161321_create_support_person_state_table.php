<?php

declare(strict_types=1);

final class m250427_161321_create_support_person_state_table extends CDbMigration
{
    private const TABLE_NAME        = 'support_person_state';
    private const FK_SUPPORT_PERSON = 'fk_support_person_state_support_person_id';
    private const FK_STATE          = 'fk_support_person_state_state_id';

    public function up(): bool
    {
        $this->createTable(self::TABLE_NAME, [
            'id'                => 'pk',
            'support_person_id' => 'int not null',
            'state_id'          => 'int unsigned not null',
        ]);

        $this->createIndex('uniq_spp_support_person_state_id', self::TABLE_NAME, ['support_person_id', 'state_id'], true);

        // Foreign Key zur support_person-Tabelle
        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id'
        );

        // Foreign Key zur states-Tabelle
        $this->addForeignKey(
            self::FK_STATE,
            self::TABLE_NAME,
            'state_id',
            'states',
            'id'
        );

        $this->copyData();

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE_NAME);

        return true;
    }

    private function copyData(): void
    {
        $step   = 1000;
        $lastId = 0;

        // get all product combos ids
        $stateIds = Yii::app()->db->createCommand()
            ->select('id')
            ->from('states')
            ->order('id ASC')
            ->queryColumn();

        $stateIds = array_map(static fn ($id) => (int) $id, $stateIds);

        do {
            $supportPersons = Yii::app()->db->createCommand()
                                            ->select('id, states')
                                            ->from('support_person')
                                            ->where('id > :lastId', [':lastId' => $lastId])
                                            ->order('id ASC')
                                            ->limit($step)
                                            ->queryAll();

            foreach ($supportPersons as $supportPerson) {
                if (! isset($supportPerson['states'])) {
                    continue;
                }

                $states = explode(';', (string) $supportPerson['states']);

                if ($states === false || count($states) === 0) {
                    continue;
                }

                $existingStates = array_intersect($stateIds, $states);

                if (count($existingStates) === 0) {
                    continue;
                }

                $this->insertMultiple(
                    self::TABLE_NAME,
                    array_map(static fn ($stateId) => [
                        'support_person_id' => (int) $supportPerson['id'],
                        'state_id'          => (int) $stateId,
                    ], $existingStates)
                );
            }

            if (!empty($supportPersons)) {
                $lastId = end($supportPersons)['id'];
            }
        } while (count($supportPersons) > 0);
    }
}
