<?php

declare(strict_types=1);

final class m250427_162500_create_support_person_assignment_subject_table extends CDbMigration
{
    private const TABLE_NAME            = 'support_person_assignment_subject';
    private const FK_SUPPORT_PERSON     = 'fk_support_person_assignment_subject_support_person_id';
    private const FK_ASSIGNMENT_SUBJECT = 'fk_support_person_assignment_subject_assignment_subject_id';

    public function up(): bool
    {
        $this->createTable(self::TABLE_NAME, [
            'id'                            => 'pk',
            'support_person_id'             => 'int not null',
            'support_assignment_subject_id' => 'int unsigned not null',
        ]);

        $this->createIndex(
            'uniq_spp_support_person_subject',
            self::TABLE_NAME,
            ['support_person_id', 'support_assignment_subject_id'],
            true
        );

        // Foreign Key zur support_person-Tabelle
        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id'
        );

        // Foreign Key zur support_assignment_subject-Tabelle
        $this->addForeignKey(
            self::FK_ASSIGNMENT_SUBJECT,
            self::TABLE_NAME,
            'support_assignment_subject_id',
            'support_assignment_subject',
            'id'
        );

        $this->copyData();

        return true;
    }

    public function down(): bool
    {
        $this->dropTable(self::TABLE_NAME);

        return true;
    }

    private function copyData(): void
    {
        $step   = 1000;
        $lastId = 0;

        // get all product combos ids
        $subjectIds = Yii::app()->db->createCommand()
                                  ->select('id')
                                  ->from('support_assignment_subject')
                                  ->order('id ASC')
                                  ->queryColumn();

        $subjectIds = array_map(static fn ($id) => (int) $id, $subjectIds);

        do {
            /** @var SupportPerson[] $supportPersons */
            $supportPersons = Yii::app()->db->createCommand()
                                            ->select('id, subjects')
                                            ->from('support_person')
                                            ->where('id > :lastId', [':lastId' => $lastId])
                                            ->order('id ASC')
                                            ->limit($step)
                                            ->queryAll();

            try {
                foreach ($supportPersons as $supportPerson) {
                    if (! isset($supportPerson['subjects'])) {
                        // If there are no subjects assigned to the support person, skip to the next one
                        continue;
                    }

                    $subjects = explode(';', (string) $supportPerson['subjects']);

                    if ($subjects === false || count($subjects) === 0) {
                        continue;
                    }

                    $existingSubjects = array_intersect($subjectIds, $subjects);

                    if (count($existingSubjects) === 0) {
                        continue;
                    }

                    $this->insertMultiple(
                        self::TABLE_NAME,
                        array_map(static fn ($subjectId) => [
                            'support_person_id'             => (int) $supportPerson['id'],
                            'support_assignment_subject_id' => (int) $subjectId,
                        ], $existingSubjects)
                    );
                }

                if (!empty($supportPersons)) {
                    $lastId = end($supportPersons)['id'];
                }
            } catch (\Exception $e) {
                break;
            }
        } while (count($supportPersons) > 0);
    }
}
