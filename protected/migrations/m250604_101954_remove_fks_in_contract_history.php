<?php

declare(strict_types=1);

final class m250604_101954_remove_fks_in_contract_history extends CDbMigration
{
    private const TABLE          = 'contract_history';
    private const FK_FROM_CLIENT = 'fk_' . self::TABLE . '_from_client';
    private const FK_TO_CLIENT   = 'fk_' . self::TABLE . '_to_client';
    private const FK_CONTRACT_ID = 'fk_' . self::TABLE . '_contract_id';

    public function up(): bool
    {
        $this->dropForeignKey(self::FK_FROM_CLIENT, self::TABLE);
        $this->dropForeignKey(self::FK_TO_CLIENT, self::TABLE);
        $this->dropForeignKey(self::FK_CONTRACT_ID, self::TABLE);

        return true;
    }

    public function down(): bool
    {
        $table        = self::TABLE;
        $fkFromClient = self::FK_FROM_CLIENT;
        $fkToClient   = self::FK_TO_CLIENT;
        $fkContractId = self::FK_CONTRACT_ID;

        $sql = <<<SQL
ALTER TABLE $table
ADD CONSTRAINT $fkFromClient FOREIGN KEY (from_client_id) REFERENCES client (id) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT $fkToClient FOREIGN KEY (to_client_id) REFERENCES client (id) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT $fkContractId FOREIGN KEY (contract_id) REFERENCES contracts (id) ON DELETE CASCADE ON UPDATE CASCADE
SQL;

        $this->execute($sql);

        return true;
    }
}
