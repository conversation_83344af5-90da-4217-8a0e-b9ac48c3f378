<?php

use Business\Company\Lv1871;

class m250610_104510_weitere_vermittlernummerformate_lv1871 extends CDbMigration
{
    private const TABLE                       = 'brokerid_pattern';
    private const COLUMN_INSURANCE_COMPANY_ID = 'insurance_company_id';
    private const COLUMN_REGEX                = 'regex';
    private const COLUMN_REPLACEMENT          = 'replacement';

    private const COMPANY_ID = Lv1871::ID;

    private const INSERTS = [
        [
            'regex'                => '/^25(\d{5})0$/',
            'replacement'          => '25${1}0',
        ],
        [
            'regex'                => '/^54(\d{5})0$/',
            'replacement'          => '54${1}0',
        ],
    ];

    public function up(): bool
    {
        foreach (self::INSERTS as $entry) {
            $this->insert(
                self::TABLE,
                [
                    self::COLUMN_INSURANCE_COMPANY_ID => self::COMPANY_ID,
                    self::COLUMN_REGEX                => $entry['regex'],
                    self::COLUMN_REPLACEMENT          => $entry['replacement'],
                ]
            );
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::INSERTS as $entry) {
            $this->delete(
                self::TABLE,
                'insurance_company_id = :id AND ' .
                self::COLUMN_REGEX . ' = :regex AND ' .
                self::COLUMN_REPLACEMENT . ' = :replacement',
                [
                    ':id'          => self::COMPANY_ID,
                    ':regex'       => $entry['regex'],
                    ':replacement' => $entry['replacement'],
                ]
            );
        }

        return true;
    }
}
