<?php

declare(strict_types=1);

use Auth\Services;

final class m250613_121023_alter_oauth2_tokens_table_add_provider_column extends CDbMigration
{
    private const TABLE_NAME  = 'oauth2_tokens';
    private const COLUMN_NAME = 'provider';

    public function up(): bool
    {
        $enumString = "ENUM('" . Services::EXTERNAL_FONDSFINANZ . "') NOT NULL DEFAULT '" . Services::EXTERNAL_FONDSFINANZ . "' AFTER user_id";

        $this->addColumn(self::TABLE_NAME, self::COLUMN_NAME, $enumString);

        $this->createIndex('idx_' . self::TABLE_NAME . '_' . self::COLUMN_NAME, self::TABLE_NAME, self::COLUMN_NAME);

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE_NAME, self::COLUMN_NAME);

        $this->dropIndex('idx_' . self::TABLE_NAME . '_' . self::COLUMN_NAME, self::TABLE_NAME);

        return true;
    }
}
