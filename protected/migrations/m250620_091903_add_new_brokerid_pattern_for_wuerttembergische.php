<?php

declare(strict_types=1);

use Business\Company\Wuerttembergische;

final class m250620_091903_add_new_brokerid_pattern_for_wuerttembergische extends CDbMigration
{
    private const TABLE                       = 'brokerid_pattern';
    private const COLUMN_INSURANCE_COMPANY_ID = 'insurance_company_id';
    private const COLUMN_REGEX                = 'regex';
    private const COLUMN_REPLACEMENT          = 'replacement';

    private const COMPANY_ID = Wuerttembergische::ID;

    private const REGEX = '/^(\d{4})-(\d{4})-(\d{1})-03$/';

    private const REPLACEMENT = '${1}-${2}-${3}-03';

    public function up(): bool
    {
        $this->insert(
            self::TABLE,
            [
                self::COLUMN_INSURANCE_COMPANY_ID => self::COMPANY_ID,
                self::COLUMN_REGEX                => self::REGEX,
                self::COLUMN_REPLACEMENT          => self::REPLACEMENT,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            'insurance_company_id = :id AND ' .
            self::COLUMN_REGEX . ' = :regex AND ' .
            self::COLUMN_REPLACEMENT . ' = :replacement',
            [
                ':id'          => self::COMPANY_ID,
                ':regex'       => self::REGEX,
                ':replacement' => self::REPLACEMENT,
            ]
        );

        return true;
    }
}
