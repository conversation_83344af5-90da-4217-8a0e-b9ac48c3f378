<?php

declare(strict_types=1);

class m250623_153600_update_belarus_country extends CDbMigration
{
    private const TABLE       = 'country';
    private const COLUMN      = 'name';
    private const COUNTRY_NUM = '112';

    public function getDbConnection()
    {
        return Yii::app()->common_data;
    }

    public function up(): bool
    {
        $this->update(self::TABLE, [self::COLUMN => 'Belarus'], 'num = "' . self::COUNTRY_NUM . '"');

        return true;
    }

    public function down(): bool
    {
        $this->update(self::TABLE, [self::COLUMN => 'Weissrussland'], 'num = "' . self::COUNTRY_NUM . '" ');

        return true;
    }
}
