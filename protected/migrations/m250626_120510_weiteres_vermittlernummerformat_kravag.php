<?php

use Business\Company\Kravag;

class m250626_120510_weiteres_vermittlernummerformat_kravag extends CDbMigration
{
    private const TABLE                       = 'brokerid_pattern';
    private const COLUMN_INSURANCE_COMPANY_ID = 'insurance_company_id';
    private const COLUMN_REGEX                = 'regex';
    private const COLUMN_REPLACEMENT          = 'replacement';

    private const COMPANY_ID = Kravag::ALLGEMEIN;

    private const REGEX = '/^(422)?([1-9]\d{5})$/';

    private const REPLACEMENT = '422${2}';

    public function up(): bool
    {
        $this->insert(
            self::TABLE,
            [
                self::COLUMN_INSURANCE_COMPANY_ID => self::COMPANY_ID,
                self::COLUMN_REGEX                => self::REGEX,
                self::COLUMN_REPLACEMENT          => self::REPLACEMENT,
            ]
        );

        return true;
    }

    public function down(): bool
    {
        $this->delete(
            self::TABLE,
            'insurance_company_id = :id AND ' .
            self::COLUMN_REGEX . ' = :regex AND ' .
            self::COLUMN_REPLACEMENT . ' = :replacement',
            [
                ':id'          => self::COMPANY_ID,
                ':regex'       => self::REGEX,
                ':replacement' => self::REPLACEMENT,
            ]
        );

        return true;
    }
}
