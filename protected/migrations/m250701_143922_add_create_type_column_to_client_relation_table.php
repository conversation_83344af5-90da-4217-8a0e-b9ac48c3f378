<?php

declare(strict_types=1);

final class m250701_143922_add_create_type_column_to_client_relation_table extends CDbMigration
{
    private const TABLE = 'client_relation';

    public function up(): bool
    {
        $this->addColumn(self::TABLE, 'create_type', 'tinyint unsigned not null default 0');

        return true;
    }

    public function down(): bool
    {
        $this->dropColumn(self::TABLE, 'create_type');

        return true;
    }
}
