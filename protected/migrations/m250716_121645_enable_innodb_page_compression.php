<?php

declare(strict_types=1);

final class m250716_121645_enable_innodb_page_compression extends CDbMigration
{
    private const TABLES = [
        'client_files',
        'contract_data_strings',
        'gdv2bipro_monitoring',
        'gdv_file_metadaten',
        'gdv_import_tracking',
        'vertragsservice_import_backup',
        'vertragsservice_tracking',
    ];

    public function up(): bool
    {
        foreach (self::TABLES as $table) {
            $this->execute("ALTER TABLE {$table} PAGE_COMPRESSED=1");
        }

        return true;
    }

    public function down(): bool
    {
        foreach (self::TABLES as $table) {
            $this->execute("ALTER TABLE {$table} PAGE_COMPRESSED=0");
        }

        return true;
    }
}
