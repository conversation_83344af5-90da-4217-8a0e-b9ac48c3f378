<?php

declare(strict_types=1);

final class m250724_094500_add_cascade_delete_to_support_person_product_combo extends CDbMigration
{
    private const TABLE_NAME        = 'support_person_product_combo';
    private const FK_SUPPORT_PERSON = 'fk_spp_support_person_id';
    private const FK_PRODUCT        = 'fk_spp_product_combo_id';

    public function up(): bool
    {
        $this->dropForeignKey(self::FK_SUPPORT_PERSON, self::TABLE_NAME);
        $this->dropForeignKey(self::FK_PRODUCT, self::TABLE_NAME);

        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            self::FK_PRODUCT,
            self::TABLE_NAME,
            'product_combo_id',
            'product_combo',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_SUPPORT_PERSON, self::TABLE_NAME);
        $this->dropForeignKey(self::FK_PRODUCT, self::TABLE_NAME);

        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id'
        );

        $this->addForeignKey(
            self::FK_PRODUCT,
            self::TABLE_NAME,
            'product_combo_id',
            'product_combo',
            'id'
        );

        return true;
    }
}
