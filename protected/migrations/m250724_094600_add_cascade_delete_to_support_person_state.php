<?php

declare(strict_types=1);

final class m250724_094600_add_cascade_delete_to_support_person_state extends CDbMigration
{
    private const TABLE_NAME        = 'support_person_state';
    private const FK_SUPPORT_PERSON = 'fk_support_person_state_support_person_id';
    private const FK_STATE          = 'fk_support_person_state_state_id';

    public function up(): bool
    {
        $this->dropForeignKey(self::FK_SUPPORT_PERSON, self::TABLE_NAME);
        $this->dropForeignKey(self::FK_STATE, self::TABLE_NAME);

        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            self::FK_STATE,
            self::TABLE_NAME,
            'state_id',
            'states',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_SUPPORT_PERSON, self::TABLE_NAME);
        $this->dropForeignKey(self::FK_STATE, self::TABLE_NAME);

        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id'
        );

        $this->addForeignKey(
            self::FK_STATE,
            self::TABLE_NAME,
            'state_id',
            'states',
            'id'
        );

        return true;
    }
}
