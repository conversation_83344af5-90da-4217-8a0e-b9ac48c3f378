<?php

declare(strict_types=1);

final class m250724_094700_add_cascade_delete_to_support_person_assignment_subject extends CDbMigration
{
    private const TABLE_NAME            = 'support_person_assignment_subject';
    private const FK_SUPPORT_PERSON     = 'fk_support_person_assignment_subject_support_person_id';
    private const FK_ASSIGNMENT_SUBJECT = 'fk_support_person_assignment_subject_assignment_subject_id';

    public function up(): bool
    {
        $this->dropForeignKey(self::FK_SUPPORT_PERSON, self::TABLE_NAME);
        $this->dropForeignKey(self::FK_ASSIGNMENT_SUBJECT, self::TABLE_NAME);

        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            self::FK_ASSIGNMENT_SUBJECT,
            self::TABLE_NAME,
            'support_assignment_subject_id',
            'support_assignment_subject',
            'id',
            'CASCADE',
            'CASCADE'
        );

        return true;
    }

    public function down(): bool
    {
        $this->dropForeignKey(self::FK_SUPPORT_PERSON, self::TABLE_NAME);
        $this->dropForeignKey(self::FK_ASSIGNMENT_SUBJECT, self::TABLE_NAME);

        $this->addForeignKey(
            self::FK_SUPPORT_PERSON,
            self::TABLE_NAME,
            'support_person_id',
            'support_person',
            'id'
        );

        $this->addForeignKey(
            self::FK_ASSIGNMENT_SUBJECT,
            self::TABLE_NAME,
            'support_assignment_subject_id',
            'support_assignment_subject',
            'id'
        );

        return true;
    }
}
