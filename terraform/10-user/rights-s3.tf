data "aws_iam_policy_document" "s3_policy_document" {
  statement {
    effect = "Allow"

    actions = [
      "s3:DeleteObject",
      "s3:DeleteObjectVersion",
      "s3:GetObject",
      "s3:GetObjectAcl",
      "s3:ListBucket",
      "s3:ListMultipartUploadParts",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:RestoreObject"
    ]

    resources = [
      "*",
    ]
  }
}

resource "aws_iam_policy" "s3_policy" {
  name   = "${var.environment}-${var.project}-s3-policy"
  policy = data.aws_iam_policy_document.s3_policy_document.json
}

resource "aws_iam_user_policy_attachment" "s3_policy_attachment" {
  policy_arn = aws_iam_policy.s3_policy.arn
  user       = aws_iam_user.this.name
}
