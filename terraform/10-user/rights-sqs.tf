data "aws_iam_policy_document" "sqs_policy_document" {
  statement {
    effect = "Allow"

    actions = [
      "sqs:DeleteMessage",
      "sqs:DeleteMessageBatch",
      "sqs:PurgeQueue",
      "sqs:ReceiveMessage",
      "sqs:SendMessage",
      "sqs:SendMessageBatch",
      "sqs:GetQueueAttributes"
    ]

    resources = [
      "*",
    ]
  }
}

resource "aws_iam_policy" "sqs_policy" {
  name   = "${var.environment}-${var.project}-sqs-policy"
  policy = data.aws_iam_policy_document.sqs_policy_document.json
}

resource "aws_iam_user_policy_attachment" "sqs_policy_attachment" {
  policy_arn = aws_iam_policy.sqs_policy.arn
  user       = aws_iam_user.this.name
}
