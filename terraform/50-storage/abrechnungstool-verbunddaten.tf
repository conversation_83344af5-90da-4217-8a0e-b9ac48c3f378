resource "aws_s3_bucket" "abrechnungen-verbunddaten" {
  bucket = "demv-${var.environment}-${var.project}-abrechnungen-verbunddaten"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-abrechnungen-verbunddaten"
    Environment = var.environment
    billing-tag = "team-liebe"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "abrechnung-verbunddaten-encryption" {
  bucket = aws_s3_bucket.abrechnungen-verbunddaten.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
