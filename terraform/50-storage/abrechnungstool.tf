resource "aws_s3_bucket" "abrechnungstool" {
  bucket = "demv-${var.environment}-${var.project}-abrechnungstool"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-abrechnungstool"
    Environment = var.environment
    billing-tag = "team-liebe"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "abrechnungstool-encryption" {
  bucket = aws_s3_bucket.abrechnungstool.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

