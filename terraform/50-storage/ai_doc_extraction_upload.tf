resource "aws_s3_bucket" "ai-doc-extraction-upload" {
  bucket = "demv-${var.environment}-${var.project}-ai-doc-extraction-upload"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-ai-doc-extraction-upload"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_cors_configuration" "ai-doc-extraction-upload-cors-configuration" {
  bucket = aws_s3_bucket.ai-doc-extraction-upload.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["POST", "PUT"]
    allowed_origins = [var.cors_origins[var.environment], var.cors_origins["live"]]
    expose_headers  = ["ETag"]
    max_age_seconds = 0
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "ai-doc-extraction-upload-lifecycle" {
  bucket = aws_s3_bucket.ai-doc-extraction-upload.id

  rule {
    id     = "delete-all-objects-after-1-day"
    status = "Enabled"

    filter {
      prefix = "uploads/"
    }

    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }

    expiration {
      days                         = 1
      expired_object_delete_marker = false
    }

    noncurrent_version_expiration {
      noncurrent_days           = 1
    }
  }
}

resource "aws_s3_bucket_versioning" "ai-doc-extraction-upload-versioning" {
  bucket = aws_s3_bucket.ai-doc-extraction-upload.id

  versioning_configuration {
    status = "Disabled"
  }
}
