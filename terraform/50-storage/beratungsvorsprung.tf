resource "aws_s3_bucket" "beratungsvorsprung" {
  tags = {
    Name        = "demv-${var.environment}-${var.project}-beratungsvorsprung"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_cors_configuration" "beratungsvorsprung-cors-configuration" {
  bucket = aws_s3_bucket.beratungsvorsprung.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["POST"]
    allowed_origins = [var.cors_origins[var.environment]]
    expose_headers  = []
    max_age_seconds = 0
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "beratungsvorsprung-encryption" {
  bucket = aws_s3_bucket.beratungsvorsprung.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
