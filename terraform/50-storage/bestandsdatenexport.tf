resource "aws_s3_bucket" "bestandsdaten-export" {
  bucket = "demv-${var.environment}-${var.project}-bestandsdaten-export"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-bestandsdaten-export"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "bestandsdaten-export-encryption" {
  bucket = aws_s3_bucket.bestandsdaten-export.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
