resource "aws_s3_bucket" "bipro-transfer" {
  bucket = "demv-${var.environment}-${var.project}-bipro-transfer"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-bipro-transfer"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "bipro-transfer-encryption" {
  bucket = aws_s3_bucket.bipro-transfer.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
