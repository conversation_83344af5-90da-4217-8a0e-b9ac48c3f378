resource "aws_s3_bucket" "client-files" {
  bucket = "demv-${var.environment}-${var.project}-client-files"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-client-files"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_cors_configuration" "client-files-cors-configuration" {
  bucket = aws_s3_bucket.client-files.id

  cors_rule {
      allowed_headers = ["*"]
      allowed_methods = ["GET"]
      allowed_origins = var.client_file_cors_origins[var.environment]
    }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "client-files-encryption" {
  bucket = aws_s3_bucket.client-files.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_versioning" "versioning_client_files" {
  bucket = aws_s3_bucket.client-files.id
  versioning_configuration {
    status = "Enabled"
  }
}
