resource "aws_s3_bucket" "gdv2bipro-files" {
  bucket = "demv-${var.environment}-${var.project}-gdv2bipro-files"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-gdv2bipro-files"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "gdv2bipro-files-encryption" {
  bucket = aws_s3_bucket.gdv2bipro-files.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
