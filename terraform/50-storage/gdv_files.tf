resource "aws_s3_bucket" "gdv-files" {
  bucket = "demv-${var.environment}-${var.project}-gdv-files"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-gdv-files"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "gdv-files-encryption" {
  bucket = aws_s3_bucket.gdv-files.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
