resource "aws_s3_bucket" "inhaltsseiten" {
  bucket = "demv-${var.environment}-${var.project}-inhaltsseiten"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-inhaltsseiten"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_acl" "inhaltsseiten-acl" {
  bucket = aws_s3_bucket.inhaltsseiten.id
  acl    = "private"
}

output "inhaltsseiten-bucket-name" {
  value = aws_s3_bucket.inhaltsseiten.bucket
}

resource "aws_s3_bucket_server_side_encryption_configuration" "inhaltsseiten-encryption" {
  bucket = aws_s3_bucket.inhaltsseiten.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
