resource "aws_s3_bucket" "largefileupload" {
  bucket = "demv-${var.environment}-${var.project}-largefileupload"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-largefileupload"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_cors_configuration" "largefileupload-cors-configuration" {
  bucket = aws_s3_bucket.largefileupload.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["POST", "PUT"]
    allowed_origins = [var.cors_origins[var.environment]]
    expose_headers  = ["ETag"]
    max_age_seconds = 0
  }
}
