resource "aws_s3_bucket" "mail" {
  bucket = "demv-${var.environment}-${var.project}-mail"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-mail"
    Environment = var.environment
    "billing-tag" = "mailer"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "mail-encryption" {
  bucket = aws_s3_bucket.mail.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
