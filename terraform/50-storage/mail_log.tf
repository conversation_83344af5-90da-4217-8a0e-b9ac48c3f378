resource "aws_s3_bucket" "mail-log" {
  bucket = "demv-${var.environment}-${var.project}-mail-log"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-mail-log"
    Environment = var.environment
    "billing-tag" = "mailer"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "mail-log-encryption" {
  bucket = aws_s3_bucket.mail-log.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
