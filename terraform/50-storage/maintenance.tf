resource "aws_s3_bucket" "maintenance" {
  bucket = "demv-${var.environment}-${var.project}-maintenance"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-maintenance"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "maintenance-encryption" {
  bucket = aws_s3_bucket.maintenance.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
