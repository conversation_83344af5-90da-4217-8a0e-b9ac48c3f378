resource "aws_s3_bucket" "vertragsservice-import-backup" {
  bucket = "demv-${var.environment}-${var.project}-vertragsservice-import-backup"

  tags = {
    Name        = "demv-${var.environment}-${var.project}-vertragsservice-import-backup"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "vertragsservice-import-backup-encryption" {
  bucket = aws_s3_bucket.vertragsservice-import-backup.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
