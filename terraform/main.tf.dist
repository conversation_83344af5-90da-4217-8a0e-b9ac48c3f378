variable "profile" {
  type        = string
  description = "Das Profil, mit dem das Setup ausgeführt werden soll"
  default     = "default"
}

variable "environment" {
  type        = string
  description = "Die Umgebung, in der die Infrastruktur aufgesetzt wird (develop, staging, live)"
}

variable "project" {
  type        = string
  description = "Das Projekt, für welches die Infrastruktur aufgesetzt wird"
}

variable "workspace_iam_roles" {
  type = map(string)
  description = "Die Auflistung der IAM-Role-IDS in den unterschiedlichen Umgebungen"
}

variable "environment_key" {
  type = string
  description = "Der Key der aktuellen Umgebung. (live, staging oder dev für die verschiedenne Entwickler)"
}

variable "cors_origins" {
  type        = map(string)
  description = "Auflistung für professional works urls nach Environment für die CORS-Richtlinie bei AWS"
}

provider "aws" {
  profile = var.profile
  region  = "eu-central-1"

  assume_role {
    role_arn = var.workspace_iam_roles[var.environment_key]
  }
}

terraform {
  backend "s3" {
    bucket               = "demv-user-state"
    key                  = "<ordername>.tfstate"
    region               = "eu-central-1"
    dynamodb_table       = "StateLocks"
    encrypt              = true
    kms_key_id           = "arn:aws:kms:eu-central-1:036213492820:key/7f55a970-9aca-4a8e-bb5f-6257e246734d"
    workspace_key_prefix = "professionalworks"
  }
}
