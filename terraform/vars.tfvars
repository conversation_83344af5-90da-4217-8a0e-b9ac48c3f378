project = "professionalworks"

workspace_iam_roles = {
  live    = "arn:aws:iam::283412793552:role/UserAccess"
  staging = "arn:aws:iam::222956442260:role/UserAccess"
  dev     = "arn:aws:iam::417230186925:role/UserAccess"
}

cors_origins = {
  live    = "https://mvp.professional.works"
  staging = "https://preview.crm.deutscher-maklerverbund.de"
  dev     = "https://dev.deutscher-maklerverbund.de"
}

client_file_cors_origins = {
  live    = [
    "https://meine-finanzen.digital",
    "capacitor://localhost",
    "https://localhost"
  ]
  staging = [
    "https://staging.meine-finanzen.digital",
    "capacitor://localhost",
    "https://localhost"
  ]
  dev     = [
    "https://staging.meine-finanzen.digital",
    "capacitor://localhost",
    "https://localhost"
  ]
}

