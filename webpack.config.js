const config = require('laravel-mix/setup/webpack.config');

// Remove the WebpackChunkHash plugin, as it causes breakage by sometime not updating the chunkhash
// when modules are added or removed, see https://github.com/laravel-mix/laravel-mix/issues/1798
const WebpackChunkHash = config.plugins.find(
    plugin => plugin.constructor.name === 'WebpackChunkHash'
);

if (config.plugins.indexOf(WebpackChunkHash) >= 0) {
    config.plugins.splice(config.plugins.indexOf(WebpackChunkHash), 1);
}

module.exports = config;
