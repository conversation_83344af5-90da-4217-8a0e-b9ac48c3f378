const mix = require('./assets/mix');

if (mix.inProduction()) {
  // Enable cache busting via versioning
  mix.version();

  // Auto-clean chunks whenever we do a release
  mix.clearBeforeCompile(['public/js/chunk']);
}

mix.setPublicPath('public/');
mix.setResourceRoot('/');

if (mix.isTesting()) {
  // This enables JS support, which in turn activates the loaders
  // to properly parse our .vue files (otherwise there'll be errors)
  mix.js('assets/mix/setup.test.js', '');
} else {
  const kundenakte = './protected/modules/kundenakte/assets/';
  const homeAssets = 'protected/views/home/<USER>/';
  const assets = 'assets/';
  mix.compileSassIn(assets);
  mix.compileJsIn(assets);
  mix.compileSassIn(kundenakte, `../${kundenakte}/css`);
  mix.copyLegacyJsIn(assets);
  mix.compileSassIn(homeAssets);


  // Spezie<PERSON> Set-up für media previews
  mix.js([
    'node_modules/pdfjs-dist/es5/build/pdf.worker.js',
    'node_modules/pdfjs-dist/es5/build/pdf.worker.entry.js'
  ], 'js/pdf-worker.js').version();

  // Main application built with vue + vuex and asynchronous component loading
  mix.js('assets/app/index.js', 'js/app.js').version();

  // Allow custom bootstrap builds by using their source files
  // This enables the babel-loader to properly compile them
  // eslint-disable-next-line no-undef
  Mix.listen('configReady', ({ module }) => {
    module.rules.find((rule) => {
      if (rule.test.test && rule.test.test('test.js')) {
        delete rule.exclude;
        rule.include = [
          `${__dirname}/assets/`,
          `${__dirname}/node_modules/bootstrap/js/`,
          `${__dirname}/node_modules/vue2-dropzone/`,
          `${__dirname}/node_modules/@demvsystems/design-components/`,
        ];

        return true;
      }

      return false;
    });
  });
}
